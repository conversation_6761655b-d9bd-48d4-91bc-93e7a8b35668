const db = require("../utils/db");

async function initVideoManagement() {
  try {
    console.log("🎬 初始化视频管理数据表...\n");

    // 1. 创建视频管理表
    const createVideoManagementTable = `
      CREATE TABLE IF NOT EXISTS video_management (
        id INT AUTO_INCREMENT PRIMARY KEY,
        file_name VARCHAR(255) NOT NULL UNIQUE,
        title VARCHAR(255) DEFAULT NULL,
        description TEXT DEFAULT NULL,
        category VARCHAR(100) DEFAULT '其他',
        tags JSON DEFAULT NULL,
        status ENUM('online', 'offline') DEFAULT 'online',
        upload_user_id INT DEFAULT NULL,
        file_size BIGINT DEFAULT NULL,
        duration INT DEFAULT NULL,
        resolution VARCHAR(50) DEFAULT NULL,
        codec VARCHAR(50) DEFAULT NULL,
        bitrate VARCHAR(50) DEFAULT NULL,
        frame_rate VARCHAR(20) DEFAULT NULL,
        cover_image VARCHAR(255) DEFAULT NULL,
        upload_time BIGINT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_file_name (file_name),
        INDEX idx_status (status),
        INDEX idx_category (category),
        INDEX idx_upload_user_id (upload_user_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    await db.query(createVideoManagementTable);
    console.log("✅ video_management 表创建成功");

    // 2. 创建视频分类表
    const createVideoCategoriesTable = `
      CREATE TABLE IF NOT EXISTS video_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT DEFAULT NULL,
        sort_order INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_name (name),
        INDEX idx_sort_order (sort_order)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    await db.query(createVideoCategoriesTable);
    console.log("✅ video_categories 表创建成功");

    // 3. 创建视频标签表
    const createVideoTagsTable = `
      CREATE TABLE IF NOT EXISTS video_tags (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL UNIQUE,
        color VARCHAR(7) DEFAULT '#1890ff',
        usage_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_name (name),
        INDEX idx_usage_count (usage_count)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    await db.query(createVideoTagsTable);
    console.log("✅ video_tags 表创建成功");

    // 4. 插入默认分类
    const defaultCategories = [
      { name: '其他', description: '未分类视频', sort_order: 0 },
      { name: '娱乐', description: '娱乐类视频', sort_order: 1 },
      { name: '教育', description: '教育类视频', sort_order: 2 },
      { name: '音乐', description: '音乐类视频', sort_order: 3 },
      { name: '体育', description: '体育类视频', sort_order: 4 },
      { name: '游戏', description: '游戏类视频', sort_order: 5 },
      { name: '科技', description: '科技类视频', sort_order: 6 },
      { name: '生活', description: '生活类视频', sort_order: 7 }
    ];

    for (const category of defaultCategories) {
      try {
        await db.query(
          "INSERT IGNORE INTO video_categories (name, description, sort_order) VALUES (?, ?, ?)",
          [category.name, category.description, category.sort_order]
        );
      } catch (error) {
        console.log(`分类 ${category.name} 已存在，跳过`);
      }
    }
    console.log("✅ 默认分类数据插入成功");

    // 5. 插入默认标签
    const defaultTags = [
      { name: '热门', color: '#ff4d4f' },
      { name: '推荐', color: '#52c41a' },
      { name: '新上传', color: '#1890ff' },
      { name: '高清', color: '#722ed1' },
      { name: '精选', color: '#fa8c16' }
    ];

    for (const tag of defaultTags) {
      try {
        await db.query(
          "INSERT IGNORE INTO video_tags (name, color) VALUES (?, ?)",
          [tag.name, tag.color]
        );
      } catch (error) {
        console.log(`标签 ${tag.name} 已存在，跳过`);
      }
    }
    console.log("✅ 默认标签数据插入成功");

    // 6. 从现有视频文件同步数据到video_management表
    console.log("\n📊 同步现有视频文件到管理表...");
    
    const fs = require("fs");
    const path = require("path");
    
    const videoDir = path.join(__dirname, "../uploads/videos");
    const isVideoFile = (filename) => {
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'];
      return videoExtensions.some(ext => filename.toLowerCase().endsWith(ext));
    };

    if (fs.existsSync(videoDir)) {
      const files = fs.readdirSync(videoDir).filter(isVideoFile);
      
      for (const file of files) {
        try {
          const filePath = path.join(videoDir, file);
          const stats = fs.statSync(filePath);
          
          // 检查是否已存在
          const existing = await db.query(
            "SELECT id FROM video_management WHERE file_name = ?",
            [file]
          );
          
          if (existing.length === 0) {
            await db.query(`
              INSERT INTO video_management (
                file_name, title, file_size, upload_time, status
              ) VALUES (?, ?, ?, ?, ?)
            `, [
              file,
              file.replace(/\.[^/.]+$/, ""), // 去掉扩展名作为标题
              stats.size,
              stats.mtime.getTime(),
              'online'
            ]);
            console.log(`  ✅ 同步视频: ${file}`);
          }
        } catch (error) {
          console.log(`  ❌ 同步失败: ${file} - ${error.message}`);
        }
      }
    }

    console.log("\n🎉 视频管理数据表初始化完成！");
    console.log("\n📋 创建的表:");
    console.log("  - video_management: 视频管理主表");
    console.log("  - video_categories: 视频分类表");
    console.log("  - video_tags: 视频标签表");
    
  } catch (error) {
    console.error("❌ 初始化失败:", error);
  } finally {
    process.exit(0);
  }
}

// 运行初始化
initVideoManagement();
