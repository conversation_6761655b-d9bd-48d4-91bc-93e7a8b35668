<template>
  <div class="file-manager-test">
    <div class="page-header">
      <h2>
        <el-icon><Folder /></el-icon>
        文件管理器测试
      </h2>
      <p>完整的文件文件夹管理系统，支持创建、移动、重命名、删除等操作</p>
    </div>

    <!-- 文件管理器组件 -->
    <FileManager />

    <!-- 功能说明 -->
    <el-card class="info-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>功能说明</span>
        </div>
      </template>
      
      <div class="feature-list">
        <div class="feature-item">
          <el-icon class="feature-icon"><FolderAdd /></el-icon>
          <div class="feature-content">
            <h4>文件夹管理</h4>
            <ul>
              <li>创建新文件夹</li>
              <li>创建子文件夹</li>
              <li>重命名文件夹</li>
              <li>删除文件夹</li>
            </ul>
          </div>
        </div>

        <div class="feature-item">
          <el-icon class="feature-icon"><Document /></el-icon>
          <div class="feature-content">
            <h4>文件操作</h4>
            <ul>
              <li>文件移动（跨分类）</li>
              <li>文件重命名</li>
              <li>文件删除</li>
              <li>文件预览</li>
            </ul>
          </div>
        </div>

        <div class="feature-item">
          <el-icon class="feature-icon"><View /></el-icon>
          <div class="feature-content">
            <h4>视图模式</h4>
            <ul>
              <li>列表视图（详细信息）</li>
              <li>网格视图（图标模式）</li>
              <li>文件夹树形导航</li>
              <li>面包屑导航</li>
            </ul>
          </div>
        </div>

        <div class="feature-item">
          <el-icon class="feature-icon"><Setting /></el-icon>
          <div class="feature-content">
            <h4>分类管理</h4>
            <ul>
              <li>图片文件管理</li>
              <li>文档文件管理</li>
              <li>视频文件管理</li>
              <li>压缩包管理</li>
              <li>媒体文件管理</li>
            </ul>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 操作指南 -->
    <el-card class="guide-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>操作指南</span>
        </div>
      </template>
      
      <div class="guide-content">
        <div class="guide-section">
          <h4>🗂️ 文件夹操作</h4>
          <ol>
            <li><strong>创建文件夹</strong>：点击"新建文件夹"按钮，输入文件夹名称</li>
            <li><strong>创建子文件夹</strong>：在文件夹树中点击文件夹旁的"+"按钮</li>
            <li><strong>导航</strong>：点击文件夹树或双击文件夹进入</li>
            <li><strong>面包屑导航</strong>：点击面包屑中的路径快速跳转</li>
          </ol>
        </div>

        <div class="guide-section">
          <h4>📁 文件操作</h4>
          <ol>
            <li><strong>移动文件</strong>：点击"移动"按钮，选择目标分类和路径</li>
            <li><strong>重命名</strong>：点击"重命名"按钮，输入新名称</li>
            <li><strong>删除</strong>：点击"删除"按钮，确认删除操作</li>
            <li><strong>双击</strong>：双击文件夹进入，双击文件预览</li>
          </ol>
        </div>

        <div class="guide-section">
          <h4>🔄 分类切换</h4>
          <ol>
            <li><strong>选择分类</strong>：使用顶部下拉菜单切换文件分类</li>
            <li><strong>跨分类移动</strong>：使用移动功能将文件移动到其他分类</li>
            <li><strong>刷新</strong>：点击刷新按钮更新文件结构</li>
          </ol>
        </div>

        <div class="guide-section">
          <h4>👁️ 视图切换</h4>
          <ol>
            <li><strong>列表视图</strong>：显示详细的文件信息（大小、修改时间等）</li>
            <li><strong>网格视图</strong>：以图标形式显示文件，适合浏览</li>
            <li><strong>文件夹树</strong>：左侧树形结构快速导航</li>
          </ol>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Folder, FolderAdd, Document, View, Setting } from '@element-plus/icons-vue';
import FileManager from '../../components/FileManager.vue';
</script>

<style scoped>
.file-manager-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h2 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #303133;
  margin-bottom: 8px;
}

.page-header p {
  color: #606266;
  font-size: 14px;
}

.info-card, .guide-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.feature-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.feature-icon {
  font-size: 24px;
  color: #409eff;
  flex-shrink: 0;
  margin-top: 4px;
}

.feature-content h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.feature-content ul {
  margin: 0;
  padding-left: 16px;
}

.feature-content li {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}

.guide-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.guide-section h4 {
  color: #303133;
  margin-bottom: 12px;
  font-size: 16px;
}

.guide-section ol {
  margin: 0;
  padding-left: 20px;
}

.guide-section li {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.guide-section strong {
  color: #303133;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-manager-test {
    padding: 12px;
  }
  
  .feature-list {
    grid-template-columns: 1fr;
  }
  
  .feature-item {
    flex-direction: column;
    text-align: center;
  }
  
  .guide-content {
    grid-template-columns: 1fr;
  }
}
</style>
