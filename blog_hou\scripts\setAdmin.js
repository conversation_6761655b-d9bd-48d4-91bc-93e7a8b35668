// 设置管理员用户
const db = require("../utils/db");

async function setAdmin() {
  try {
    console.log("🔧 设置管理员用户...");
    
    // 将第一个用户设置为管理员
    const result = await db.query("UPDATE users SET role = 'admin' WHERE id = 19");
    
    if (result.affectedRows > 0) {
      console.log("✅ 成功将用户 ID 19 (admin123) 设置为管理员");
    } else {
      console.log("❌ 未找到用户 ID 19");
    }
    
    // 验证设置结果
    const adminUsers = await db.query("SELECT id, username, email, role FROM users WHERE role = 'admin'");
    console.log(`\n👑 当前管理员用户 (${adminUsers.length}个):`);
    adminUsers.forEach(admin => {
      console.log(`  ID: ${admin.id}, 用户名: ${admin.username}, 邮箱: ${admin.email}, 角色: ${admin.role}`);
    });
    
  } catch (error) {
    console.error("❌ 设置管理员失败:", error.message);
  } finally {
    process.exit(0);
  }
}

setAdmin();
