<template>
  <div class="system-monitor">
    <!-- 调试信息 -->
    <div v-if="false" style="background: #f0f0f0; padding: 10px; margin-bottom: 20px;">
      <h4>调试信息:</h4>
      <p>systemOverview: {{ systemOverview ? '有数据' : '无数据' }}</p>
      <p>apiStats: {{ apiStats ? '有数据' : '无数据' }}</p>
      <p>logs: {{ logs.length }}条日志</p>
      <pre>{{ JSON.stringify(systemOverview, null, 2) }}</pre>
    </div>

    <!-- 系统概览卡片 -->
    <div class="overview-cards">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="overview-card cpu-card">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="32"><Cpu /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">CPU使用率</div>
                <div class="card-value">{{ cpuUsage }}%</div>
                <div class="card-desc">{{ systemOverview?.cpu?.cores || 0 }}核心</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card memory-card">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="32"><Monitor /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">内存使用率</div>
                <div class="card-value">{{ systemOverview?.memory?.usagePercent || 0 }}%</div>
                <div class="card-desc">{{ formatBytes(systemOverview?.memory?.used || 0) }} / {{ formatBytes(systemOverview?.memory?.total || 0) }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card database-card">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="32"><DataBoard /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">数据库状态</div>
                <div class="card-value">
                  <el-tag :type="systemOverview?.database?.connected ? 'success' : 'danger'">
                    {{ systemOverview?.database?.connected ? '正常' : '异常' }}
                  </el-tag>
                </div>
                <div class="card-desc">响应时间: {{ systemOverview?.database?.responseTime || 0 }}ms</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card uptime-card">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="32"><Clock /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">系统运行时间</div>
                <div class="card-value">{{ formatUptime(systemOverview?.system?.uptime || 0) }}</div>
                <div class="card-desc">进程: {{ formatUptime(systemOverview?.system?.processUptime || 0) }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 实时监控图表 -->
    <el-row :gutter="16" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>实时性能监控</span>
              <el-button size="small" @click="toggleRealTimeMonitor">
                {{ isRealTimeActive ? '停止' : '开始' }}监控
              </el-button>
            </div>
          </template>
          <div class="chart-container" ref="performanceChartRef"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>数据库表统计</span>
          </template>
          <div class="chart-container" ref="databaseChartRef"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- API统计和系统信息 -->
    <el-row :gutter="16" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card class="info-card">
          <template #header>
            <span>API性能统计</span>
          </template>
          <div v-if="apiStats" class="api-stats">
            <div class="stat-row">
              <span class="label">总请求数:</span>
              <span class="value">{{ apiStats.totalRequests }}</span>
            </div>
            <div class="stat-row">
              <span class="label">成功率:</span>
              <span class="value success">{{ apiStats.successRate }}%</span>
            </div>
            <div class="stat-row">
              <span class="label">平均响应时间:</span>
              <span class="value">{{ apiStats.averageResponseTime }}ms</span>
            </div>
            <div class="stat-row">
              <span class="label">错误请求:</span>
              <span class="value error">{{ apiStats.errorRequests }}</span>
            </div>
          </div>
          
          <el-divider />
          
          <div class="top-endpoints">
            <h4>热门接口</h4>
            <el-table :data="apiStats?.topEndpoints || []" size="small">
              <el-table-column prop="path" label="接口路径" />
              <el-table-column prop="count" label="调用次数" width="80" />
              <el-table-column prop="avgTime" label="平均耗时" width="80">
                <template #default="scope">
                  {{ scope.row.avgTime }}ms
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="info-card">
          <template #header>
            <span>系统信息</span>
          </template>
          <div v-if="systemOverview" class="system-info">
            <div class="info-group">
              <h4>基本信息</h4>
              <div class="info-row">
                <span class="label">操作系统:</span>
                <span class="value">{{ systemOverview.system?.platform }} {{ systemOverview.system?.arch }}</span>
              </div>
              <div class="info-row">
                <span class="label">主机名:</span>
                <span class="value">{{ systemOverview.system?.hostname }}</span>
              </div>
              <div class="info-row">
                <span class="label">Node.js版本:</span>
                <span class="value">{{ systemOverview.system?.nodeVersion }}</span>
              </div>
            </div>
            
            <div class="info-group">
              <h4>进程内存</h4>
              <div class="info-row">
                <span class="label">堆内存使用:</span>
                <span class="value">{{ systemOverview.processMemory?.heapUsagePercent }}%</span>
              </div>
              <div class="info-row">
                <span class="label">已用堆内存:</span>
                <span class="value">{{ formatBytes(systemOverview.processMemory?.heapUsed || 0) }}</span>
              </div>
              <div class="info-row">
                <span class="label">总堆内存:</span>
                <span class="value">{{ formatBytes(systemOverview.processMemory?.heapTotal || 0) }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统日志 -->
    <el-card class="logs-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>系统日志</span>
          <div>
            <el-select v-model="logLevel" size="small" style="margin-right: 10px;" @change="loadLogs">
              <el-option label="全部" value="all" />
              <el-option label="信息" value="info" />
              <el-option label="警告" value="warn" />
              <el-option label="错误" value="error" />
            </el-select>
            <el-button size="small" @click="loadLogs">刷新</el-button>
          </div>
        </div>
      </template>
      <div class="logs-container">
        <div v-for="log in logs" :key="log.timestamp" class="log-item" :class="`log-${log.level.toLowerCase()}`">
          <div class="log-time">{{ formatTime(log.timestamp) }}</div>
          <div class="log-level">{{ log.level }}</div>
          <div class="log-message">{{ log.message }}</div>
          <div v-if="log.details" class="log-details">{{ JSON.stringify(log.details) }}</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Cpu, Monitor, DataBoard, Clock } from '@element-plus/icons-vue';
import { systemMonitorApi } from '../../utils/api';
import * as echarts from 'echarts';

// 响应式数据
const systemOverview = ref<any>(null);
const databaseStats = ref<any>(null);
const apiStats = ref<any>(null);
const realtimeData = ref<any>(null);
const logs = ref<any[]>([]);
const logLevel = ref('all');

// 实时监控
const isRealTimeActive = ref(false);
const realtimeInterval = ref<any>(null);
const performanceChartRef = ref<HTMLElement>();
const databaseChartRef = ref<HTMLElement>();
let performanceChart: any = null;
let databaseChart: any = null;

// 性能数据历史
const performanceHistory = ref<any[]>([]);
const maxHistoryLength = 20;

// 计算属性
const cpuUsage = ref(0);

// 初始化
onMounted(async () => {
  await loadAllData();
  await nextTick();
  initCharts();
});

onUnmounted(() => {
  if (realtimeInterval.value) {
    clearInterval(realtimeInterval.value);
  }
  if (performanceChart) {
    performanceChart.dispose();
  }
  if (databaseChart) {
    databaseChart.dispose();
  }
});

// 加载所有数据
async function loadAllData() {
  await Promise.all([
    loadSystemOverview(),
    loadDatabaseStats(),
    loadApiStats(),
    loadLogs()
  ]);
}

// 加载系统概览
async function loadSystemOverview() {
  try {
    const res = await systemMonitorApi.getOverview();
    console.log('系统概览API响应:', res);

    // 检查响应数据结构
    if (res.data && res.data.code === 200 && res.data.data) {
      // 标准响应格式
      systemOverview.value = res.data.data;
      console.log('✅ 系统概览数据设置成功(标准格式):', systemOverview.value);
      cpuUsage.value = Math.min(100, Math.round(parseFloat(systemOverview.value.memory?.usagePercent || 0)));
    } else if (res.data && res.data.system) {
      // 直接数据格式
      systemOverview.value = res.data;
      console.log('✅ 系统概览数据设置成功(直接格式):', systemOverview.value);
      cpuUsage.value = Math.min(100, Math.round(parseFloat(systemOverview.value.memory?.usagePercent || 0)));
    } else {
      console.error('❌ 系统概览API返回错误:', res.data);
    }
  } catch (error) {
    console.error('❌ 加载系统概览失败:', error);
    ElMessage.error('加载系统概览失败');
  }
}

// 加载数据库统计
async function loadDatabaseStats() {
  try {
    const res = await systemMonitorApi.getDatabaseStats();
    console.log('数据库统计API响应:', res);
    if (res.data && res.data.code === 200 && res.data.data) {
      databaseStats.value = res.data.DataBoard;
      console.log('✅ 数据库统计数据设置成功(标准格式):', databaseStats.value);
      updateDatabaseChart();
    } else if (res.data && res.data.version) {
      databaseStats.value = res.data;
      console.log('✅ 数据库统计数据设置成功(直接格式):', databaseStats.value);
      updateDatabaseChart();
    }
  } catch (error) {
    console.error('❌ 加载数据库统计失败:', error);
  }
}

// 加载API统计
async function loadApiStats() {
  try {
    const res = await systemMonitorApi.getApiStats();
    console.log('API统计响应:', res);
    if (res.data && res.data.code === 200 && res.data.data) {
      apiStats.value = res.data.data;
      console.log('✅ API统计数据设置成功(标准格式):', apiStats.value);
    } else if (res.data && res.data.totalRequests) {
      apiStats.value = res.data;
      console.log('✅ API统计数据设置成功(直接格式):', apiStats.value);
    }
  } catch (error) {
    console.error('❌ 加载API统计失败:', error);
  }
}

// 加载系统日志
async function loadLogs() {
  try {
    const res = await systemMonitorApi.getLogs({
      level: logLevel.value,
      limit: 50
    });
    console.log('系统日志API响应:', res);
    if (res.data && res.data.code === 200 && res.data.data) {
      logs.value = res.data.data.logs;
      console.log('✅ 系统日志数据设置成功(标准格式):', logs.value.length, '条日志');
    } else if (res.data && res.data.logs) {
      logs.value = res.data.logs;
      console.log('✅ 系统日志数据设置成功(直接格式):', logs.value.length, '条日志');
    }
  } catch (error) {
    console.error('❌ 加载系统日志失败:', error);
  }
}

// 加载实时数据
async function loadRealtimeData() {
  try {
    const res = await systemMonitorApi.getRealtime();
    console.log('实时数据API响应:', res);

    let data = null;
    if (res.data && res.data.code === 200 && res.data.data) {
      data = res.data.data;
    } else if (res.data && res.data.cpu) {
      data = res.data;
    }

    if (data) {
      realtimeData.value = data;
      console.log('✅ 实时数据设置成功:', data);

      // 添加到历史数据
      const newDataPoint = {
        timestamp: new Date(data.timestamp),
        cpu: Math.max(parseFloat(data.cpu?.loadAvg?.[0] || 0) * 10, parseFloat(data.memory?.usagePercent || 0) * 0.3), // CPU使用率估算
        memory: parseFloat(data.memory?.usagePercent || 0),
        heap: parseFloat(data.process?.heapUsagePercent || 0)
      };

      performanceHistory.value.push(newDataPoint);
      console.log('📈 添加性能数据点:', newDataPoint);

      // 保持历史数据长度
      if (performanceHistory.value.length > maxHistoryLength) {
        performanceHistory.value.shift();
      }

      console.log('📊 当前历史数据长度:', performanceHistory.value.length);
      updatePerformanceChart();
    } else {
      console.error('❌ 实时数据格式错误:', res.data);
    }
  } catch (error) {
    console.error('加载实时数据失败:', error);
  }
}

// 切换实时监控
function toggleRealTimeMonitor() {
  if (isRealTimeActive.value) {
    clearInterval(realtimeInterval.value);
    isRealTimeActive.value = false;
  } else {
    realtimeInterval.value = setInterval(loadRealtimeData, 2000);
    isRealTimeActive.value = true;
    loadRealtimeData();
  }
}

// 初始化图表
function initCharts() {
  console.log('🔄 初始化图表...');
  console.log('性能图表容器:', performanceChartRef.value);
  console.log('数据库图表容器:', databaseChartRef.value);

  if (performanceChartRef.value) {
    performanceChart = echarts.init(performanceChartRef.value);
    console.log('✅ 性能图表初始化成功:', performanceChart);
  } else {
    console.error('❌ 性能图表容器不存在');
  }

  if (databaseChartRef.value) {
    databaseChart = echarts.init(databaseChartRef.value);
    console.log('✅ 数据库图表初始化成功:', databaseChart);
  } else {
    console.error('❌ 数据库图表容器不存在');
  }

  updateDatabaseChart();
}

// 更新性能图表
function updatePerformanceChart() {
  console.log('🔄 更新性能图表，图表对象:', performanceChart);
  console.log('📊 历史数据长度:', performanceHistory.value.length);

  if (!performanceChart) {
    console.error('❌ 性能图表对象不存在');
    return;
  }

  if (performanceHistory.value.length === 0) {
    console.warn('⚠️ 没有历史数据');
    return;
  }

  const times = performanceHistory.value.map(item =>
    item.timestamp.toLocaleTimeString()
  );
  const cpuData = performanceHistory.value.map(item => item.cpu);
  const memoryData = performanceHistory.value.map(item => item.memory);
  const heapData = performanceHistory.value.map(item => item.heap);

  console.log('📈 图表数据:', { times, cpuData, memoryData, heapData });

  const option = {
    title: { text: '实时性能监控', left: 'center' },
    tooltip: { trigger: 'axis' },
    legend: { data: ['CPU负载', '内存使用率', '堆内存使用率'], bottom: 0 },
    xAxis: { type: 'category', data: times },
    yAxis: { type: 'value', max: 100, axisLabel: { formatter: '{value}%' } },
    series: [
      { name: 'CPU负载', type: 'line', data: cpuData, smooth: true, color: '#67c23a' },
      { name: '内存使用率', type: 'line', data: memoryData, smooth: true, color: '#e6a23c' },
      { name: '堆内存使用率', type: 'line', data: heapData, smooth: true, color: '#f56c6c' }
    ]
  };

  performanceChart.setOption(option);
  console.log('✅ 图表更新完成');
}

// 更新数据库图表
function updateDatabaseChart() {
  if (!databaseChart || !databaseStats.value) return;
  
  const tableData = databaseStats.value.tables?.slice(0, 10) || [];
  const names = tableData.map((table: any) => table.table_name);
  const sizes = tableData.map((table: any) => table.size_mb);
  
  const option = {
    title: { text: '数据库表大小 (MB)', left: 'center' },
    tooltip: { trigger: 'axis' },
    xAxis: { 
      type: 'category', 
      data: names,
      axisLabel: { rotate: 45 }
    },
    yAxis: { type: 'value' },
    series: [{
      type: 'bar',
      data: sizes,
      itemStyle: { color: '#409EFF' }
    }]
  };
  
  databaseChart.setOption(option);
}

// 工具函数
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (days > 0) return `${days}天 ${hours}小时`;
  if (hours > 0) return `${hours}小时 ${minutes}分钟`;
  return `${minutes}分钟`;
}

function formatTime(timestamp: string): string {
  return new Date(timestamp).toLocaleString();
}
</script>

<style scoped lang="less">
.system-monitor {
  padding: 20px;

  .overview-cards {
    .overview-card {
      height: 120px;

      .card-content {
        display: flex;
        align-items: center;
        height: 100%;

        .card-icon {
          margin-right: 15px;

          .el-icon {
            color: #409EFF;
          }
        }

        .card-info {
          flex: 1;

          .card-title {
            font-size: 14px;
            color: #909399;
            margin-bottom: 8px;
          }

          .card-value {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .card-desc {
            font-size: 12px;
            color: #909399;
          }
        }
      }

      &.cpu-card .card-icon .el-icon {
        color: #67c23a;
      }

      &.memory-card .card-icon .el-icon {
        color: #e6a23c;
      }

      &.database-card .card-icon .el-icon {
        color: #409EFF;
      }

      &.uptime-card .card-icon .el-icon {
        color: #909399;
      }
    }
  }

  .chart-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .chart-container {
      height: 300px;
      width: 100%;
    }
  }

  .info-card {
    .api-stats {
      .stat-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        .label {
          color: #909399;
        }

        .value {
          font-weight: 600;

          &.success {
            color: #67c23a;
          }

          &.error {
            color: #f56c6c;
          }
        }
      }
    }

    .top-endpoints {
      h4 {
        margin: 10px 0;
        color: #303133;
      }
    }

    .system-info {
      .info-group {
        margin-bottom: 20px;

        h4 {
          margin: 0 0 10px 0;
          color: #303133;
          font-size: 14px;
        }

        .info-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;

          .label {
            color: #909399;
            font-size: 13px;
          }

          .value {
            color: #303133;
            font-size: 13px;
            font-weight: 500;
          }
        }
      }
    }
  }

  .logs-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logs-container {
      max-height: 400px;
      overflow-y: auto;

      .log-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border-radius: 4px;
        margin-bottom: 4px;
        font-size: 13px;

        .log-time {
          width: 120px;
          color: #909399;
          font-family: monospace;
        }

        .log-level {
          width: 60px;
          font-weight: 600;
          text-align: center;
        }

        .log-message {
          flex: 1;
          margin: 0 10px;
        }

        .log-details {
          color: #909399;
          font-family: monospace;
          font-size: 12px;
        }

        &.log-info {
          background: #f0f9ff;
          border-left: 3px solid #409EFF;

          .log-level {
            color: #409EFF;
          }
        }

        &.log-warn {
          background: #fdf6ec;
          border-left: 3px solid #e6a23c;

          .log-level {
            color: #e6a23c;
          }
        }

        &.log-error {
          background: #fef0f0;
          border-left: 3px solid #f56c6c;

          .log-level {
            color: #f56c6c;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .system-monitor {
    padding: 10px;

    .overview-cards {
      .el-col {
        margin-bottom: 16px;
      }
    }

    .chart-container {
      height: 250px !important;
    }
  }
}
</style>
