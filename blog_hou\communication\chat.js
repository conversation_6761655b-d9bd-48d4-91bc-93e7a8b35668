// Socket.IO 写法
const { Server } = require("socket.io");
const db = require("../utils/db"); // 你的数据库工具
const logger = require("../plugin/logger");

const SYSTEM_ID = "system";
const SYSTEM_NICKNAME = "系统";

// 在线用户管理
const onlineUsers = new Map(); // socketId -> userInfo
const userSockets = new Map(); // userId -> socketId

// 统计信息
const stats = {
  totalConnections: 0,
  activeConnections: 0,
  messagesSent: 0,
  messagesReceived: 0,
  errors: 0,
  startTime: Date.now()
};

// 配置
const MAX_CONNECTIONS = 1000;
const MESSAGE_RATE_LIMIT = 100; // 每分钟最大消息数

// 工具函数
const buildSystemMessage = (message) => {
  return {
    id: SYSTEM_ID,
    nickname: SYSTEM_NICKNAME,
    avatar: "",
    message,
    type: "text",
    time: Date.now(),
  };
};

// 获取在线用户列表
const getOnlineUsersList = () => {
  const users = [];
  onlineUsers.forEach((userInfo, socketId) => {
    users.push({
      ...userInfo,
      lastSeen: Date.now(),
      connectionTime: Date.now() - (userInfo.connectedAt || Date.now())
    });
  });
  return users;
};

// 获取统计信息
const getStats = () => {
  return {
    ...stats,
    activeConnections: onlineUsers.size,
    uptime: Date.now() - stats.startTime
  };
};

// 数据库操作函数
async function saveMessage(msg) {
  const {
    id: from_id,
    nickname: from_nickname,
    avatar: from_avatar,
    to,
    type = "text",
    message,
    url = null,
    filename = null,
    time,
  } = msg;

  console.log("保存群聊消息到数据库:", {
    from_id,
    from_nickname,
    from_avatar,
    to,
    type,
    message,
    url,
    filename,
    time
  });

  // 群聊
  await db.query(
    `INSERT INTO chat_message
    (from_id, from_nickname, from_avatar, to_id, type, message, url, filename, time)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
    [
      from_id,
      from_nickname,
      from_avatar,
      to || null,
      type,
      message || "", // 图片消息可为空或为描述
      url || null, // 重点：图片URL
      filename,
      time,
    ]
  );

  console.log("群聊消息保存成功");
}

// 向指定用户发送消息 - 优化版本
function sendToUser(wss, userId, data) {
  const msg = JSON.stringify(data);
  let sent = false;

  // 优先从映射表查找
  const ws = onlineUsersMap.get(userId);
  if (ws && ws.readyState === WebSocket.OPEN) {
    try {
      ws.send(msg);
      connectionStats.messagesSent++;
      sent = true;

      logger.debug('向用户发送消息成功', {
        userId,
        messageType: data.type
      });
    } catch (error) {
      connectionStats.errors++;
      logger.error('向用户发送消息失败', {
        userId,
        error: error.message
      });

      // 连接已断开，从映射表中移除
      onlineUsersMap.delete(userId);
      ws.isAlive = false;
    }
  }

  // 如果映射表查找失败，遍历所有客户端（兜底方案）
  if (!sent) {
    wss.clients.forEach((client) => {
      if (
        client.readyState === WebSocket.OPEN &&
        client.userInfo?.id === userId
      ) {
        try {
          client.send(msg);
          connectionStats.messagesSent++;
          sent = true;

          // 更新映射表
          onlineUsersMap.set(userId, client);
        } catch (error) {
          connectionStats.errors++;
          logger.error('向用户发送消息失败(遍历)', {
            userId,
            error: error.message
          });
        }
      }
    });
  }

  return sent;
}

// 构建系统消息
function buildSystemMessage(message) {
  return {
    id: SYSTEM_ID,
    nickname: SYSTEM_NICKNAME,
    avatar: "",
    message,
    type: "text",
    time: Date.now(),
  };
}

// 获取所有在线用户 - 优化版本
function getOnlineUsers(wss) {
  const users = [];
  const userIds = new Set(); // 防止重复用户

  wss.clients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN && client.userInfo && !userIds.has(client.userInfo.id)) {
      users.push({
        ...client.userInfo,
        lastSeen: client.lastHeartbeat || Date.now(),
        connectionTime: Date.now() - (client.connectedAt || Date.now())
      });
      userIds.add(client.userInfo.id);
    }
  });

  return users;
}

// 心跳检测
function heartbeat() {
  this.isAlive = true;
  this.lastHeartbeat = Date.now();

  if (this.connectionId) {
    connectionManager.updateHeartbeat(this.connectionId);
  }
}

// 清理死连接
function cleanupDeadConnections(wss) {
  const now = Date.now();
  let cleanedCount = 0;

  wss.clients.forEach((ws) => {
    if (ws.readyState === WebSocket.OPEN) {
      if (!ws.isAlive) {
        // 连接已死，终止连接
        ws.terminate();
        cleanedCount++;

        if (ws.connectionId) {
          connectionManager.removeConnection(ws.connectionId);
        }

        logger.warn('清理死连接', {
          userId: ws.userInfo?.id,
          connectionId: ws.connectionId
        });
      } else if (now - (ws.lastHeartbeat || 0) > CONNECTION_TIMEOUT) {
        // 超时连接，终止连接
        ws.terminate();
        cleanedCount++;

        if (ws.connectionId) {
          connectionManager.removeConnection(ws.connectionId);
        }

        logger.warn('清理超时连接', {
          userId: ws.userInfo?.id,
          connectionId: ws.connectionId,
          timeout: now - (ws.lastHeartbeat || 0)
        });
      } else {
        // 发送心跳检测
        ws.isAlive = false;
        try {
          ws.ping();
        } catch (error) {
          logger.error('发送心跳失败', {
            userId: ws.userInfo?.id,
            error: error.message
          });
        }
      }
    }
  });

  if (cleanedCount > 0) {
    logger.info('连接清理完成', {
      cleanedCount,
      activeConnections: connectionStats.activeConnections
    });
  }

  connectionStats.lastCleanup = now;
}

// 定期清理和统计
function startMaintenanceTasks(wss) {
  // 心跳检测和连接清理
  const heartbeatInterval = setInterval(() => {
    cleanupDeadConnections(wss);
  }, HEARTBEAT_INTERVAL);

  // 统计信息记录
  const statsInterval = setInterval(() => {
    const stats = connectionManager.getStats();
    logger.info('WebSocket统计信息', stats);

    // 如果连接数过多，记录警告
    if (stats.activeConnections > MAX_CONNECTIONS * 0.8) {
      logger.warn('WebSocket连接数接近上限', {
        activeConnections: stats.activeConnections,
        maxConnections: MAX_CONNECTIONS
      });
    }
  }, 5 * 60 * 1000); // 每5分钟记录一次

  return { heartbeatInterval, statsInterval };
}

// 存储消息
async function saveMessage(msg) {
  const {
    id: from_id,
    nickname: from_nickname,
    avatar: from_avatar,
    to,
    type = "text",
    message,
    url = null,
    filename = null,
    time,
  } = msg;

  console.log("保存群聊消息到数据库:", {
    from_id,
    from_nickname,
    from_avatar,
    to,
    type,
    message,
    url,
    filename,
    time
  });

  // 群聊
  await db.query(
    `INSERT INTO chat_message 
    (from_id, from_nickname, from_avatar, to_id, type, message, url, filename, time)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
    [
      from_id,
      from_nickname,
      from_avatar,
      to || null,
      type,
      message || "", // 图片消息可为空或为描述
      url || null, // 重点：图片URL
      filename,
      time,
    ]
  );
  
  console.log("群聊消息保存成功");
}

// 存储离线消息（未读）
async function saveOfflineMessage(msg, isRead = false) {
  console.log("保存私聊消息到数据库:", {
    from_id: msg.id,
    from_nickname: msg.nickname,
    from_avatar: msg.avatar,
    to_id: msg.to,
    type: msg.type || "text",
    message: msg.message || "",
    url: msg.url || null,
    filename: msg.filename || null,
    time: msg.time,
    is_read: isRead ? 1 : 0
  });

  await db.query(
    `INSERT INTO offline_messages (from_id, from_nickname, from_avatar, to_id, type, message, url, filename, time, is_read)
     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
    [
      msg.id,
      msg.nickname,
      msg.avatar, // 这里保存的是头像URL
      msg.to,
      msg.type || "text",
      msg.message || "",
      msg.url || null,
      msg.filename || null,
      msg.time,
      isRead ? 1 : 0
    ]
  );
  
  console.log("私聊消息保存成功");
}

// 获取某用户未读离线消息
async function getOfflineMessages(userId) {
  const rows = await db.query(
    `SELECT * FROM offline_messages WHERE to_id = ? AND is_read = 0 ORDER BY time ASC`,
    [userId]
  );
  return rows;
}

// 标记离线消息已读
async function markOfflineMessagesRead(userId) {
  await db.query(
    `UPDATE offline_messages SET is_read = 1 WHERE to_id = ? AND is_read = 0`,
    [userId]
  );
}

// 是否为好友
const isFriend = async (userId, friendId) => {
  const rows = await db.query(
    "SELECT 1 FROM friends WHERE user_id=? AND friend_id=?",
    [userId, friendId]
  );
  return rows.length > 0;
};

// 发送私聊消息，找不到连接则存离线消息
async function sendToUserWithOffline(userId, data) {
  const ws = onlineUsersMap.get(userId);
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify(data));
    console.log(`✅ 正在向用户 ${userId} 推送私聊消息`, data);
    return true;
  } else {
    console.warn(`❌ 未找到用户 ${userId} 的连接，消息将存为离线`);
    return false;
  }
}

// 广播系统消息 - 优化版本
function broadcastSystemMsg(wss, message, excludeUserId = null) {
  const systemMessage = buildSystemMessage(message);
  broadcast(wss, systemMessage, excludeUserId);
}

// 主逻辑 - 优化版本
function setupChat(server) {
  const wss = new WebSocket.Server({
    server,
    perMessageDeflate: {
      zlibDeflateOptions: {
        level: 3,
        chunkSize: 1024,
      },
      threshold: 1024,
      concurrencyLimit: 10,
      clientMaxWindowBits: 13,
      serverMaxWindowBits: 13,
    },
    maxPayload: 16 * 1024 * 1024, // 16MB 最大消息大小
    clientTracking: true
  });

  // 启动维护任务
  const maintenanceIntervals = startMaintenanceTasks(wss);

  wss.on("connection", (ws, req) => {
    // 检查连接数限制
    if (connectionStats.activeConnections >= MAX_CONNECTIONS) {
      logger.warn('连接数达到上限，拒绝新连接', {
        activeConnections: connectionStats.activeConnections,
        maxConnections: MAX_CONNECTIONS,
        clientIP: req.socket.remoteAddress
      });

      ws.close(1013, '服务器繁忙，请稍后再试');
      return;
    }

    // 初始化连接属性
    ws.isAlive = true;
    ws.connectedAt = Date.now();
    ws.lastHeartbeat = Date.now();

    // 设置心跳响应
    ws.on('pong', heartbeat);

    // 连接超时处理
    const connectionTimeout = setTimeout(() => {
      if (!ws.userInfo) {
        logger.warn('连接超时未认证，关闭连接', {
          clientIP: req.socket.remoteAddress
        });
        ws.close(1000, '连接超时');
      }
    }, 10000); // 10秒内必须发送用户信息
    ws.once("message", async (msg) => {
      try {
        clearTimeout(connectionTimeout);

        const data = JSON.parse(msg);

        // 验证用户信息
        if (!data.id || !data.nickname) {
          throw new Error('缺少必要的用户信息');
        }

        // 处理头像URL - 如果是纯文件名，则保持原样；如果为空则使用默认
        let avatarUrl = data.avatar || 'moren.png';
        // 如果包含路径，提取文件名
        if (avatarUrl.includes('/')) {
          avatarUrl = avatarUrl.split('/').pop();
        }

        const userInfo = {
          id: data.id,
          nickname: data.nickname,
          avatar: avatarUrl,
        };

        // 检查是否已有相同用户连接
        const existingWs = onlineUsersMap.get(data.id);
        if (existingWs && existingWs.readyState === WebSocket.OPEN) {
          logger.info('用户重复连接，关闭旧连接', {
            userId: data.id,
            nickname: data.nickname
          });
          existingWs.close(1000, '新连接已建立');
        }

        // 添加到连接管理器
        const connectionId = connectionManager.addConnection(ws, userInfo);

        // 广播在线用户更新
        const onlineUsers = getOnlineUsers(wss);
        broadcast(wss, { type: "onlineUsers", users: onlineUsers });

        // 发送欢迎消息
        broadcastSystemMsg(
          wss,
          `${userInfo.nickname}进入聊天室，当前在线人数：${onlineUsers.length}`,
          userInfo.id
        );

        // 新用户上线，推送离线消息
        const offlineMessages = await getOfflineMessages(data.id);
        if (offlineMessages.length > 0) {
          console.log(`📨 向用户 ${data.id} 推送 ${offlineMessages.length} 条离线消息`);
          for (const msg of offlineMessages) {
            ws.send(JSON.stringify(msg));
          }
          // 标记已读
          await markOfflineMessagesRead(data.id);
        }

        ws.on("message", async (msg2) => {
          try {
            // 检查消息频率限制
            if (!connectionManager.checkRateLimit(connectionId)) {
              ws.send(JSON.stringify(buildSystemMessage("消息发送过于频繁，请稍后再试")));
              logger.warn('消息频率限制触发', {
                userId: ws.userInfo.id,
                connectionId
              });
              return;
            }

            const data2 = JSON.parse(msg2);
            data2.time = Date.now();

            // 更新连接统计
            connectionManager.incrementMessageCount(connectionId);

            if (data2.to) {
              // 私聊
              // 私聊：校验是否为好友
              if (await isFriend(data2.id, data2.to)) {
                // 发送给目标用户（如果在线）
                const targetOnline = await sendToUserWithOffline(data2.to, data2);
                // 发送给发送者（确认消息已发送）
                sendToUser(wss, data2.id, data2);
                // 私聊消息保存到offline_messages表，根据目标用户是否在线决定已读状态
                await saveOfflineMessage(data2, targetOnline);
                console.log("私聊消息：", data2, "目标用户在线：", targetOnline);
              } else {
                sendToUser(
                  wss,
                  data2.id,
                  buildSystemMessage("请先添加对方为好友并通过后才能私聊")
                );
              }
            } else {
              // 群聊
              // 保证有 id、nickname、avatar 字段
              data2.id = data2.id || ws.userInfo.id;
              data2.nickname = data2.nickname || ws.userInfo.nickname;
              data2.avatar = data2.avatar || ws.userInfo.avatar;
              data2.from_avatar = data2.avatar; // 为了显示时使用
              // 群聊消息的to_id应该为null
              data2.to = null;
              broadcast(wss, data2);
              console.log("群聊消息：", data2);
              await saveMessage(data2);
            }
          } catch (err) {
            console.error("消息处理错误：", err);
            ws.send(
              JSON.stringify(buildSystemMessage("消息格式错误：" + err.message))
            );
          }
        });
      } catch (err) {
        ws.send(
          JSON.stringify(buildSystemMessage("用户信息格式错误：" + err.message))
        );
        ws.close();
      }
    });

    ws.on("close", (code, reason) => {
      if (ws.connectionId) {
        connectionManager.removeConnection(ws.connectionId);
      }

      if (ws.userInfo && ws.userInfo.id) {
        const onlineUsers = getOnlineUsers(wss);
        broadcast(wss, { type: "onlineUsers", users: onlineUsers });

        broadcastSystemMsg(
          wss,
          `${ws.userInfo.nickname}离开聊天室，当前在线人数：${onlineUsers.length}`,
          ws.userInfo.id
        );

        logger.info('用户断开连接', {
          userId: ws.userInfo.id,
          nickname: ws.userInfo.nickname,
          connectionId: ws.connectionId,
          code,
          reason: reason?.toString(),
          activeConnections: connectionStats.activeConnections
        });
      }
    });

    ws.on("error", (error) => {
      connectionStats.errors++;
      logger.error('WebSocket连接错误', {
        userId: ws.userInfo?.id,
        connectionId: ws.connectionId,
        error: error.message
      });

      if (ws.connectionId) {
        connectionManager.removeConnection(ws.connectionId);
      }
    });
  });

  // 服务器关闭时的清理
  wss.on('close', () => {
    clearInterval(maintenanceIntervals.heartbeatInterval);
    clearInterval(maintenanceIntervals.statsInterval);

    logger.info('WebSocket服务器已关闭', {
      finalStats: connectionManager.getStats()
    });
  });

  // 添加获取统计信息的方法
  wss.getStats = () => connectionManager.getStats();
  wss.getOnlineUsers = () => getOnlineUsers(wss);
  wss.broadcastMessage = (data, excludeUserId) => broadcast(wss, data, excludeUserId);
  wss.sendToUser = (userId, data) => sendToUser(wss, userId, data);
  wss.getConnectionCount = () => connectionStats.activeConnections;
  wss.cleanup = () => cleanupDeadConnections(wss);

  // 重写close方法以支持优雅关闭
  const originalClose = wss.close.bind(wss);
  wss.close = (callback) => {
    logger.info('正在关闭WebSocket服务器...');

    // 清理维护任务
    if (maintenanceIntervals.heartbeatInterval) {
      clearInterval(maintenanceIntervals.heartbeatInterval);
    }
    if (maintenanceIntervals.statsInterval) {
      clearInterval(maintenanceIntervals.statsInterval);
    }

    // 通知所有客户端服务器即将关闭
    wss.clients.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(JSON.stringify({
            type: 'server_shutdown',
            message: '服务器正在关闭，请稍后重新连接'
          }));
          ws.close(1000, 'Server shutting down');
        } catch (error) {
          ws.terminate();
        }
      }
    });

    // 清理数据
    onlineUsersMap.clear();
    connectionManager.connections.clear();
    connectionStats.activeConnections = 0;

    // 调用原始close方法
    originalClose(callback);

    logger.info('WebSocket服务器已关闭');
  };

  logger.info('WebSocket服务器已启动', {
    maxConnections: MAX_CONNECTIONS,
    heartbeatInterval: HEARTBEAT_INTERVAL,
    connectionTimeout: CONNECTION_TIMEOUT
  });

  return wss;
}

module.exports = setupChat;
