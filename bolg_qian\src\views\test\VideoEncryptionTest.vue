<template>
  <div class="test-page">
    <el-card class="test-card">
      <template #header>
        <h2>🧪 视频加密功能测试</h2>
      </template>
      
      <div class="test-section">
        <h3>📋 测试清单</h3>
        <el-checkbox-group v-model="completedTests">
          <div class="test-item" v-for="test in testItems" :key="test.id">
            <el-checkbox :label="test.id">
              {{ test.name }}
            </el-checkbox>
            <el-text size="small" type="info">{{ test.description }}</el-text>
          </div>
        </el-checkbox-group>
      </div>

      <el-divider />

      <div class="test-section">
        <h3>🎬 视频加密组件</h3>
        <Shipinjiami />
      </div>

      <el-divider />

      <div class="test-section">
        <h3>📊 测试结果</h3>
        <div class="test-results">
          <el-progress 
            :percentage="testProgress" 
            :status="testProgress === 100 ? 'success' : 'active'"
            :stroke-width="10"
          >
            <template #default="{ percentage }">
              <span class="progress-text">{{ percentage }}% 完成</span>
            </template>
          </el-progress>
          
          <div class="test-summary">
            <el-tag :type="testProgress === 100 ? 'success' : 'info'" size="large">
              已完成: {{ completedTests.length }} / {{ testItems.length }} 项测试
            </el-tag>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import Shipinjiami from '@/components/Shipinjiami.vue';

const completedTests = ref([]);

const testItems = [
  {
    id: 'ui-design',
    name: '🎨 UI设计优化',
    description: '检查新的现代化界面设计是否正常显示'
  },
  {
    id: 'file-format',
    name: '📁 文件格式自动补充',
    description: '测试输入文件名时是否自动补充正确的后缀'
  },
  {
    id: 'mode-switch',
    name: '🔄 模式切换',
    description: '测试加密/解密模式切换是否正常工作'
  },
  {
    id: 'file-validation',
    name: '✅ 文件验证',
    description: '测试文件格式验证和错误提示'
  },
  {
    id: 'progress-display',
    name: '📊 进度显示',
    description: '测试处理进度是否正确显示'
  },
  {
    id: 'log-system',
    name: '📝 日志系统',
    description: '测试日志记录和显示功能'
  },
  {
    id: 'error-handling',
    name: '🚨 错误处理',
    description: '测试各种错误情况的处理'
  },
  {
    id: 'performance-stats',
    name: '📈 性能统计',
    description: '测试性能统计数据的显示'
  },
  {
    id: 'file-selector',
    name: '📂 文件选择器',
    description: '测试文件选择对话框功能'
  },
  {
    id: 'responsive-design',
    name: '📱 响应式设计',
    description: '测试在不同屏幕尺寸下的显示效果'
  }
];

const testProgress = computed(() => {
  return Math.round((completedTests.value.length / testItems.length) * 100);
});
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.test-card h2 {
  margin: 0;
  color: #409eff;
  text-align: center;
}

.test-section {
  margin-bottom: 24px;
}

.test-section h3 {
  color: #2c3e50;
  margin-bottom: 16px;
  font-size: 18px;
}

.test-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;
  padding: 8px;
  border-radius: 6px;
  background: #f8f9fa;
}

.test-item .el-text {
  margin-top: 4px;
  margin-left: 24px;
}

.test-results {
  text-align: center;
}

.progress-text {
  font-weight: 600;
  color: #409eff;
}

.test-summary {
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .test-page {
    padding: 10px;
  }
  
  .test-section h3 {
    font-size: 16px;
  }
}
</style>
