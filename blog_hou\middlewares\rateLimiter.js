const logger = require('../plugin/logger');
const redisCache = require('../utils/redisCache');

// 内存存储（Redis不可用时的备选方案）
const memoryStore = new Map();

class RateLimiter {
  constructor(options = {}) {
    this.windowMs = options.windowMs || 15 * 60 * 1000; // 15分钟
    this.maxRequests = options.maxRequests || 100;
    this.message = options.message || '请求过于频繁，请稍后再试';
    this.skipSuccessfulRequests = options.skipSuccessfulRequests || false;
    this.skipFailedRequests = options.skipFailedRequests || false;
  }

  async getKey(ctx) {
    // 优先使用用户ID，其次使用IP
    const userId = ctx.state.user?.id;
    const ip = ctx.ip || ctx.request.ip;
    
    return userId ? `rate_limit:user:${userId}` : `rate_limit:ip:${ip}`;
  }

  async increment(key) {
    const now = Date.now();
    const windowStart = now - this.windowMs;

    if (redisCache.isAvailable()) {
      // 使用Redis存储
      try {
        const current = await redisCache.get(key) || { count: 0, resetTime: now + this.windowMs };
        
        // 检查是否需要重置窗口
        if (now > current.resetTime) {
          current.count = 1;
          current.resetTime = now + this.windowMs;
        } else {
          current.count++;
        }
        
        await redisCache.set(key, current, Math.ceil(this.windowMs / 1000));
        
        return {
          count: current.count,
          resetTime: current.resetTime,
          remaining: Math.max(0, this.maxRequests - current.count)
        };
      } catch (error) {
        logger.error('Redis限流错误:', error);
        // 降级到内存存储
        return this.incrementMemory(key, windowStart);
      }
    } else {
      // 使用内存存储
      return this.incrementMemory(key, windowStart);
    }
  }

  incrementMemory(key, windowStart) {
    const now = Date.now();
    
    if (!memoryStore.has(key)) {
      memoryStore.set(key, []);
    }
    
    const requests = memoryStore.get(key);
    
    // 清理过期的请求记录
    const validRequests = requests.filter(time => time > windowStart);
    
    // 添加当前请求
    validRequests.push(now);
    
    memoryStore.set(key, validRequests);
    
    return {
      count: validRequests.length,
      resetTime: now + this.windowMs,
      remaining: Math.max(0, this.maxRequests - validRequests.length)
    };
  }

  middleware() {
    return async (ctx, next) => {
      const key = await this.getKey(ctx);
      const result = await this.increment(key);
      
      // 设置响应头
      ctx.set({
        'X-RateLimit-Limit': this.maxRequests.toString(),
        'X-RateLimit-Remaining': result.remaining.toString(),
        'X-RateLimit-Reset': new Date(result.resetTime).toISOString()
      });
      
      // 检查是否超过限制
      if (result.count > this.maxRequests) {
        ctx.status = 429;
        ctx.body = {
          success: false,
          message: this.message,
          retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000)
        };
        
        logger.warn('API限流触发', {
          key,
          count: result.count,
          limit: this.maxRequests,
          ip: ctx.ip,
          path: ctx.path,
          userAgent: ctx.get('User-Agent')
        });
        
        return;
      }
      
      try {
        await next();
        
        // 如果配置了跳过成功请求，则不计入限制
        if (this.skipSuccessfulRequests && ctx.status < 400) {
          // 这里可以实现减少计数的逻辑
        }
      } catch (error) {
        // 如果配置了跳过失败请求，则不计入限制
        if (this.skipFailedRequests && ctx.status >= 400) {
          // 这里可以实现减少计数的逻辑
        }
        throw error;
      }
    };
  }
}

// 不同端点的限流配置
const rateLimitConfigs = {
  // 登录接口 - 严格限制
  login: new RateLimiter({
    windowMs: 15 * 60 * 1000, // 15分钟
    maxRequests: 5,           // 最多5次尝试
    message: '登录尝试过于频繁，请15分钟后再试'
  }),
  
  // 注册接口 - 中等限制
  register: new RateLimiter({
    windowMs: 60 * 60 * 1000, // 1小时
    maxRequests: 3,           // 最多3次注册
    message: '注册过于频繁，请1小时后再试'
  }),
  
  // 上传接口 - 中等限制
  upload: new RateLimiter({
    windowMs: 10 * 60 * 1000, // 10分钟
    maxRequests: 20,          // 最多20次上传
    message: '上传过于频繁，请稍后再试'
  }),
  
  // 一般API - 宽松限制
  general: new RateLimiter({
    windowMs: 15 * 60 * 1000, // 15分钟
    maxRequests: 100,         // 最多100次请求
    message: '请求过于频繁，请稍后再试'
  })
};

// 根据路径选择限流器
function getRateLimiter(path) {
  if (path.includes('/user/login')) {
    return rateLimitConfigs.login;
  } else if (path.includes('/user/reg')) {
    return rateLimitConfigs.register;
  } else if (path.includes('/upload')) {
    return rateLimitConfigs.upload;
  } else {
    return rateLimitConfigs.general;
  }
}

// 智能限流中间件
function smartRateLimitMiddleware() {
  return async (ctx, next) => {
    const rateLimiter = getRateLimiter(ctx.path);
    return rateLimiter.middleware()(ctx, next);
  };
}

// 清理过期的内存记录
setInterval(() => {
  const now = Date.now();
  for (const [key, requests] of memoryStore.entries()) {
    const validRequests = requests.filter(time => time > now - 15 * 60 * 1000);
    if (validRequests.length === 0) {
      memoryStore.delete(key);
    } else {
      memoryStore.set(key, validRequests);
    }
  }
}, 5 * 60 * 1000); // 每5分钟清理一次

module.exports = {
  RateLimiter,
  smartRateLimitMiddleware,
  rateLimitConfigs
};
