const Router = require("koa-router");
const { handleResponse } = require("../../middlewares/responseHandler");
const verifyToken = require("../../middlewares/koaJwtMiddleware");
const articleLikesService = require("../../utils/sql/articleLikesService");
const UAParser = require('ua-parser-js');

const articleLikes = new Router();

// 点赞/取消点赞文章（支持匿名用户）
articleLikes.post("/toggle", async (ctx) => {
  try {
    const { article_id, like_type = 'like' } = ctx.request.body;
    
    if (!article_id) {
      return handleResponse(ctx, 400, { error: "文章ID不能为空" });
    }

    if (!['like', 'dislike'].includes(like_type)) {
      return handleResponse(ctx, 400, { error: "点赞类型无效" });
    }

    // 获取用户信息
    const user = ctx.state.user;
    const user_id = user?.userId || user?.id || null;
    
    // 获取IP和用户代理信息
    const ip_address = ctx.request.ip || ctx.ip;
    const user_agent = ctx.request.header['user-agent'] || '';
    
    // 解析设备信息
    const parser = new UAParser(user_agent);
    const device_info = parser.getResult();
    
    // 生成会话ID（用于匿名用户）
    const crypto = require('crypto');
    const session_id = crypto.createHash('md5')
      .update(ip_address + user_agent + new Date().toDateString())
      .digest('hex');

    // 检查文章是否存在
    const articleExists = await articleLikesService.checkArticleExists(article_id);

    if (!articleExists) {
      return handleResponse(ctx, 404, { error: "文章不存在" });
    }

    // 检查是否已经点赞过
    let existingLike;
    if (user_id) {
      // 登录用户
      existingLike = await articleLikesService.checkUserLike(user_id, article_id);
    } else {
      // 匿名用户
      existingLike = await articleLikesService.checkAnonymousLike(ip_address, session_id, article_id);
    }

    let action = '';
    let message = '';

    if (existingLike) {
      const currentLike = existingLike;

      if (currentLike.like_type === like_type) {
        // 取消点赞
        await articleLikesService.removeLike(currentLike.id);
        action = 'removed';
        message = like_type === 'like' ? '取消点赞成功' : '取消踩成功';
      } else {
        // 切换点赞类型
        await articleLikesService.updateLikeType(currentLike.id, like_type);
        action = 'updated';
        message = like_type === 'like' ? '已切换为点赞' : '已切换为踩';
      }
    } else {
      // 新增点赞
      await articleLikesService.addLike(user_id, article_id, ip_address, user_agent, session_id, like_type);

      action = 'added';
      message = like_type === 'like' ? '点赞成功' : '踩成功';
    }

    // 获取最新的点赞统计
    const stats = await getArticleLikeStats(article_id);

    return handleResponse(ctx, 200, {
      message,
      data: {
        action,
        like_type,
        stats
      }
    });

  } catch (error) {
    console.error('点赞操作失败:', error);
    return handleResponse(ctx, 500, { error: "点赞操作失败" });
  }
});

// 获取文章点赞统计
articleLikes.get("/stats/:articleId", async (ctx) => {
  try {
    const { articleId } = ctx.params;
    
    if (!articleId) {
      return handleResponse(ctx, 400, { error: "文章ID不能为空" });
    }

    const stats = await getArticleLikeStats(articleId);
    
    // 如果用户已登录，检查用户的点赞状态
    let userLikeStatus = null;
    const user = ctx.state.user;
    const user_id = user?.userId || user?.id;
    
    if (user_id) {
      userLikeStatus = await articleLikesService.getUserLikeStatus(user_id, articleId);
    } else {
      // 匿名用户，通过IP和session检查
      const ip_address = ctx.request.ip || ctx.ip;
      const user_agent = ctx.request.header['user-agent'] || '';
      const crypto = require('crypto');
      const session_id = crypto.createHash('md5')
        .update(ip_address + user_agent + new Date().toDateString())
        .digest('hex');

      userLikeStatus = await articleLikesService.getAnonymousLikeStatus(ip_address, session_id, articleId);
    }

    return handleResponse(ctx, 200, {
      data: {
        ...stats,
        userLikeStatus
      }
    });

  } catch (error) {
    console.error('获取点赞统计失败:', error);
    return handleResponse(ctx, 500, { error: "获取点赞统计失败" });
  }
});

// 获取文章的点赞用户列表（需要登录）
articleLikes.get("/users/:articleId", verifyToken, async (ctx) => {
  try {
    const { articleId } = ctx.params;
    const { page = 1, limit = 20, like_type = 'like' } = ctx.query;
    const offset = (page - 1) * limit;

    if (!articleId) {
      return handleResponse(ctx, 400, { error: "文章ID不能为空" });
    }

    // 获取点赞用户列表（只显示登录用户）
    const users = await articleLikesService.getArticleLikeUsers(articleId, like_type, limit, offset);

    // 获取总数
    const total = await articleLikesService.getArticleLikeUsersCount(articleId, like_type);

    return handleResponse(ctx, 200, {
      data: {
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取点赞用户列表失败:', error);
    return handleResponse(ctx, 500, { error: "获取点赞用户列表失败" });
  }
});

// 获取用户的点赞历史（需要登录）
articleLikes.get("/history", verifyToken, async (ctx) => {
  try {
    const user = ctx.state.user;
    const user_id = user?.userId || user?.id;
    const { page = 1, limit = 20, like_type } = ctx.query;
    const offset = (page - 1) * limit;
     console.log(user,user_id)

    if (!user_id) {
      return handleResponse(ctx, 401, { error: "用户未登录" });
    }

    let whereClause = 'al.user_id = ?';
    let params = [user_id];
    
    if (like_type && ['like', 'dislike'].includes(like_type)) {
      whereClause += ' AND al.like_type = ?';
      params.push(like_type);
    }

    // 获取用户点赞历史
    const history = await articleLikesService.getUserLikeHistory(user_id, like_type, limit, offset);

    // 获取总数
    const total = await articleLikesService.getUserLikeHistoryCount(user_id, like_type);

    return handleResponse(ctx, 200, {
      data: {
        history,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取用户点赞历史失败:', error);
    return handleResponse(ctx, 500, { error: "获取用户点赞历史失败" });
  }
});

// 辅助函数：获取文章点赞统计
async function getArticleLikeStats(articleId) {
  return await articleLikesService.getArticleLikeStats(articleId);
}

module.exports = articleLikes;
