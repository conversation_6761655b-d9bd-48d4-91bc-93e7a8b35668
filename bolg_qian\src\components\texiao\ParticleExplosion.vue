<template>
  <div class="explosion" v-show="visible" @animationend="onAnimationEnd">
    <div class="particle" v-for="n in 30" :key="n" :style="randomStyle(n)"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

const visible = ref(true);

const emit = defineEmits(["done"]);

function onAnimationEnd() {
  visible.value = false;
  // 爆炸动画结束后emit事件通知父组件
  emit("done");
}

// 随机粒子位置和颜色
function randomStyle(n: number) {
  const angle = (360 / 30) * n;
  const distance = 100 + Math.random() * 50;
  const x = Math.cos((angle * Math.PI) / 180) * distance;
  const y = Math.sin((angle * Math.PI) / 180) * distance;
  const colors = ["#ff5050", "#ffcc00", "#00ccff", "#00ff99", "#ff33cc"];
  const color = colors[Math.floor(Math.random() * colors.length)];
  return {
    transform: `translate(${x}px, ${y}px)`,
    backgroundColor: color,
  };
}
</script>

<style scoped>
.explosion {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 200px;
  height: 200px;
  margin-left: -100px;
  margin-top: -100px;
  pointer-events: none;
  z-index: 9999;
  animation: explosionFade 1s forwards;
}

.particle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  opacity: 1;
  animation: particleMove 1s forwards;
}

@keyframes particleMove {
  from {
    opacity: 1;
    transform: translate(0, 0);
  }

  to {
    opacity: 0;
  }
}

@keyframes explosionFade {
  to {
    opacity: 0;
  }
}
</style>
