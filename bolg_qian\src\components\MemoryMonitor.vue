<template>
  <div class="memory-monitor" v-if="showMonitor">
    <el-card class="monitor-card" :class="{ 'warning': isWarning, 'critical': isCritical }">
      <template #header>
        <div class="monitor-header">
          <span>内存监控</span>
          <el-button size="small" @click="toggleMonitor" type="text">
            {{ expanded ? '收起' : '展开' }}
          </el-button>
        </div>
      </template>
      
      <div v-if="expanded" class="monitor-content">
        <!-- 内存使用情况 -->
        <div class="memory-info">
          <div class="memory-item">
            <span class="label">已用内存:</span>
            <span class="value">{{ formatMemory(memoryInfo.usedJSHeapSize) }}</span>
          </div>
          <div class="memory-item">
            <span class="label">总内存:</span>
            <span class="value">{{ formatMemory(memoryInfo.totalJSHeapSize) }}</span>
          </div>
          <div class="memory-item">
            <span class="label">使用率:</span>
            <span class="value" :class="getUsageClass()">{{ usagePercentage }}%</span>
          </div>
        </div>

        <!-- 内存使用进度条 -->
        <el-progress 
          :percentage="usagePercentage" 
          :color="getProgressColor()"
          :show-text="false"
          class="memory-progress"
        />

        <!-- 视频统计 -->
        <div class="video-stats" v-if="videoStats">
          <div class="stats-item">
            <span class="label">活跃视频:</span>
            <span class="value">{{ videoStats.totalVideos }}</span>
          </div>
          <div class="stats-item">
            <span class="label">播放中:</span>
            <span class="value">{{ videoStats.playingVideos }}</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="actions">
          <el-button size="small" @click="forceCleanup" type="warning">
            清理内存
          </el-button>
          <el-button size="small" @click="refreshStats" type="primary">
            刷新
          </el-button>
        </div>

        <!-- 警告信息 -->
        <div v-if="warningMessage" class="warning-message">
          <el-alert :title="warningMessage" type="warning" :closable="false" />
        </div>
      </div>
      
      <!-- 简化显示 -->
      <div v-else class="monitor-simple">
        <span class="usage-text" :class="getUsageClass()">
          内存: {{ usagePercentage }}%
        </span>
        <div class="usage-indicator" :class="getUsageClass()"></div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElCard, ElButton, ElProgress, ElAlert, ElMessage } from 'element-plus'
import { videoMemoryManager } from '@/utils/videoMemoryManager'

interface MemoryInfo {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
}

const showMonitor = ref(false)
const expanded = ref(false)
const memoryInfo = ref<MemoryInfo>({
  usedJSHeapSize: 0,
  totalJSHeapSize: 0,
  jsHeapSizeLimit: 0
})
const videoStats = ref(null)
const updateInterval = ref<number>()

// 计算使用率
const usagePercentage = computed(() => {
  if (memoryInfo.value.totalJSHeapSize === 0) return 0
  return Math.round((memoryInfo.value.usedJSHeapSize / memoryInfo.value.totalJSHeapSize) * 100)
})

// 是否显示警告
const isWarning = computed(() => usagePercentage.value >= 70)
const isCritical = computed(() => usagePercentage.value >= 85)

// 警告信息
const warningMessage = computed(() => {
  if (usagePercentage.value >= 85) {
    return '内存使用率过高！建议立即清理视频缓存'
  } else if (usagePercentage.value >= 70) {
    return '内存使用率较高，建议关闭部分视频'
  }
  return ''
})

// 格式化内存大小
const formatMemory = (bytes: number): string => {
  const mb = bytes / (1024 * 1024)
  return `${mb.toFixed(1)}MB`
}

// 获取使用率样式类
const getUsageClass = () => {
  if (usagePercentage.value >= 85) return 'critical'
  if (usagePercentage.value >= 70) return 'warning'
  return 'normal'
}

// 获取进度条颜色
const getProgressColor = () => {
  if (usagePercentage.value >= 85) return '#f56c6c'
  if (usagePercentage.value >= 70) return '#e6a23c'
  return '#67c23a'
}

// 切换监控器显示
const toggleMonitor = () => {
  expanded.value = !expanded.value
}

// 更新内存信息
const updateMemoryInfo = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory as MemoryInfo
    memoryInfo.value = { ...memory }
    
    // 自动显示监控器（当内存使用率超过70%时）
    if (usagePercentage.value >= 70 && !showMonitor.value) {
      showMonitor.value = true
      ElMessage.warning('检测到内存使用率较高，已开启内存监控')
    }
  }
}

// 更新视频统计
const updateVideoStats = () => {
  videoStats.value = videoMemoryManager.getStats()
}

// 刷新统计信息
const refreshStats = () => {
  updateMemoryInfo()
  updateVideoStats()
  ElMessage.success('统计信息已刷新')
}

// 强制清理内存
const forceCleanup = () => {
  try {
    // 清理视频内存
    videoMemoryManager.cleanup()
    
    // 触发垃圾回收（如果可用）
    if ('gc' in window) {
      (window as any).gc()
    }
    
    // 延迟更新统计
    setTimeout(() => {
      updateMemoryInfo()
      updateVideoStats()
    }, 1000)
    
    ElMessage.success('内存清理完成')
  } catch (error) {
    console.error('内存清理失败:', error)
    ElMessage.error('内存清理失败')
  }
}

// 启动监控
const startMonitoring = () => {
  updateInterval.value = window.setInterval(() => {
    updateMemoryInfo()
    updateVideoStats()
  }, 2000) // 每2秒更新一次
}

// 停止监控
const stopMonitoring = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
    updateInterval.value = undefined
  }
}

onMounted(() => {
  // 检查是否支持内存监控
  if ('memory' in performance) {
    startMonitoring()
    updateMemoryInfo()
    updateVideoStats()
  } else {
    console.warn('当前浏览器不支持内存监控')
  }
})

onUnmounted(() => {
  stopMonitoring()
})

// 暴露控制方法
defineExpose({
  show: () => { showMonitor.value = true },
  hide: () => { showMonitor.value = false },
  toggle: () => { showMonitor.value = !showMonitor.value }
})
</script>

<style scoped>
.memory-monitor {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  min-width: 200px;
}

.monitor-card {
  transition: all 0.3s ease;
}

.monitor-card.warning {
  border-color: #e6a23c;
}

.monitor-card.critical {
  border-color: #f56c6c;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.monitor-content {
  space-y: 12px;
}

.memory-info,
.video-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.memory-item,
.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  font-size: 12px;
  color: #666;
}

.value {
  font-size: 12px;
  font-weight: bold;
}

.value.normal {
  color: #67c23a;
}

.value.warning {
  color: #e6a23c;
}

.value.critical {
  color: #f56c6c;
}

.memory-progress {
  margin: 8px 0;
}

.actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.warning-message {
  margin-top: 12px;
}

.monitor-simple {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.usage-text {
  font-size: 12px;
  font-weight: bold;
}

.usage-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #67c23a;
}

.usage-indicator.warning {
  background-color: #e6a23c;
}

.usage-indicator.critical {
  background-color: #f56c6c;
}
</style>
