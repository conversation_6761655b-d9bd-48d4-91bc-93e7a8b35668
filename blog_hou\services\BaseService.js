// 基础服务类
const logger = require("../plugin/logger");
const db = require("../utils/db");

class BaseService {
  constructor(tableName) {
    this.tableName = tableName;
    this.db = db;
    this.logger = logger;
  }

  // 通用查询方法
  async query(sql, params = []) {
    try {
      const result = await this.db.query(sql, params);
      this.logger.debug(`SQL查询执行成功: ${this.tableName}`, {
        sql: sql.substring(0, 100),
        paramCount: params.length,
        resultCount: Array.isArray(result) ? result.length : 1
      });
      return result;
    } catch (error) {
      this.logger.error(`SQL查询执行失败: ${this.tableName}`, {
        sql: sql.substring(0, 100),
        params,
        error: error.message
      });
      throw error;
    }
  }

  // 带缓存的查询
  async queryWithCache(sql, params = [], cacheKey = null, ttl = 300000) {
    try {
      const result = await this.db.queryWithCache(sql, params, cacheKey, ttl);
      this.logger.debug(`缓存查询执行成功: ${this.tableName}`, {
        sql: sql.substring(0, 100),
        cacheKey,
        ttl
      });
      return result;
    } catch (error) {
      this.logger.error(`缓存查询执行失败: ${this.tableName}`, {
        sql: sql.substring(0, 100),
        params,
        cacheKey,
        error: error.message
      });
      throw error;
    }
  }

  // 通用分页查询
  async paginate(baseQuery, params = [], page = 1, limit = 10, countQuery = null) {
    try {
      const offset = (page - 1) * limit;
      
      // 构建分页查询
      const paginatedQuery = `${baseQuery} LIMIT ? OFFSET ?`;
      const paginatedParams = [...params, limit, offset];
      
      // 构建计数查询
      const finalCountQuery = countQuery || `SELECT COUNT(*) as total FROM (${baseQuery}) as count_table`;
      
      // 并行执行查询和计数
      const [data, countResult] = await Promise.all([
        this.query(paginatedQuery, paginatedParams),
        this.query(finalCountQuery, params)
      ]);
      
      const total = countResult[0].total;
      const totalPages = Math.ceil(total / limit);
      
      return {
        data,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      this.logger.error(`分页查询失败: ${this.tableName}`, {
        baseQuery: baseQuery.substring(0, 100),
        page,
        limit,
        error: error.message
      });
      throw error;
    }
  }

  // 通用创建方法
  async create(data) {
    try {
      const fields = Object.keys(data);
      const values = Object.values(data);
      const placeholders = fields.map(() => '?').join(', ');
      
      const sql = `INSERT INTO ${this.tableName} (${fields.join(', ')}) VALUES (${placeholders})`;
      const result = await this.query(sql, values);
      
      this.logger.info(`记录创建成功: ${this.tableName}`, {
        insertId: result.insertId,
        affectedRows: result.affectedRows
      });
      
      return result.insertId;
    } catch (error) {
      this.logger.error(`记录创建失败: ${this.tableName}`, {
        data,
        error: error.message
      });
      throw error;
    }
  }

  // 通用更新方法
  async update(id, data) {
    try {
      const fields = Object.keys(data);
      const values = Object.values(data);
      const setClause = fields.map(field => `${field} = ?`).join(', ');
      
      const sql = `UPDATE ${this.tableName} SET ${setClause} WHERE id = ?`;
      const result = await this.query(sql, [...values, id]);
      
      this.logger.info(`记录更新成功: ${this.tableName}`, {
        id,
        affectedRows: result.affectedRows
      });
      
      return result.affectedRows > 0;
    } catch (error) {
      this.logger.error(`记录更新失败: ${this.tableName}`, {
        id,
        data,
        error: error.message
      });
      throw error;
    }
  }

  // 通用删除方法
  async delete(id) {
    try {
      const sql = `DELETE FROM ${this.tableName} WHERE id = ?`;
      const result = await this.query(sql, [id]);
      
      this.logger.info(`记录删除成功: ${this.tableName}`, {
        id,
        affectedRows: result.affectedRows
      });
      
      return result.affectedRows > 0;
    } catch (error) {
      this.logger.error(`记录删除失败: ${this.tableName}`, {
        id,
        error: error.message
      });
      throw error;
    }
  }

  // 软删除方法
  async softDelete(id) {
    try {
      const sql = `UPDATE ${this.tableName} SET is_deleted = 1, deleted_at = NOW() WHERE id = ?`;
      const result = await this.query(sql, [id]);
      
      this.logger.info(`记录软删除成功: ${this.tableName}`, {
        id,
        affectedRows: result.affectedRows
      });
      
      return result.affectedRows > 0;
    } catch (error) {
      this.logger.error(`记录软删除失败: ${this.tableName}`, {
        id,
        error: error.message
      });
      throw error;
    }
  }

  // 通用查找方法
  async findById(id) {
    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE id = ?`;
      const result = await this.query(sql, [id]);
      
      return result.length > 0 ? result[0] : null;
    } catch (error) {
      this.logger.error(`按ID查找失败: ${this.tableName}`, {
        id,
        error: error.message
      });
      throw error;
    }
  }

  // 通用查找方法（带条件）
  async findWhere(conditions = {}, orderBy = 'id DESC', limit = null) {
    try {
      const whereClause = Object.keys(conditions).length > 0 
        ? 'WHERE ' + Object.keys(conditions).map(key => `${key} = ?`).join(' AND ')
        : '';
      
      const limitClause = limit ? `LIMIT ${limit}` : '';
      
      const sql = `SELECT * FROM ${this.tableName} ${whereClause} ORDER BY ${orderBy} ${limitClause}`;
      const params = Object.values(conditions);
      
      const result = await this.query(sql, params);
      return result;
    } catch (error) {
      this.logger.error(`条件查找失败: ${this.tableName}`, {
        conditions,
        error: error.message
      });
      throw error;
    }
  }

  // 检查记录是否存在
  async exists(conditions) {
    try {
      const whereClause = Object.keys(conditions).map(key => `${key} = ?`).join(' AND ');
      const sql = `SELECT 1 FROM ${this.tableName} WHERE ${whereClause} LIMIT 1`;
      const params = Object.values(conditions);
      
      const result = await this.query(sql, params);
      return result.length > 0;
    } catch (error) {
      this.logger.error(`存在性检查失败: ${this.tableName}`, {
        conditions,
        error: error.message
      });
      throw error;
    }
  }

  // 获取记录总数
  async count(conditions = {}) {
    try {
      const whereClause = Object.keys(conditions).length > 0 
        ? 'WHERE ' + Object.keys(conditions).map(key => `${key} = ?`).join(' AND ')
        : '';
      
      const sql = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereClause}`;
      const params = Object.values(conditions);
      
      const result = await this.query(sql, params);
      return result[0].total;
    } catch (error) {
      this.logger.error(`计数查询失败: ${this.tableName}`, {
        conditions,
        error: error.message
      });
      throw error;
    }
  }

  // 批量创建
  async batchCreate(dataArray) {
    try {
      if (!dataArray || dataArray.length === 0) {
        return [];
      }

      const fields = Object.keys(dataArray[0]);
      const values = dataArray.map(data => Object.values(data));
      
      const result = await this.db.batchInsert(this.tableName, fields, values);
      
      this.logger.info(`批量创建成功: ${this.tableName}`, {
        count: dataArray.length,
        affectedRows: result.affectedRows
      });
      
      return result;
    } catch (error) {
      this.logger.error(`批量创建失败: ${this.tableName}`, {
        count: dataArray.length,
        error: error.message
      });
      throw error;
    }
  }

  // 事务处理
  async transaction(callback) {
    try {
      const result = await this.db.transaction(callback);
      
      this.logger.info(`事务执行成功: ${this.tableName}`);
      return result;
    } catch (error) {
      this.logger.error(`事务执行失败: ${this.tableName}`, {
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = BaseService;
