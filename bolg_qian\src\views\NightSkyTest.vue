<template>
  <div class="nightsky-test">
    <div class="test-content">
      <h1>夜空星辰特效测试</h1>
      
      <div class="test-section">
        <h2>交互测试区域</h2>
        <p>测试页面元素的点击功能是否正常工作，同时验证空白区域点击是否产生爆炸效果</p>
        
        <div class="button-grid">
          <el-button type="primary" @click="handleClick('主要按钮')">
            <el-icon><Star /></el-icon>
            主要按钮
          </el-button>
          <el-button type="success" @click="handleClick('成功按钮')">
            <el-icon><Check /></el-icon>
            成功按钮
          </el-button>
          <el-button type="warning" @click="handleClick('警告按钮')">
            <el-icon><Warning /></el-icon>
            警告按钮
          </el-button>
          <el-button type="danger" @click="handleClick('危险按钮')">
            <el-icon><Close /></el-icon>
            危险按钮
          </el-button>
        </div>
        
        <div class="interactive-cards">
          <el-card class="test-card" @click="handleClick('卡片1')" shadow="hover">
            <template #header>
              <span>可点击卡片 1</span>
            </template>
            <p>这是一个可点击的卡片，测试点击事件是否正常</p>
            <el-button type="text" @click.stop="handleClick('卡片内按钮1')">
              卡片内按钮
            </el-button>
          </el-card>
          
          <el-card class="test-card" @click="handleClick('卡片2')" shadow="hover">
            <template #header>
              <span>可点击卡片 2</span>
            </template>
            <p>另一个可点击的卡片，验证交互功能</p>
            <el-button type="text" @click.stop="handleClick('卡片内按钮2')">
              卡片内按钮
            </el-button>
          </el-card>
        </div>
        
        <div class="form-section">
          <h3>表单元素测试</h3>
          <el-form :model="form" label-width="100px">
            <el-form-item label="输入框:">
              <el-input 
                v-model="form.input" 
                placeholder="测试输入框"
                @focus="handleFocus('输入框')"
              />
            </el-form-item>
            <el-form-item label="选择器:">
              <el-select v-model="form.select" placeholder="测试选择器">
                <el-option label="选项1" value="1" />
                <el-option label="选项2" value="2" />
                <el-option label="选项3" value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="开关:">
              <el-switch v-model="form.switch" @change="handleClick('开关切换')" />
            </el-form-item>
          </el-form>
        </div>
        
        <div class="menu-section">
          <h3>菜单测试</h3>
          <el-menu mode="horizontal" @select="handleMenuSelect">
            <el-menu-item index="1">菜单项 1</el-menu-item>
            <el-menu-item index="2">菜单项 2</el-menu-item>
            <el-menu-item index="3">菜单项 3</el-menu-item>
          </el-menu>
        </div>
      </div>
      
      <div class="test-results">
        <h3>交互记录</h3>
        <div class="click-log">
          <div v-for="(log, index) in clickLogs" :key="index" class="log-item">
            {{ log }}
          </div>
        </div>
        <el-button @click="clearLogs" size="small" type="primary">
          <el-icon><Delete /></el-icon>
          清空记录
        </el-button>
      </div>
      
      <div class="instructions">
        <h3>测试说明</h3>
        <div class="instruction-grid">
          <div class="instruction-item">
            <el-icon class="instruction-icon success"><Check /></el-icon>
            <span>所有按钮、卡片、表单元素都应该正常响应点击</span>
          </div>
          <div class="instruction-item">
            <el-icon class="instruction-icon primary"><Star /></el-icon>
            <span>点击空白区域应该产生彩色粒子爆炸效果</span>
          </div>
          <div class="instruction-item">
            <el-icon class="instruction-icon warning"><Warning /></el-icon>
            <span>夜空特效不应该阻止页面交互</span>
          </div>
          <div class="instruction-item">
            <el-icon class="instruction-icon info"><InfoFilled /></el-icon>
            <span>星星应该在背景中闪烁，流星应该划过天空</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 夜空星辰特效 -->
    <NightSky />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { 
  Star, Check, Warning, Close, Delete, InfoFilled 
} from '@element-plus/icons-vue'
import NightSky from '@/components/texiao/NightSky.vue'

const form = ref({
  input: '',
  select: '',
  switch: false
})

const clickLogs = ref([])

const handleClick = (element) => {
  const timestamp = new Date().toLocaleTimeString()
  clickLogs.value.unshift(`${timestamp} - 点击了: ${element}`)
  
  // 限制日志数量
  if (clickLogs.value.length > 15) {
    clickLogs.value = clickLogs.value.slice(0, 15)
  }
}

const handleFocus = (element) => {
  const timestamp = new Date().toLocaleTimeString()
  clickLogs.value.unshift(`${timestamp} - 聚焦: ${element}`)
}

const handleMenuSelect = (index) => {
  handleClick(`菜单项 ${index}`)
}

const clearLogs = () => {
  clickLogs.value = []
}
</script>

<style scoped>
.nightsky-test {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(to bottom, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
}

.test-content {
  position: relative;
  z-index: 10;
  max-width: 900px;
  margin: 0 auto;
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  margin-top: var(--spacing-lg);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.test-content h1 {
  text-align: center;
  color: white;
  margin-bottom: var(--spacing-xl);
  font-size: var(--text-3xl);
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.test-section h2,
.test-section h3 {
  color: white;
  margin-bottom: var(--spacing-md);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.test-section p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-lg);
  line-height: var(--leading-relaxed);
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.interactive-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.test-card {
  cursor: pointer;
  transition: all var(--transition-normal);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.test-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  background: rgba(255, 255, 255, 0.15);
}

.test-card :deep(.el-card__header) {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.test-card :deep(.el-card__body) {
  color: rgba(255, 255, 255, 0.9);
}

.form-section,
.menu-section {
  background: rgba(255, 255, 255, 0.05);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section :deep(.el-form-item__label) {
  color: white;
}

.menu-section :deep(.el-menu) {
  background: rgba(255, 255, 255, 0.1);
  border: none;
}

.menu-section :deep(.el-menu-item) {
  color: rgba(255, 255, 255, 0.8);
  border-bottom: 2px solid transparent;
}

.menu-section :deep(.el-menu-item:hover) {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.test-results {
  background: rgba(0, 0, 0, 0.3);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.test-results h3 {
  margin-bottom: var(--spacing-md);
  color: white;
}

.click-log {
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: var(--spacing-md);
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

.log-item {
  padding: var(--spacing-xs);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.8);
}

.instructions {
  background: rgba(255, 255, 255, 0.05);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  border-left: 4px solid #00d4ff;
}

.instructions h3 {
  margin-bottom: var(--spacing-md);
  color: white;
}

.instruction-grid {
  display: grid;
  gap: var(--spacing-sm);
}

.instruction-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: rgba(255, 255, 255, 0.9);
  line-height: var(--leading-relaxed);
}

.instruction-icon {
  font-size: var(--text-lg);
  flex-shrink: 0;
}

.instruction-icon.success {
  color: #67c23a;
}

.instruction-icon.primary {
  color: #409eff;
}

.instruction-icon.warning {
  color: #e6a23c;
}

.instruction-icon.info {
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .test-content {
    margin: var(--spacing-md);
    padding: var(--spacing-lg);
  }
  
  .button-grid {
    grid-template-columns: 1fr;
  }
  
  .interactive-cards {
    grid-template-columns: 1fr;
  }
}
</style>
