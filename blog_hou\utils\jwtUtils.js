const jwt = require("jsonwebtoken");
const { getAppConfig } = require("../config");

// 生成token
const generateToken = (user) => {
  console.log("生成的user:", user);
  const appConfig = getAppConfig();

  return jwt.sign(
    { id: user.id, userId: user.id, username: user.username, role: user.role },
    appConfig.jwt.secret,
    {
      expiresIn: appConfig.jwt.expiresIn,
      algorithm: appConfig.jwt.algorithm,
      issuer: appConfig.jwt.issuer,
      audience: appConfig.jwt.audience
    }
  );
};

// 验证token
const verifyToken = (token) => {
  const appConfig = getAppConfig();

  return jwt.verify(token, appConfig.jwt.secret, {
    algorithms: [appConfig.jwt.algorithm],
    issuer: appConfig.jwt.issuer,
    audience: appConfig.jwt.audience
  });
};

// 解码token（不验证签名）
const decodeToken = (token) => {
  return jwt.decode(token);
};

module.exports = {
  generateToken,
  verifyToken,
  decodeToken
};
