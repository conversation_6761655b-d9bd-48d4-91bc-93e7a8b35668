const Router = require("koa-router");
const mysql = require("mysql");
const fs = require("fs");
const path = require("path");
const send = require("koa-send");
const logger = require("../plugin/logger");

const daosql = new Router();

const dbConfig = {
  host: "localhost",
  user: "root",
  password: "0519",
  database: "myblog",
};

function escapeValue(val) {
  if (val === null) return "NULL";
  if (typeof val === "number") return val;
  return `'${val.toString().replace(/\\/g, "\\\\").replace(/'/g, "\\'")}'`;
}

daosql.get("/export/download", async (ctx) => {
  const connection = mysql.createConnection(dbConfig);

  const query = (sql) =>
    new Promise((resolve, reject) => {
      connection.query(sql, (err, results) => {
        if (err) reject(err);
        else resolve(results);
      });
    });

  try {
    connection.connect();

    const tables = await query("SHOW TABLES");
    const tableNames = tables.map((row) => Object.values(row)[0]);

    let sqlOutput = "";
    sqlOutput += `-- 导出时间: ${new Date().toLocaleString()}\n`;
    sqlOutput += `-- 数据库: ${dbConfig.database}\n`;
    sqlOutput += `-- ---------------------------------------------\n\n`;

    for (const tableName of tableNames) {
      const createResult = await query(`SHOW CREATE TABLE \`${tableName}\``);
      const createTableSql = createResult[0]["Create Table"];

      sqlOutput += `-- ----------------------------\n`;
      sqlOutput += `-- Table structure for \`${tableName}\`\n`;
      sqlOutput += `-- ----------------------------\n`;
      sqlOutput += `DROP TABLE IF EXISTS \`${tableName}\`;\n`;
      sqlOutput += createTableSql + ";\n\n";

      const rows = await query(`SELECT * FROM \`${tableName}\``);
      if (rows.length > 0) {
        sqlOutput += `-- ----------------------------\n`;
        sqlOutput += `-- Records of \`${tableName}\`\n`;
        sqlOutput += `-- ----------------------------\n`;

        rows.forEach((row) => {
          const keys = Object.keys(row)
            .map((k) => `\`${k}\``)
            .join(", ");
          const values = Object.values(row).map(escapeValue).join(", ");
          sqlOutput += `INSERT INTO \`${tableName}\` (${keys}) VALUES (${values});\n`;
        });
        sqlOutput += "\n";
      }
    }

    connection.end();

    const exportDir = path.join(__dirname, "export");
    if (!fs.existsSync(exportDir)) fs.mkdirSync(exportDir);

    const sqlFile = path.join(exportDir, "latest_export.sql");

    fs.writeFileSync(sqlFile, sqlOutput, "utf8");

    // 直接发送文件给客户端，触发下载
    await send(ctx, "latest_export.sql", { root: exportDir });
    logger.info(`导出sql成功 - IP: ${ctx.ip}`);
  } catch (err) {
    connection.end();
    ctx.status = 500;
    ctx.body = { error: err.message };
  }
});

module.exports = daosql;
