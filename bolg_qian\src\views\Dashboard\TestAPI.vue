<template>
  <div class="test-api">
    <h2>API测试页面</h2>
    <div class="test-buttons">
      <el-button @click="testFavoriteAPI" type="primary">测试收藏历史API</el-button>
      <el-button @click="testFavoriteToggle" type="success">测试收藏切换</el-button>
      <el-button @click="testFavoriteStats" type="info">测试收藏统计</el-button>
    </div>
    <div v-if="result" class="result">
      <h3>测试结果:</h3>
      <pre>{{ result }}</pre>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import request from '../../utils/index'

export default {
  name: 'TestAPI',
  setup() {
    const result = ref('')

    const testFavoriteAPI = async () => {
      try {
        console.log('🚀 开始测试收藏历史API...')

        // 检查本地存储的token
        const token = localStorage.getItem('token')
        console.log('当前Token:', token ? token.substring(0, 50) + '...' : '无Token')

        // 解析token查看用户信息
        if (token) {
          try {
            const payload = JSON.parse(atob(token.split('.')[1]))
            console.log('Token payload:', payload)
          } catch (e) {
            console.log('Token解析失败:', e.message)
          }
        }

        const params = {
          page: 1,
          limit: 20
        }

        console.log('请求参数:', params)
        console.log('请求URL: /article-favorites/history')

        const response = await request.get('/article-favorites/history', { params })
        console.log('API响应:', response)

        result.value = `Token: ${token ? '存在' : '不存在'}\n\n${JSON.stringify(response, null, 2)}`

      } catch (error) {
        console.error('API测试失败:', error)
        result.value = `错误: ${error.message}\n${JSON.stringify(error.response?.data || {}, null, 2)}`
      }
    }

    const testFavoriteToggle = async () => {
      try {
        console.log('🚀 开始测试收藏切换API...')

        // 使用一个存在的文章ID进行测试
        const articleId = 39
        const response = await request.post('/article-favorites/toggle', {
          article_id: articleId
        })
        console.log('收藏切换响应:', response)

        result.value = `收藏切换测试结果:\n${JSON.stringify(response, null, 2)}`

      } catch (error) {
        console.error('收藏切换测试失败:', error)
        result.value = `收藏切换错误: ${error.message}\n${JSON.stringify(error.response?.data || {}, null, 2)}`
      }
    }

    const testFavoriteStats = async () => {
      try {
        console.log('🚀 开始测试收藏统计API...')

        // 使用一个存在的文章ID进行测试
        const articleId = 39
        const response = await request.get(`/article-favorites/stats/${articleId}`)
        console.log('收藏统计响应:', response)

        result.value = `收藏统计测试结果:\n${JSON.stringify(response, null, 2)}`

      } catch (error) {
        console.error('收藏统计测试失败:', error)
        result.value = `收藏统计错误: ${error.message}\n${JSON.stringify(error.response?.data || {}, null, 2)}`
      }
    }

    return {
      result,
      testFavoriteAPI,
      testFavoriteToggle,
      testFavoriteStats
    }
  }
}
</script>

<style scoped>
.test-api {
  padding: 20px;
}

.test-buttons {
  margin-bottom: 20px;
}

.test-buttons .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.result {
  margin-top: 20px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 4px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
