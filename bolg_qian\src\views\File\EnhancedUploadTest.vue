<template>
  <div class="enhanced-upload-test">
    <div class="page-header">
      <h2>
        <el-icon><Upload /></el-icon>
        增强版文件上传测试
      </h2>
      <p>测试双通道文件上传功能：普通文件通道和视频媒体通道</p>
    </div>

    <!-- 上传组件 -->
    <EnhancedUpload />

    <!-- 上传说明 -->
    <el-card class="info-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>功能说明</span>
        </div>
      </template>
      
      <div class="feature-list">
        <div class="feature-item">
          <el-icon class="feature-icon"><Document /></el-icon>
          <div class="feature-content">
            <h4>普通文件通道</h4>
            <p>支持文档、图片、压缩包等文件类型</p>
            <p>保存位置: <code>/uploads/documents/</code></p>
            <p>访问路径: <code>http://**************:3000/documents/文件名</code></p>
          </div>
        </div>

        <div class="feature-item">
          <el-icon class="feature-icon"><VideoPlay /></el-icon>
          <div class="feature-content">
            <h4>视频媒体通道</h4>
            <p>支持视频、音频等媒体文件类型</p>
            <p>保存位置: <code>/public/media/</code></p>
            <p>访问路径: <code>http://**************:3000/media/文件名</code></p>
          </div>
        </div>

        <div class="feature-item">
          <el-icon class="feature-icon"><Setting /></el-icon>
          <div class="feature-content">
            <h4>智能特性</h4>
            <ul>
              <li>自动文件类型检测和通道验证</li>
              <li>支持拖拽上传和点击选择</li>
              <li>大文件自动分片上传（>10MB）</li>
              <li>实时上传进度显示</li>
              <li>批量文件上传支持</li>
              <li>文件大小限制（100MB）</li>
            </ul>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 测试结果展示 -->
    <el-card class="test-results" style="margin-top: 20px;" v-if="testResults.length > 0">
      <template #header>
        <div class="card-header">
          <span>上传测试结果</span>
          <el-button size="small" @click="clearResults">清空结果</el-button>
        </div>
      </template>
      
      <el-table :data="testResults" style="width: 100%">
        <el-table-column prop="fileName" label="文件名" />
        <el-table-column prop="fileSize" label="文件大小" width="120">
          <template #default="{ row }">
            {{ formatFileSize(row.fileSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="channel" label="上传通道" width="120">
          <template #default="{ row }">
            <el-tag :type="row.channel === 'media' ? 'warning' : 'info'">
              {{ row.channel === 'media' ? '视频媒体' : '普通文件' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
              {{ row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="uploadTime" label="上传时间" width="180" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button 
              size="small" 
              type="primary" 
              @click="openFile(row)"
              v-if="row.status === 'success'"
            >
              访问文件
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Upload, Document, VideoPlay, Setting } from '@element-plus/icons-vue';
import EnhancedUpload from '../../components/EnhancedUpload.vue';

// 测试结果数据
const testResults = ref<any[]>([]);

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (!size) return '0 B';
  const units = ['B', 'KB', 'MB', 'GB'];
  let index = 0;
  let fileSize = size;
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  return `${fileSize.toFixed(1)} ${units[index]}`;
};

// 清空测试结果
const clearResults = () => {
  testResults.value = [];
};

// 打开文件
const openFile = (row: any) => {
  const baseUrl = 'http://**************:3000';
  const path = row.channel === 'media' ? '/media/' : '/documents/';
  const url = `${baseUrl}${path}${row.fileName}`;
  window.open(url, '_blank');
};

// 监听上传完成事件（如果需要的话）
// 这里可以添加事件监听来收集上传结果
</script>

<style scoped>
.enhanced-upload-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h2 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #303133;
  margin-bottom: 8px;
}

.page-header p {
  color: #606266;
  font-size: 14px;
}

.info-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.feature-icon {
  font-size: 24px;
  color: #409eff;
  flex-shrink: 0;
  margin-top: 4px;
}

.feature-content h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.feature-content p {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}

.feature-content code {
  background: #f1f2f3;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.feature-content ul {
  margin: 8px 0 0 0;
  padding-left: 16px;
}

.feature-content li {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}

.test-results {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-upload-test {
    padding: 12px;
  }
  
  .feature-list {
    grid-template-columns: 1fr;
  }
  
  .feature-item {
    flex-direction: column;
    text-align: center;
  }
}
</style>
