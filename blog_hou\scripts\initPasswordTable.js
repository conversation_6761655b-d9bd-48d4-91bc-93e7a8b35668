// 初始化密码管理表
const db = require("../utils/db");

async function initPasswordTable() {
  try {
    // 创建访问密码管理表
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS \`access_passwords\` (
        \`id\` int(11) NOT NULL AUTO_INCREMENT,
        \`password_type\` varchar(50) NOT NULL COMMENT '密码类型：photo_wall, media',
        \`password_hash\` varchar(255) NOT NULL COMMENT '加密后的密码',
        \`security_question\` varchar(255) NOT NULL COMMENT '密保问题',
        \`security_answer_hash\` varchar(255) NOT NULL COMMENT '加密后的密保答案',
        \`created_by\` int(11) NOT NULL COMMENT '创建者用户ID',
        \`created_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        \`updated_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (\`id\`),
        UNIQUE KEY \`uk_password_type\` (\`password_type\`),
        KEY \`idx_created_by\` (\`created_by\`),
        CONSTRAINT \`fk_access_passwords_user\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访问密码管理表';
    `;

    await db.query(createTableSQL);
    console.log("✅ 密码管理表创建成功");

    // 检查是否需要添加role字段到users表
    const checkRoleSQL = "SHOW COLUMNS FROM users LIKE 'role'";
    const roleExists = await db.query(checkRoleSQL);
    
    if (roleExists.length === 0) {
      // 添加role字段
      const addRoleSQL = "ALTER TABLE users ADD COLUMN role VARCHAR(20) DEFAULT 'user' COMMENT '用户角色：admin, user'";
      await db.query(addRoleSQL);
      console.log("✅ 用户角色字段添加成功");
      
      // 将第一个用户设置为admin
      const updateAdminSQL = "UPDATE users SET role = 'admin' WHERE id = 1";
      await db.query(updateAdminSQL);
      console.log("✅ 第一个用户已设置为管理员");
    } else {
      console.log("ℹ️  用户角色字段已存在");
    }

    console.log("🎉 密码管理系统初始化完成");
  } catch (error) {
    console.error("❌ 初始化失败:", error);
  } finally {
    process.exit(0);
  }
}

// 运行初始化
initPasswordTable(); 