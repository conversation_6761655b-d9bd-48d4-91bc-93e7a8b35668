const Router = require("koa-router");
const bodyParser = require("koa-bodyparser");
const {
  searchUser,
  showFriendList,
  saveChatRecord,
  showChatRecord,
  sendFriendRequest,
  getPendingRequests,
  handleFriendRequest,
  checkFriendRequest, 
} = require("../utils/sql/chatService");

const friend = new Router();

friend.use(bodyParser());

// 搜索用户
friend.post("/search", async (ctx) => {
  const { username } = ctx.request.body;
  console.log("搜索用户:", username);
  if (!username) {
    ctx.body = { code: 1, msg: "用户名不能为空" };
    return;
  }
  const user = await searchUser(username);
  if (user) {
    ctx.body = { code: 0, msg: "用户存在", data: {
      id: user.id,
      username: user.username,
    } };
    console.log("搜索到用户:", user);
  } else {
    ctx.body = { code: 1, msg: "用户不存在" };
  }
});

// 发送好友请求
friend.post("/add", async (ctx) => {
  const { user_id, friend_id } = ctx.request.body;
  console.log("发送好友请求:", user_id, friend_id);
  if (!user_id || !friend_id) {
    ctx.body = { code: 1, msg: "参数不完整" };
    return;
  }
  // 检查是否已发送过请求
  const exist = await checkFriendRequest(user_id, friend_id);
  if (exist) {
    ctx.body = { code: 1, msg: "已发送过请求，请等待对方处理" };
    return;
  }
  const success = await sendFriendRequest(user_id, friend_id);
  ctx.body = success
    ? { code: 0, msg: "好友请求已发送，等待对方同意" }
    : { code: 1, msg: "发送请求失败" };
});

// 查询待处理好友请求
friend.post("/pendingRequest", async (ctx) => {
  const { user_id } = ctx.request.body;
  if (!user_id) {
    ctx.body = { code: 1, msg: "用户id不能为空" };
    return;
  }
  const list = await getPendingRequests(user_id);
  ctx.body = { code: 0, data: list };
});

// 处理好友请求
friend.post("/handleRequest", async (ctx) => {
  const { request_id, agree } = ctx.request.body;
  if (!request_id || typeof agree === 'undefined') {
    ctx.body = { code: 1, msg: "参数不完整" };
    return;
  }
  const success = await handleFriendRequest(request_id, agree);
  ctx.body = success
    ? { code: 0, msg: agree ? "已同意好友请求" : "已拒绝好友请求" }
    : { code: 1, msg: "操作失败" };
});

// 显示所有好友
friend.post("/list", async (ctx) => {
  const { user_id } = ctx.request.body;
  if (!user_id) {
    ctx.body = { code: 1, msg: "用户id不能为空" };
    return;
  }
  const list = await showFriendList(user_id);
  ctx.body = { code: 0, msg: "好友列表", data: list };
});

// 存储聊天记录
friend.post("/chat", async (ctx) => {
  const { user_id, friend_id, message } = ctx.request.body;
  if (!user_id || !friend_id || !message) {
    ctx.body = { code: 1, msg: "参数不完整" };
    return;
  } 
  const success = await saveChatRecord(user_id, friend_id, message);
  if (success) {
    ctx.body = { code: 0, msg: "聊天记录保存成功" };
  } else {
    ctx.body = { code: 1, msg: "聊天记录保存失败" };
  }
});

// 显示聊天记录
friend.post("/chatList", async (ctx) => {
  const { user_id, friend_id } = ctx.request.body;
  if (!user_id || !friend_id) {
    ctx.body = { code: 1, msg: "参数不完整" };
    return;
  }
  const list = await showChatRecord(user_id, friend_id);
  ctx.body = { code: 0, msg: "聊天记录列表", data: list };
});

module.exports = friend;
