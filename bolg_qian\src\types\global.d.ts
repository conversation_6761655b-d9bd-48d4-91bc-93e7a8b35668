// 全局类型声明

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@/utils/api' {
  export function dashboardApi(): Promise<any>
  export function getCountApi(dateRange?: string): Promise<any>
}

declare module '@/types/dashboard' {
  export interface StatItem {
    label: string
    value: number | string
  }

  export interface GrowthData {
    date: string
    count: number
  }

  export interface DownloadData {
    date: string
    file_name: string
    file_type: string
    count: number
  }

  export interface DashboardResponse {
    data: {
      userCount: number
      articleCount: number
      shareCount: number
      resourceCount: number
      downloadCount: number
    }
  }

  export interface TrendResponse {
    data: {
      userGrowth: GrowthData[]
      articlePublish: GrowthData[]
      downloadData: DownloadData[]
    }
  }
}

declare module 'markdown-it' {
  const MarkdownIt: any;
  export default MarkdownIt;
}

declare module '@/plugin/copy.js' {
  export function copyText(text: string, successMsg?: string, failMsg?: string): void;
}

// 扩展AxiosResponse类型
declare module 'axios' {
  interface AxiosResponse<T = any> {
    message?: string;
  }

  export function create(axiosOption: { baseURL: string; withCredentials: boolean; timeout: number }) {
    throw new Error("Function not implemented.")
  }
} 