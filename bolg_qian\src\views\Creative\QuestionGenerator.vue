<template>
  <div class="question-generator">
    <el-card class="file-upload-card">
      <template #header>
        <div class="card-header">
          <h2>题目生成器</h2>
          <el-tag type="info">支持 TXT 和 CSV 格式</el-tag>
        </div>
      </template>
      
      <div class="upload-section">
        <el-upload
          class="upload-box"
          drag
          action=""
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleFileChange"
          accept=".txt,.csv"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            拖拽文件到此处或 <em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              <p>支持格式：TXT 或 CSV</p>
              <p>文件格式说明：</p>
              <ul>
                <li>判断题：问题（判断题）</li>
                <li>单选题：问题（单选题）+ 选项A-D + 答案</li>
                <li>填空题：问题（填空题）+ 答案</li>
                <li>解答题：问题（解答题）+ 答案</li>
              </ul>
            </div>
        </template>
        </el-upload>

        <div v-if="importedFile" class="file-info">
          <el-alert
            title="文件已上传"
            type="success"
            :closable="false"
            show-icon
          >
            <template #default>已上传: {{ importedFile.name }}</template>
          </el-alert>
          <div class="action-buttons">
            <el-button type="primary" @click="generateQuestions" :loading="generating">
              生成题目
            </el-button>
            <el-button type="danger" @click="resetFile">
              清除文件
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <template v-if="questions.length">
      <el-card class="questions-card">
        <template #header>
          <div class="card-header">
            <h2>题目列表 ({{ questions.length }}题)</h2>
            <div class="header-actions">
              <el-button type="success" @click="checkAllAnswers" :disabled="allQuestionsAnswered">
                全部提交
              </el-button>
              <el-button type="info" @click="exportResults">
                导出结果
              </el-button>
            </div>
          </div>
        </template>
        
        <div class="questions-progress">
          <el-progress 
            :percentage="progressPercentage" 
            :format="progressFormat"
            :status="progressStatus"
          />
        </div>
        
        <el-collapse v-model="activeQuestion">
          <el-collapse-item 
            v-for="(q, i) in questions" 
            :key="i"
            :name="i"
            :class="{ 
              'question-correct': q.result === true, 
              'question-incorrect': q.result === false,
              'question-unanswered': q.result === undefined
            }"
          >
            <template #title>
              <div class="question-title">
                <span class="question-number">{{ i + 1 }}.</span>
                <span class="question-content">{{ q.content }}</span>
                <span class="question-type-tag" :class="'type-' + q.type">
                  {{ getQuestionTypeLabel(q.type) }}
                </span>
                <span v-if="q.result !== undefined" class="result-icon">
                  <el-icon v-if="q.result === true" class="correct-icon"><CircleCheckFilled /></el-icon>
                  <el-icon v-else-if="q.result === false" class="incorrect-icon"><CircleCloseFilled /></el-icon>
                  <el-icon v-else-if="q.result === null" class="no-answer-icon"><InfoFilled /></el-icon>
                </span>
              </div>
            </template>
            
            <!-- 判断题 -->
            <template v-if="q.type === 'judge'">
              <div class="question-options">
                <el-radio-group v-model="q.userAnswer" :disabled="q.result !== undefined">
                  <el-radio label="对">对</el-radio>
                  <el-radio label="错">错</el-radio>
                </el-radio-group>
              </div>
            </template>
            
            <!-- 单选题 -->
            <template v-else-if="q.type === 'single'">
              <div class="question-options">
                <el-radio-group v-model="q.userAnswer" :disabled="q.result !== undefined">
                  <el-radio 
                    v-for="(opt, index) in q.options" 
                    :key="index" 
                    :label="opt"
                  >
                    {{ getOptionLabel(index) }}. {{ opt }}
                  </el-radio>
                </el-radio-group>
              </div>
            </template>
            
            <!-- 填空题 -->
            <template v-else-if="q.type === 'fill'">
              <div class="question-input">
                <el-input
                  v-model="q.userAnswer"
                  placeholder="请输入答案"
                  :disabled="q.result !== undefined"
                />
              </div>
            </template>
            
            <!-- 解答题 -->
            <template v-else>
              <div class="question-input">
                <el-input
                  v-model="q.userAnswer"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入你的解答"
                  :disabled="q.result !== undefined"
                />
              </div>
            </template>
            
            <div class="question-actions">
              <el-button 
                type="primary" 
                @click="checkAnswer(i)"
                :disabled="!q.userAnswer || q.result !== undefined"
              >
                提交答案
              </el-button>
              
              <el-button 
                type="warning" 
                @click="resetAnswer(i)"
                v-if="q.result !== undefined"
              >
                重新作答
              </el-button>
            </div>
            
            <div v-if="q.result !== undefined" class="answer-feedback">
              <div v-if="q.result === true" class="correct-answer">
                <el-alert
                  title="回答正确"
                  type="success"
                  :closable="false"
                  show-icon
                />
              </div>
              <div v-else-if="q.result === false" class="incorrect-answer">
                <el-alert
                  title="回答错误"
                  type="error"
                  :closable="false"
                  show-icon
                >
                  <template v-if="q.answer">
                    <strong>正确答案：</strong> {{ q.answer }}
                  </template>
                </el-alert>
              </div>
              <div v-else class="no-standard-answer">
                <el-alert
                  title="无标准答案"
                  type="info"
                  :closable="false"
                  show-icon
                >
                  该题目未设置标准答案，无法评判
                </el-alert>
              </div>
              
              <div v-if="q.type === 'essay' && q.result === false" class="similarity-score">
                <p>答案相似度: {{ q.similarity ? (q.similarity * 100).toFixed(1) + '%' : '计算中...' }}</p>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
        
        <div class="summary-section" v-if="questions.length && answeredCount > 0">
          <h3>答题情况统计</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-statistic title="总题数" :value="questions.length" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="已答题数" :value="answeredCount" />
            </el-col>
            <el-col :span="8">
              <el-statistic 
                title="正确率" 
                :value="correctRate" 
                :precision="1"
                suffix="%"
                :value-style="{ color: Number(correctRate) > 80 ? '#67C23A' : Number(correctRate) > 60 ? '#E6A23C' : '#F56C6C' }"
              />
            </el-col>
          </el-row>
        </div>
      </el-card>
        </template>
    
    <el-dialog
      v-model="previewDialogVisible"
      title="文件预览"
      width="70%"
    >
      <div class="file-preview">
        <pre>{{ filePreview }}</pre>
      </div>
      <template #footer>
        <span>
          <el-button @click="previewDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="confirmGeneration">确认生成</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { UploadFilled, CircleCheckFilled, CircleCloseFilled, InfoFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 状态变量
const questions = ref<any[]>([])
const importedFile = ref<File | null>(null)
const fileContent = ref<string>('')
const filePreview = ref<string>('')
const previewDialogVisible = ref<boolean>(false)
const generating = ref<boolean>(false)
const activeQuestion = ref<number[]>([])

// 计算属性
const allQuestionsAnswered = computed(() => {
  return questions.value.every(q => q.result !== undefined)
})

const answeredCount = computed(() => {
  return questions.value.filter(q => q.result !== undefined).length
})

const correctCount = computed(() => {
  return questions.value.filter(q => q.result === true).length
})

const correctRate = computed(() => {
  if (answeredCount.value === 0) return 0
  return (correctCount.value / answeredCount.value * 100).toFixed(1)
})

const progressPercentage = computed(() => {
  if (questions.value.length === 0) return 0
  return Math.round((answeredCount.value / questions.value.length) * 100)
})

const progressStatus = computed(() => {
  if (progressPercentage.value < 100) return ''
  return Number(correctRate.value) >= 60 ? 'success' : 'exception'
})

// 方法
function progressFormat(percentage: number) {
  return answeredCount.value + '/' + questions.value.length
}

function handleFileChange(file: any) {
  if (file && file.raw) {
    importedFile.value = file.raw
    const reader = new FileReader()
  reader.onload = (e) => {
      const text = e.target?.result as string
      fileContent.value = text
      filePreview.value = text.length > 1000 ? text.slice(0, 1000) + '...(内容过长已省略)' : text
      // previewDialogVisible.value = true // 可选：是否自动打开预览
    }
    reader.readAsText(file.raw, 'utf-8')
  }
}

function resetFile() {
  importedFile.value = null
  fileContent.value = ''
  filePreview.value = ''
  questions.value = []
  localStorage.removeItem('local_questions') // 新增
}

function generateQuestions() {
  if (!fileContent.value) {
    ElMessage.warning('请先上传文件')
    return
  }
  
  generating.value = true
  
  try {
    // 尝试检测文件格式
    let lines: string[]
    const isCSV = importedFile.value?.name.toLowerCase().endsWith('.csv')
    
    if (isCSV) {
      // CSV 处理
      lines = parseCSV(fileContent.value)
    } else {
      // TXT 处理
      lines = fileContent.value.split(/\r?\n/).map(line => line.trim()).filter(line => line)
    }

    const result: any[] = []
    let i = 0
    
    while (i < lines.length) {
      const line = lines[i]
      
      // 判断题
      if (line.includes('判断题')) {
        const content = line.replace(/（判断题）|（判断）|\(判断题\)|\(判断\)/g, '').replace(/^\d+[\.\s、]+/g, '').trim()
        let answer
        
        // 检查下一行是否包含答案
        if (i + 1 < lines.length && lines[i + 1].startsWith('答案')) {
          answer = lines[i + 1].replace(/^答案[：:]/g, '').trim()
          i += 2 // 跳过答案行
        } else {
          i++
        }
        
        result.push({
          type: 'judge',
          content,
          answer,
          userAnswer: '',
          result: undefined,
          similarity: undefined
        })
      }
      // 单选题
      else if (line.includes('单选题') || line.match(/（[A-D]）|\([A-D]\)/)) {
        const content = line.replace(/（单选题）|（选择题）|\(单选题\)|\(选择题\)/g, '')
                           .replace(/^\d+[\.\s、]+/g, '')
                           .trim()
        const options: string[] = []
        let answer
        
        i++
        // 收集选项
        while (i < lines.length && 
              (lines[i].match(/^[A-D][\.、\s]/g) || lines[i].match(/^[A-D]:/))) {
          options.push(lines[i].replace(/^[A-D][\.、\s:]/g, '').trim())
          i++
        }
        
        // 检查是否有答案
        if (i < lines.length && lines[i].match(/^答案[：:]/g)) {
          answer = lines[i].replace(/^答案[：:]/g, '').trim()
          i++
        }
        
        result.push({
          type: 'single',
          content,
          options,
          answer,
          userAnswer: '',
          result: undefined,
          similarity: undefined
        })
      }
      // 填空题
      else if (line.includes('填空题')) {
        const content = line.replace(/（填空题）|\(填空题\)/g, '').replace(/^\d+[\.\s、]+/g, '').trim()
        let answer
        
        // 检查下一行是否包含答案
        if (i + 1 < lines.length && lines[i + 1].startsWith('答案')) {
          answer = lines[i + 1].replace(/^答案[：:]/g, '').trim()
          i += 2 // 跳过答案行
        } else {
          i++
        }
        
        result.push({
          type: 'fill',
          content,
          answer,
          userAnswer: '',
          result: undefined,
          similarity: undefined
        })
      }
      // 解答题
      else if (line.includes('解答题') || line.includes('简答题')) {
        const content = line.replace(/（解答题）|（简答题）|\(解答题\)|\(简答题\)/g, '').replace(/^\d+[\.\s、]+/g, '').trim()
        let answer = ''
        
        i++
        // 收集答案（可能是多行）
        if (i < lines.length && lines[i].startsWith('答案')) {
          i++ // 跳过"答案:"这一行
          
          // 收集直到下一个问题的所有行作为答案
          while (i < lines.length && 
                 !lines[i].match(/（判断题）|（单选题）|（填空题）|（解答题）|（简答题）|\(判断题\)|\(单选题\)|\(填空题\)|\(解答题\)|\(简答题\)/g) &&
                 !lines[i].match(/^\d+[\.\s、]+/g)) {
            answer += lines[i] + '\n'
            i++
          }
        }
        
        result.push({
          type: 'essay',
          content,
          answer: answer.trim(),
          userAnswer: '',
          result: undefined,
          similarity: undefined
        })
      }
      // 其他（默认填空题）
      else {
        // 如果这行有题号，认为是题目
        if (line.match(/^\d+[\.\s、]+/)) {
          const content = line.replace(/^\d+[\.\s、]+/g, '').trim()
          let answer
          
          // 检查下一行是否包含答案
          if (i + 1 < lines.length && lines[i + 1].startsWith('答案')) {
            answer = lines[i + 1].replace(/^答案[：:]/g, '').trim()
            i += 2 // 跳过答案行
          } else {
            i++
          }
          
          result.push({
            type: 'fill',
            content,
            answer,
            userAnswer: '',
            result: undefined,
            similarity: undefined
          })
        } else {
          // 不是题目，跳过
          i++
        }
      }
    }
    
    questions.value = result
    
    if (questions.value.length === 0) {
      ElMessage.warning('未识别到有效题目，请检查文件格式')
    } else {
      ElMessage.success(`成功生成 ${questions.value.length} 道题目`)
      activeQuestion.value = [0]
      saveQuestionsToLocal() // 新增：保存到本地
    }
  } catch (error) {
    console.error('解析文件出错:', error)
    ElMessage.error('解析文件出错，请检查文件格式')
  } finally {
    generating.value = false
  }
}

// CSV 解析（简单实现，不处理引号内的逗号）
function parseCSV(text: string): string[] {
  return text.split(/\r?\n/).map(line => line.trim()).filter(line => line)
}

function confirmGeneration() {
  previewDialogVisible.value = false
  generateQuestions()
}

function getQuestionTypeLabel(type: string): string {
  const types: Record<string, string> = {
    'judge': '判断题',
    'single': '单选题',
    'fill': '填空题',
    'essay': '解答题'
  }
  return types[type] || '未知类型'
}

function getOptionLabel(index: number): string {
  return String.fromCharCode(65 + index) // A, B, C, D...
}

// 检查单个答案
function checkAnswer(index: number) {
  const q = questions.value[index]
  
  if (!q.userAnswer) {
    ElMessage.warning('请先作答')
    return
  }
  
  if (q.answer === undefined || q.answer === '') {
    q.result = null // 没有标准答案
    return
  }
  
  // 规范化用户答案和标准答案（去除多余空格、转为小写）
  const normalizedUserAnswer = normalizeAnswer(q.userAnswer)
  const normalizedStandardAnswer = normalizeAnswer(q.answer)
  
  if (q.type === 'essay') {
    // 解答题使用相似度匹配
    q.similarity = calculateSimilarity(normalizedUserAnswer, normalizedStandardAnswer)
    
    // 保存原始相似度用于显示
    q.originalSimilarity = q.similarity
    
    // 添加关键词匹配加权
    const keywordMatch = checkKeywordMatch(normalizedUserAnswer, normalizedStandardAnswer)
    if (typeof q.similarity === 'number' && typeof keywordMatch === 'number' && keywordMatch > q.similarity) {
      q.similarity = (q.similarity + keywordMatch) / 2
    }
    
    // 降低相似度门槛，更加宽松
    q.result = typeof q.similarity === 'number' && q.similarity >= 0.5 // 50% 相似度认为正确
    
    // 对于非常高的相似度，直接判定为正确
    if (typeof q.similarity === 'number' && q.similarity >= 0.8) {
      q.result = true
    }
  } else if (q.type === 'fill') {
    // 填空题允许更加灵活的匹配
    
    // 1. 检查完全匹配（忽略大小写和多余空格）
    if (normalizedUserAnswer === normalizedStandardAnswer) {
      q.result = true
      return
    }
    
    // 2. 检查是否包含关键词
    const keywords = extractKeywords(normalizedStandardAnswer)
    const userWords = normalizedUserAnswer.split(/\s+/)
    
    // 如果标准答案很短（少于3个字），要求精确匹配
    if (q.answer.length < 3 && normalizedUserAnswer !== normalizedStandardAnswer) {
      q.result = false
      return
    }
    
    // 检查关键词匹配度
    let matchedKeywords = 0
    for (const keyword of keywords) {
      if (keyword.length < 2) continue // 忽略过短的词
      
      if (normalizedUserAnswer.includes(keyword)) {
        matchedKeywords++
      }
    }
    
    // 计算关键词匹配率
    const keywordMatchRate = keywords.length > 0 ? matchedKeywords / keywords.length : 0
    
    // 如果匹配率超过70%，认为是正确的
    q.result = keywordMatchRate >= 0.7
    
    // 保存匹配率用于可能的显示
    q.similarity = keywordMatchRate
  } else if (q.type === 'judge') {
    // 判断题处理 - 更加灵活的匹配
    // 标准化"对"和"错"的各种表达
    const trueAnswers = ['对', '正确', 'yes', 'true', '√', 't', 'y', '1']
    const falseAnswers = ['错', '不对', '错误', 'no', 'false', '×', 'f', 'n', '0']
    
    const userAnswerLower = normalizedUserAnswer.toLowerCase()
    const standardAnswerLower = normalizedStandardAnswer.toLowerCase()
    
    // 检查用户答案是否和标准答案在同一类别
    const userAnswerIsTrue = trueAnswers.some(ans => userAnswerLower.includes(ans))
    const userAnswerIsFalse = falseAnswers.some(ans => userAnswerLower.includes(ans))
    const standardAnswerIsTrue = trueAnswers.some(ans => standardAnswerLower.includes(ans))
    const standardAnswerIsFalse = falseAnswers.some(ans => standardAnswerLower.includes(ans))
    
    // 如果用户答案和标准答案同属"对"或同属"错"，则正确
    q.result = (userAnswerIsTrue && standardAnswerIsTrue) || 
               (userAnswerIsFalse && standardAnswerIsFalse)
  } else {
    // 单选题 - 检查选项是否匹配
    // 提取选项字母（A、B、C、D）并比较
    const userLetter = normalizedUserAnswer.match(/^[A-Da-d]/)
    const standardLetter = normalizedStandardAnswer.match(/^[A-Da-d]/)
    
    if (userLetter && standardLetter) {
      q.result = userLetter[0].toUpperCase() === standardLetter[0].toUpperCase()
    } else {
      // 如果没有找到字母，则直接比较完整答案
      q.result = normalizedUserAnswer === normalizedStandardAnswer
    }
  }
  
  // 显示结果消息
  if (q.result === true) {
    ElMessage.success('回答正确！')
  } else if (q.result === false) {
    ElMessage.error('回答不正确')
  } else {
    ElMessage.info('已提交回答，但无标准答案')
  }
  saveQuestionsToLocal() // 新增：保存到本地
}

// 规范化答案文本
function normalizeAnswer(text: string): string {
  if (!text) return ''
  
  // 转为小写
  let normalized = text.toLowerCase()
  
  // 去除标点符号（保留中文）
  normalized = normalized.replace(/[.,!?;:'"()\[\]{}。，！？；：""''（）【】{}]/g, '')
  
  // 替换多个空格为单个空格
  normalized = normalized.replace(/\s+/g, ' ')
  
  // 去除首尾空格
  normalized = normalized.trim()
  
  return normalized
}

// 提取关键词
function extractKeywords(text: string): string[] {
  if (!text) return []
  
  // 拆分文本为单词
  const words = text.split(/\s+/)
  
  // 过滤掉常见的停用词
  const stopWords = ['的', '了', '和', '与', '或', '在', '是', '有', '并', '且', 'a', 'an', 'the', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'and', 'or']
  
  return words.filter(word => {
    // 过滤掉停用词和过短的词
    return word.length > 1 && !stopWords.includes(word)
  })
}

// 检查关键词匹配度
function checkKeywordMatch(userAnswer: string, standardAnswer: string): number {
  const standardKeywords = extractKeywords(standardAnswer)
  const userKeywords = extractKeywords(userAnswer)
  
  if (standardKeywords.length === 0) return 0
  
  let matchCount = 0
  for (const keyword of standardKeywords) {
    if (userKeywords.includes(keyword)) {
      matchCount++
    } else if (userAnswer.includes(keyword)) {
      // 即使单词不完全匹配，但文本中包含也给部分分数
      matchCount += 0.5
    }
  }
  
  return matchCount / standardKeywords.length
}

// 改进的相似度计算算法
function calculateSimilarity(str1: string, str2: string): number {
  if (!str1 || !str2) return 0
  
  // 将文本转换为小写并分词
  const words1 = str1.split(/\s+/).filter(w => w.length > 1)
  const words2 = str2.split(/\s+/).filter(w => w.length > 1)
  
  if (words1.length === 0 || words2.length === 0) return 0
  
  // 计算共同词汇（更灵活的匹配）
  let matchCount = 0
  
  for (const word1 of words1) {
    // 检查是否有完全匹配的词
    if (words2.includes(word1)) {
      matchCount += 1
      continue
    }
    
    // 检查是否有部分匹配的词
    for (const word2 of words2) {
      // 如果词语长度小于3，需要完全匹配
      if (word1.length < 3 || word2.length < 3) {
        if (word1 === word2) {
          matchCount += 1
          break
        }
      } 
      // 对于较长的词，检查是否包含部分
      else if (word1.includes(word2) || word2.includes(word1)) {
        matchCount += 0.7 // 部分匹配给0.7的权重
        break
      }
      // 对于长词，计算编辑距离相似度
      else if (word1.length > 4 && word2.length > 4) {
        const editDistance = calculateEditDistance(word1, word2)
        const maxLength = Math.max(word1.length, word2.length)
        const similarity = 1 - (editDistance / maxLength)
        
        if (similarity > 0.7) { // 相似度超过70%
          matchCount += similarity * 0.8 // 乘以0.8的权重
          break
        }
      }
    }
  }
  
  // 使用Dice系数计算相似度
  return (2 * matchCount) / (words1.length + words2.length)
}

// 莱文斯坦编辑距离算法
function calculateEditDistance(str1: string, str2: string): number {
  const m = str1.length
  const n = str2.length
  const dp: number[][] = Array(m + 1).fill(0).map(() => Array(n + 1).fill(0))
  
  // 初始化
  for (let i = 0; i <= m; i++) {
    dp[i][0] = i
  }
  for (let j = 0; j <= n; j++) {
    dp[0][j] = j
  }
  
  // 填充dp表
  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      if (str1[i - 1] === str2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1]
      } else {
        dp[i][j] = Math.min(
          dp[i - 1][j] + 1,    // 删除
          dp[i][j - 1] + 1,    // 插入
          dp[i - 1][j - 1] + 1 // 替换
        )
      }
    }
  }
  
  return dp[m][n]
}

// 检查所有答案
function checkAllAnswers() {
  if (questions.value.length === 0) return
  
  const unansweredCount = questions.value.filter(q => !q.userAnswer).length
  if (unansweredCount > 0) {
    ElMessage.warning(`还有 ${unansweredCount} 题未作答`)
    return
  }
  
  // 检查每个问题
  questions.value.forEach((_, index) => {
    if (questions.value[index].result === undefined) {
      checkAnswer(index)
    }
  })
  
  ElMessage.success('所有答案已提交')
}

// 重置单个答案
function resetAnswer(index: number) {
  const q = questions.value[index]
  q.userAnswer = ''
  q.result = undefined
  q.similarity = undefined
}

// 导出结果
function exportResults() {
  if (questions.value.length === 0) {
    ElMessage.warning('没有题目可导出')
    return
  }
  
  // 确认框
  ElMessageBox.confirm('确定要导出当前结果吗？', '导出确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    // 生成CSV内容
    let csvContent = 'data:text/csv;charset=utf-8,题号,题目内容,题目类型,你的答案,正确答案,是否正确\n'
    
    questions.value.forEach((q, i) => {
      // 处理可能包含逗号的内容
      const content = `"${q.content.replace(/"/g, '""')}"`
      const userAnswer = `"${(q.userAnswer || '').replace(/"/g, '""')}"`
      const answer = `"${(q.answer || '').replace(/"/g, '""')}"`
      const result = q.result === true ? '正确' : q.result === false ? '错误' : '未评判'
      
      csvContent += `${i+1},${content},${getQuestionTypeLabel(q.type)},${userAnswer},${answer},${result}\n`
    })
    
    // 创建下载链接
    const encodedUri = encodeURI(csvContent)
    const link = document.createElement('a')
    link.setAttribute('href', encodedUri)
    link.setAttribute('download', `题目结果_${new Date().toLocaleDateString()}.csv`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('导出成功')
  }).catch(() => {})
}

// 监听问题变化，自动滚动到活动的问题
watch(activeQuestion, (newValue) => {
  if (newValue.length > 0) {
    setTimeout(() => {
      const element = document.querySelector(`.el-collapse-item[name="${newValue[0]}"]`)
      element?.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }, 100)
  }
})

// 1. 加载本地题目
onMounted(() => {
  const local = localStorage.getItem('local_questions')
  if (local) {
    try {
      questions.value = JSON.parse(local)
    } catch {}
  }
})

// 2. 存储题目到本地
function saveQuestionsToLocal() {
  localStorage.setItem('local_questions', JSON.stringify(questions.value))
}
</script>

<style scoped>
.question-generator {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.file-upload-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
}

.upload-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.upload-box {
  width: 100%;
}

.file-info {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.action-buttons {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.questions-card {
  margin-top: 30px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.questions-progress {
  margin-bottom: 20px;
}

.question-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.question-number {
  font-weight: bold;
  margin-right: 4px;
}

.question-content {
  flex: 1;
}

.question-type-tag {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8em;
  margin-left: 8px;
}

.type-judge {
  background-color: #f0f9eb;
  color: #67c23a;
}

.type-single {
  background-color: #f2f6fc;
  color: #409eff;
}

.type-fill {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.type-essay {
  background-color: #fef0f0;
  color: #f56c6c;
}

.question-options {
  margin: 15px 0;
}

.question-input {
  margin: 15px 0;
}

.question-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.result-icon {
  margin-left: 8px;
}

.correct-icon {
  color: #67c23a;
}

.incorrect-icon {
  color: #f56c6c;
}

.no-answer-icon {
  color: #909399;
}

.answer-feedback {
  margin-top: 15px;
}

.similarity-score {
  margin-top: 10px;
  font-style: italic;
  color: #606266;
}

.summary-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.summary-section h3 {
  margin-top: 0;
  margin-bottom: 20px;
}

.file-preview {
  max-height: 400px;
  overflow-y: auto;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
}

.file-preview pre {
  margin: 0;
  white-space: pre-wrap;
}

.question-correct :deep(.el-collapse-item__header) {
  border-left: 4px solid #67c23a;
  padding-left: 10px;
}

.question-incorrect :deep(.el-collapse-item__header) {
  border-left: 4px solid #f56c6c;
  padding-left: 10px;
}

.question-unanswered :deep(.el-collapse-item__header) {
  border-left: 4px solid #909399;
  padding-left: 10px;
}

.el-upload__tip ul {
  padding-left: 20px;
  margin: 5px 0;
}

.el-upload__tip li {
  margin: 3px 0;
}

@media (max-width: 768px) {
  .question-generator {
    padding: 10px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}
</style>