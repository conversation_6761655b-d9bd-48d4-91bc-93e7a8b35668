const Router = require("koa-router");
const fs = require("fs");
const path = require("path");
const { handleResponse } = require("./responseHandler");
const { Throttle } = require("stream-throttle"); // 新增

const resource = new Router();
const resourceDir = path.join(__dirname, "../uploads/resource");
const logger = require("../plugin/logger");

// 下载频率限制
const downloadMap = new Map(); // key: ip+filename, value: 时间戳

// 获取资源列表
resource.get("/list", async (ctx) => {
  if (!fs.existsSync(resourceDir)) {
    return handleResponse(ctx, 200, { data: [] });
  }
  const files = fs.readdirSync(resourceDir).map((file) => {
    const stat = fs.statSync(path.join(resourceDir, file));
    return {
      filename: file,
      size: stat.size,
      mtime: stat.mtime,
    };
  });
  return handleResponse(ctx, 200, { data: files });
});

// 下载资源
resource.get("/:filename", async (ctx) => {
  const { filename } = ctx.params;
  const ip = ctx.ip;
  const key = `${ip}_${filename}`;
  const now = Date.now();

  // 60秒内同IP同文件只允许一次下载
  if (downloadMap.has(key) && now - downloadMap.get(key) < 60 * 1000) {
    logger.warn(`重复下载拦截：${filename} - IP: ${ip}`);
    return handleResponse(ctx, 429, { error: "请勿频繁重复下载同一文件" });
  }
  downloadMap.set(key, now);

  const filePath = path.join(resourceDir, filename);
  if (!fs.existsSync(filePath)) {
    logger.warn(`下载失败：文件不存在 - ${filename} - IP: ${ip}`);
    return handleResponse(ctx, 404, { error: "资源不存在" });
  }
  ctx.set(
    "Content-Disposition",
    `attachment; filename=${encodeURIComponent(filename)}`
  );
  logger.info(`文件下载：${filename} - IP: ${ip}`);
  ctx.body = fs
    .createReadStream(filePath)
    .pipe(new Throttle({ rate: 100 * 1024 * 1024 })); // 可选：限速为 100MB/s
});

// 预览
resource.get("/prew/:filename", async (ctx) => {
  const { filename } = ctx.params;
  const filePath = path.join(resourceDir, filename);
  if (!fs.existsSync(filePath)) {
    logger.warn(`预览失败：文件不存在 - ${filename} - IP: ${ctx.ip}`);
    ctx.status = 404;
    ctx.body = "文件不存在";
    return;
  }
  const ext = path.extname(filename).toLowerCase().replace(".", "");

  // 常见类型映射
  const typeMap = {
    txt: "text/plain; charset=utf-8",
    csv: "text/csv; charset=utf-8",
    json: "application/json; charset=utf-8",
    jpg: "image/jpeg",
    jpeg: "image/jpeg",
    png: "image/png",
    gif: "image/gif",
    bmp: "image/bmp",
    webp: "image/webp",
    svg: "image/svg+xml",
    pdf: "application/pdf",
    mp4: "video/mp4",
    mp3: "audio/mpeg",
    wav: "audio/wav",
    ogg: "audio/ogg",
    docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    pptx: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    zip: "application/zip",
    rar: "application/vnd.rar",
    "7z": "application/x-7z-compressed",
  };

  if (typeMap[ext]) {
    ctx.set("Content-Type", typeMap[ext]);
    logger.info(`文件预览：${filename} - IP: ${ctx.ip}`);
    // 文本类直接读取内容
    if (["txt", "csv", "json"].includes(ext)) {
      ctx.body = fs.readFileSync(filePath, "utf-8");
    } else {
      // 其它类型用流
      ctx.body = fs.createReadStream(filePath);
    }
  } else {
    logger.warn(`预览失败：不支持的类型 - ${filename} - IP: ${ctx.ip}`);
    ctx.status = 415;
    ctx.body = "不支持的预览类型";
  }
});

module.exports = resource;
