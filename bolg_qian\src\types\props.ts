// 表单字段错误信息类型
export interface Errors {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// 表单数据类型
export interface FormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// 登录成功响应数据类型
export interface LoginResponse {
  token: string;
  user: {
    id: number;
    username: string;
    email: string;
    nickname?: string;
    avatar?: string;
  };
}
