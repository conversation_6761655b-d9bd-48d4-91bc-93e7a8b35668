const Router = require("koa-router");
const logger = require("../../plugin/logger");
const mediaFeaturesService = require("../../utils/sql/mediaFeaturesService");

const mediaFeatures = new Router();

// 通用响应处理
const handleResponse = (ctx, code, data, message = "") => {
  ctx.status = code;
  ctx.body = { code, data, message };
};

// ==================== 播放历史相关 ====================

// 保存播放历史
mediaFeatures.post("/play-history", async (ctx) => {
  try {
    const { userId, fileName, progress, currentTime } = ctx.request.body;

    if (!userId || !fileName) {
      return handleResponse(ctx, 400, null, "用户ID和文件名不能为空");
    }

    const playTime = Date.now();

    // 使用 INSERT ... ON DUPLICATE KEY UPDATE 来处理重复记录
    const sql = `
      INSERT INTO media_play_history (user_id, file_name, progress, \`current_time\`, play_time)
      VALUES (?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        progress = VALUES(progress),
        \`current_time\` = VALUES(\`current_time\`),
        play_time = VALUES(play_time),
        updated_at = CURRENT_TIMESTAMP
    `;

    await mediaFeaturesService.savePlayHistory(userId, fileName, progress, currentTime, playTime);

    logger.info(`播放历史已保存: ${fileName} - ${userId}`);
    return handleResponse(ctx, 200, { success: true }, "播放历史保存成功");
  } catch (error) {
    logger.error("保存播放历史失败:", error);
    return handleResponse(ctx, 500, null, "保存播放历史失败");
  }
});

// 获取播放历史
mediaFeatures.get("/play-history", async (ctx) => {
  try {
    const { userId, page = 1, limit = 50 } = ctx.query;

    if (!userId) {
      return handleResponse(ctx, 400, null, "用户ID不能为空");
    }

    const offset = (page - 1) * limit;

    const sql = `
      SELECT
        mph.*,
        ms.file_size,
        ms.duration,
        ms.category,
        COALESCE(vm.status, 'online') as video_status
      FROM media_play_history mph
      LEFT JOIN media_stats ms ON mph.file_name = ms.file_name
      LEFT JOIN video_management vm ON mph.file_name = vm.file_name
      WHERE mph.user_id = ? AND COALESCE(vm.status, 'online') = 'online'
      ORDER BY mph.play_time DESC
      LIMIT ? OFFSET ?
    `;

    const { history, total } = await mediaFeaturesService.getPlayHistory(userId, limit, offset);

    return handleResponse(ctx, 200, {
      list: history,
      total,
      page: parseInt(page),
      limit: parseInt(limit)
    });
  } catch (error) {
    logger.error("获取播放历史失败:", error);
    return handleResponse(ctx, 500, null, "获取播放历史失败");
  }
});

// 清空播放历史
mediaFeatures.delete("/play-history/:userId", async (ctx) => {
  try {
    const { userId } = ctx.params;

    if (!userId) {
      return handleResponse(ctx, 400, null, "用户ID不能为空");
    }

    await mediaFeaturesService.clearPlayHistory(userId);

    logger.info(`用户 ${userId} 的播放历史已清空`);
    return handleResponse(ctx, 200, { success: true }, "播放历史清空成功");
  } catch (error) {
    logger.error("清空播放历史失败:", error);
    return handleResponse(ctx, 500, null, "清空播放历史失败");
  }
});

// ==================== 喜欢功能相关 ====================

// 喜欢媒体
mediaFeatures.post("/like", async (ctx) => {
  try {
    const { userId, fileName } = ctx.request.body;

    if (!userId || !fileName) {
      return handleResponse(ctx, 400, null, "用户ID和文件名不能为空");
    }

    // 检查是否已经喜欢
    const existing = await mediaFeaturesService.checkMediaLike(userId, fileName);

    if (existing.length > 0) {
      return handleResponse(ctx, 400, null, "已经喜欢过该媒体");
    }

    // 添加喜欢记录
    await mediaFeaturesService.addMediaLike(userId, fileName);

    // 更新统计
    await mediaFeaturesService.updateMediaStats(fileName, 'like_count', 1);

    logger.info(`用户 ${userId} 喜欢了媒体: ${fileName}`);
    return handleResponse(ctx, 200, { success: true }, "喜欢成功");
  } catch (error) {
    logger.error("喜欢媒体失败:", error);
    return handleResponse(ctx, 500, null, "喜欢失败");
  }
});

// 取消喜欢 (POST方式，更可靠)
mediaFeatures.post("/unlike", async (ctx) => {
  try {
    const { userId, fileName } = ctx.request.body;

    logger.info(`取消喜欢请求: userId=${userId}, fileName=${fileName}`);

    if (!userId || !fileName) {
      logger.warn(`取消喜欢参数错误: userId=${userId}, fileName=${fileName}`);
      return handleResponse(ctx, 400, null, "用户ID和文件名不能为空");
    }

    // 先检查记录是否存在
    const existing = await mediaFeaturesService.checkMediaLike(userId, fileName);
    logger.info(`查找喜欢记录: 找到 ${existing.length} 条记录`);

    if (existing.length === 0) {
      logger.warn(`未找到喜欢记录: userId=${userId}, fileName=${fileName}`);
      return handleResponse(ctx, 400, null, "未找到喜欢记录，可能已经取消过了");
    }

    const result = await mediaFeaturesService.removeMediaLike(userId, fileName);
    logger.info(`删除结果: affectedRows=${result.affectedRows}`);

    if (result.affectedRows === 0) {
      return handleResponse(ctx, 400, null, "删除失败，记录可能已被删除");
    }

    // 更新统计
    await mediaFeaturesService.updateMediaStats(fileName, 'like_count', -1);

    logger.info(`用户 ${userId} 取消喜欢媒体: ${fileName}`);
    return handleResponse(ctx, 200, { success: true }, "取消喜欢成功");
  } catch (error) {
    logger.error("取消喜欢失败:", error);
    return handleResponse(ctx, 500, null, "取消喜欢失败");
  }
});

// 取消喜欢 (DELETE方式，保留兼容性)
mediaFeatures.delete("/like", async (ctx) => {
  try {
    const { userId, fileName } = ctx.request.body;

    logger.info(`取消喜欢请求: userId=${userId}, fileName=${fileName}`);

    if (!userId || !fileName) {
      logger.warn(`取消喜欢参数错误: userId=${userId}, fileName=${fileName}`);
      return handleResponse(ctx, 400, null, "用户ID和文件名不能为空");
    }

    // 先检查记录是否存在
    const existing = await mediaFeaturesService.checkMediaLike(userId, fileName);
    logger.info(`查找喜欢记录: 找到 ${existing.length} 条记录`);

    if (existing.length === 0) {
      logger.warn(`未找到喜欢记录: userId=${userId}, fileName=${fileName}`);
      return handleResponse(ctx, 400, null, "未找到喜欢记录，可能已经取消过了");
    }

    const result = await mediaFeaturesService.removeMediaLike(userId, fileName);
    logger.info(`删除结果: affectedRows=${result.affectedRows}`);

    if (result.affectedRows === 0) {
      return handleResponse(ctx, 400, null, "删除失败，记录可能已被删除");
    }

    // 更新统计
    await mediaFeaturesService.updateMediaStats(fileName, 'like_count', -1);

    logger.info(`用户 ${userId} 取消喜欢媒体: ${fileName}`);
    return handleResponse(ctx, 200, { success: true }, "取消喜欢成功");
  } catch (error) {
    logger.error("取消喜欢失败:", error);
    return handleResponse(ctx, 500, null, "取消喜欢失败");
  }
});

// 获取喜欢的媒体列表
mediaFeatures.get("/liked", async (ctx) => {
  try {
    const { userId, page = 1, limit = 12 } = ctx.query;

    if (!userId) {
      return handleResponse(ctx, 400, null, "用户ID不能为空");
    }

    const offset = (page - 1) * limit;

    const sql = `
      SELECT
        ml.file_name,
        ml.created_at as liked_at,
        ms.*,
        COALESCE(vm.status, 'online') as video_status
      FROM media_likes ml
      LEFT JOIN media_stats ms ON ml.file_name = ms.file_name
      LEFT JOIN video_management vm ON ml.file_name = vm.file_name
      WHERE ml.user_id = ? AND COALESCE(vm.status, 'online') = 'online'
      ORDER BY ml.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const { liked, total } = await mediaFeaturesService.getUserLikedMedia(userId, limit, offset);

    return handleResponse(ctx, 200, {
      list: liked,
      total,
      page: parseInt(page),
      limit: parseInt(limit)
    });
  } catch (error) {
    logger.error("获取喜欢列表失败:", error);
    return handleResponse(ctx, 500, null, "获取喜欢列表失败");
  }
});

// ==================== 收藏功能相关 ====================

// 收藏媒体
mediaFeatures.post("/collect", async (ctx) => {
  try {
    const { userId, fileName } = ctx.request.body;

    if (!userId || !fileName) {
      return handleResponse(ctx, 400, null, "用户ID和文件名不能为空");
    }

    // 检查是否已经收藏
    const existing = await mediaFeaturesService.checkMediaCollection(userId, fileName);

    if (existing.length > 0) {
      return handleResponse(ctx, 400, null, "已经收藏过该媒体");
    }

    // 添加收藏记录
    await mediaFeaturesService.addMediaCollection(userId, fileName);

    // 更新统计
    await mediaFeaturesService.updateMediaStats(fileName, 'collect_count', 1);

    logger.info(`用户 ${userId} 收藏了媒体: ${fileName}`);
    return handleResponse(ctx, 200, { success: true }, "收藏成功");
  } catch (error) {
    logger.error("收藏媒体失败:", error);
    return handleResponse(ctx, 500, null, "收藏失败");
  }
});

// 取消收藏 (POST方式，更可靠)
mediaFeatures.post("/uncollect", async (ctx) => {
  try {
    const { userId, fileName } = ctx.request.body;

    logger.info(`取消收藏请求: userId=${userId}, fileName=${fileName}`);

    if (!userId || !fileName) {
      logger.warn(`取消收藏参数错误: userId=${userId}, fileName=${fileName}`);
      return handleResponse(ctx, 400, null, "用户ID和文件名不能为空");
    }

    // 先检查记录是否存在
    const existing = await mediaFeaturesService.checkMediaCollection(userId, fileName);
    logger.info(`查找收藏记录: 找到 ${existing.length} 条记录`);

    if (existing.length === 0) {
      logger.warn(`未找到收藏记录: userId=${userId}, fileName=${fileName}`);
      return handleResponse(ctx, 400, null, "未找到收藏记录，可能已经取消过了");
    }

    const result = await mediaFeaturesService.removeMediaCollection(userId, fileName);
    logger.info(`删除结果: affectedRows=${result.affectedRows}`);

    if (result.affectedRows === 0) {
      return handleResponse(ctx, 400, null, "删除失败，记录可能已被删除");
    }

    // 更新统计
    await mediaFeaturesService.updateMediaStats(fileName, 'collect_count', -1);

    logger.info(`用户 ${userId} 取消收藏媒体: ${fileName}`);
    return handleResponse(ctx, 200, { success: true }, "取消收藏成功");
  } catch (error) {
    logger.error("取消收藏失败:", error);
    return handleResponse(ctx, 500, null, "取消收藏失败");
  }
});

// 取消收藏 (DELETE方式，保留兼容性)
mediaFeatures.delete("/collect", async (ctx) => {
  try {
    const { userId, fileName } = ctx.request.body;

    if (!userId || !fileName) {
      return handleResponse(ctx, 400, null, "用户ID和文件名不能为空");
    }

    const result = await mediaFeaturesService.removeMediaCollection(userId, fileName);

    if (result.affectedRows === 0) {
      return handleResponse(ctx, 400, null, "未找到收藏记录");
    }

    // 更新统计
    await mediaFeaturesService.updateMediaStats(fileName, 'collect_count', -1);

    logger.info(`用户 ${userId} 取消收藏媒体: ${fileName}`);
    return handleResponse(ctx, 200, { success: true }, "取消收藏成功");
  } catch (error) {
    logger.error("取消收藏失败:", error);
    return handleResponse(ctx, 500, null, "取消收藏失败");
  }
});

// 获取收藏的媒体列表
mediaFeatures.get("/collected", async (ctx) => {
  try {
    const { userId, page = 1, limit = 12 } = ctx.query;

    if (!userId) {
      return handleResponse(ctx, 400, null, "用户ID不能为空");
    }

    const offset = (page - 1) * limit;

    const sql = `
      SELECT
        mc.file_name,
        mc.created_at as collected_at,
        ms.*,
        COALESCE(vm.status, 'online') as video_status
      FROM media_collections mc
      LEFT JOIN media_stats ms ON mc.file_name = ms.file_name
      LEFT JOIN video_management vm ON mc.file_name = vm.file_name
      WHERE mc.user_id = ? AND COALESCE(vm.status, 'online') = 'online'
      ORDER BY mc.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const { collected, total } = await mediaFeaturesService.getUserCollectedMedia(userId, limit, offset);

    return handleResponse(ctx, 200, {
      list: collected,
      total,
      page: parseInt(page),
      limit: parseInt(limit)
    });
  } catch (error) {
    logger.error("获取收藏列表失败:", error);
    return handleResponse(ctx, 500, null, "获取收藏列表失败");
  }
});

// ==================== 统计功能相关 ====================

// 更新播放次数
mediaFeatures.post("/play-count/:fileName", async (ctx) => {
  try {
    const { fileName } = ctx.params;

    if (!fileName) {
      return handleResponse(ctx, 400, null, "文件名不能为空");
    }

    await updateMediaStats(decodeURIComponent(fileName), 'play_count', 1);

    return handleResponse(ctx, 200, { success: true }, "播放次数更新成功");
  } catch (error) {
    logger.error("更新播放次数失败:", error);
    return handleResponse(ctx, 500, null, "更新播放次数失败");
  }
});

// 获取媒体统计信息
mediaFeatures.get("/stats/:fileName", async (ctx) => {
  try {
    const { fileName } = ctx.params;

    if (!fileName) {
      return handleResponse(ctx, 400, null, "文件名不能为空");
    }

    const stats = await mediaFeaturesService.getMediaStats(fileName);

    if (!stats) {
      return handleResponse(ctx, 404, null, "未找到媒体统计信息");
    }

    return handleResponse(ctx, 200, stats);
  } catch (error) {
    logger.error("获取媒体统计失败:", error);
    return handleResponse(ctx, 500, null, "获取媒体统计失败");
  }
});

// ==================== 辅助函数 ====================

// 更新媒体统计 - 现在使用服务层
async function updateMediaStats(fileName, field, increment) {
  return await mediaFeaturesService.updateMediaStats(fileName, field, increment);
}

module.exports = mediaFeatures;
