<template>
  <div class="resource-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><FolderOpened /></el-icon>
          资源中心
        </h1>
        <p class="page-subtitle">浏览和下载共享资源文件</p>
      </div>
    </div>

    <div class="page-container">
      <!-- 下载状态卡片 -->
      <div v-if="downloadStatus" class="status-section">
        <el-card class="status-card">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon><DataAnalysis /></el-icon>
                <span>下载状态</span>
              </div>
              <el-button size="small" type="primary" link @click="refreshDownloadStatus">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div class="status-grid">
            <div class="status-item">
              <div class="status-icon user-level">
                <el-icon><User /></el-icon>
              </div>
              <div class="status-content">
                <div class="status-label">用户等级</div>
                <div class="status-value">{{ downloadStatus.userLevel }}</div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-icon download-speed">
                <el-icon><Download /></el-icon>
              </div>
              <div class="status-content">
                <div class="status-label">下载速度</div>
                <div class="status-value">{{ downloadStatus.speedLimit?.download }}KB/s</div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-icon concurrent">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="status-content">
                <div class="status-label">并发下载</div>
                <div class="status-value">{{ downloadStatus.concurrent?.current }}/{{ downloadStatus.concurrent?.max }}</div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-icon daily-usage">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="status-content">
                <div class="status-label">今日使用</div>
                <div class="status-value">
                  {{ (downloadStatus.dailyUsage?.used / 1024 / 1024).toFixed(2) }}MB
                  <span v-if="downloadStatus.dailyUsage?.limit">
                    / {{ (downloadStatus.dailyUsage.limit / 1024 / 1024).toFixed(2) }}MB
                  </span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 工具栏 -->
      <div class="toolbar-section">
        <div class="toolbar-left">
          <div class="search-container">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索资源文件..."
              @input="filterResources"
              clearable
              class="search-input"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>
        <div class="toolbar-right">
          <div class="resource-stats">
            <span class="stats-text">
              <el-icon><Document /></el-icon>
              共 {{ resources.length }} 个资源
            </span>
          </div>
        </div>
      </div>

      <!-- 资源表格 -->
      <div class="table-section">
        <el-card class="table-card">
          <el-table
            :data="resources"
            class="resource-table"
            :header-cell-style="{ background: 'var(--bg-tertiary)', color: 'var(--text-primary)' }"
            stripe
            @row-click="handleRowClick"
          >
            <el-table-column prop="filename" label="资源名称" min-width="300">
              <template #default="scope">
                <div class="file-info">
                  <div class="file-icon">
                    <el-icon :class="getFileIconClass(scope.row.filename)">
                      <component :is="getFileIcon(scope.row.filename)" />
                    </el-icon>
                  </div>
                  <div class="file-details">
                    <div class="file-name">{{ scope.row.filename }}</div>
                    <div class="file-type">{{ getFileType(scope.row.filename) }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="size" label="文件大小" width="120" align="center">
              <template #default="scope">
                <el-tag size="small" effect="light">
                  {{ formatSize(scope.row.size) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="mtime" label="上传时间" width="180" align="center">
              <template #default="scope">
                <div class="upload-time">
                  <el-icon><Clock /></el-icon>
                  {{ formatTime(scope.row.mtime) }}
                </div>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="scope">
                <div class="action-buttons">
                  <el-button
                    type="primary"
                    size="small"
                    :loading="downloadingSet.has(scope.row.filename)"
                    :disabled="downloadingSet.has(scope.row.filename)"
                    @click.stop="download(scope.row)"
                    class="action-btn"
                  >
                    <el-icon><ArrowDown /></el-icon>
                    {{ downloadingSet.has(scope.row.filename) ? '下载中' : '下载' }}
                  </el-button>
                  <el-button
                    v-if="canPreview(scope.row.filename)"
                    type="success"
                    size="small"
                    @click.stop="preview(scope.row)"
                    class="action-btn"
                    plain
                  >
                    <el-icon><View /></el-icon>
                    预览
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 空状态 -->
          <div v-if="resources.length === 0" class="empty-state">
            <el-empty description="暂无资源文件">
              <el-button type="primary" @click="refreshDownloadStatus">
                <el-icon><Refresh /></el-icon>
                刷新列表
              </el-button>
            </el-empty>
          </div>
        </el-card>
      </div>
    </div>

    <el-dialog
      v-model="previewVisible"
      :width="previewType === 'text' ? '80%' : '60%'"
      :title="getPreviewTitle()"
      :fullscreen="isFullscreen"
      @close="handlePreviewClose"
    >
      <!-- 图片预览 -->
      <div v-if="previewType === 'image'" style="text-align:center;">
        <img
          :src="previewContent"
          style="max-width:100%;max-height:60vh;"
          @error="handleImageError"
          @load="handleImageLoad"
        />
        <div v-if="imageLoadError" class="error-message">
          <p>图片加载失败</p>
          <p class="error-details">路径: {{ previewContent }}</p>
          <el-button size="small" @click="retryImageLoad">重试</el-button>
        </div>
      </div>

      <!-- 视频预览 -->
      <div v-else-if="previewType === 'video'" class="video-preview-container">
        <div class="video-preview-toolbar">
          <div class="video-info">
            <el-icon><VideoPlay /></el-icon>
            <span>视频预览 (限制10秒)</span>
          </div>
          <div class="video-actions">
            <el-button size="small" @click="downloadCurrentFile" type="primary">
              <el-icon><Download /></el-icon>
              下载完整视频
            </el-button>
          </div>
        </div>

        <div class="video-wrapper">
          <video
            ref="videoPlayer"
            :src="previewContent"
            controls
            preload="metadata"
            @loadedmetadata="handleVideoLoaded"
            @timeupdate="handleVideoTimeUpdate"
            @error="handleVideoError"
            class="preview-video"
          >
            您的浏览器不支持视频播放
          </video>

          <div v-if="videoLoadError" class="error-message">
            <p>视频加载失败</p>
            <p class="error-details">路径: {{ previewContent }}</p>
            <el-button size="small" @click="retryVideoLoad">重试</el-button>
          </div>
        </div>

        <div class="video-preview-footer">
          <div class="video-controls">
            <el-progress
              :percentage="videoProgress"
              :show-text="false"
              class="video-progress"
            />
            <span class="time-info">
              {{ formatVideoTime(currentVideoTime) }} / {{ videoPreviewLimit }}秒 (预览限制)
            </span>
          </div>
          <div class="video-tips">
            <el-alert
              title="预览提示"
              description="视频预览限制为10秒，下载完整文件可观看全部内容"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
        </div>
      </div>

      <!-- 文本预览 -->
      <div v-else-if="previewType === 'text'" class="text-preview-container">
        <div class="text-preview-toolbar">
          <div class="file-info">
            <el-tag size="small">{{ currentFileExtension.toUpperCase() }}</el-tag>
            <span class="file-size">{{ formatTextSize(previewContent) }}</span>
          </div>
          <div class="preview-actions">
            <el-button size="small" @click="copyTextContent">
              复制
            </el-button>
            <el-button size="small" @click="toggleFullscreen">
              {{ isFullscreen ? '退出全屏' : '全屏' }}
            </el-button>
          </div>
        </div>

        <div class="text-content" :class="getTextContentClass()">
          <pre><code>{{ previewContent }}</code></pre>
        </div>

        <div class="text-preview-footer">
          <span class="line-count">共 {{ getLineCount() }} 行</span>
          <span class="char-count">{{ previewContent.length }} 字符</span>
        </div>
      </div>

      <template #footer v-if="previewType === 'text'">
        <el-button @click="previewVisible = false">关闭</el-button>
        <el-button type="primary" @click="downloadCurrentFile">下载文件</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  ArrowDown, FolderOpened, DataAnalysis, Refresh, User, Download,
  Connection, Calendar, Search, Document, Clock, View,
  Picture, VideoPlay, Folder, Files, DocumentCopy
} from '@element-plus/icons-vue';
import { GetResourceListApi, DownloadResourceApi, PreviewResourceApi, getResourceDownloadCountApi, speedDownloadApi } from "../../utils/api";

const resources = ref<any[]>([]);
const filteredResources = ref<any[]>([]); // 存储原始资源列表
const downloadingSet = ref<Set<string>>(new Set());
const searchKeyword = ref<string>("");

// 限速下载相关状态
const downloadStatus = ref<any>(null);
const downloadProgress = ref<Map<string, number>>(new Map());

onMounted(async () => {
  const res = await GetResourceListApi();
  console.log("获取资源列表", res);
  if (res?.data) {
    filteredResources.value = res.data.map((item: any) => {
      console.log("映射资源项:", item); // 调试日志
      return {
        filename: item.file_name,
        file_name: item.file_name,
        file_path: item.file_path,  // 🔧 修复：添加file_path映射
        size: item.file_size,
        mtime: item.created_at,
        created_at: item.created_at,
        updated_at: item.updated_at,
        file_type: item.file_type
      };
    });
    resources.value = filteredResources.value; // 初始化资源列表
    console.log("映射后的资源列表:", filteredResources.value); // 调试日志
  }

  // 获取下载状态
  await refreshDownloadStatus();
});

// 刷新下载状态
async function refreshDownloadStatus() {
  try {
    const statusRes = await speedDownloadApi.getStatus();
    if (statusRes.data.code === 200) {
      downloadStatus.value = statusRes.data.data;
    }
  } catch (error) {
    console.error('获取下载状态失败:', error);
  }
}

function getFileType(filename: string) {
  const ext = filename.split(".").pop()?.toLowerCase() || "";
  return ext;
}

function formatSize(size: number) {
  if (size < 1024) return size + " B";
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + " KB";
  if (size < 1024 * 1024 * 1024) return (size / 1024 / 1024).toFixed(1) + " MB";
  return (size / 1024 / 1024 / 1024).toFixed(1) + " GB";
}

function formatTime(time: string | Date) {
  const d = new Date(time);
  return d.toLocaleString();
}



// 下载文件
async function download(row: any) {
  if (downloadingSet.value.has(row.filename)) return;
  downloadingSet.value.add(row.filename);

  try {
    console.log('开始下载文件:', row.filename);
    console.log('期望文件大小:', row.size, 'bytes');

    const res = await DownloadResourceApi(row.filename);

    console.log('下载响应:', {
      status: res.status,
      headers: res.headers,
      dataType: typeof res.data,
      isBlob: res.data instanceof Blob,
      dataSize: res.data instanceof Blob ? res.data.size : res.data?.length || 'unknown'
    });

    // 确保响应数据是Blob类型
    let blob: Blob;
    if (res.data instanceof Blob) {
      blob = res.data;
      console.log('✅ 使用原始Blob数据');
    } else {
      // 如果不是Blob，创建一个新的Blob
      blob = new Blob([res.data], { type: 'application/octet-stream' });
      console.log('⚠️ 转换为Blob数据');
    }

    console.log('最终下载的文件大小:', blob.size, 'bytes');

    // 检查文件大小是否异常
    if (blob.size < 100 && row.size > 1000) {
      console.error('❌ 文件大小异常！期望:', row.size, '实际:', blob.size);
      ElMessage.error(`文件下载异常：文件大小不匹配（期望 ${row.size} bytes，实际 ${blob.size} bytes）`);
      return;
    }

    // 创建下载链接
    const link = document.createElement("a");
    link.href = window.URL.createObjectURL(blob);
    link.download = row.filename;

    // 添加到DOM并触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(link.href);

    // 统计下载日志
    const user_id = parseInt(localStorage.getItem("id") || "0");
    const file_type = getFileType(row.filename);
    await getResourceDownloadCountApi(
      row.filename,
      user_id,
      file_type
    );

    ElMessage.success(`下载完成: ${row.filename}`);
    console.log('文件下载成功:', row.filename);

  } catch (error: any) {
    console.error('下载失败:', error);
    if (error.response?.status === 429) {
      ElMessage.error("下载过于频繁，请稍后再试");
    } else if (error.response?.status === 404) {
      ElMessage.error("文件不存在");
    } else {
      ElMessage.error("下载失败: " + (error.message || "未知错误"));
    }
  } finally {
    downloadingSet.value.delete(row.filename);
  }
}



function canPreview(filename: string) {
  const ext = getFileType(filename);
  const imageExts = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "ico"];
  const videoExts = ["mp4", "avi", "mov", "wmv", "flv", "mkv", "webm", "m4v"];
  const textExts = ["txt", "md", "json", "xml", "js", "css", "html", "py", "java"];

  return [...imageExts, ...videoExts, ...textExts].includes(ext);
}

async function preview(row: any) {
  const ext = getFileType(row.filename);
  const filename = row.filename || row.file_name;

  // 设置当前文件信息
  currentFileName.value = filename;
  currentFileExtension.value = ext;

  // 图片文件预览
  if (["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "ico"].includes(ext)) {
    previewType.value = "image";

    // 详细调试信息
    console.log('预览图片文件:', {
      filename: filename,
      file_path: row.file_path,
      extension: ext
    });

    // 使用数据库中的完整路径，但转换为代理路径避免跨域问题
    if (row.file_path) {
      // 将完整URL转换为代理路径
      const fullUrl = row.file_path;
      if (fullUrl.includes('192.168.31.222:3000')) {
        // 提取路径部分，使用前端代理
        const pathPart = fullUrl.replace('http://192.168.31.222:3000', '');
        previewContent.value = baseUrl + pathPart;
        console.log('使用代理路径:', previewContent.value);
      } else {
        previewContent.value = fullUrl;
        console.log('使用原始路径:', fullUrl);
      }
    } else {
      previewContent.value = baseUrl + "/resource/" + filename;
      console.log('使用默认路径:', previewContent.value);
    }

    console.log('最终图片预览路径:', previewContent.value);
    previewVisible.value = true;
  }
  // 视频文件预览
  else if (["mp4", "avi", "mov", "wmv", "flv", "mkv", "webm", "m4v"].includes(ext)) {
    previewType.value = "video";

    console.log('预览视频文件:', {
      filename: filename,
      file_path: row.file_path,
      extension: ext
    });

    // 构建视频预览路径
    if (row.file_path) {
      const fullUrl = row.file_path;
      if (fullUrl.includes('192.168.31.222:3000')) {
        const pathPart = fullUrl.replace('http://192.168.31.222:3000', '');
        previewContent.value = baseUrl + pathPart;
      } else {
        previewContent.value = fullUrl;
      }
    } else {
      previewContent.value = baseUrl + "/resource/" + filename;
    }

    console.log('最终视频预览路径:', previewContent.value);
    previewVisible.value = true;
  }
  // 文本文件预览
  else if (isTextFile(ext)) {
    try {
      console.log('预览文本文件:', filename, '类型:', ext);
      previewType.value = "text";

      const res = await PreviewResourceApi(filename);
      console.log('文本预览响应:', res);

      // 处理不同的响应格式
      let textContent = '';
      if (typeof res === 'string') {
        textContent = res;
      } else if (res && typeof res.data === 'string') {
        textContent = res.data;
      } else if (res && res.data) {
        textContent = String(res.data);
      } else {
        textContent = '无法读取文件内容';
      }

      previewContent.value = textContent;
      previewVisible.value = true;

    } catch (error: any) {
      console.error('文本预览失败:', error);
      ElMessage.error(`文本预览失败: ${error.message || '未知错误'}`);
    }
  }
  // 不支持的文件类型
  else {
    ElMessage.warning(`不支持预览 .${ext} 文件类型`);
  }
}

// 判断是否为文本文件
function isTextFile(ext: string): boolean {
  const textExtensions = [
    // 基础文本文件
    'txt', 'text', 'log', 'md', 'markdown',
    // 配置文件
    'json', 'xml', 'yaml', 'yml', 'ini', 'conf', 'config',
    // 代码文件
    'js', 'ts', 'jsx', 'tsx', 'vue', 'html', 'htm', 'css', 'scss', 'sass', 'less',
    'php', 'py', 'java', 'c', 'cpp', 'h', 'hpp', 'cs', 'go', 'rs', 'rb', 'pl',
    // 脚本文件
    'sh', 'bat', 'cmd', 'ps1',
    // 数据文件
    'csv', 'tsv', 'sql',
    // 其他文本格式
    'rtf', 'tex', 'latex'
  ];

  return textExtensions.includes(ext.toLowerCase());
}

// 文本预览相关方法
function getPreviewTitle(): string {
  if (previewType.value === 'image') {
    return '图片预览';
  } else if (previewType.value === 'text') {
    return `文本预览 - ${currentFileName.value}`;
  }
  return '文件预览';
}

function formatTextSize(text: string): string {
  const size = new Blob([text]).size;
  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
  return `${(size / 1024 / 1024).toFixed(1)} MB`;
}

function getLineCount(): number {
  if (!previewContent.value) return 0;
  return previewContent.value.split('\n').length;
}

function getTextContentClass(): string {
  const ext = currentFileExtension.value.toLowerCase();
  const codeExtensions = ['js', 'ts', 'jsx', 'tsx', 'vue', 'html', 'css', 'json', 'xml', 'php', 'py', 'java', 'c', 'cpp'];
  return codeExtensions.includes(ext) ? 'code-content' : 'text-content';
}

async function copyTextContent() {
  try {
    await navigator.clipboard.writeText(previewContent.value);
    ElMessage.success('内容已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    ElMessage.error('复制失败');
  }
}

function toggleFullscreen() {
  isFullscreen.value = !isFullscreen.value;
}

async function downloadCurrentFile() {
  if (currentFileName.value) {
    // 复用现有的下载逻辑
    const currentRow = resources.value.find(r => r.filename === currentFileName.value);
    if (currentRow) {
      await download(currentRow);
    }
  }
}

const baseUrl = "/api";
const previewVisible = ref(false);
const previewType = ref("");
const previewContent = ref("");
const imageLoadError = ref(false);
const isFullscreen = ref(false);
const currentFileName = ref("");

// 视频预览相关
const videoPlayer = ref<HTMLVideoElement | null>(null);
const videoLoadError = ref(false);
const videoProgress = ref(0);
const currentVideoTime = ref(0);
const videoPreviewLimit = ref(10); // 10秒预览限制
const currentFileExtension = ref("");

// 图片加载错误处理
function handleImageError() {
  imageLoadError.value = true;
  console.error('图片加载失败:', previewContent.value);
  ElMessage.error('图片加载失败，请检查文件路径');
}

function handleImageLoad() {
  imageLoadError.value = false;
  console.log('图片加载成功:', previewContent.value);
}

function retryImageLoad() {
  imageLoadError.value = false;
  // 强制重新加载图片
  const img = document.querySelector('.el-dialog img') as HTMLImageElement;
  if (img) {
    img.src = img.src + '?t=' + Date.now();
  }
}

// 添加过滤方法
function filterResources() {
  resources.value = filteredResources.value.filter((resource: any) =>
    resource.filename.toLowerCase().includes(searchKeyword.value.toLowerCase())
  );
}

// 获取文件图标
function getFileIcon(filename: string) {
  const ext = filename.split('.').pop()?.toLowerCase() || '';
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm'];
  const documentExts = ['pdf', 'doc', 'docx', 'txt', 'rtf'];
  const archiveExts = ['zip', 'rar', '7z', 'tar', 'gz'];

  if (imageExts.includes(ext)) return Picture;
  if (videoExts.includes(ext)) return VideoPlay;
  if (documentExts.includes(ext)) return DocumentCopy;
  if (archiveExts.includes(ext)) return Folder;
  return Files;
}

// 获取文件图标样式类
function getFileIconClass(filename: string) {
  const ext = filename.split('.').pop()?.toLowerCase() || '';
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm'];
  const documentExts = ['pdf', 'doc', 'docx', 'txt', 'rtf'];
  const archiveExts = ['zip', 'rar', '7z', 'tar', 'gz'];

  if (imageExts.includes(ext)) return 'file-icon-image';
  if (videoExts.includes(ext)) return 'file-icon-video';
  if (documentExts.includes(ext)) return 'file-icon-document';
  if (archiveExts.includes(ext)) return 'file-icon-archive';
  return 'file-icon-default';
}

// 处理行点击
function handleRowClick(row: any) {
  // 可以在这里添加行点击逻辑，比如选中文件等
  console.log('点击了文件:', row.filename);
}

// 视频预览相关方法
function handleVideoLoaded() {
  console.log('视频元数据加载完成');
  videoLoadError.value = false;
  currentVideoTime.value = 0;
  videoProgress.value = 0;
}

function handleVideoTimeUpdate() {
  if (videoPlayer.value) {
    const video = videoPlayer.value;
    currentVideoTime.value = video.currentTime;

    // 计算进度百分比（基于10秒限制）
    videoProgress.value = Math.min((video.currentTime / videoPreviewLimit.value) * 100, 100);

    // 如果播放时间超过10秒，暂停视频并重置到开始
    if (video.currentTime >= videoPreviewLimit.value) {
      video.pause();
      video.currentTime = 0;
      ElMessage.warning('预览时间已达到10秒限制，请下载完整文件观看');
    }
  }
}

function handleVideoError() {
  console.error('视频加载失败');
  videoLoadError.value = true;
}

function retryVideoLoad() {
  videoLoadError.value = false;
  if (videoPlayer.value) {
    videoPlayer.value.load();
  }
}

function formatVideoTime(seconds: number): string {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

// 预览对话框关闭处理
function handlePreviewClose() {
  // 重置视频相关状态
  if (previewType.value === 'video' && videoPlayer.value) {
    const video = videoPlayer.value;
    video.pause();
    video.currentTime = 0;
  }

  // 重置所有预览状态
  videoLoadError.value = false;
  videoProgress.value = 0;
  currentVideoTime.value = 0;
  imageLoadError.value = false;
  currentFileName.value = '';
  previewType.value = '';
  previewContent.value = '';
}

// handlePreviewClose函数已在上面定义，删除重复声明
</script>

<style scoped>
/* 页面容器 */
.resource-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  font-family: var(--font-family-sans);
}

/* 页面头部 */
.page-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-xl) 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin: 0;
}

/* 页面容器 */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

/* 状态区域 */
.status-section {
  margin-bottom: var(--spacing-lg);
}

.status-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal);
}

.status-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.status-icon.user-level {
  background: var(--primary-gradient);
}

.status-icon.download-speed {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}

.status-icon.concurrent {
  background: linear-gradient(135deg, #e6a23c 0%, #f0a020 100%);
}

.status-icon.daily-usage {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
}

.status-content {
  flex: 1;
}

.status-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.status-value {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

/* 工具栏区域 */
.toolbar-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.search-container {
  position: relative;
}

.search-input {
  width: 300px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.resource-stats {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.stats-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 表格区域 */
.table-section {
  margin-bottom: var(--spacing-lg);
}

.table-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

.resource-table {
  width: 100%;
}

.resource-table :deep(.el-table__header) {
  background: var(--bg-tertiary);
}

.resource-table :deep(.el-table__row:hover) {
  background: var(--bg-tertiary);
  cursor: pointer;
}

/* 文件信息 */
.file-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.file-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.file-icon-image {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.file-icon-video {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.file-icon-document {
  background: linear-gradient(135deg, #45b7d1 0%, #96c93d 100%);
}

.file-icon-archive {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.file-icon-default {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  word-break: break-all;
}

.file-type {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.upload-time {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: var(--spacing-xs);
}

.action-btn {
  border-radius: var(--radius-sm);
  font-weight: var(--font-medium);
}

/* 空状态 */
.empty-state {
  padding: var(--spacing-2xl);
  text-align: center;
}

/* 预览相关样式 */
.text-preview-container {
  display: flex;
  flex-direction: column;
  height: 70vh;
}

.text-preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: var(--spacing-md);
}

.preview-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.text-content {
  flex: 1;
  overflow: auto;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-tertiary);
}

.text-content pre {
  margin: 0;
  padding: var(--spacing-md);
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  white-space: pre-wrap;
  word-wrap: break-word;
  color: var(--text-primary);
}

.text-preview-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
  border-top: 1px solid var(--border-light);
  margin-top: var(--spacing-md);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.line-count, .char-count {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
}

/* 视频预览样式 */
.video-preview-container {
  display: flex;
  flex-direction: column;
  height: 70vh;
}

.video-preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: var(--spacing-md);
}

.video-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.video-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.video-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

.preview-video {
  width: 100%;
  max-height: 100%;
  border-radius: var(--radius-md);
}

.video-preview-footer {
  padding: var(--spacing-md) 0;
  border-top: 1px solid var(--border-light);
  margin-top: var(--spacing-md);
}

.video-controls {
  margin-bottom: var(--spacing-md);
}

.video-progress {
  margin-bottom: var(--spacing-xs);
}

.time-info {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.video-tips {
  margin-top: var(--spacing-sm);
}

.error-message {
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--error-color);
  background: rgba(245, 108, 108, 0.1);
  border: 1px solid rgba(245, 108, 108, 0.3);
  border-radius: var(--radius-md);
  margin-top: var(--spacing-lg);
}

.error-details {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  margin: var(--spacing-sm) 0;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: var(--spacing-md);
  }

  .header-content {
    padding: 0 var(--spacing-md);
  }

  .page-title {
    font-size: var(--text-2xl);
  }

  .status-grid {
    grid-template-columns: 1fr;
  }

  .toolbar-section {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .search-input {
    width: 100%;
  }

  .resource-table :deep(.el-table__body-wrapper) {
    overflow-x: auto;
  }

  .file-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .action-buttons {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .page-container {
    padding: var(--spacing-sm);
  }

  .status-item {
    padding: var(--spacing-md);
  }

  .status-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  .file-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }

  .resource-table :deep(.el-table th),
  .resource-table :deep(.el-table td) {
    padding: var(--spacing-xs) !important;
  }

  .action-btn {
    font-size: var(--text-xs);
    padding: var(--spacing-xs);
  }
}
</style>
