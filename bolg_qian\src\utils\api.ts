import request from "./index";

// 用户相关
export const loginApi = (params: { username: string; password: string }) =>
  request.post("/user/login", params);

export const registerApi = (params: {
  username: string;
  email: string;
  password: string;
}) => request.post("/user/register", params);

export const logoutApi = (username: string) =>
  request.post("/user/logout", { username });

export const GetProfileApi = (params: any) =>
  request.post("/user/profile", params);

export const UpdateProfileApi = (formData: FormData) =>
  request.post("/user/updateUser", formData);

// 文章相关
export const ShowArticlesApi = (params: any) =>
  request.post("/articles/pages", params);

export const ShowArticlesByPageApi = (params: any) =>
  request.post("/articles/pagesNumber", params);

export const AddArticleApi = (params: any) =>
  request.post("/articles/add", params);

export const UpdateArticleApi = (params: any) =>
  request.post("/articles/update", params);

export const DeleteArticleApi = (params: any) =>
  request.post("/articles/delete", params);

export const FakeDeleteArticleApi = (params: any) =>
  request.post("/articles/fakeDelete", params);

export const PublishArticleApi = (formData: FormData) =>
  request.post("/articles/add", formData);

export const GetArticleByIdApi = (params: any) =>
  request.post("/articles/detail", params);

export const CommentArticleApi = (params: any) =>
  request.post("/articles/comments", params);

export const SubmitCommentApi = (params: any) =>
  request.post("/articles/addComment", params);

export const ShareArticleApi = (id: number, is_share: number) =>
  request.post("/articles/share", { id, is_share });

export const GetSharedArticlesApi = (params: any) =>
  request.get("/articles/shared", params);

export const RestoreArticleApi = (params: any) =>
  request.post("/articles/restore", params);

export const DeleteRestoredArticleApi = (params: any) =>
  request.post("/articles/deletedByUser", params);

// 日志相关
export const ShowLogsApi = (params: { page: number; limit: number }) =>
  request.get("/logs/showlogs", { params });

// 上传相关
export const UploadImageApi = (formData: FormData) =>
  request.post("/upload/updateAvatar", formData);

export const UploadApi = (formData: FormData, config = {}) =>
  request.post("/upload/uploads", formData, config);

export const UploadChunkApi = (formData: FormData, config = {}) =>
  request.post("/upload/uploads/chunk", formData, config);

export const MergeChunksApi = (data: any) =>
  request.post("/upload/uploads/merge", data);

// 查询已上传分片
export const QueryUploadedChunksApi = (fileHash: string) =>
  request.get("/upload/uploads/chunks", { params: { fileHash } });

// 媒体相关
export const GetMediaApi = (params: { filename: string }) =>
  request.get(`/media/${params.filename}`);

export const GetMediaListApi = () => request.get("/media/list");

export const GetMediaCoverListApi = (params: {
  page: number;
  pageSize: number;
  search?: string;
  category?: string;
  sortBy?: string;
  tab?: string;
  userId?: number;
}) => request.get("/media/listpic", { params });

// 媒体功能相关API
export const likeMediaApi = (params: { userId: number; fileName: string }) =>
  request.post("/media-features/like", params);

export const unlikeMediaApi = (params: { userId: number; fileName: string }) =>
  request.post("/media-features/unlike", params);

export const collectMediaApi = (params: { userId: number; fileName: string }) =>
  request.post("/media-features/collect", params);

export const uncollectMediaApi = (params: { userId: number; fileName: string }) =>
  request.post("/media-features/uncollect", params);

export const savePlayHistoryApi = (params: {
  userId: number;
  fileName: string;
  progress?: number;
  currentTime?: number;
}) => request.post("/media-features/play-history", params);

export const getMediaPlayHistoryApi = (params: { userId: number; page?: number; limit?: number }) =>
  request.get("/media-features/play-history", { params });

export const clearMediaPlayHistoryApi = (userId: number) =>
  request.delete(`/media-features/play-history/${userId}`);

export const getLikedMediaApi = (params: { userId: number; page?: number; limit?: number }) =>
  request.get("/media-features/liked", { params });

// ==================== 视频管理相关API ====================

// 获取视频管理列表
export const getVideoManagementListApi = (params: {
  page?: number;
  pageSize?: number;
  search?: string;
  category?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: string;
}) => request.get("/video-management/list", { params });

// 获取视频详情
export const getVideoDetailApi = (fileName: string) =>
  request.get(`/video-management/detail/${encodeURIComponent(fileName)}`);

// 更新视频信息
export const updateVideoInfoApi = (fileName: string, data: {
  title?: string;
  description?: string;
  category?: string;
  tags?: string[];
  status?: string;
}) => request.put(`/video-management/update/${encodeURIComponent(fileName)}`, data);

// 上架视频
export const onlineVideoApi = (fileName: string) =>
  request.post(`/video-management/online/${encodeURIComponent(fileName)}`);

// 下架视频
export const offlineVideoApi = (fileName: string) =>
  request.post(`/video-management/offline/${encodeURIComponent(fileName)}`);

// 批量上架下架
export const batchVideoStatusApi = (data: {
  fileNames: string[];
  status: 'online' | 'offline';
}) => request.post("/video-management/batch-status", data);

// 删除视频
export const deleteVideoApi = (fileName: string) =>
  request.delete(`/video-management/delete/${encodeURIComponent(fileName)}`);

// 获取分类列表
export const getVideoCategoriesApi = () =>
  request.get("/video-management/categories");

// 添加分类
export const addVideoCategoryApi = (data: {
  name: string;
  description?: string;
  sort_order?: number;
}) => request.post("/video-management/categories", data);

// 获取标签列表
export const getVideoTagsApi = () =>
  request.get("/video-management/tags");

// 添加标签
export const addVideoTagApi = (data: {
  name: string;
  color?: string;
}) => request.post("/video-management/tags", data);

export const getCollectedMediaApi = (params: { userId: number; page?: number; limit?: number }) =>
  request.get("/media-features/collected", { params });

// 墙相关
export const ShowWallApi = (params: any) =>
  request.post("/showwall/list", params);

// 图片stream相关
export const getImageStreamApi = (imagePath: string) =>
  request.get(imagePath, { responseType: "blob" });

export const getImageStreamUrl = (imagePath: string) => {
  const token = localStorage.getItem("token");
  const url = `/api${imagePath}`;
  return token ? `${url}?token=${token}` : url;
};

// 标签相关
export const GetTagsApi = () => request.get("/user/tech-tags");

// 导出SQL相关
export const ExportSqlApi = () =>
  request.get("/daosql/export/download", { responseType: "blob" });

// 加密相关
export const EncryptImagesApi = (params: any) =>
  request.post("/jiami/images", params);

export const EncryptVideosApi = (params: any) =>
  request.post("/jiamivideos/videos", params);

export const EncryptSingleVideoApi = (params: any) =>
  request.post("/jiamivideos/video1", params);

// 获取可加密/解密的文件列表
export const GetEncryptableFilesApi = (params: { type: string }) =>
  request.get("/jiamivideos/files", { params });

// 聊天相关
export const ChatApi = (params: any) =>
  request.post("/bigmodel/chatai", params);

// AI聊天历史相关
export const SaveAiChatHistoryApi = (params: {
  user_message: string;
  bot_response: string;
  model_used?: string;
}) => request.post("/bigmodel/save-history", params);

export const GetAiChatHistoryApi = (params: { page?: number; limit?: number }) =>
  request.get("/bigmodel/history", { params });
export const SearchFriendApi = (q: string) =>
  request.get("/chat/search", { params: { q } });

export const GetFriendsApi = (params: any) =>
  request.post("/chat/friends", params);

// 好友相关
// ...existing code...



// 留言相关
export const GetBugListApi = () => request.post("/bugsend/list");

export const AddBugApi = (params: any) => request.post("/bugsend/add", params);

export const UpdateBugStatusApi = (
  id: number,
  status: number,
  severity: string
) => request.post("/bugsend/updateStatus", { id, status, severity });

export const DeleteBugApi = (id: number) =>
  request.post("/bugsend/delete", { id });

export const DeleteBugsApi = (ids: number[]) =>
  request.post("/bugsend/deleteBatch", { ids });

export const UpdateBugApi = (params: any) =>
  request.post("/bugsend/update", params);
export const GetCreativeListApi = () => request.post("/creative/list");

// 创意相关
export const AddCreativeApi = (params: any) =>
  request.post("/creative/add", params);

export const DeleteCreativeApi = (id: number) =>
  request.post("/creative/delete", { id });
// 音乐相关

// 获取音乐列表
export const getMusicListApi = () => request.get("/music/list");

// 流式播放音乐（返回音频流，前端可用 <audio src=...> 直接播放）
export const streamMusicApi = (filename: string, userId?: number) =>
  request.get(`/music/stream/${filename}`, {
    params: userId ? { userId } : undefined,
    responseType: "blob", // 音频流
  });

// 获取歌词
export const getLyricsApi = (filename: string) =>
  request.get(`/music/lyrics/${filename}`);

// 获取音乐封面
export const getMusicCoverApi = (filename: string) =>
  request.get(`/music/cover/${filename}`, { responseType: "blob" });

// 喜欢音乐
export const likeMusicApi = (userId: number, musicId: string) =>
  request.post("/music/like", { userId, musicId });

// 收藏音乐
export const collectMusicApi = (userId: number, musicId: string) =>
  request.post("/music/collect", { userId, musicId });

// 获取播放历史
export const getPlayHistoryApi = (userId: number) =>
  request.get("/music/play-history", { params: { userId } });

export const GetResourceListApi = () => request.get("/resource/list");

// 资源相关
export const DownloadResourceApi = (filename: string) =>
  request.post(`/resource/`, { filename }, { responseType: "blob" });

export const PreviewResourceApi = (filename: string) =>
  request.get(`/resource/prew/${filename}`);

export const GetResourceListByUserApi = (user_id: number) =>
  request.post("/resourcesdir/list", { user_id });

export const CancelShareOrShareApi = (
  user_id: number,
  file_id: number,
  is_share: number
) =>
  request.post("/resourcesdir/cancelShareOrShare", {
    user_id,
    file_id,
    is_share,
  });

export const DeleteResourceApi = (user_id: number, file_id: number) =>
  request.post("/resourcesdir/delete", { user_id, file_id });

export const PermanentlyDeleteResourceApi = (
  user_id: number,
  file_id: number,
  file_name: string
) =>
  request.post("/resourcesdir/permanentlyDelete", {
    user_id,
    file_id,
    file_name,
  });

// 统计相关
export const dashboardApi = () => request.get("/dashboard/data");

export const getCountApi = (dateRange?: string) => request.get("/dashboard/qushi", {
  params: dateRange ? { dateRange } : undefined
});

// 浏览记录相关API
export const recordViewHistoryApi = (data: {
  article_id: number;
  view_duration?: number;
  scroll_depth?: number;
}) => request.post("/view-history/record", data);

export const getUserViewHistoryApi = (userId: number, params?: {
  page?: number;
  limit?: number;
}) => request.get(`/view-history/user/${userId}`, { params });

export const getUserViewStatsApi = (userId: number) =>
  request.get(`/view-history/user/${userId}/stats`);

export const getArticleViewStatsApi = (articleId: number, params?: {
  period?: string;
}) => request.get(`/view-history/article/${articleId}/stats`, { params });

export const getResourceDownloadCountApi = (
  file_name: string,
  user_id: number,
  file_type: string
) =>
  request.post("/resource/downloadCount", {
    file_name,
    user_id,
    file_type,
  });

// 好友相关

// 获取好友列表 1 *
export const GetFriendListApi = (user_id: number) =>
  request.get("/friend/list", { params: { user_id } });

// 搜索用户 2 *
export const SearchUserApi = (keyword: string) =>
  request.post("/friend/search", { keyword });

// 发送好友申请 3 *
export const ApplyFriendApi = (from_id: number, to_id: number) =>
  request.post("/friend/apply", { from_id, to_id });

// 处理添加好友 同意 拒绝 4
export const HandleAddFriendApi = (request_id: number, accept: boolean) =>
  request.post("/friend/apply/handle", { request_id, accept });

// 获取待处理的好友申请列表 5
export const GetFriendApplyListApi = (user_id: number) =>
  request.get("/friend/apply/list", { params: { user_id } });

// 聊天图片上传 6 *
export const UploadChatImageApi = (formData: FormData) =>
  request.post("/friend/chat_images", formData);

interface ChatMessage {
  id: number;
  from_id: string;
  from_nickname: string;
  from_avatar: string;
  to_id: string | null;
  message: string;
  type: string;
  url: string | null;
  filename: string | null;
  time: number;
}
// 获取聊天记录 7 *
export const GetChatRecordApi = (params: {
  to?: string | number;
  userId: string | number;
}) =>
  request.get<{ code: number; data: ChatMessage[] }>("/friend/history", {
    params,
  });

// 标记离线消息为已读 8 *
export const MarkOfflineMessagesReadApi = (userId: string | number) =>
  request.post("/friend/mark-read", { userId });

// 获取未读消息数量 9 *
export const GetUnreadMessageCountApi = (userId: string | number) =>
  request.get("/friend/unread-count", { params: { userId } });

// 公开文章接口（无需登录）
export const GetPublicArticlesApi = (params: { page: number; limit: number }) =>
  request.post("/articles/public/pages", params);

export const GetPublicArticleDetailApi = (params: { id: number }) =>
  request.post("/articles/public/detail", params);

export const GetPublicArticleCommentsApi = (params: { article_id: number }) =>
  request.post("/articles/public/comments", params);

export const SubmitPublicCommentApi = (params: { 
  username: string; 
  article_id: number; 
  content: string; 
  parent_id?: number 
}) => request.post("/articles/public/addComment", params);

// 密码管理相关接口
export const GetPasswordListApi = () => request.get("/password/list");

export const SetPasswordApi = (params: {
  passwordType: string;
  password: string;
  securityQuestion: string;
  securityAnswer: string;
}) => request.post("/password/create", params);

export const VerifyPasswordApi = (params: {
  passwordType: string;
  password: string;
}) => request.post("/password/verify", params);

export const GetSecurityQuestionApi = (passwordType: string) =>
  request.get(`/password/security-question/${passwordType}`);

export const ResetPasswordBySecurityApi = (params: {
  passwordType: string;
  securityAnswer: string;
  newPassword: string;
}) => request.post("/password/reset-by-security", params);

export const DeletePasswordApi = (passwordType: string) =>
  request.delete(`/password/delete/${passwordType}`);

// 限速下载相关API
export const speedDownloadApi = {
  // 限速下载文件
  download: (filename: string) =>
    request.get(`/speed-download/download/${filename}`, {
      responseType: 'blob'
    }),

  // 获取下载状态
  getStatus: () => request.get('/speed-download/status'),

  // 取消下载
  cancel: (sessionId: string) =>
    request.post(`/speed-download/cancel/${sessionId}`),

  // 获取下载历史
  getHistory: (page: number = 1, limit: number = 20) =>
    request.get('/speed-download/history', {
      params: { page, limit }
    })
};

// 用户等级管理相关API
export const userLevelApi = {
  // 获取用户列表
  getUsers: (params: {
    page?: number;
    limit?: number;
    level?: string;
    search?: string;
  }) => request.get('/user-level/users', { params }),

  // 获取等级统计
  getLevelStats: () => request.get('/user-level/level-stats'),

  // 修改用户等级
  changeLevel: (data: {
    userId: number;
    newLevel: string;
    reason?: string;
  }) => request.post('/user-level/change-level', data),

  // 批量修改用户等级
  batchChangeLevel: (data: {
    userIds: number[];
    newLevel: string;
    reason?: string;
  }) => request.post('/user-level/batch-change-level', data),

  // 获取等级变更日志
  getLevelLogs: (params: {
    page?: number;
    limit?: number;
    userId?: number;
  }) => request.get('/user-level/level-logs', { params })
};

// 系统监控相关API
export const systemMonitorApi = {
  // 获取系统概览
  getOverview: () => request.get('/system-monitor/overview'),

  // 获取数据库统计
  getDatabaseStats: () => request.get('/system-monitor/database-stats'),

  // 获取API统计
  getApiStats: () => request.get('/system-monitor/api-stats'),

  // 获取实时监控数据
  getRealtime: () => request.get('/system-monitor/realtime'),

  // 获取系统日志
  getLogs: (params: {
    level?: string;
    limit?: number;
  }) => request.get('/system-monitor/logs', { params })
};

// WebSocket管理相关API
export const websocketApi = {
  // 启动WebSocket服务器
  start: () => request.post('/websocket/start'),

  // 停止WebSocket服务器
  stop: () => request.post('/websocket/stop'),

  // 获取WebSocket状态
  getStatus: () => request.get('/websocket/status'),

  // 获取在线用户
  getOnlineUsers: () => request.get('/websocket/online-users'),

  // 获取WebSocket统计
  getStats: () => request.get('/websocket/stats'),

  // 健康检查
  health: () => request.get('/websocket/health')
};
