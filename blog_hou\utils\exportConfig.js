const path = require("path");

/**
 * 数据库导出配置
 */
module.exports = {
  // 导出目录配置
  EXPORT: {
    DIR: path.join(__dirname, "../export"),
    SQL_FILENAME: "latest_export.sql",
    ENCODING: "utf8"
  },
  
  // 数据库导出配置
  DATABASE: {
    MAX_RECORDS_PER_TABLE: 2, // 每个表导出的最大记录数
    INCLUDE_DATA: true, // 是否包含数据
    INCLUDE_STRUCTURE: true, // 是否包含表结构
    ADD_DROP_TABLE: true, // 是否添加DROP TABLE语句
    ADD_COMMENTS: true // 是否添加注释
  },
  
  // 文件下载配置
  DOWNLOAD: {
    CONTENT_TYPE: "application/sql",
    ATTACHMENT_FILENAME: "database_export.sql"
  }
}; 