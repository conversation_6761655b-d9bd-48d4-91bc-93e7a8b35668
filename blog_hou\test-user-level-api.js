const axios = require('axios');

// 测试用户等级管理API
async function testUserLevelAPI() {
  const baseURL = 'http://localhost:3000';
  
  // 首先登录获取token
  try {
    console.log("=== 1. 用户登录 ===");
    const loginResponse = await axios.post(`${baseURL}/user/login`, {
      username: 'admin123',
      password: '123456'
    });
    
    console.log("登录状态:", loginResponse.status);
    console.log("登录响应:", loginResponse.data);
    
    const token = loginResponse.data.token;
    console.log("获取到token:", token.substring(0, 50) + "...");
    
    // 设置请求头
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    
    console.log("\n=== 2. 测试等级统计API ===");
    const statsResponse = await axios.get(`${baseURL}/user-level/level-stats`, { headers });
    console.log("等级统计状态:", statsResponse.status);
    console.log("等级统计数据大小:", JSON.stringify(statsResponse.data).length, "字节");
    console.log("等级统计数据:", statsResponse.data);
    
    console.log("\n=== 3. 测试用户列表API ===");
    const usersResponse = await axios.get(`${baseURL}/user-level/users?page=1&limit=5`, { headers });
    console.log("用户列表状态:", usersResponse.status);
    console.log("用户列表数据大小:", JSON.stringify(usersResponse.data).length, "字节");
    console.log("用户列表数据:", JSON.stringify(usersResponse.data, null, 2));
    
    if (usersResponse.data.data && usersResponse.data.data.users) {
      console.log("用户数量:", usersResponse.data.data.users.length);
      console.log("分页信息:", usersResponse.data.data.pagination);
    }
    
  } catch (error) {
    console.error("测试失败:", error.response?.data || error.message);
  }
}

testUserLevelAPI();
