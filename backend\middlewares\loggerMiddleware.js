// 错误日志记录中间件
const logger = require('../plugin/logger');

module.exports = async (ctx, next) => {
  const start = Date.now();
  try {
    await next();
    const ms = Date.now() - start;
    logger.info(`${ctx.method} ${ctx.url} - ${ctx.status} - ${ms}ms - IP: ${ctx.ip}`);
  } catch (err) {
    logger.error(`${ctx.method} ${ctx.url} - ${ctx.status} - ${err.message} - IP: ${ctx.ip}`);
    throw err;
  }
};





