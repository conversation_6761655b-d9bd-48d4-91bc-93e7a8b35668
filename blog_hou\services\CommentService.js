// 评论服务类
const BaseService = require('./BaseService');

class CommentService extends BaseService {
  constructor() {
    super('comments');
  }

  // 创建评论
  async createComment(commentData) {
    try {
      const {
        article_id,
        username,
        comment,
        parent_id = null,
        user_id = null
      } = commentData;

      const data = {
        article_id,
        username,
        comment,
        parent_id,
        user_id,
        status: 1, // 默认审核通过
        created_at: new Date(),
        updated_at: new Date()
      };

      const commentId = await this.create(data);

      this.logger.info('评论创建成功', { commentId, article_id, username });

      return commentId;
    } catch (error) {
      this.logger.error('创建评论失败', { commentData, error: error.message });
      throw error;
    }
  }

  // 获取文章评论
  async getArticleComments(articleId, options = {}) {
    const {
      page = 1,
      limit = 20,
      orderBy = 'created_at ASC'
    } = options;

    try {
      const baseQuery = `
        SELECT c.*, u.nickname, u.avatar
        FROM ${this.tableName} c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.article_id = ? AND c.status = 1
        ORDER BY ${orderBy}
      `;

      const result = await this.paginate(baseQuery, [articleId], page, limit);

      // 构建评论树结构
      const comments = this.buildCommentTree(result.data);

      return {
        ...result,
        data: comments
      };
    } catch (error) {
      this.logger.error('获取文章评论失败', { articleId, options, error: error.message });
      throw error;
    }
  }

  // 构建评论树结构
  buildCommentTree(comments) {
    const commentMap = new Map();
    const rootComments = [];

    // 创建评论映射
    comments.forEach(comment => {
      comment.replies = [];
      comment.created_at = new Date(comment.created_at).toISOString();
      comment.updated_at = new Date(comment.updated_at).toISOString();
      commentMap.set(comment.id, comment);
    });

    // 构建树结构
    comments.forEach(comment => {
      if (comment.parent_id) {
        const parent = commentMap.get(comment.parent_id);
        if (parent) {
          parent.replies.push(comment);
        }
      } else {
        rootComments.push(comment);
      }
    });

    return rootComments;
  }

  // 获取评论详情
  async getCommentById(id) {
    try {
      const sql = `
        SELECT c.*, u.nickname, u.avatar
        FROM ${this.tableName} c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.id = ?
      `;

      const result = await this.query(sql, [id]);

      if (result.length === 0) {
        return null;
      }

      const comment = result[0];
      comment.created_at = new Date(comment.created_at).toISOString();
      comment.updated_at = new Date(comment.updated_at).toISOString();

      return comment;
    } catch (error) {
      this.logger.error('获取评论详情失败', { id, error: error.message });
      throw error;
    }
  }

  // 更新评论
  async updateComment(id, updateData) {
    try {
      const { comment, status } = updateData;

      const data = {
        updated_at: new Date()
      };

      if (comment !== undefined) data.comment = comment;
      if (status !== undefined) data.status = status;

      const success = await this.update(id, data);

      if (success) {
        this.logger.info('评论更新成功', { id, updateData });
      }

      return success;
    } catch (error) {
      this.logger.error('更新评论失败', { id, updateData, error: error.message });
      throw error;
    }
  }

  // 删除评论
  async deleteComment(id) {
    try {
      // 软删除：将状态设为0
      const success = await this.update(id, {
        status: 0,
        updated_at: new Date()
      });

      if (success) {
        this.logger.info('评论删除成功', { id });
      }

      return success;
    } catch (error) {
      this.logger.error('删除评论失败', { id, error: error.message });
      throw error;
    }
  }

  // 获取用户评论
  async getUserComments(userId, options = {}) {
    const {
      page = 1,
      limit = 10,
      orderBy = 'created_at DESC'
    } = options;

    try {
      const baseQuery = `
        SELECT c.*, a.title as article_title
        FROM ${this.tableName} c
        LEFT JOIN articles a ON c.article_id = a.id
        WHERE c.user_id = ? AND c.status = 1
        ORDER BY ${orderBy}
      `;

      const result = await this.paginate(baseQuery, [userId], page, limit);

      // 格式化时间
      result.data = result.data.map(comment => ({
        ...comment,
        created_at: new Date(comment.created_at).toISOString(),
        updated_at: new Date(comment.updated_at).toISOString()
      }));

      return result;
    } catch (error) {
      this.logger.error('获取用户评论失败', { userId, options, error: error.message });
      throw error;
    }
  }

  // 获取评论统计
  async getCommentStats(articleId = null) {
    try {
      let sql, params;

      if (articleId) {
        sql = `
          SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN parent_id IS NULL THEN 1 ELSE 0 END) as root_comments,
            SUM(CASE WHEN parent_id IS NOT NULL THEN 1 ELSE 0 END) as replies
          FROM ${this.tableName}
          WHERE article_id = ?
        `;
        params = [articleId];
      } else {
        sql = `
          SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN parent_id IS NULL THEN 1 ELSE 0 END) as root_comments,
            SUM(CASE WHEN parent_id IS NOT NULL THEN 1 ELSE 0 END) as replies,
            SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_comments_30d
          FROM ${this.tableName}
        `;
        params = [];
      }

      const cacheKey = articleId ? `comment_stats_${articleId}` : 'comment_stats_global';
      const result = await this.queryWithCache(sql, params, cacheKey, 300000);

      return result[0];
    } catch (error) {
      this.logger.error('获取评论统计失败', { articleId, error: error.message });
      throw error;
    }
  }

  // 获取最新评论
  async getLatestComments(limit = 10) {
    try {
      const sql = `
        SELECT c.id, c.comment, c.created_at, c.username, a.title as article_title, a.id as article_id
        FROM ${this.tableName} c
        LEFT JOIN articles a ON c.article_id = a.id
        WHERE c.status = 1
        ORDER BY c.created_at DESC
        LIMIT ?
      `;

      const result = await this.queryWithCache(sql, [limit], `latest_comments_${limit}`, 300000);

      return result.map(comment => ({
        ...comment,
        comment: comment.comment.length > 100 ? comment.comment.substring(0, 100) + '...' : comment.comment,
        created_at: new Date(comment.created_at).toISOString()
      }));
    } catch (error) {
      this.logger.error('获取最新评论失败', { limit, error: error.message });
      throw error;
    }
  }

  // 审核评论
  async moderateComment(id, status, moderatorId) {
    try {
      const success = await this.update(id, {
        status,
        moderated_by: moderatorId,
        moderated_at: new Date(),
        updated_at: new Date()
      });

      if (success) {
        this.logger.info('评论审核成功', { id, status, moderatorId });
      }

      return success;
    } catch (error) {
      this.logger.error('审核评论失败', { id, status, moderatorId, error: error.message });
      throw error;
    }
  }

  // 批量审核评论
  async batchModerateComments(ids, status, moderatorId) {
    try {
      const results = await Promise.all(
        ids.map(id => this.moderateComment(id, status, moderatorId))
      );

      const successCount = results.filter(result => result).length;

      this.logger.info('批量审核评论完成', { 
        total: ids.length, 
        success: successCount, 
        status, 
        moderatorId 
      });

      return successCount;
    } catch (error) {
      this.logger.error('批量审核评论失败', { ids, status, moderatorId, error: error.message });
      throw error;
    }
  }

  // 搜索评论
  async searchComments(keyword, options = {}) {
    const {
      page = 1,
      limit = 10,
      articleId = null,
      status = 1
    } = options;

    try {
      let whereConditions = ['c.comment LIKE ?'];
      let params = [`%${keyword}%`];

      if (articleId) {
        whereConditions.push('c.article_id = ?');
        params.push(articleId);
      }

      if (status !== null) {
        whereConditions.push('c.status = ?');
        params.push(status);
      }

      const whereClause = whereConditions.join(' AND ');

      const baseQuery = `
        SELECT c.*, a.title as article_title, u.nickname, u.avatar
        FROM ${this.tableName} c
        LEFT JOIN articles a ON c.article_id = a.id
        LEFT JOIN users u ON c.user_id = u.id
        WHERE ${whereClause}
        ORDER BY c.created_at DESC
      `;

      const result = await this.paginate(baseQuery, params, page, limit);

      // 格式化时间
      result.data = result.data.map(comment => ({
        ...comment,
        created_at: new Date(comment.created_at).toISOString(),
        updated_at: new Date(comment.updated_at).toISOString()
      }));

      return result;
    } catch (error) {
      this.logger.error('搜索评论失败', { keyword, options, error: error.message });
      throw error;
    }
  }
}

module.exports = CommentService;
