const db = require("../db");

// 检查是否在短时间内重复访问同一文章
async function checkRecentView(article_id, ip_address) {
  const sql = `
    SELECT id FROM view_history 
    WHERE article_id = ? AND ip_address = ? AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
    ORDER BY created_at DESC LIMIT 1
  `;
  const result = await db.query(sql, [article_id, ip_address]);
  return result.length > 0 ? result[0] : null;
}

// 更新现有浏览记录
async function updateViewRecord(recordId, view_duration, scroll_depth) {
  const sql = `
    UPDATE view_history 
    SET view_duration = GREATEST(view_duration, ?), 
        scroll_depth = GREATEST(scroll_depth, ?),
        updated_at = NOW()
    WHERE id = ?
  `;
  const result = await db.query(sql, [view_duration, scroll_depth, recordId]);
  return result;
}

// 插入新的浏览记录
async function insertViewRecord(user_id, article_id, ip_address, user_agent, referrer, session_id, view_duration, scroll_depth, device_type, browser, os) {
  const sql = `
    INSERT INTO view_history 
    (user_id, article_id, ip_address, user_agent, referrer, session_id, 
     view_duration, scroll_depth, device_type, browser, os)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;
  const result = await db.query(sql, [user_id, article_id, ip_address, user_agent, referrer, session_id, view_duration, scroll_depth, device_type, browser, os]);
  return result;
}

// 更新文章浏览量
async function updateArticleViews(article_id) {
  const sql = `UPDATE articles SET views = views + 1 WHERE id = ?`;
  const result = await db.query(sql, [article_id]);
  return result;
}

// 获取用户统计信息
async function getUserStats(userId) {
  const sql = `SELECT * FROM user_view_stats WHERE user_id = ?`;
  const result = await db.query(sql, [userId]);
  return result.length > 0 ? result[0] : null;
}

// 获取用户浏览历史
async function getUserViewHistory(userId, limit, offset) {
  const sql = `
    SELECT vh.*, a.title, a.summary, a.cover_image, a.user_id as author_id,
           u.username as author_name, u.avatar as author_avatar
    FROM view_history vh
    LEFT JOIN articles a ON vh.article_id = a.id
    LEFT JOIN users u ON a.user_id = u.id
    WHERE vh.user_id = ? AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
    ORDER BY vh.created_at DESC
    LIMIT ? OFFSET ?
  `;
  const result = await db.query(sql, [userId, parseInt(limit), offset]);
  return result;
}

// 获取用户浏览历史总数
async function getUserViewHistoryCount(userId) {
  const sql = `
    SELECT COUNT(*) as count FROM view_history vh
    LEFT JOIN articles a ON vh.article_id = a.id
    WHERE vh.user_id = ? AND a.is_deleted = 0
  `;
  const result = await db.query(sql, [userId]);
  return result[0]?.count || 0;
}

// 获取文章浏览统计
async function getArticleViewStats(articleId, dateCondition = '') {
  const sql = `
    SELECT
      COUNT(*) as total_views,
      COUNT(DISTINCT vh.ip_address) as unique_visitors,
      AVG(vh.view_duration) as avg_duration,
      AVG(vh.scroll_depth) as avg_scroll_depth,
      COUNT(CASE WHEN vh.device_type = 'mobile' THEN 1 END) as mobile_views,
      COUNT(CASE WHEN vh.device_type = 'desktop' THEN 1 END) as desktop_views,
      COUNT(CASE WHEN vh.device_type = 'tablet' THEN 1 END) as tablet_views
    FROM view_history vh
    WHERE vh.article_id = ? ${dateCondition}
  `;
  const result = await db.query(sql, [articleId]);
  return result[0] || {};
}

// 获取文章每日浏览量趋势
async function getArticleDailyTrend(articleId, dateCondition = '') {
  const sql = `
    SELECT 
      DATE(vh.created_at) as date,
      COUNT(*) as views,
      COUNT(DISTINCT vh.ip_address) as unique_visitors
    FROM view_history vh
    WHERE vh.article_id = ? ${dateCondition}
    GROUP BY DATE(vh.created_at)
    ORDER BY date DESC
    LIMIT 30
  `;
  const result = await db.query(sql, [articleId]);
  return result;
}

// 更新文章统计
async function updateArticleStats(articleId) {
  const statsQuery = `
    SELECT 
      COUNT(*) as total_views,
      COUNT(DISTINCT ip_address) as unique_visitors,
      AVG(view_duration) as avg_duration,
      MAX(created_at) as last_view_at
    FROM view_history 
    WHERE article_id = ?
  `;
  const stats = await db.query(statsQuery, [articleId]);
  const stat = stats[0];
  
  const updateQuery = `
    INSERT INTO article_view_stats 
    (article_id, total_views, unique_visitors, avg_view_duration, last_view_at)
    VALUES (?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
    total_views = VALUES(total_views),
    unique_visitors = VALUES(unique_visitors),
    avg_view_duration = VALUES(avg_view_duration),
    last_view_at = VALUES(last_view_at)
  `;
  const result = await db.query(updateQuery, [articleId, stat.total_views, stat.unique_visitors, stat.avg_duration, stat.last_view_at]);
  return result;
}

// 更新用户统计
async function updateUserStats(userId) {
  const statsQuery = `
    SELECT 
      COUNT(*) as total_views,
      COUNT(DISTINCT article_id) as unique_articles,
      AVG(view_duration) as avg_duration,
      MAX(created_at) as last_view_at
    FROM view_history 
    WHERE user_id = ?
  `;
  const stats = await db.query(statsQuery, [userId]);
  const stat = stats[0];
  
  const updateQuery = `
    INSERT INTO user_view_stats 
    (user_id, total_views, unique_articles, avg_view_duration, last_view_at)
    VALUES (?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
    total_views = VALUES(total_views),
    unique_articles = VALUES(unique_articles),
    avg_view_duration = VALUES(avg_view_duration),
    last_view_at = VALUES(last_view_at)
  `;
  const result = await db.query(updateQuery, [userId, stat.total_views, stat.unique_articles, stat.avg_duration, stat.last_view_at]);
  return result;
}

// 清理旧的浏览记录
async function cleanupOldRecords(days) {
  const sql = `
    DELETE FROM view_history 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
  `;
  const result = await db.query(sql, [days]);
  return result;
}

module.exports = {
  checkRecentView,
  updateViewRecord,
  insertViewRecord,
  updateArticleViews,
  getUserStats,
  getUserViewHistory,
  getUserViewHistoryCount,
  getArticleViewStats,
  getArticleDailyTrend,
  updateArticleStats,
  updateUserStats,
  cleanupOldRecords,
};
