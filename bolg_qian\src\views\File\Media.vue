<template>
  <div class="media-container">
    <el-card shadow="always">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-bold">🎞️ 媒体播放列表</span>
          <el-button @click="$router.back()">返回</el-button>
        </div>
      </template>

      <!-- 筛选和搜索区域 -->
      <div class="mb-4">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-select v-model="activeTab" placeholder="选择标签" @change="handleTabChange">
              <el-option label="全部" value="all" />
              <el-option label="喜欢" value="liked" />
              <el-option label="收藏" value="collected" />
              <el-option label="播放历史" value="history" />
              <el-option label="最新视频" value="latest" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="selectedCategory" placeholder="选择分类" @change="handleCategoryChange">
              <el-option label="全部分类" value="" />
              <el-option v-for="category in categories" :key="category" :label="category" :value="category" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="sortBy" placeholder="排序方式" @change="handleSortChange">
              <el-option label="默认排序" value="default" />
              <el-option label="最新上传" value="newest" />
              <el-option label="最受欢迎" value="popular" />
              <el-option label="文件大小" value="size" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <!-- 搜索框 -->
            <el-input
              v-model="search"
              placeholder="搜索媒体文件..."
              clearable
              @clear="fetchMediaList"
              @keyup.enter="fetchMediaList"
            >
              <template #append>
                <el-button @click="fetchMediaList" :loading="loading">搜索</el-button>
              </template>
            </el-input>
          </el-col>
        </el-row>
      </div>

      <!-- 内容加载骨架屏 -->
      <el-skeleton :loading="loading" animated>
        <el-row :gutter="20">
          <el-col v-for="file in filteredList" :key="file.file_name" :xs="24" :sm="12" :md="8">
            <el-card class="mb-4" shadow="hover">
              <!-- 媒体信息头部 -->
              <div class="media-header mb-2">
                <p class="truncate text-sm font-medium" :title="file.file_name">
                  {{ file.file_name }}
                </p>
                <div class="media-meta">
                  <span class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</span>
                  <span class="text-xs text-gray-500 ml-2">{{ formatDate(file.upload_time) }}</span>
                </div>
              </div>

              <!-- 封面图 or 播放器 -->
              <div v-if="!playing[file.file_name]" class="media-cover" @click="play(file.file_name)">
                <img :src="getCoverUrl(file.cover)" class="w-full rounded object-cover"
                  style="max-height: 200px; cursor: pointer; min-height: 150px; background-color: #f5f5f5;"
                  :alt="file.file_name" @load="onImageLoad(file.file_name)"
                  @error="onImageError(file.file_name, file.cover)" />
                <div class="play-icon">
                  <el-icon :size="48" color="#fff">
                    <VideoPlay />
                  </el-icon>
                </div>
              </div>

              <video v-else-if="isVideo(file.file_name)" :src="getMediaUrl(file.file_name)" controls autoplay
                preload="metadata" muted controlsList="nodownload" class="w-full rounded"
                @error="(e) => onError(file.file_name, e)"
                @play="handlePlay" @pause="handlePause" @ended="handleEnded" @loadstart="onLoadStart(file.file_name)"
                :ref="el => setVideoRef(file.file_name, el)" />

              <audio v-else :src="getMediaUrl(file.file_name)" controls preload="metadata" class="w-full"
                @error="onError(file.file_name)" @play="handlePlay" />

              <!-- 操作按钮区域 -->
              <div class="media-actions mt-3">
                <div class="flex justify-between items-center">
                  <div class="flex items-center space-x-4">
                    <!-- 播放次数 -->
                    <span class="text-xs text-gray-500">
                      <el-icon>
                        <VideoPlay />
                      </el-icon>
                      {{ file.play_count || 0 }}
                    </span>
                    <!-- 喜欢数 -->
                    <span class="text-xs text-gray-500">
                      <el-icon>
                        <StarFilled />
                      </el-icon>
                      {{ file.like_count || 0 }}
                    </span>
                    <!-- 收藏数 -->
                    <span class="text-xs text-gray-500">
                      <el-icon>
                        <CollectionTag />
                      </el-icon>
                      {{ file.collect_count || 0 }}
                    </span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <!-- 喜欢按钮 -->
                    <el-button :type="file.is_liked ? 'danger' : 'default'" :icon="file.is_liked ? StarFilled : Star"
                      size="small" :loading="likeLoading[file.file_name]" @click="toggleLike(file.file_name)">
                      {{ file.is_liked ? '已喜欢' : '喜欢' }}
                    </el-button>
                    <!-- 收藏按钮 -->
                    <el-button :type="file.is_collected ? 'warning' : 'default'"
                      :icon="file.is_collected ? CollectionTag : Collection" size="small"
                      :loading="collectLoading[file.file_name]" @click="toggleCollect(file.file_name)">
                      {{ file.is_collected ? '已收藏' : '收藏' }}
                    </el-button>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-skeleton>

      <!-- 分页组件 -->
      <el-pagination v-model:currentPage="page" :page-size="pageSize" :total="total" layout="prev, pager, next"
        class="mt-4" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import {
  GetMediaCoverListApi,
  likeMediaApi,
  unlikeMediaApi,
  collectMediaApi,
  uncollectMediaApi,
  savePlayHistoryApi,
  clearMediaPlayHistoryApi
} from '../../utils/api'
import { ElMessage } from 'element-plus'
import { VideoPlay, Star, StarFilled, Collection, CollectionTag } from '@element-plus/icons-vue'
// import SearchBox from '@/common/SearchBox.vue'
// import { videoMemoryManager } from '@/utils/videoMemoryManager'
// import { testVideoUrl, logVideoUrlInfo } from '@/utils/videoUrlTest'

const mediaList = ref<any[]>([])
const loading = ref(true)
const search = ref('')
const currentMedia = ref<HTMLMediaElement | null>(null)
const playing = ref<Record<string, boolean>>({})
const page = ref(1)
const pageSize = ref(12)
const total = ref(0)

// 新增的状态变量
const activeTab = ref('all')
const selectedCategory = ref('')
const sortBy = ref('default')
const categories = ref(['电影', '电视剧', '动漫', '纪录片', '音乐', '其他'])
// 从JWT token中获取userId
const getUserIdFromToken = () => {
  try {
    const token = localStorage.getItem('token')
    if (!token) return 0

    const payload = JSON.parse(atob(token.split('.')[1]))
    return payload.userId || 0
  } catch (error) {
    console.error('解析token失败:', error)
    return 0
  }
}

const userId = ref(getUserIdFromToken())
const likeLoading = ref<Record<string, boolean>>({})
const collectLoading = ref<Record<string, boolean>>({})
const playHistory = ref<any[]>([])

// 调试信息
console.log('当前用户ID:', userId.value)

// 拉取媒体列表
const fetchMediaList = async () => {
  loading.value = true
  try {
    console.log('🔄 开始获取媒体列表，参数:', {
      page: page.value,
      pageSize: pageSize.value,
      search: search.value.trim(),
      category: selectedCategory.value,
      sortBy: sortBy.value,
      tab: activeTab.value,
      userId: userId.value
    })

    const res = await GetMediaCoverListApi({
      page: page.value,
      pageSize: pageSize.value,
      search: search.value.trim(),
      category: selectedCategory.value,
      sortBy: sortBy.value,
      tab: activeTab.value,
      userId: userId.value
    })

    console.log('📥 获取媒体列表响应:', res)
    console.log('📥 响应类型:', typeof res)
    console.log('📥 响应结构:', Object.keys(res || {}))

    if (res && (res as any).code === 200) {
      mediaList.value = (res as any).data?.media_list || []
      total.value = (res as any).data?.total || 0
      console.log('✅ 媒体列表设置成功:', {
        count: mediaList.value.length,
        total: total.value,
        firstItem: mediaList.value[0]
      })
    } else {
      console.warn('⚠️ API响应异常:', res)
      mediaList.value = []
      total.value = 0
      ElMessage.warning(`API响应异常: ${(res as any)?.message || '未知错误'}`)
    }
  } catch (err) {
    console.error('❌ 获取媒体列表失败:', err)
    ElMessage.error('媒体文件获取失败')
    mediaList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const getMediaUrl = (filename: string) => {
  // 使用完整的API路径，因为这是后端的API路由，不是静态资源
  return `/api/media/videos/${encodeURIComponent(filename.trim())}`
}

// 工具函数
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (timestamp: number) => {
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDuration = (seconds: number) => {
  if (!seconds) return '00:00'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const getCoverUrl = (coverUrl: string) => {
  // 通过前端代理/api拼接图片路径，兼容相对路径和文件名
  if (!coverUrl) return ''
  // 如果已经是 http(s) 开头，直接返回
  if (/^https?:\/\//i.test(coverUrl)) return coverUrl
  // 否则拼接代理前缀
  return `/api${coverUrl.startsWith('/') ? coverUrl : '/' + coverUrl}`
}

// filteredList 直接用 mediaList，不再前端过滤
const filteredList = computed(() => mediaList.value)

const isVideo = (filename: string) => /\.(mp4|webm|mov)$/i.test(filename)

const handlePlay = (e: Event) => {
  const target = e.target as HTMLMediaElement
  // const filename = target._memoryManagerId || ''

  // 暂停其他视频
  // if (filename) {
  //   videoMemoryManager.pauseOtherVideos(filename)
  // }

  if (currentMedia.value && currentMedia.value !== target) {
    currentMedia.value.pause()
  }
  currentMedia.value = target
}

const handlePause = (e: Event) => {
  const target = e.target as HTMLMediaElement
  console.log('视频暂停:', target.src)
}

const handleEnded = (e: Event) => {
  const target = e.target as HTMLMediaElement
  console.log('视频播放结束:', target.src)
  // 清理当前播放状态
  const filename = Object.keys(playing.value).find(key => playing.value[key])
  if (filename) {
    playing.value[filename] = false
  }
}

const play = async (filename: string) => {
  const videoUrl = getMediaUrl(filename)
  console.log(`🎯 点击播放视频：${filename}`, {
    filename,
    videoUrl,
    timestamp: new Date().toISOString()
  })

  // 测试视频URL是否可访问
  try {
    const response = await fetch(videoUrl, { method: 'HEAD' })
    console.log(`📊 视频URL测试结果:`, {
      url: videoUrl,
      status: response.status,
      statusText: response.statusText,
      contentType: response.headers.get('content-type')
    })

    if (!response.ok) {
      ElMessage.error(`视频无法访问: ${response.status} ${response.statusText}`)
      return
    }
  } catch (error) {
    console.error(`❌ 视频URL测试失败:`, error)
    ElMessage.error(`视频连接测试失败: ${error.message}`)
    return
  }

  playing.value = { [filename]: true }
  // 保存播放历史
  savePlayHistory(filename)
}

const onError = (filename: string, event?: Event) => {
  const target = event?.target as HTMLMediaElement
  const errorDetails = {
    filename,
    src: target?.src,
    error: target?.error,
    networkState: target?.networkState,
    readyState: target?.readyState
  }
  console.error(`媒体加载失败：`, errorDetails)

  // 显示用户友好的错误信息
  ElMessage.error(`视频 ${filename} 加载失败，请检查网络连接`)
}

const onImageLoad = (filename: string) => {
  console.log(`✅ 封面图片加载成功：${filename}`)
}

const onImageError = (filename: string, originalCoverUrl: string) => {
  const processedUrl = getCoverUrl(originalCoverUrl)
  console.error(`❌ 封面图片加载失败：${filename}`, {
    原始URL: originalCoverUrl,
    处理后URL: processedUrl,
    filename
  })
}

const onLoadStart = (filename: string) => {
  const videoUrl = getMediaUrl(filename)
  console.log(`🎬 开始加载视频：${filename}`, {
    filename,
    videoUrl,
    timestamp: new Date().toISOString()
  })
}

// 新增的处理函数
const handleTabChange = () => {
  page.value = 1
  fetchMediaList()
}

const handleCategoryChange = () => {
  page.value = 1
  fetchMediaList()
}

const handleSortChange = () => {
  page.value = 1
  fetchMediaList()
}

// 切换喜欢
const toggleLike = async (filename: string) => {
  console.log('点击喜欢按钮:', { filename, userId: userId.value })

  // 检查userId是否有效
  if (!userId.value || userId.value === 0) {
    ElMessage.error('请先登录')
    return
  }

  likeLoading.value[filename] = true
  try {
    const file = mediaList.value.find(f => f.file_name === filename)
    if (!file) {
      console.error('找不到文件:', filename)
      return
    }

    console.log('当前文件状态:', {
      filename,
      is_liked: file.is_liked,
      like_count: file.like_count
    })

    if (file.is_liked) {
      console.log('取消喜欢:', { userId: userId.value, fileName: filename })
      await unlikeMediaApi({ userId: userId.value, fileName: filename })
      file.is_liked = false
      file.like_count = Math.max(0, (file.like_count || 0) - 1)
      ElMessage.success('已取消喜欢')

      // 如果当前在"喜欢"标签页，重新获取数据
      if (activeTab.value === 'liked') {
        fetchMediaList()
      }
    } else {
      console.log('添加喜欢:', { userId: userId.value, fileName: filename })
      await likeMediaApi({ userId: userId.value, fileName: filename })
      file.is_liked = true
      file.like_count = (file.like_count || 0) + 1
      ElMessage.success('已添加到喜欢')
    }
  } catch (error) {
    console.error('切换喜欢状态失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    likeLoading.value[filename] = false
  }
}

// 切换收藏
const toggleCollect = async (filename: string) => {
  console.log('点击收藏按钮:', { filename, userId: userId.value })

  // 检查userId是否有效
  if (!userId.value || userId.value === 0) {
    ElMessage.error('请先登录')
    return
  }

  collectLoading.value[filename] = true
  try {
    const file = mediaList.value.find(f => f.file_name === filename)
    if (!file) {
      console.error('找不到文件:', filename)
      return
    }

    console.log('当前文件状态:', {
      filename,
      is_collected: file.is_collected,
      collect_count: file.collect_count
    })

    if (file.is_collected) {
      console.log('取消收藏:', { userId: userId.value, fileName: filename })
      await uncollectMediaApi({ userId: userId.value, fileName: filename })
      file.is_collected = false
      file.collect_count = Math.max(0, (file.collect_count || 0) - 1)
      ElMessage.success('已取消收藏')

      // 如果当前在"收藏"标签页，重新获取数据
      if (activeTab.value === 'collected') {
        fetchMediaList()
      }
    } else {
      console.log('添加收藏:', { userId: userId.value, fileName: filename })
      await collectMediaApi({ userId: userId.value, fileName: filename })
      file.is_collected = true
      file.collect_count = (file.collect_count || 0) + 1
      ElMessage.success('已添加到收藏')
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    collectLoading.value[filename] = false
  }
}

// 保存播放历史
const savePlayHistory = async (filename: string, progress = 0, currentTime = 0) => {
  try {
    await savePlayHistoryApi({
      userId: userId.value,
      fileName: filename,
      progress,
      currentTime
    })
  } catch (error) {
    console.error('保存播放历史失败:', error)
  }
}

// 清空播放历史
const clearHistory = async () => {
  try {
    await clearMediaPlayHistoryApi(userId.value)
    playHistory.value = []
    ElMessage.success('播放历史已清空')
  } catch (error) {
    console.error('清空播放历史失败:', error)
    ElMessage.error('清空失败，请重试')
  }
}



onMounted(fetchMediaList)

// 只监听 search，重置 page 并拉取
watch(search, () => {
  page.value = 1
  fetchMediaList()
})

// 只监听 page/pageSize，直接拉取
watch([page, pageSize], fetchMediaList)

const videoRefs = ref<Record<string, HTMLVideoElement | null>>({})
const observers = ref<Record<string, IntersectionObserver>>({})

// 修改 setVideoRef，接收 filename 和 el
const setVideoRef = (filename: string, el: any) => {
  // 清理旧的引用
  if (videoRefs.value[filename] && videoRefs.value[filename] !== el) {
    // videoMemoryManager.unregisterVideo(filename)
    cleanupVideoElement(videoRefs.value[filename])
  }

  videoRefs.value[filename] = el
  if (el) {
    // 注册到内存管理器
    // videoMemoryManager.registerVideo(filename, el)

    if (observers.value[filename]) observers.value[filename].disconnect()
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (!entry.isIntersecting && !el.paused) {
            el.pause()
            // 释放视频内存
            el.currentTime = 0
          }
          // 重新进入视口且之前是播放状态，则自动播放
          if (entry.isIntersecting && el.paused && playing.value[filename]) {
            el.play().catch(() => { }) // 防止自动播放被浏览器拦截报错
          }
        })
      },
      { threshold: 0.1, rootMargin: '50px' } // 增加阈值和边距，减少频繁触发
    )
    observer.observe(el)
    observers.value[filename] = observer
  } else {
    // 从内存管理器注销
    // videoMemoryManager.unregisterVideo(filename)

    if (observers.value[filename]) {
      observers.value[filename].disconnect()
      delete observers.value[filename]
    }
  }
}

// 清理视频元素
const cleanupVideoElement = (videoEl: HTMLVideoElement) => {
  if (videoEl) {
    videoEl.pause()
    videoEl.currentTime = 0
    videoEl.src = ''
    videoEl.load() // 重置视频元素
  }
}

// 页面卸载时清理所有 observer
onUnmounted(() => {
  // 清理所有观察器
  Object.values(observers.value).forEach((observer) => observer.disconnect())
  observers.value = {}

  // 清理所有视频元素
  Object.values(videoRefs.value).forEach(videoEl => {
    if (videoEl) {
      cleanupVideoElement(videoEl)
    }
  })
  videoRefs.value = {}

  // 清理播放状态
  playing.value = {}
  currentMedia.value = null

  console.log('Media组件已清理完成')
})
</script>

<style scoped>
.media-container {
  max-width: 1200px;
  margin: 50px auto;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.media-cover {
  position: relative;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}
</style>
