import { createRequire } from 'module';
const require = createRequire(import.meta.url);

const db = require('../../blog_hou/utils/db');

async function createFavoriteForCurrentUser() {
  try {
    console.log('🎯 为当前用户(admin123, ID: 19)创建收藏数据...\n');

    const userId = 19; // admin123

    // 1. 检查用户是否已有收藏记录
    const existingFavorites = await db.query('SELECT * FROM article_favorites WHERE user_id = ?', [userId]);
    console.log(`用户 ${userId} 现有收藏记录: ${existingFavorites.length} 条`);

    if (existingFavorites.length > 0) {
      console.log('✅ 用户已有收藏记录:');
      existingFavorites.forEach(fav => {
        console.log(`  - 收藏ID: ${fav.id}, 文章ID: ${fav.article_id}, 时间: ${fav.created_at}`);
      });
      return;
    }

    // 2. 获取前5篇文章
    const articles = await db.query('SELECT id, title FROM articles WHERE is_deleted = 0 ORDER BY id LIMIT 5');
    console.log(`找到 ${articles.length} 篇文章可用于创建收藏记录`);

    if (articles.length === 0) {
      console.log('❌ 没有找到可用的文章');
      return;
    }

    // 3. 为用户创建收藏记录
    console.log('\n开始创建收藏记录...');
    for (const article of articles) {
      await db.query(`
        INSERT INTO article_favorites (user_id, article_id, ip_address, user_agent, session_id, created_at)
        VALUES (?, ?, '127.0.0.1', 'Test Browser', 'test_session_admin', NOW())
      `, [userId, article.id]);
      
      console.log(`✅ 创建收藏记录: 用户 ${userId} -> 文章 ${article.id} (${article.title})`);
    }

    // 4. 验证创建结果
    console.log('\n📊 验证收藏数据:');
    const finalFavorites = await db.query(`
      SELECT af.*, a.title, a.summary, a.cover_image, a.user_id as author_id,
             u.username as author_name, u.avatar as author_avatar
      FROM article_favorites af
      LEFT JOIN articles a ON af.article_id = a.id
      LEFT JOIN users u ON a.user_id = u.id
      WHERE af.user_id = ? AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
      ORDER BY af.created_at DESC
    `, [userId]);

    console.log(`✅ 用户 ${userId} 的收藏记录数量: ${finalFavorites.length}`);
    finalFavorites.forEach(fav => {
      console.log(`  - 收藏ID: ${fav.id}, 文章: ${fav.title}, 作者: ${fav.author_name}, 时间: ${fav.created_at}`);
    });

    console.log('\n🎉 收藏数据创建完成！现在可以测试收藏历史功能了。');

  } catch (error) {
    console.error('❌ 操作失败:', error.message);
    console.error(error);
  }
}

createFavoriteForCurrentUser();
