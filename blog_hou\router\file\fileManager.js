const Router = require("koa-router");
const fs = require("fs");
const path = require("path");
const { handleResponse } = require("../../middlewares/responseHandler");
// JWT认证由全局中间件处理
const logger = require("../../plugin/logger");
const db = require("../../utils/db");

const fileManager = new Router();

// 基础路径配置
const BASE_PATHS = {
  images: path.join(__dirname, "../../uploads/images"),
  documents: path.join(__dirname, "../../uploads/documents"),
  videos: path.join(__dirname, "../../uploads/videos"),
  archives: path.join(__dirname, "../../uploads/archives"),
  media: path.join(__dirname, "../../public/media"),
  avatars: path.join(__dirname, "../../uploads/avatars"),
  articles: path.join(__dirname, "../../uploads/articles"),
  chat_images: path.join(__dirname, "../../uploads/chat_images"),
  resource: path.join(__dirname, "../../uploads/resource")
};

// 获取文件夹结构
fileManager.get("/structure", async (ctx) => {
  try {
    const { category = 'all' } = ctx.query;
    
    const structure = {};
    
    // 获取指定分类或所有分类的文件夹结构
    const categoriesToScan = category === 'all' ? Object.keys(BASE_PATHS) : [category];
    
    for (const cat of categoriesToScan) {
      if (BASE_PATHS[cat] && fs.existsSync(BASE_PATHS[cat])) {
        structure[cat] = await scanDirectory(BASE_PATHS[cat], cat);
      }
    }
    
    logger.info(`获取文件夹结构 - 分类: ${category} - IP: ${ctx.ip}`);
    return handleResponse(ctx, 200, { data: structure });
    
  } catch (error) {
    logger.error("获取文件夹结构失败:", error);
    return handleResponse(ctx, 500, { error: "获取文件夹结构失败" });
  }
});

// 创建文件夹
fileManager.post("/create-folder", async (ctx) => {
  try {
    const { category, folderPath, folderName } = ctx.request.body;
    
    if (!category || !folderName) {
      return handleResponse(ctx, 400, { error: "缺少必要参数" });
    }
    
    if (!BASE_PATHS[category]) {
      return handleResponse(ctx, 400, { error: "无效的分类" });
    }
    
    // 构建完整路径
    const basePath = BASE_PATHS[category];
    const targetPath = folderPath 
      ? path.join(basePath, folderPath, folderName)
      : path.join(basePath, folderName);
    
    // 检查文件夹是否已存在
    if (fs.existsSync(targetPath)) {
      return handleResponse(ctx, 400, { error: "文件夹已存在" });
    }
    
    // 验证路径安全性
    if (!targetPath.startsWith(basePath)) {
      return handleResponse(ctx, 400, { error: "无效的路径" });
    }
    
    // 创建文件夹
    fs.mkdirSync(targetPath, { recursive: true });
    
    logger.info(`创建文件夹成功 - 路径: ${targetPath} - 用户: ${ctx.state.user?.username} - IP: ${ctx.ip}`);
    return handleResponse(ctx, 200, { message: "文件夹创建成功", path: targetPath });
    
  } catch (error) {
    logger.error("创建文件夹失败:", error);
    return handleResponse(ctx, 500, { error: "创建文件夹失败" });
  }
});

// 移动文件
fileManager.post("/move-file", async (ctx) => {
  try {
    const { fileName, fromCategory, fromPath, toCategory, toPath } = ctx.request.body;
    
    if (!fileName || !fromCategory || !toCategory) {
      return handleResponse(ctx, 400, { error: "缺少必要参数" });
    }
    
    if (!BASE_PATHS[fromCategory] || !BASE_PATHS[toCategory]) {
      return handleResponse(ctx, 400, { error: "无效的分类" });
    }
    
    // 构建源路径和目标路径
    const fromBasePath = BASE_PATHS[fromCategory];
    const toBasePath = BASE_PATHS[toCategory];
    
    const sourcePath = fromPath 
      ? path.join(fromBasePath, fromPath, fileName)
      : path.join(fromBasePath, fileName);
      
    const targetPath = toPath 
      ? path.join(toBasePath, toPath, fileName)
      : path.join(toBasePath, fileName);
    
    // 验证路径安全性
    if (!sourcePath.startsWith(fromBasePath) || !targetPath.startsWith(toBasePath)) {
      return handleResponse(ctx, 400, { error: "无效的路径" });
    }
    
    // 检查源文件是否存在
    if (!fs.existsSync(sourcePath)) {
      return handleResponse(ctx, 404, { error: "源文件不存在" });
    }
    
    // 检查目标文件是否已存在
    if (fs.existsSync(targetPath)) {
      return handleResponse(ctx, 400, { error: "目标位置已存在同名文件" });
    }
    
    // 确保目标目录存在
    const targetDir = path.dirname(targetPath);
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }
    
    // 移动文件
    fs.renameSync(sourcePath, targetPath);
    
    // 更新数据库中的文件路径
    await updateFilePathInDatabase(fileName, fromCategory, toCategory, fromPath, toPath);
    
    logger.info(`移动文件成功 - ${sourcePath} -> ${targetPath} - 用户: ${ctx.state.user?.username} - IP: ${ctx.ip}`);
    return handleResponse(ctx, 200, { message: "文件移动成功" });
    
  } catch (error) {
    logger.error("移动文件失败:", error);
    return handleResponse(ctx, 500, { error: "移动文件失败" });
  }
});

// 重命名文件/文件夹
fileManager.post("/rename", async (ctx) => {
  try {
    const { category, oldPath, newName, type } = ctx.request.body;
    
    if (!category || !oldPath || !newName || !type) {
      return handleResponse(ctx, 400, { error: "缺少必要参数" });
    }
    
    if (!BASE_PATHS[category]) {
      return handleResponse(ctx, 400, { error: "无效的分类" });
    }
    
    const basePath = BASE_PATHS[category];
    const fullOldPath = path.join(basePath, oldPath);
    const newPath = path.join(path.dirname(fullOldPath), newName);
    
    // 验证路径安全性
    if (!fullOldPath.startsWith(basePath) || !newPath.startsWith(basePath)) {
      return handleResponse(ctx, 400, { error: "无效的路径" });
    }
    
    // 检查源是否存在
    if (!fs.existsSync(fullOldPath)) {
      return handleResponse(ctx, 404, { error: `${type === 'file' ? '文件' : '文件夹'}不存在` });
    }
    
    // 检查新名称是否已存在
    if (fs.existsSync(newPath)) {
      return handleResponse(ctx, 400, { error: "新名称已存在" });
    }
    
    // 重命名
    fs.renameSync(fullOldPath, newPath);
    
    // 如果是文件，更新数据库
    if (type === 'file') {
      await updateFileNameInDatabase(path.basename(oldPath), newName);
    }
    
    logger.info(`重命名成功 - ${fullOldPath} -> ${newPath} - 用户: ${ctx.state.user?.username} - IP: ${ctx.ip}`);
    return handleResponse(ctx, 200, { message: "重命名成功" });
    
  } catch (error) {
    logger.error("重命名失败:", error);
    return handleResponse(ctx, 500, { error: "重命名失败" });
  }
});

// 删除文件/文件夹
fileManager.delete("/delete", async (ctx) => {
  try {
    const { category, targetPath, type } = ctx.request.body;
    
    if (!category || !targetPath || !type) {
      return handleResponse(ctx, 400, { error: "缺少必要参数" });
    }
    
    if (!BASE_PATHS[category]) {
      return handleResponse(ctx, 400, { error: "无效的分类" });
    }
    
    const basePath = BASE_PATHS[category];
    const fullPath = path.join(basePath, targetPath);
    
    // 验证路径安全性
    if (!fullPath.startsWith(basePath)) {
      return handleResponse(ctx, 400, { error: "无效的路径" });
    }
    
    // 检查是否存在
    if (!fs.existsSync(fullPath)) {
      return handleResponse(ctx, 404, { error: `${type === 'file' ? '文件' : '文件夹'}不存在` });
    }
    
    // 删除
    if (type === 'file') {
      fs.unlinkSync(fullPath);
      // 更新数据库
      await markFileAsDeletedInDatabase(path.basename(targetPath));
    } else {
      fs.rmSync(fullPath, { recursive: true, force: true });
    }
    
    logger.info(`删除成功 - ${fullPath} - 用户: ${ctx.state.user?.username} - IP: ${ctx.ip}`);
    return handleResponse(ctx, 200, { message: "删除成功" });
    
  } catch (error) {
    logger.error("删除失败:", error);
    return handleResponse(ctx, 500, { error: "删除失败" });
  }
});

// 辅助函数：扫描目录结构
async function scanDirectory(dirPath, category, relativePath = '') {
  const result = {
    name: path.basename(dirPath),
    type: 'folder',
    path: relativePath,
    children: []
  };
  
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const itemRelativePath = relativePath ? `${relativePath}/${item}` : item;
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        // 递归扫描子文件夹
        const subFolder = await scanDirectory(itemPath, category, itemRelativePath);
        result.children.push(subFolder);
      } else {
        // 文件信息
        result.children.push({
          name: item,
          type: 'file',
          path: itemRelativePath,
          size: stat.size,
          mtime: stat.mtime,
          extension: path.extname(item).toLowerCase()
        });
      }
    }
    
    // 排序：文件夹在前，文件在后
    result.children.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'folder' ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });
    
  } catch (error) {
    logger.error(`扫描目录失败: ${dirPath}`, error);
  }
  
  return result;
}

// 辅助函数：更新数据库中的文件路径
async function updateFilePathInDatabase(fileName, fromCategory, toCategory, fromPath, toPath) {
  try {
    // 构建新的URL路径
    const newUrlPath = toPath 
      ? `http://192.168.31.222:3000/${toCategory}/${toPath}/${fileName}`
      : `http://192.168.31.222:3000/${toCategory}/${fileName}`;
    
    await db.query(
      "UPDATE files SET file_path = ? WHERE file_name = ?",
      [newUrlPath, fileName]
    );
    
    logger.info(`更新数据库文件路径: ${fileName} -> ${newUrlPath}`);
  } catch (error) {
    logger.error("更新数据库文件路径失败:", error);
  }
}

// 辅助函数：更新数据库中的文件名
async function updateFileNameInDatabase(oldName, newName) {
  try {
    await db.query(
      "UPDATE files SET file_name = ? WHERE file_name = ?",
      [newName, oldName]
    );
    
    logger.info(`更新数据库文件名: ${oldName} -> ${newName}`);
  } catch (error) {
    logger.error("更新数据库文件名失败:", error);
  }
}

// 辅助函数：标记文件为已删除
async function markFileAsDeletedInDatabase(fileName) {
  try {
    await db.query(
      "UPDATE files SET is_deleted = 1 WHERE file_name = ?",
      [fileName]
    );
    
    logger.info(`标记文件为已删除: ${fileName}`);
  } catch (error) {
    logger.error("标记文件为已删除失败:", error);
  }
}

module.exports = fileManager;
