const { parentPort, workerData } = require("worker_threads");
const fs = require("fs");
const path = require("path");
const crypto = require("crypto");

const { file, baseDir, action } = workerData;

const algorithm = "aes-256-cbc";
const password = "mysecret";
const key = crypto.createHash("sha256").update(password).digest();

const inputPath = path.join(baseDir, file);
const outputFileName = action === "encrypt" ? file + ".enc" : file.replace(/\.enc$/, "");
const outputPath = path.join(baseDir, outputFileName);

async function encryptFile() {
  return new Promise((resolve, reject) => {
    const iv = crypto.randomBytes(16);
    const readStream = fs.createReadStream(inputPath);
    const cipher = crypto.createCipheriv(algorithm, key, iv);
    const writeStream = fs.createWriteStream(outputPath);

    writeStream.write(iv);

    let totalSize = fs.statSync(inputPath).size;
    let processedSize = 0;

    readStream.on("data", (chunk) => {
      processedSize += chunk.length;
      parentPort.postMessage({
        status: "progress",
        data: {
          current: processedSize,
          total: totalSize,
          filename: file,
          status: "加密中",
        },
      });
    });

    writeStream.on("finish", () => {
      fs.unlinkSync(inputPath);
      resolve();
    });

    readStream.pipe(cipher).pipe(writeStream);
    readStream.on("error", reject);
    writeStream.on("error", reject);
  });
}

async function decryptFile() {
  return new Promise((resolve, reject) => {
    const inputBuffer = fs.readFileSync(inputPath);
    const iv = inputBuffer.slice(0, 16);
    const encryptedData = inputBuffer.slice(16);

    let decipher = crypto.createDecipheriv(algorithm, key, iv);
    let decryptedChunks = [];
    let offset = 0;
    let totalSize = encryptedData.length;

    while (offset < totalSize) {
      const chunkSize = Math.min(64 * 1024, totalSize - offset);
      const chunk = encryptedData.slice(offset, offset + chunkSize);
      decryptedChunks.push(decipher.update(chunk));

      offset += chunkSize;

      parentPort.postMessage({
        status: "progress",
        data: {
          current: offset,
          total: totalSize,
          filename: file,
          status: "解密中",
        },
      });
    }
    decryptedChunks.push(decipher.final());
    const decrypted = Buffer.concat(decryptedChunks);

    fs.writeFileSync(outputPath, decrypted);
    fs.unlinkSync(inputPath);
    resolve();
  });
}

(async () => {
  try {
    if (action === "encrypt") {
      await encryptFile();
    } else if (action === "decrypt") {
      await decryptFile();
    } else {
      throw new Error("未知操作类型");
    }
    parentPort.postMessage({ status: "done", file });
  } catch (error) {
    parentPort.postMessage({ status: "error", error: error.message });
  }
})();
