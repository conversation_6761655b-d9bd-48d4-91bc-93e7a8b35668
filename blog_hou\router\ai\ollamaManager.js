const Router = require("koa-router");
const ollamaManager = require("../../utils/ollamaManager");
const { handleResponse } = require("../../middlewares/responseHandler");

const router = new Router();

// 自动启动Ollama服务
router.post("/auto-start", async (ctx) => {
  try {
    console.log('📱 收到前端自动启动Ollama请求');
    
    // 检查当前状态
    const status = await ollamaManager.getStatus();
    
    if (status.running) {
      console.log('✅ Ollama已在运行，无需启动');
      return handleResponse(ctx, 200, {
        message: "Ollama服务已在运行",
        data: status
      });
    }

    if (status.starting) {
      console.log('⏳ Ollama正在启动中');
      return handleResponse(ctx, 200, {
        message: "Ollama正在启动中，请稍候",
        data: status
      });
    }

    if (!status.installed) {
      console.log('❌ Ollama未安装');
      return handleResponse(ctx, 400, {
        message: "Ollama未安装，请先安装Ollama",
        data: { installUrl: "https://ollama.ai" }
      });
    }

    // 启动Ollama
    console.log('🚀 开始启动Ollama服务...');
    const result = await ollamaManager.startOllama();
    
    if (result.success) {
      const newStatus = await ollamaManager.getStatus();
      return handleResponse(ctx, 200, {
        message: result.message,
        data: {
          ...newStatus,
          startupTime: result.startupTime
        }
      });
    } else {
      return handleResponse(ctx, 500, {
        message: result.message,
        data: status
      });
    }

  } catch (error) {
    console.error('❌ 自动启动Ollama失败:', error);
    return handleResponse(ctx, 500, {
      message: "启动Ollama服务失败",
      error: error.message
    });
  }
});

// 手动启动Ollama服务
router.post("/start", async (ctx) => {
  try {
    console.log('🔧 手动启动Ollama服务');
    const result = await ollamaManager.startOllama();
    
    if (result.success) {
      const status = await ollamaManager.getStatus();
      return handleResponse(ctx, 200, {
        message: result.message,
        data: status
      });
    } else {
      return handleResponse(ctx, 500, {
        message: result.message
      });
    }
  } catch (error) {
    console.error('❌ 启动Ollama失败:', error);
    return handleResponse(ctx, 500, {
      message: "启动Ollama服务失败",
      error: error.message
    });
  }
});

// 停止Ollama服务
router.post("/stop", async (ctx) => {
  try {
    console.log('🛑 停止Ollama服务');
    const result = await ollamaManager.stopOllama();
    const status = await ollamaManager.getStatus();
    
    return handleResponse(ctx, 200, {
      message: result.message,
      data: status
    });
  } catch (error) {
    console.error('❌ 停止Ollama失败:', error);
    return handleResponse(ctx, 500, {
      message: "停止Ollama服务失败",
      error: error.message
    });
  }
});

// 获取Ollama状态
router.get("/status", async (ctx) => {
  try {
    const status = await ollamaManager.getStatus();
    return handleResponse(ctx, 200, {
      message: "获取状态成功",
      data: status
    });
  } catch (error) {
    console.error('❌ 获取Ollama状态失败:', error);
    return handleResponse(ctx, 500, {
      message: "获取状态失败",
      error: error.message
    });
  }
});

// 获取Ollama日志
router.get("/logs", async (ctx) => {
  try {
    const { lines = 50 } = ctx.query;
    const logs = ollamaManager.getLog(parseInt(lines));
    
    return handleResponse(ctx, 200, {
      message: "获取日志成功",
      data: {
        logs,
        lines: parseInt(lines)
      }
    });
  } catch (error) {
    console.error('❌ 获取Ollama日志失败:', error);
    return handleResponse(ctx, 500, {
      message: "获取日志失败",
      error: error.message
    });
  }
});

// 重启Ollama服务
router.post("/restart", async (ctx) => {
  try {
    console.log('🔄 重启Ollama服务');
    
    // 先停止
    await ollamaManager.stopOllama();
    
    // 等待一下
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 再启动
    const result = await ollamaManager.startOllama();
    
    if (result.success) {
      const status = await ollamaManager.getStatus();
      return handleResponse(ctx, 200, {
        message: "Ollama服务重启成功",
        data: status
      });
    } else {
      return handleResponse(ctx, 500, {
        message: `重启失败: ${result.message}`
      });
    }
  } catch (error) {
    console.error('❌ 重启Ollama失败:', error);
    return handleResponse(ctx, 500, {
      message: "重启Ollama服务失败",
      error: error.message
    });
  }
});

// 健康检查
router.get("/health", async (ctx) => {
  try {
    const status = await ollamaManager.getStatus();
    const isHealthy = status.installed && status.running && status.modelCount > 0;
    
    return handleResponse(ctx, isHealthy ? 200 : 503, {
      message: isHealthy ? "服务健康" : "服务不健康",
      data: {
        healthy: isHealthy,
        status,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    return handleResponse(ctx, 503, {
      message: "健康检查失败",
      error: error.message
    });
  }
});

module.exports = router;
