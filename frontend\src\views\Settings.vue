<!-- setting.vue -->
<template>
  <div class="settings-container">
    <el-row :gutter="24">
      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="setting-card">
          <daochusql />
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="setting-card">
          <jiami />
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="setting-card">
          <shipinjiami />
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="setting-card">
          <div style="width: 100%; height: 100px; line-height: 100px; text-align: center; font-size: 18px; color: #909399;">
            <router-link to="/user/intro">修改个人信息</router-link>
          </div>

        </el-card>
      </el-col>
    </el-row>

  </div>

</template>

<script setup lang="ts">
</script>

<style scoped>
.settings-container {
  padding: 24px;
}

.setting-card {
  margin-bottom: 24px;
}
</style>
