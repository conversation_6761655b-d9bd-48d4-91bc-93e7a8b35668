const db = require("../db");
const logger = require("../../plugin/logger");

// 保存播放历史
async function savePlayHistory(userId, fileName, progress, currentTime, playTime) {
  const sql = `
    INSERT INTO media_play_history (user_id, file_name, progress, \`current_time\`, play_time, updated_at)
    VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    ON DUPLICATE KEY UPDATE
        progress = VALUES(progress),
        \`current_time\` = VALUES(\`current_time\`),
        play_time = VALUES(play_time),
        updated_at = CURRENT_TIMESTAMP
  `;
  const result = await db.query(sql, [userId, fileName, progress || 0, currentTime || 0, playTime]);
  return result;
}

// 获取播放历史
async function getPlayHistory(userId, limit, offset) {
  const sql = `
    SELECT
      mph.*,
      COALESCE(vm.status, 'online') as status
    FROM media_play_history mph
    LEFT JOIN video_management vm ON mph.file_name = vm.file_name
    WHERE mph.user_id = ? AND COALESCE(vm.status, 'online') = 'online'
    ORDER BY mph.play_time DESC
    LIMIT ? OFFSET ?
  `;
  const history = await db.query(sql, [parseInt(userId), parseInt(limit), offset]);

  // 获取总数
  const countSql = "SELECT COUNT(*) as total FROM media_play_history WHERE user_id = ?";
  const countResult = await db.query(countSql, [parseInt(userId)]);
  const total = countResult[0].total;

  return { history, total };
}

// 清空播放历史
async function clearPlayHistory(userId) {
  const sql = "DELETE FROM media_play_history WHERE user_id = ?";
  const result = await db.query(sql, [parseInt(userId)]);
  return result;
}

// 检查媒体喜欢状态
async function checkMediaLike(userId, fileName) {
  const sql = "SELECT id FROM media_likes WHERE user_id = ? AND file_name = ?";
  const result = await db.query(sql, [userId, fileName]);
  return result;
}

// 添加媒体喜欢
async function addMediaLike(userId, fileName) {
  const sql = "INSERT INTO media_likes (user_id, file_name) VALUES (?, ?)";
  const result = await db.query(sql, [userId, fileName]);
  return result;
}

// 删除媒体喜欢
async function removeMediaLike(userId, fileName) {
  const sql = "DELETE FROM media_likes WHERE user_id = ? AND file_name = ?";
  const result = await db.query(sql, [userId, fileName]);
  return result;
}

// 获取用户喜欢的媒体列表
async function getUserLikedMedia(userId, limit, offset) {
  const sql = `
    SELECT
      ml.*,
      COALESCE(vm.status, 'online') as status
    FROM media_likes ml
    LEFT JOIN video_management vm ON ml.file_name = vm.file_name
    WHERE ml.user_id = ? AND COALESCE(vm.status, 'online') = 'online'
    ORDER BY ml.created_at DESC
    LIMIT ? OFFSET ?
  `;
  const liked = await db.query(sql, [parseInt(userId), parseInt(limit), offset]);

  // 获取总数
  const countSql = "SELECT COUNT(*) as total FROM media_likes WHERE user_id = ?";
  const countResult = await db.query(countSql, [parseInt(userId)]);
  const total = countResult[0].total;

  return { liked, total };
}

// 检查媒体收藏状态
async function checkMediaCollection(userId, fileName) {
  const sql = "SELECT id FROM media_collections WHERE user_id = ? AND file_name = ?";
  const result = await db.query(sql, [userId, fileName]);
  return result;
}

// 添加媒体收藏
async function addMediaCollection(userId, fileName) {
  const sql = "INSERT INTO media_collections (user_id, file_name) VALUES (?, ?)";
  const result = await db.query(sql, [userId, fileName]);
  return result;
}

// 删除媒体收藏
async function removeMediaCollection(userId, fileName) {
  const sql = "DELETE FROM media_collections WHERE user_id = ? AND file_name = ?";
  const result = await db.query(sql, [userId, fileName]);
  return result;
}

// 获取用户收藏的媒体列表
async function getUserCollectedMedia(userId, limit, offset) {
  const sql = `
    SELECT
      mc.*,
      COALESCE(vm.status, 'online') as status
    FROM media_collections mc
    LEFT JOIN video_management vm ON mc.file_name = vm.file_name
    WHERE mc.user_id = ? AND COALESCE(vm.status, 'online') = 'online'
    ORDER BY mc.created_at DESC
    LIMIT ? OFFSET ?
  `;
  const collected = await db.query(sql, [parseInt(userId), parseInt(limit), offset]);

  // 获取总数
  const countSql = "SELECT COUNT(*) as total FROM media_collections WHERE user_id = ?";
  const countResult = await db.query(countSql, [parseInt(userId)]);
  const total = countResult[0].total;

  return { collected, total };
}

// 获取媒体统计信息
async function getMediaStats(fileName) {
  const sql = "SELECT * FROM media_stats WHERE file_name = ?";
  const result = await db.query(sql, [decodeURIComponent(fileName)]);
  return result.length > 0 ? result[0] : null;
}

// 更新媒体统计
async function updateMediaStats(fileName, field, increment) {
  try {
    // 先尝试更新
    const updateSql = `UPDATE media_stats SET ${field} = ${field} + ? WHERE file_name = ?`;
    const result = await db.query(updateSql, [increment, fileName]);

    // 如果没有记录，则创建新记录
    if (result.affectedRows === 0) {
      const insertSql = `
        INSERT INTO media_stats (file_name, ${field}, upload_time)
        VALUES (?, ?, ?)
      `;
      await db.query(insertSql, [fileName, Math.max(0, increment), Date.now()]);
    }
  } catch (error) {
    logger.error(`更新媒体统计失败 (${field}):`, error);
    throw error;
  }
}

module.exports = {
  savePlayHistory,
  getPlayHistory,
  clearPlayHistory,
  checkMediaLike,
  addMediaLike,
  removeMediaLike,
  getUserLikedMedia,
  checkMediaCollection,
  addMediaCollection,
  removeMediaCollection,
  getUserCollectedMedia,
  getMediaStats,
  updateMediaStats,
};
