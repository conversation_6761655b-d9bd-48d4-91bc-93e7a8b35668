<template>
    <div class="creative-record">
        <el-card>
            <el-form :model="form" label-width="80px" class="creative-form">
                <el-form-item label="标题">
                    <el-input v-model="form.title" maxlength="100" placeholder="请输入创意标题" />
                </el-form-item>
                <el-form-item label="内容">
                    <el-input v-model="form.content" type="textarea" maxlength="1000" rows="6" placeholder="请输入创意内容" />
                </el-form-item>
                <el-form-item label="标签">
                    <el-select v-model="form.tags" multiple placeholder="请选择标签" style="width: 100%">
                        <el-option v-for="tag in tagOptions" :key="tag" :label="tag" :value="tag" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submit">保存创意</el-button>
                </el-form-item>
            </el-form>
            <el-divider>历史创意</el-divider>
            <el-table :data="creativeList" style="width: 100%">
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="title" label="标题" />
                <el-table-column prop="content" label="内容">
                    <template #default="{ row }">
                        <span>{{ row.content.length > 20 ? row.content.slice(0, 20) + '...' : row.content }}</span>
                        <el-link type="primary" @click="showDetail(row.content)" style="margin-left: 6px;">详情</el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="tags" label="标签" width="280">
                    <template #default="{ row }">
                        <el-tag v-for="tag in row.tags" :key="tag" style="margin-right: 4px;">{{ tag }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="create_time" label="创建时间" width="180">
                    <template #default="{ row }">
                        {{ formatTime(row.create_time) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                    <template #default="{ row }">
                        <el-button type="danger" size="small" @click="deleteCreative(row.id)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <el-dialog v-model="detailDialogVisible" title="创意内容详情" width="400px">
            <div style="white-space: pre-wrap;">{{ detailContent }}</div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import dayjs from "dayjs";


import { GetCreativeListApi, AddCreativeApi, DeleteCreativeApi } from "@/utils/api";

// 临时本地存储模拟
const creativeList = ref<any[]>([]);
const tagOptions = ref<string[]>(["灵感", "设计", "技术", "生活", "随笔"]);
const form = ref({
    title: "",
    content: "",
    tags: [] as string[],
});
const detailDialogVisible = ref(false);
const detailContent = ref("");

// 获取创意列表（实际应调用API）
const fetchCreativeList = async () => {
    const data = await GetCreativeListApi();
    // 处理 tags 字段为数组
    creativeList.value = (data.data || []).map((item: any) => {
        let tags = [];
        try {
            tags = JSON.parse(item.tags || "[]");
        } catch {
            tags = [];
        }
        return { ...item, tags };
    });
};

// 保存创意（实际应调用API）
const submit = async () => {
    if (!form.value.title || !form.value.content) {
        ElMessage.warning("请填写标题和内容");
        return;
    }
    const newCreative = {
        id: Date.now(),
        title: form.value.title,
        content: form.value.content,
        tags: [...form.value.tags],
        create_time: new Date().toISOString(),
    };
    creativeList.value.unshift(newCreative);
    const res = await AddCreativeApi(newCreative);
    if (res.code === 0) {
        ElMessage.success("创意已保存");
        form.value.title = "";
        form.value.content = "";
        form.value.tags = [];
    } else {
        ElMessage.error("保存失败");
    }

};

// 删除创意（实际应调用API）
const deleteCreative = (id: number) => {
    ElMessageBox.confirm("确定要删除该创意吗？", "提示", {
        type: "warning",
    })
        .then(() => {
            creativeList.value = creativeList.value.filter(item => item.id !== id);
            const res = DeleteCreativeApi(id);
            if (res.code === 0) {
                ElMessage.success("删除成功");
            } else {
                ElMessage.error("删除失败");
            }
        })
    ElMessage.success("删除成功");
};

// 显示内容详情
const showDetail = (content: string) => {
    detailContent.value = content;
    detailDialogVisible.value = true;
};

// 时间格式化
const formatTime = (time: string) => {
    return time ? dayjs(time).format("YYYY-MM-DD HH:mm:ss") : "";
};

onMounted(() => {
    fetchCreativeList();

});
</script>

<style scoped lang="less">
.creative-record {
    margin: 30px auto;
    max-width: 1100px; // 加宽
}

.title {
    font-size: 20px;
    font-weight: bold;
}

.creative-form {
    margin-bottom: 24px;
}
</style>