# 双通道上传组件使用说明

## 功能概述

新的双通道上传组件 `DualChannelUpload.vue` 提供了两个专门的上传通道：

### 1. 文档通道 (Document Channel)
- **用途**: 上传文档、图片、压缩包等一般文件
- **支持格式**: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, RTF, ZIP, RAR, 7Z, TAR, GZ, JPG, JPEG, PNG, GIF, BMP, WEBP, SVG
- **存储位置**: 根据文件类型自动分类存储到不同目录
- **访问方式**: 通过后端API访问，需要权限验证

### 2. 媒体通道 (Media Channel)
- **用途**: 专门上传视频、音频等媒体文件
- **支持格式**: MP4, AVI, MOV, WMV, FLV, MKV, WEBM, M4V, 3GP, MP3, WAV, FLAC, AAC, OGG, WMA, M4A
- **存储位置**: 统一存储到 `public/media` 目录
- **访问方式**: 直接通过HTTP访问，无需权限验证

## 主要特性

### 🚀 智能上传策略
- **小文件** (< 10MB): 使用普通上传，速度快
- **大文件** (≥ 10MB): 自动分片上传，支持断点续传

### 📊 实时进度监控
- 上传进度条显示
- 实时速度计算
- 文件状态跟踪

### 🔄 并发上传控制
- 最多同时上传3个文件
- 避免服务器过载
- 优化用户体验

### 📱 响应式设计
- 桌面端和移动端适配
- 拖拽上传支持
- 现代化UI界面

## 使用方法

### 1. 访问组件
```
http://localhost:5175/index/dashboard/uploads
```
**注意**: 新的双通道上传组件已经替换了原来的上传组件，直接访问原来的上传页面即可使用新功能。

### 2. 选择上传通道
- 点击"文档通道"或"媒体通道"按钮
- 根据文件类型选择合适的通道

### 3. 上传文件
- 拖拽文件到上传区域
- 或点击选择文件按钮
- 支持多文件同时选择

### 4. 监控进度
- 查看文件列表和上传进度
- 实时速度显示
- 上传统计信息

### 5. 查看结果
- 上传完成后显示结果列表
- 成功文件显示访问链接
- 失败文件显示错误信息

## 后端接口

### 普通上传接口
```
POST /upload/uploads
Content-Type: multipart/form-data

参数:
- file: 文件数据
- user_id: 用户ID
- username: 用户名
- channel: 上传通道 (document/media)
```

### 分片上传接口
```
POST /upload/uploads/chunk
Content-Type: multipart/form-data

参数:
- chunk: 分片数据
- chunkIndex: 分片索引
- fileHash: 文件哈希
- fileName: 文件名
- user_id: 用户ID
- totalChunks: 总分片数
- channel: 上传通道
```

### 分片合并接口
```
POST /upload/merge
Content-Type: application/json

参数:
- fileHash: 文件哈希
- fileName: 文件名
- user_id: 用户ID
- totalChunks: 总分片数
- fileType: 文件类型
- fileSize: 文件大小
- channel: 上传通道
```

## 文件存储结构

```
uploads/
├── documents/     # 文档文件
├── images/        # 图片文件
├── videos/        # 视频文件 (通过document通道上传)
├── archives/      # 压缩包文件
└── chunks/        # 分片临时文件

public/
└── media/         # 媒体文件 (通过media通道上传)
```

## 技术实现

### 前端技术栈
- Vue 3 + Composition API
- Element Plus UI组件
- TypeScript
- Vite构建工具

### 核心功能
- 文件类型检测和验证
- 拖拽上传支持
- 进度监控和速度计算
- 并发控制和队列管理
- 错误处理和重试机制

### 样式特性
- 现代化渐变设计
- 响应式布局
- 平滑动画效果
- 直观的视觉反馈

## 注意事项

1. **文件大小限制**: 单文件最大支持几GB，具体取决于服务器配置
2. **并发限制**: 同时最多上传3个文件，避免服务器压力过大
3. **网络要求**: 大文件上传需要稳定的网络连接
4. **浏览器兼容**: 支持现代浏览器，IE不支持
5. **权限验证**: 需要登录后才能使用上传功能

## 故障排除

### 上传失败
- 检查网络连接
- 确认文件格式是否支持
- 查看浏览器控制台错误信息

### 进度卡住
- 刷新页面重试
- 检查服务器状态
- 确认磁盘空间充足

### 文件访问问题
- 媒体文件: 直接访问 `http://server/media/filename`
- 文档文件: 通过API访问，需要权限验证

## 更新说明

### ✅ 组件替换完成
- **原上传组件** (`/index/dashboard/uploads`) 已被新的双通道上传组件完全替换
- **保持向后兼容**: 所有原有的上传功能都得到保留和增强
- **无需更改访问路径**: 继续使用原来的URL访问即可体验新功能

### 🚀 新功能亮点
- **双通道设计**: 文档和媒体文件分别处理，更加专业
- **智能上传**: 根据文件大小自动选择最优上传策略
- **现代化界面**: 全新的UI设计，更好的用户体验
- **增强功能**: 实时进度、并发控制、错误处理等

### 📍 访问地址
- **主上传页面**: `http://localhost:5175/index/dashboard/uploads`
- **移动端上传**: `http://localhost:5175/index/uploadsmobile`
