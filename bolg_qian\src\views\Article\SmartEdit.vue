<template>
  <div class="smart-editor-page">
    <!-- AI状态栏 -->
    <div class="ai-status-section">
      <div class="status-container">
        <div class="ai-status-bar" :class="{ 'ai-ready': aiStatus.available }">
          <div class="status-info">
            <el-icon class="status-icon"><i-ep-Cpu /></el-icon>
            <span class="status-text">{{ aiStatus.available ? `AI助手 ${aiStatus.currentModel} 已就绪` : 'AI服务离线' }}</span>
            <el-tag v-if="aiStatus.available" size="small" type="success" effect="light">
              今日使用: {{ dailyUsage }}
            </el-tag>
          </div>

          <!-- 智能建议 -->
          <div class="smart-tips" v-if="smartTips.length > 0">
            <el-icon><i-ep-Sunny /></el-icon>
            <span class="tips-label">AI建议:</span>
            <div class="tips-buttons">
              <el-button
                v-for="tip in smartTips.slice(0, 2)"
                :key="tip.id"
                size="small"
                type="primary"
                link
                @click="applyTip(tip)"
                class="tip-btn"
              >
                {{ tip.text }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主编辑区 -->
    <div class="editor-container">
      <div class="editor-card">
        <!-- 标题区域 -->
        <div class="title-section">
          <div class="section-header">
            <h2 class="section-title">
              <el-icon><i-ep-EditPen /></el-icon>
              文章标题
            </h2>
          </div>

          <div class="title-input-wrapper">
            <el-input
              v-model="article.title"
              placeholder="请输入文章标题..."
              size="large"
              class="title-input"
              @input="onTitleChange"
            >
              <template #suffix>
                <div class="title-actions">
                  <el-tooltip content="AI生成标题" placement="top">
                    <el-button
                      :icon="Star"
                      circle
                      size="small"
                      type="primary"
                      :loading="loading.title"
                      @click="generateTitles"
                      :disabled="!article.content || article.content.length < 50"
                    />
                  </el-tooltip>
                  <el-tooltip content="优化标题" placement="top">
                    <el-button
                      :icon="Edit"
                      circle
                      size="small"
                      type="success"
                      :loading="loading.titleOptimize"
                      @click="optimizeTitle"
                      :disabled="!article.title"
                    />
                  </el-tooltip>
                </div>
              </template>
            </el-input>
          </div>

          <!-- 标题建议 -->
          <div class="title-suggestions" v-if="titleSuggestions.length > 0">
            <div class="suggestions-header">
              <el-icon><i-ep-Star /></el-icon>
              <span>AI建议标题</span>
            </div>
            <div class="suggestions-list">
              <el-tag
                v-for="(title, index) in titleSuggestions.slice(0, 3)"
                :key="index"
                class="suggestion-tag"
                @click="applyTitle(title)"
                effect="light"
                type="primary"
              >
                {{ title }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 元信息 -->
        <div class="meta-section">
          <div class="section-header">
            <h3 class="section-title">
              <el-icon><i-ep-Collection /></el-icon>
              文章信息
            </h3>
          </div>

          <div class="meta-form">
            <el-row :gutter="16">
              <el-col :xs="24" :sm="8" :md="8">
                <div class="form-item">
                  <label class="form-label">分类</label>
                  <el-select v-model="article.category" placeholder="选择分类" class="form-select">
                    <el-option label="技术" value="技术" />
                    <el-option label="艺术" value="艺术" />
                    <el-option label="生活" value="生活" />
                    <el-option label="教程" value="教程" />
                    <el-option label="经验分享" value="经验分享" />
                  </el-select>
                </div>
              </el-col>
              <el-col :xs="24" :sm="14" :md="14">
                <div class="form-item">
                  <label class="form-label">标签</label>
                  <el-select
                    v-model="article.tags"
                    multiple
                    filterable
                    allow-create
                    placeholder="添加标签..."
                    class="form-select"
                  >
                    <el-option
                      v-for="tag in commonTags"
                      :key="tag"
                      :label="tag"
                      :value="tag"
                    />
                  </el-select>
                </div>
              </el-col>
              <el-col :xs="24" :sm="2" :md="2">
                <div class="form-item">
                  <label class="form-label">&nbsp;</label>
                  <el-tooltip content="AI推荐标签" placement="top">
                    <el-button
                      :icon="PriceTag"
                      circle
                      type="warning"
                      :loading="loading.tags"
                      @click="suggestTags"
                      :disabled="!article.title && !article.content"
                      class="ai-btn"
                    />
                  </el-tooltip>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

      <!-- 智能工具栏 -->
      <div class="smart-toolbar">
        <div class="toolbar-left">
          <el-button-group size="small">
            <el-tooltip content="AI续写">
              <el-button 
                :icon="EditPen"
                :loading="loading.continue"
                @click="continueWriting"
                :disabled="!article.content"
              >
                续写
              </el-button>
            </el-tooltip>
            <el-tooltip content="内容优化">
              <el-button 
                :icon="Tools"
                :loading="loading.optimize"
                @click="optimizeContent"
                :disabled="!selectedText && !article.content"
              >
                优化
              </el-button>
            </el-tooltip>
            <el-tooltip content="生成大纲">
              <el-button
                :icon="List"
                :loading="loading.outline"
                @click="generateOutline"
              >
                大纲
              </el-button>
            </el-tooltip>
            <el-tooltip content="标题帮写内容">
              <el-button
                :icon="DocumentAdd"
                :loading="loading.titleToContent"
                @click="generateContentFromTitle"
                :disabled="!article.title"
              >
                标题帮写
              </el-button>
            </el-tooltip>
            <el-tooltip content="语法检查">
              <el-button
                :icon="Check"
                :loading="loading.grammar"
                @click="checkGrammar"
                :disabled="!article.content"
              >
                检查
              </el-button>
            </el-tooltip>
          </el-button-group>
        </div>
        
        <div class="toolbar-right">
          <div class="content-stats">
            <span>{{ contentStats.wordCount }}字</span>
            <span>{{ contentStats.paragraphs }}段</span>
            <span>约{{ contentStats.readTime }}分钟</span>
          </div>
        </div>
      </div>

      <!-- 编辑器 -->
      <div class="editor-wrapper">
        <v-md-editor 
          v-model="article.content" 
          :height="600" 
          preview
          @input="onContentChange"
          @focus="onEditorFocus"
        />
      </div>

      <!-- 封面上传 -->
      <div class="cover-section">
        <div class="section-title">文章封面</div>
        <el-upload
          class="cover-uploader"
          :show-file-list="false"
          :before-upload="beforeUpload"
          accept="image/*"
        >
          <div v-if="previewUrl" class="cover-preview">
            <img :src="previewUrl" alt="封面" />
            <div class="cover-overlay">
              <el-button size="small" @click.stop="removeFile">删除</el-button>
            </div>
          </div>
          <div v-else class="cover-placeholder">
            <el-icon><Plus /></el-icon>
            <div>点击上传封面</div>
          </div>
        </el-upload>
      </div>

        <!-- 操作按钮 -->
        <div class="action-section">
          <div class="action-buttons">
            <el-button size="large" @click="saveDraft" :loading="saving" class="action-btn">
              <el-icon><i-ep-DocumentCopy /></el-icon>
              保存草稿
            </el-button>
            <el-button type="primary" size="large" @click="publishArticle" :loading="publishing" class="action-btn">
              <el-icon><i-ep-Upload /></el-icon>
              发布文章
            </el-button>
            <el-button @click="$router.push('/index')" class="action-btn">
              <el-icon><i-ep-Back /></el-icon>
              返回
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- AI结果对话框 -->
    <el-dialog v-model="showDialog" :title="dialogTitle" width="600px">
      <div v-if="dialogType === 'titles'" class="dialog-content">
        <div class="dialog-tip">点击标题直接应用：</div>
        <div class="title-list">
          <div 
            v-for="(title, index) in dialogData" 
            :key="index"
            class="title-item"
            @click="applyTitle(title)"
          >
            {{ title }}
          </div>
        </div>
      </div>
      
      <div v-else-if="dialogType === 'optimize'" class="dialog-content">
        <div class="optimize-result">
          <h4>优化结果：</h4>
          <div class="result-text">{{ dialogData }}</div>
        </div>
      </div>
      
      <div v-else-if="dialogType === 'outline'" class="dialog-content">
        <div class="outline-result">
          <h4>生成的大纲：</h4>
          <div class="result-text" v-html="formatOutline(dialogData)"></div>
        </div>
      </div>

      <div v-else-if="dialogType === 'titleToContent'" class="dialog-content">
        <div class="content-result">
          <h4>基于标题生成的内容：</h4>
          <div class="result-text">{{ dialogData }}</div>
          <div class="content-options">
            <el-button size="small" @click="appendContent">追加到现有内容</el-button>
            <el-button size="small" type="warning" @click="replaceContent">替换现有内容</el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showDialog = false">关闭</el-button>
        <el-button 
          v-if="dialogType !== 'titles'" 
          type="primary" 
          @click="applyResult"
        >
          应用结果
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { PublishArticleApi } from "../../utils/api";
import { aiWritingApi, ollamaApi } from "../../utils/aiApi";
import VMdEditor from "@kangc/v-md-editor";
import "@kangc/v-md-editor/lib/style/base-editor.css";
import githubTheme from "@kangc/v-md-editor/lib/theme/github.js";
import "@kangc/v-md-editor/lib/theme/style/github.css";
import hljs from "highlight.js";
import {
  Cpu, Star, PriceTag, Tools, List, Edit, EditPen, Check,
  Sunny, Plus, DocumentAdd, Collection, Upload, Back, DocumentCopy
} from '@element-plus/icons-vue';

VMdEditor.use(githubTheme, { Hljs: hljs });

const router = useRouter();

// 核心数据
const article = ref({
  title: "",
  category: "",
  tags: [],
  content: ""
});

// AI状态
const aiStatus = reactive({
  available: false,
  models: [],
  currentModel: ''
});

// 加载状态
const loading = reactive({
  title: false,
  titleOptimize: false,
  tags: false,
  optimize: false,
  outline: false,
  continue: false,
  grammar: false,
  titleToContent: false
});

// 智能建议
const smartTips = ref([]);
const titleSuggestions = ref([]);
const selectedText = ref('');

// 统计
const dailyUsage = ref(0);

// 对话框
const showDialog = ref(false);
const dialogTitle = ref('');
const dialogType = ref('');
const dialogData = ref(null);

// 上传
const file = ref(null);
const previewUrl = ref('');
const saving = ref(false);
const publishing = ref(false);

// 常用标签
const commonTags = ref(['Vue', 'JavaScript', 'CSS', 'HTML', 'Node.js', 'React', 'TypeScript', 'Python']);

// 计算属性
const contentStats = computed(() => {
  const content = article.value.content;
  const wordCount = content.length;
  const paragraphs = content.split('\n\n').filter(p => p.trim()).length;
  const readTime = Math.ceil(wordCount / 200);

  return { wordCount, paragraphs, readTime };
});

// AI功能方法
const checkAIStatus = async () => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      aiStatus.available = false;
      return;
    }

    console.log('🔍 检查AI服务状态...');
    const response = await aiWritingApi.getStatus();

    if (response.code === 200 && response.data.available) {
      // AI服务可用
      aiStatus.available = true;
      aiStatus.models = response.data.models;
      aiStatus.currentModel = response.data.currentModel;

      console.log('✅ AI服务已可用');
      // 模拟使用统计
      dailyUsage.value = Math.floor(Math.random() * 10);
    } else {
      // AI服务不可用，尝试自动启动Ollama
      console.log('⚠️ AI服务不可用，尝试自动启动Ollama...');
      aiStatus.available = false;

      await autoStartOllama();
    }
  } catch (error) {
    console.error('❌ AI状态检查失败:', error);
    aiStatus.available = false;

    // 如果是网络错误，也尝试启动Ollama
    if (error.code === 'ECONNREFUSED' || error.response?.status === 500) {
      console.log('🔧 检测到连接问题，尝试启动Ollama...');
      await autoStartOllama();
    }
  }
};

// 自动启动Ollama服务
const autoStartOllama = async () => {
  try {
    console.log('🚀 正在自动启动Ollama服务...');
    ElMessage.info('正在启动AI服务，请稍候...');

    const response = await ollamaApi.autoStart();

    if (response.code === 200) {
      console.log('✅ Ollama启动成功:', response.message);
      ElMessage.success('AI服务启动成功！');

      // 等待一下，然后重新检查AI状态
      setTimeout(async () => {
        await recheckAIStatus();
      }, 3000);

    } else {
      console.error('❌ Ollama启动失败:', response.message);
      ElMessage.error(`AI服务启动失败: ${response.message}`);
    }
  } catch (error) {
    console.error('❌ 自动启动Ollama失败:', error);

    let errorMessage = 'AI服务启动失败';
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    ElMessage.error(errorMessage);

    // 如果是Ollama未安装的错误，提供安装指导
    if (errorMessage.includes('未安装')) {
      ElMessageBox.alert(
        'Ollama未安装，请先安装Ollama才能使用AI功能。\n\n安装地址: https://ollama.ai',
        'AI服务不可用',
        {
          confirmButtonText: '我知道了',
          type: 'warning'
        }
      );
    }
  }
};

// 重新检查AI状态
const recheckAIStatus = async () => {
  try {
    console.log('🔄 重新检查AI服务状态...');
    const response = await aiWritingApi.getStatus();

    if (response.code === 200 && response.data.available) {
      aiStatus.available = true;
      aiStatus.models = response.data.models;
      aiStatus.currentModel = response.data.currentModel;

      console.log('✅ AI服务现已可用');
      ElMessage.success('AI服务已就绪，可以开始使用智能写作功能！');

      // 模拟使用统计
      dailyUsage.value = Math.floor(Math.random() * 10);
    } else {
      console.log('⚠️ AI服务仍不可用');
      aiStatus.available = false;
    }
  } catch (error) {
    console.error('❌ 重新检查AI状态失败:', error);
    aiStatus.available = false;
  }
};

// 内容变化处理
const onContentChange = (content) => {
  article.value.content = content;
  analyzeContent();
};

const onTitleChange = () => {
  if (article.value.title && article.value.content) {
    generateSmartTips();
  }
};

// 智能分析
const analyzeContent = () => {
  const tips = [];

  if (!article.value.title && article.value.content.length > 100) {
    tips.push({
      id: 'need-title',
      text: '建议添加标题',
      action: 'generate-title'
    });
  }

  if (article.value.tags.length === 0 && article.value.content.length > 200) {
    tips.push({
      id: 'need-tags',
      text: '建议添加标签',
      action: 'suggest-tags'
    });
  }

  smartTips.value = tips;
};

const generateSmartTips = () => {
  if (!article.value.category) {
    smartTips.value.push({
      id: 'need-category',
      text: '建议选择分类',
      action: 'select-category'
    });
  }
};

// AI功能实现
const generateTitles = async () => {
  loading.title = true;
  try {
    const response = await aiWritingApi.generateTitle({
      content: article.value.content,
      category: article.value.category,
      count: 5
    });

    if (response.code === 200) {
      titleSuggestions.value = response.data.titles;
      ElMessage.success('生成了多个标题建议');
    } else {
      ElMessage.error(response.message || '标题生成失败');
    }
  } catch (error) {
    ElMessage.error('标题生成失败');
  } finally {
    loading.title = false;
  }
};

const optimizeTitle = async () => {
  loading.titleOptimize = true;
  try {
    const response = await aiWritingApi.optimizeContent({
      content: article.value.title,
      style: 'professional'
    });

    if (response.code === 200) {
      const result = await ElMessageBox.confirm(
        `优化后的标题：\n"${response.data.optimizedContent}"\n\n是否应用？`,
        '标题优化',
        { type: 'info' }
      );

      if (result === 'confirm') {
        article.value.title = response.data.optimizedContent;
        ElMessage.success('标题已优化');
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('标题优化失败');
    }
  } finally {
    loading.titleOptimize = false;
  }
};

const suggestTags = async () => {
  loading.tags = true;
  try {
    const response = await aiWritingApi.suggestTags({
      title: article.value.title,
      content: article.value.content,
      category: article.value.category,
      maxTags: 5
    });

    if (response.code === 200) {
      const newTags = [...article.value.tags, ...response.data.tags]
        .filter((tag, index, arr) => arr.indexOf(tag) === index);
      article.value.tags = newTags;
      ElMessage.success(`推荐了 ${response.data.tags.length} 个标签`);
    }
  } catch (error) {
    ElMessage.error('标签推荐失败');
  } finally {
    loading.tags = false;
  }
};

const continueWriting = async () => {
  loading.continue = true;
  try {
    const lastParagraph = article.value.content.split('\n\n').pop();
    const prompt = `请基于以下内容继续写作：\n${lastParagraph}`;

    const response = await aiWritingApi.optimizeContent({
      content: prompt,
      style: 'creative'
    });

    if (response.code === 200) {
      article.value.content += '\n\n' + response.data.optimizedContent;
      ElMessage.success('内容续写完成');
    }
  } catch (error) {
    ElMessage.error('续写失败');
  } finally {
    loading.continue = false;
  }
};

const optimizeContent = async () => {
  loading.optimize = true;
  try {
    const content = selectedText.value || article.value.content;
    const response = await aiWritingApi.optimizeContent({
      content,
      style: 'professional'
    });

    if (response.code === 200) {
      dialogTitle.value = '内容优化结果';
      dialogType.value = 'optimize';
      dialogData.value = response.data.optimizedContent;
      showDialog.value = true;
    }
  } catch (error) {
    ElMessage.error('内容优化失败');
  } finally {
    loading.optimize = false;
  }
};

const generateOutline = async () => {
  loading.outline = true;
  try {
    const response = await aiWritingApi.generateOutline({
      topic: article.value.title || '文章大纲',
      category: article.value.category,
      depth: 'medium'
    });

    if (response.code === 200) {
      dialogTitle.value = '文章大纲';
      dialogType.value = 'outline';
      dialogData.value = response.data.outline;
      showDialog.value = true;
    }
  } catch (error) {
    ElMessage.error('大纲生成失败');
  } finally {
    loading.outline = false;
  }
};

const checkGrammar = async () => {
  loading.grammar = true;
  try {
    await new Promise(resolve => setTimeout(resolve, 1000));
    ElMessage.success('语法检查完成，未发现问题');
  } catch (error) {
    ElMessage.error('语法检查失败');
  } finally {
    loading.grammar = false;
  }
};

// 标题帮写内容功能
const generateContentFromTitle = async () => {
  if (!article.value.title) {
    ElMessage.warning('请先输入标题');
    return;
  }

  loading.titleToContent = true;
  try {
    console.log('🚀 基于标题生成内容:', article.value.title);

    // 构建提示词
    const prompt = `请基于以下标题写一篇详细的文章内容：

标题：${article.value.title}
分类：${article.value.category || '通用'}
标签：${article.value.tags.join(', ') || '无'}

要求：
1. 内容要详细、有条理
2. 包含引言、主体和结论
3. 使用Markdown格式
4. 字数在800-1500字之间
5. 语言要专业且易懂

请开始写作：`;

    // 直接调用Ollama API
    const response = await fetch('http://192.168.31.222:11434/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: 'qwen2.5:0.5b', // 使用轻量模型
        messages: [{ role: 'user', content: prompt }],
        stream: false
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const generatedContent = data.message?.content;

    if (generatedContent) {
      dialogTitle.value = '基于标题生成的内容';
      dialogType.value = 'titleToContent';
      dialogData.value = generatedContent;
      showDialog.value = true;
      ElMessage.success('内容生成完成！');
    } else {
      throw new Error('生成的内容为空');
    }

  } catch (error) {
    console.error('❌ 标题帮写内容失败:', error);

    let errorMessage = '内容生成失败';
    if (error.message.includes('fetch')) {
      errorMessage = 'AI服务连接失败，请检查Ollama是否启动';
    } else if (error.message.includes('HTTP')) {
      errorMessage = 'AI服务响应异常，请稍后重试';
    }

    ElMessage.error(errorMessage);
  } finally {
    loading.titleToContent = false;
  }
};

// 应用结果
const applyTitle = (title) => {
  article.value.title = title;
  titleSuggestions.value = [];
  ElMessage.success('标题已应用');
};

const applyTip = (tip) => {
  switch (tip.action) {
    case 'generate-title':
      generateTitles();
      break;
    case 'suggest-tags':
      suggestTags();
      break;
    default:
      break;
  }
};

const applyResult = () => {
  if (dialogType.value === 'optimize') {
    if (selectedText.value) {
      article.value.content = article.value.content.replace(selectedText.value, dialogData.value);
    } else {
      article.value.content = dialogData.value;
    }
  } else if (dialogType.value === 'outline') {
    article.value.content = dialogData.value;
  } else if (dialogType.value === 'titleToContent') {
    // 标题帮写内容的默认行为是追加
    appendContent();
    return; // 避免重复关闭对话框
  }

  showDialog.value = false;
  ElMessage.success('结果已应用');
};

// 追加内容到现有内容
const appendContent = () => {
  if (dialogType.value === 'titleToContent') {
    const separator = article.value.content ? '\n\n---\n\n' : '';
    article.value.content += separator + dialogData.value;
    showDialog.value = false;
    ElMessage.success('内容已追加到文章末尾');
  }
};

// 替换现有内容
const replaceContent = () => {
  if (dialogType.value === 'titleToContent') {
    ElMessageBox.confirm(
      '确定要替换现有内容吗？此操作不可撤销。',
      '确认替换',
      {
        confirmButtonText: '确定替换',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      article.value.content = dialogData.value;
      showDialog.value = false;
      ElMessage.success('内容已替换');
    }).catch(() => {
      // 用户取消，不做任何操作
    });
  }
};

const formatOutline = (outline) => {
  return outline
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^\* (.*$)/gim, '<li>$1</li>')
    .replace(/\n/g, '<br>');
};

// 编辑器事件
const onEditorFocus = () => {
  // 编辑器获得焦点时的处理
};

// 文件上传
const beforeUpload = (rawFile) => {
  const isImage = rawFile.type.startsWith('image/');
  if (!isImage) {
    ElMessage.error('只能上传图片文件');
    return false;
  }

  const isLt2M = rawFile.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!');
    return false;
  }

  const reader = new FileReader();
  reader.onload = (e) => {
    previewUrl.value = e.target.result;
  };
  reader.readAsDataURL(rawFile);

  // 保存文件对象，添加原始文件名
  file.value = rawFile;
  file.value.originalName = rawFile.name;

  return false; // 阻止自动上传
};

const removeFile = () => {
  file.value = null;
  previewUrl.value = '';
};

// 保存和发布
const saveDraft = async () => {
  saving.value = true;
  try {
    await new Promise(resolve => setTimeout(resolve, 1000));
    ElMessage.success('草稿已保存');
  } catch (error) {
    ElMessage.error('保存失败');
  } finally {
    saving.value = false;
  }
};

const publishArticle = async () => {
  if (!article.value.title || !article.value.content) {
    ElMessage.warning('标题和内容不能为空');
    return;
  }

  if (!file.value) {
    ElMessage.warning('请上传封面图片');
    return;
  }

  publishing.value = true;
  try {
    console.log('📤 开始发布文章...');
    console.log('文件信息:', {
      name: file.value.name,
      size: file.value.size,
      type: file.value.type,
      originalName: file.value.originalName
    });

    const fd = new FormData();

    // 添加文件 - 确保文件对象正确
    fd.append("file", file.value, file.value.name);

    // 添加其他字段
    fd.append("user_id", localStorage.getItem("id") || "1");
    fd.append("title", article.value.title);
    fd.append("category", article.value.category || "技术");

    // 处理标签 - 确保是字符串格式
    const tagsString = Array.isArray(article.value.tags)
      ? article.value.tags.join(',')
      : (article.value.tags || '');
    fd.append("tags", tagsString);

    fd.append("content", article.value.content);

    // 添加封面图片名称
    fd.append("cover_image", file.value.originalName || file.value.name);

    console.log('📋 FormData内容:');
    for (let [key, value] of fd.entries()) {
      if (key === 'file') {
        console.log(`${key}:`, {
          name: value.name,
          size: value.size,
          type: value.type,
          lastModified: value.lastModified
        });
      } else {
        console.log(`${key}:`, value);
      }
    }

    // 发送请求
    console.log('🚀 发送发布请求...');
    const res = await PublishArticleApi(fd);
    console.log('📨 发布响应:', res);

    if (res.code === 200) {
      ElMessage.success("文章发布成功！");

      // 清空表单
      article.value = {
        title: "",
        category: "",
        tags: [],
        content: ""
      };
      file.value = null;
      previewUrl.value = '';

      // 跳转到首页
      setTimeout(() => router.push("/index/home"), 1500);
    } else {
      ElMessage.error(res.message || "发布失败");
      console.error('发布失败详情:', res);
    }
  } catch (error) {
    console.error('❌ 发布失败:', error);

    // 详细的错误信息
    let errorMessage = "发布失败";
    if (error.response) {
      console.error('响应错误:', error.response.data);
      errorMessage = error.response.data?.message || error.response.data?.error || "服务器错误";
    } else if (error.request) {
      console.error('请求错误:', error.request);
      errorMessage = "网络请求失败";
    } else {
      console.error('其他错误:', error.message);
      errorMessage = error.message;
    }

    ElMessage.error(errorMessage);
  } finally {
    publishing.value = false;
  }
};

// 生命周期
onMounted(() => {
  checkAIStatus();
});

// 监听内容变化
watch(() => article.value.content, analyzeContent, { immediate: true });
</script>

<style scoped>
/* 页面容器 */
.smart-editor-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  font-family: var(--font-family-sans);
}

/* AI状态区域 */
.ai-status-section {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-md) 0;
}

.status-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.ai-status-bar {
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all var(--transition-normal);
}

.ai-status-bar.ai-ready {
  border-left: 4px solid var(--success-color);
  background: rgba(16, 185, 129, 0.05);
}

.status-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.status-icon {
  font-size: var(--text-lg);
  color: var(--primary-color);
}

.status-text {
  font-size: var(--text-sm);
}

.smart-tips {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.tips-label {
  font-weight: var(--font-medium);
}

.tips-buttons {
  display: flex;
  gap: var(--spacing-xs);
}

.tip-btn {
  font-size: var(--text-xs);
}

/* 编辑器容器 */
.editor-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

.editor-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

/* 区域标题 */
.section-header {
  margin-bottom: var(--spacing-md);
}

.section-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 标题区域 */
.title-section {
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
}

.title-input-wrapper {
  margin-bottom: var(--spacing-md);
}

.title-input {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
}

.title-input :deep(.el-input__wrapper) {
  border: none;
  border-bottom: 2px solid var(--border-light);
  border-radius: 0;
  padding: var(--spacing-md) 0;
  box-shadow: none;
}

.title-input :deep(.el-input__wrapper:focus) {
  border-bottom-color: var(--primary-color);
}

.title-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.title-suggestions {
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  border: 1px solid var(--border-light);
}

.suggestions-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.suggestions-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.suggestion-tag {
  cursor: pointer;
  transition: all var(--transition-normal);
}

.suggestion-tag:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* 元信息区域 */
.meta-section {
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-tertiary);
}

.meta-form {
  margin-top: var(--spacing-md);
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.form-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.form-select {
  width: 100%;
}

.ai-btn {
  width: 100%;
}

/* 智能工具栏 */
.smart-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-tertiary);
}

.content-stats {
  display: flex;
  gap: var(--spacing-md);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.content-stats span {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  font-weight: var(--font-medium);
}

/* 编辑器包装器 */
.editor-wrapper {
  margin: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-md);
  overflow: hidden;
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
}

/* 封面区域 */
.cover-section {
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
}

.cover-uploader .cover-preview {
  position: relative;
  width: 200px;
  height: 120px;
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.cover-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-normal);
  color: white;
}

.cover-overlay:hover {
  opacity: 1;
}

.cover-placeholder {
  width: 200px;
  height: 120px;
  border: 2px dashed var(--border-medium);
  border-radius: var(--radius-md);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  color: var(--text-tertiary);
}

.cover-placeholder:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.cover-placeholder .el-icon {
  font-size: 32px;
  margin-bottom: var(--spacing-xs);
}

/* 操作区域 */
.action-section {
  padding: var(--spacing-xl);
  border-top: 1px solid var(--border-light);
  background: var(--bg-tertiary);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.action-btn {
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  min-width: 120px;
}

/* 对话框内容 */
.dialog-content .dialog-tip {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.title-list .title-item {
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  border: 1px solid var(--border-light);
}

.title-item:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.optimize-result .result-text,
.outline-result .result-text,
.content-result .result-text {
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  line-height: var(--leading-relaxed);
  white-space: pre-wrap;
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: var(--spacing-md);
  border-left: 4px solid var(--primary-color);
  border: 1px solid var(--border-light);
}

.content-options {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-container,
  .editor-container {
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }

  .ai-status-bar {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .smart-tips {
    width: 100%;
    justify-content: flex-start;
  }

  .title-section,
  .meta-section,
  .cover-section,
  .action-section {
    padding: var(--spacing-lg);
  }

  .smart-toolbar {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .content-stats {
    width: 100%;
    justify-content: space-between;
  }

  .editor-wrapper {
    margin: var(--spacing-md);
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
  }

  .form-item .ai-btn {
    margin-top: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .status-container,
  .editor-container {
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
  }

  .title-section,
  .meta-section,
  .cover-section,
  .action-section {
    padding: var(--spacing-md);
  }

  .title-input {
    font-size: var(--text-xl);
  }

  .suggestions-list {
    flex-direction: column;
  }

  .content-stats {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .cover-placeholder {
    width: 100%;
    height: 100px;
  }
}
</style>
