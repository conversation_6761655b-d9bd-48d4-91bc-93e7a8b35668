const fs = require("fs");
const path = require("path");
const Router = require("koa-router");
const mime = require("mime-types");
const { handleResponse } = require("../../middlewares/responseHandler");
const { promisify } = require("util");
const exec = promisify(require("child_process").exec);
const { Worker } = require('worker_threads');
const mediaService = require("../../utils/sql/mediaService");

const videoRouter = new Router();
const videoDir = path.join(__dirname, "../../public/media");
const coverDir = path.join(videoDir, "covers");
const ffmpegPath = "D:\\ffmpeg-7.0.2-full_build\\bin\\ffmpeg.exe"; // 更新为你的路径

// 确保封面目录存在
fs.promises.mkdir(coverDir, { recursive: true }).catch(console.error);

// 判断是否为支持的媒体文件（视频、音频、图片）
const isMediaFile = (file) => {
  const ext = path.extname(file).toLowerCase();
  const videoExts = [".mp4", ".avi", ".mkv", ".mov", ".webm"];
  const audioExts = [".mp3", ".wav", ".flac", ".aac"];
  const imageExts = [".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp"];
  return [...videoExts, ...audioExts, ...imageExts].includes(ext);
};

// 判断是否为视频文件（用于生成封面）
const isVideoFile = (file) => {
  const ext = path.extname(file).toLowerCase();
  return [".mp4", ".avi", ".mkv", ".mov", ".webm"].includes(ext);
};

// 判断是否为图片文件
const isImageFile = (file) => {
  const ext = path.extname(file).toLowerCase();
  return [".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp"].includes(ext);
};

// 列出视频文件（只显示已上架的视频）
videoRouter.get("/list", async (ctx) => {
  try {
    const files = await fs.promises.readdir(videoDir);
    const videoFiles = files.filter(isVideoFile);

    // 过滤掉已下架的视频
    const onlineVideos = [];
    for (const file of videoFiles) {
      try {
        // 检查视频管理状态
        const status = await mediaService.getVideoStatus(file);

        if (status === 'online') {
          onlineVideos.push({ file_name: file });
        }
      } catch (error) {
        console.log(`检查视频 ${file} 状态失败:`, error.message);
        // 如果检查失败，默认显示（向后兼容）
        onlineVideos.push({ file_name: file });
      }
    }

    console.log(`📺 视频列表: 总计 ${videoFiles.length} 个，已上架 ${onlineVideos.length} 个`);
    ctx.body = { code: 200, data: { media_list: onlineVideos } };
  } catch (err) {
    console.error("读取目录失败:", err);
    ctx.status = 500;
    ctx.body = { code: 500, message: "服务器内部错误", error: err.message };
  }
});

// 视频流接口 (支持 Range 请求)
videoRouter.get("/videos/:filename", async (ctx) => {
  const videoPath = path.join(videoDir, ctx.params.filename);
  if (!fs.existsSync(videoPath)) {
    return handleResponse(ctx, 404, { error: "视频文件不存在" });
  }

  try {
    const stat = await fs.promises.stat(videoPath);
    const fileSize = stat.size;
    const range = ctx.headers.range;
    const mimeType = mime.lookup(videoPath) || "application/octet-stream";

    let start = 0,
      end = fileSize - 1;
    let status = 200;
    const headers = {
      "Content-Type": mimeType,
      "Accept-Ranges": "bytes",
      "Content-Length": fileSize,
    };

    if (range) {
      const [startStr, endStr] = range.replace(/bytes=/, "").split("-");
      start = parseInt(startStr, 10);
      end = endStr ? parseInt(endStr, 10) : end;
      if (isNaN(start) || isNaN(end) || start > end) {
        return handleResponse(ctx, 416, { error: "无效的范围请求" });
      }
      status = 206;
      headers["Content-Range"] = `bytes ${start}-${end}/${fileSize}`;
      headers["Content-Length"] = end - start + 1;
    }

    ctx.status = status;
    ctx.set(headers);
    ctx.body = fs.createReadStream(videoPath, { start, end });
  } catch (err) {
    console.error("视频流错误:", err);
    handleResponse(ctx, 500, { error: "视频读取失败" });
  }
});

const maxWorkers = 1; // 进一步减少Worker数量到1个
const workers = [];
let taskQueue = [];
let busyWorkers = 0;
const workerPath = path.join(__dirname, "coverWorker.js");
let workerIdleTimeout = new Map(); // Worker空闲超时管理

// 初始化worker（延迟初始化）
// for (let i = 0; i < maxWorkers; i++) startWorker();

function startWorker() {
  if (workers.length >= maxWorkers) return null;

  const worker = new Worker(workerPath);
  const workerId = workers.length;

  worker.on("message", (msg) => {
    busyWorkers--;

    // 设置Worker空闲超时（2分钟后关闭）
    workerIdleTimeout.set(workerId, setTimeout(() => {
      terminateWorker(workerId);
    }, 2 * 60 * 1000));

    processQueue();
  });

  worker.on("error", (err) => {
    busyWorkers--;
    console.error("Worker error:", err);
    terminateWorker(workerId);
    processQueue();
  });

  workers.push(worker);
  return worker;
}

function terminateWorker(workerId) {
  if (workers[workerId]) {
    workers[workerId].terminate();
    workers[workerId] = null;

    if (workerIdleTimeout.has(workerId)) {
      clearTimeout(workerIdleTimeout.get(workerId));
      workerIdleTimeout.delete(workerId);
    }

    console.log(`Worker ${workerId} 已终止`);
  }
}

function processQueue() {
  if (taskQueue.length === 0 || busyWorkers >= maxWorkers) return;

  // 查找可用的Worker或创建新的
  let worker = workers.find(w => w && w.threadId);
  if (!worker) {
    worker = startWorker();
    if (!worker) return; // 无法创建更多Worker
  }

  const task = taskQueue.shift();
  busyWorkers++;

  // 清除该Worker的空闲超时
  const workerId = workers.indexOf(worker);
  if (workerIdleTimeout.has(workerId)) {
    clearTimeout(workerIdleTimeout.get(workerId));
    workerIdleTimeout.delete(workerId);
  }

  worker.postMessage(task);
}

// 异步生成缩略图
function generateCoverAsync(videoPath, coverPath) {
  taskQueue.push({ ffmpegPath, videoPath, coverPath });
  processQueue();
}

// 优化后的 /listpic 接口 - 包含媒体功能数据
videoRouter.get("/listpic", async (ctx) => {
  try {
    const page = parseInt(ctx.query.page, 10) || 1;
    const pageSize = parseInt(ctx.query.pageSize, 10) || 12;
    const search = (ctx.query.search || '').toLowerCase();
    const category = ctx.query.category || '';
    const sortBy = ctx.query.sortBy || 'default';
    const tab = ctx.query.tab || 'all';
    const userId = ctx.query.userId ? parseInt(ctx.query.userId) : null;

    console.log(`🎬 获取媒体列表 - 页码: ${page}, 每页: ${pageSize}, 搜索: "${search}", 分类: "${category}", 排序: "${sortBy}", 标签: "${tab}", 用户: ${userId}`);

    let files = (await fs.promises.readdir(videoDir)).filter(isVideoFile);

    // 构建文件信息（包含统计数据）
    let fileInfos = await Promise.all(files.map(async (file) => {
      const filePath = path.join(videoDir, file);
      const stats = fs.statSync(filePath);

      const coverName = `${path.basename(file, path.extname(file))}.jpg`;
      const coverPath = path.join(coverDir, coverName);
      const videoPath = path.join(videoDir, file);

      // 为视频文件生成封面
      if (!fs.existsSync(coverPath)) {
        generateCoverAsync(videoPath, coverPath);
      }

      // 获取数据库中的统计信息
      let mediaStats = null;
      try {
        mediaStats = await mediaService.getMediaStats(file);
      } catch (error) {
        console.log(`获取 ${file} 统计信息失败:`, error.message);
      }

      // 检查视频管理状态（只显示已上架的视频）
      let videoStatus = 'online'; // 默认为上架状态
      try {
        videoStatus = await mediaService.getVideoStatus(file);
      } catch (error) {
        console.log(`获取 ${file} 视频管理状态失败:`, error.message);
      }

      // 如果视频已下架，跳过此视频
      if (videoStatus === 'offline') {
        return null;
      }

      // 检查用户的喜欢和收藏状态
      let isLiked = false;
      let isCollected = false;
      if (userId) {
        try {
          isLiked = await mediaService.checkUserLike(userId, file);
          isCollected = await mediaService.checkUserCollection(userId, file);
        } catch (error) {
          console.log(`获取用户状态失败:`, error.message);
        }
      }

      return {
        file_name: file,
        size: stats.size,
        cover: `/media/covers/${coverName}`,
        upload_time: stats.mtime.getTime(),
        play_count: mediaStats?.play_count || 0,
        like_count: mediaStats?.like_count || 0,
        collect_count: mediaStats?.collect_count || 0,
        duration: mediaStats?.duration || null,
        category: mediaStats?.category || '其他',
        resolution: mediaStats?.resolution || null,
        codec: mediaStats?.codec || null,
        is_liked: isLiked,
        is_collected: isCollected
      };
    }));

    // 过滤掉已下架的视频（null值）
    fileInfos = fileInfos.filter(file => file !== null);
    console.log(`📺 过滤下架视频后剩余 ${fileInfos.length} 个文件`);

    // 搜索过滤
    if (search) {
      fileInfos = fileInfos.filter(file =>
        file.file_name.toLowerCase().includes(search)
      );
      console.log(`🔍 搜索 "${search}" 后剩余 ${fileInfos.length} 个文件`);
    }

    // 分类过滤
    if (category) {
      fileInfos = fileInfos.filter(file => file.category === category);
    }

    // 根据标签页筛选
    if (tab === "liked" && userId) {
      fileInfos = fileInfos.filter(file => file.is_liked);
    } else if (tab === "collected" && userId) {
      fileInfos = fileInfos.filter(file => file.is_collected);
    } else if (tab === "latest") {
      // 最新视频：按上传时间排序，取最近30天的
      const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
      fileInfos = fileInfos.filter(file => file.upload_time > thirtyDaysAgo);
    }

    // 排序
    switch (sortBy) {
      case "newest":
        fileInfos.sort((a, b) => b.upload_time - a.upload_time);
        break;
      case "popular":
        fileInfos.sort((a, b) => b.play_count - a.play_count);
        break;
      case "size":
        fileInfos.sort((a, b) => b.size - a.size);
        break;
      default:
        // 默认排序：综合考虑上传时间和播放次数
        fileInfos.sort((a, b) => {
          const scoreA = a.play_count * 0.3 + (a.upload_time / 1000000) * 0.7;
          const scoreB = b.play_count * 0.3 + (b.upload_time / 1000000) * 0.7;
          return scoreB - scoreA;
        });
        break;
    }

    // 分页
    const total = fileInfos.length;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const pageFiles = fileInfos.slice(start, end);

    console.log(`📄 分页结果: ${pageFiles.length} 个文件 (第${page}页)`);

    ctx.body = {
      code: 200,
      data: {
        media_list: pageFiles,
        total,
        page,
        pageSize
      }
    };
  } catch (err) {
    console.error("封面列表接口错误:", err);
    ctx.status = 500;
    ctx.body = { code: 500, message: "服务器内部错误", error: err.message };
  }
});

module.exports = videoRouter;
