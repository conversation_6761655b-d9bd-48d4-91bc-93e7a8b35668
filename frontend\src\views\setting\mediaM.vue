<template>
    <div class="media-manager">
        <el-card>
            <h2>媒体文件管理</h2>
            <el-table :data="fileList" style="width: 100%">
                <el-table-column prop="name" label="文件名" />
                <el-table-column prop="path" label="路径" />
                <el-table-column label="操作" width="220">
                    <template #default="scope">
                        <el-button size="small" @click="openRename(scope.row)">重命名</el-button>
                        <el-button size="small" @click="openMove(scope.row)">移动</el-button>
                        <el-button size="small" type="danger" @click="deleteFile(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>

        <!-- 重命名对话框 -->
        <el-dialog v-model="renameDialog.visible" title="重命名文件">
            <el-input v-model="renameDialog.newName" placeholder="输入新文件名" />
            <template #footer>
                <el-button @click="renameDialog.visible = false">取消</el-button>
                <el-button type="primary" @click="confirmRename">确定</el-button>
            </template>
        </el-dialog>

        <!-- 移动对话框 -->
        <el-dialog v-model="moveDialog.visible" title="移动文件">
            <el-input v-model="moveDialog.newPath" placeholder="输入目标路径" />
            <template #footer>
                <el-button @click="moveDialog.visible = false">取消</el-button>
                <el-button type="primary" @click="confirmMove">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
// 假设你有这些API
import { getMediaFiles, renameFile, moveFile, deleteFileApi } from "@/utils/api";

const fileList = ref([]);
const renameDialog = ref({
    visible: false,
    row: null,
    newName: ""
});
const moveDialog = ref({
    visible: false,
    row: null,
    newPath: ""
});

// 获取文件列表
const fetchFiles = async () => {
    // const res = await getMediaFiles();
    // fileList.value = res.data;
    // 示例数据
    fileList.value = [
        { name: "video1.mp4", path: "/media/video1.mp4" },
        { name: "image1.jpg", path: "/media/image1.jpg" }
    ];
};

onMounted(fetchFiles);

// 打开重命名对话框
const openRename = (row) => {
    renameDialog.value.visible = true;
    renameDialog.value.row = row;
    renameDialog.value.newName = row.name;
};

// 确认重命名
const confirmRename = async () => {
    const { row, newName } = renameDialog.value;
    if (!newName || newName === row.name) {
        ElMessage.warning("请输入新的文件名");
        return;
    }
    // await renameFile(row.path, newName);
    ElMessage.success("重命名成功（示例）");
    row.name = newName;
    renameDialog.value.visible = false;
};

// 打开移动对话框
const openMove = (row) => {
    moveDialog.value.visible = true;
    moveDialog.value.row = row;
    moveDialog.value.newPath = "";
};

// 确认移动
const confirmMove = async () => {
    const { row, newPath } = moveDialog.value;
    if (!newPath) {
        ElMessage.warning("请输入目标路径");
        return;
    }
    // await moveFile(row.path, newPath);
    ElMessage.success("移动成功（示例）");
    row.path = newPath + "/" + row.name;
    moveDialog.value.visible = false;
};

// 删除文件
const deleteFile = async (row) => {
    // await deleteFileApi(row.path);
    ElMessage.success("删除成功（示例）");
    fileList.value = fileList.value.filter(f => f !== row);
};
</script>

<style scoped>
.media-manager {
    max-width: 800px;
    margin: 0 auto;
    padding: 24px 0;
}
</style>