<template>
  <div class="blog-container">
    <!-- 头部导航栏 -->
    <el-header class="header" style="display: flex; justify-content: space-between; align-items: center">
      <el-menu mode="horizontal" background-color="#545c64" text-color="#fff" active-text-color="#ffd04b" class="menu"
        :default-active="activeIndex">
        <el-menu-item index="1">首页</el-menu-item>
        <el-menu-item index="2">
          <router-link to="/index/allarticles">文章</router-link>
        </el-menu-item>
        <el-menu-item index="3">
          <router-link to="/index/resource">资源</router-link>
        </el-menu-item>
        <el-menu-item index="9">
          <router-link to="/index/chat">聊天室</router-link>
        </el-menu-item>
        <!-- 折叠日志监控和上传文件 -->
        <el-sub-menu index="group1">
          <template #title>更多</template>
          <el-menu-item index="7">
            <router-link to="/index/edit">写文章</router-link>
          </el-menu-item>
          <el-menu-item index="8">
            <router-link to="/index/model">ai大模型</router-link>
          </el-menu-item>
          <el-menu-item index="9">
            <router-link to="/index/chat">聊天室</router-link>
          </el-menu-item>
          <el-menu-item index="10" @click="handlePhotoWallClick">
            照片墙
          </el-menu-item>
          <el-menu-item index="11">
            <router-link to="/index/pagman">文章管理</router-link>
          </el-menu-item>
          <el-menu-item index="12">
            <router-link to="/index/logs">日志监控</router-link>
          </el-menu-item>
          <el-menu-item index="13">
            <router-link to="/index/uploads">上传文件</router-link>
          </el-menu-item>
          <el-menu-item index="14">
            <router-link to="/index/rtc">通话连连</router-link>
          </el-menu-item>
          <el-menu-item index="15">
            <router-link to="/index/bug">bug管理</router-link>
          </el-menu-item>
          <el-menu-item index="15">
            <router-link to="/index/create">创意中心</router-link>
          </el-menu-item>
        </el-sub-menu>
      </el-menu>

      <!-- 退出按钮单独放右边 -->
      <el-menu mode="horizontal" background-color="#545c64" text-color="#fff" active-text-color="#ffd04b" class="menu">
        <el-menu-item index="6" @click="logout">退出</el-menu-item>
      </el-menu>
    </el-header>

    <!-- 移动端菜单按钮 -->
    <el-button class="mobile-menu-btn" icon="Menu" @click="drawerVisible = true" v-if="isMobile" circle />
    <el-drawer v-model="drawerVisible" direction="ltr" size="70%" :with-header="false">
      <el-menu mode="vertical" background-color="#545c64" text-color="#fff" active-text-color="#ffd04b"
        :default-active="activeIndex" @select="drawerVisible = false">
        <el-menu-item index="1">首页</el-menu-item>
        <el-menu-item index="2">
          <router-link to="/index/allarticles">文章</router-link>
        </el-menu-item>
        <el-menu-item index="3">
          <router-link to="/index/resource">资源</router-link>
        </el-menu-item>
        <el-menu-item index="9">
          <router-link to="/index/chat">聊天室</router-link>
        </el-menu-item>
        <el-menu-item index="7">
          <router-link to="/index/edit">写文章</router-link>
        </el-menu-item>
        <el-sub-menu index="group1">
          <template #title>更多</template>

          <el-menu-item index="8">
            <router-link to="/index/model">ai大模型</router-link>
          </el-menu-item>

          <el-menu-item index="10" @click="handlePhotoWallClick">
            照片墙
          </el-menu-item>
          <el-menu-item index="11">
            <router-link to="/index/pagman">文章管理</router-link>
          </el-menu-item>
          <el-menu-item index="12">
            <router-link to="/index/logs">日志监控</router-link>
          </el-menu-item>
          <el-menu-item index="13">
            <router-link to="/index/uploads">上传文件</router-link>
          </el-menu-item>
          <el-menu-item index="14">
            <router-link to="/index/rtc">通话连接</router-link>
          </el-menu-item>
          <el-menu-item index="15">
            <router-link to="/index/bug">bug管理</router-link>
          </el-menu-item>
          <el-menu-item index="15">
            <router-link to="/index/create">创意中心</router-link>
          </el-menu-item>
        </el-sub-menu>
        <el-menu-item index="6" @click="logout">退出</el-menu-item>
      </el-menu>
    </el-drawer>

    <!-- 主要内容区 -->
    <el-main class="main-content">
      <!-- 个人信息卡片 -->
      <el-row :gutter="20">
        <el-col :span="isMobile ? 24 : 6">
          <el-card class="profile-card">
            <div class="avatar-container">
              <el-avatar :size="isMobile ? 80 : 120" :src="imgurl" />
            </div>
            <h2>{{ userInfo.username }}</h2>
            <p class="title">{{ userInfo.address }}</p>
            <p class="location">{{ userInfo.position }}</p>

            <div class="social-links">
              <el-link :href="userInfo.github" target="_blank" type="primary">
                <el-icon><i-ep-Github /></el-icon> GitHub
              </el-link>
              <el-link :href="userInfo.weibo" target="_blank" type="danger">
                <el-icon><i-ep-Postcard /></el-icon> 微博
              </el-link>
            </div>

            <el-divider />

            <div class="tech-stack">
              <h3>技术栈</h3>
              <el-tag v-for="tech in techStack" :key="tech" :type="getRandomTagType()" class="tech-tag">
                {{ tech }}
              </el-tag>
            </div>
          </el-card>
        </el-col>

        <el-col :span="isMobile ? 24 : 18">
          <!-- 个人简介 -->
          <el-card class="intro-card">
            <h2>个人简介</h2>
            <p>{{ userInfo.intro }}</p>

            <el-divider />

            <!-- 最新文章 -->
            <h3>最新文章</h3>
            <el-timeline>
              <el-timeline-item v-for="(article, index) in latestArticles" :key="index" :timestamp="article.created_at"
                placement="top">
                <el-card>
                  <h4>{{ article.title }}</h4>
                  <p>{{ article.summary }}</p>
                  <el-button type="text" @click="viewArticle(article.id)">阅读全文</el-button>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </el-card>
        </el-col>
      </el-row>
    </el-main>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import {
  ShowArticlesByPageApi,
  ShowArticlesApi,
  GetProfileApi,
  GetTagsApi,
} from "../utils/api";
import { useRouter } from "vue-router";

const router = useRouter();

// 用户信息
const userInfo = ref({
  github: "https://github.com/yourusername",
  weibo: "https://weibo.com/yourusername",
});


// 技术栈
const techStack = ref([]);

// 最新文章
const latestArticles = ref([]);

const params = ref({
  user_id: localStorage.getItem("id"),
  page: 1,
  limit: 100,
});

// 获取文章 ShowArticlesApi()
const getArticles = async () => {
  const res = await ShowArticlesByPageApi({ ...params.value });
  // 按 created_at 降序排序
  latestArticles.value = (res.data || []).sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
  console.log(latestArticles.value);
};

// 获取个人信息
const id = localStorage.getItem("id");
const imgurl = ref("");
const getProfile = async () => {
  const res = await GetProfileApi({ id });
  console.log(res);
  userInfo.value = res.user;
  console.log(userInfo.value.username);
  imgurl.value = `http://192.168.31.222:3000/avatars/${res.user.avatar}`;
  // tech_tags: "[\"Vue.js\", \"React\", \"Node.js\"]" 存入techStack
  techStack.value = JSON.parse(userInfo.value.tech_tags);
};

// 随机获取tag类型
const getRandomTagType = () => {
  const types = ["", "success", "info", "warning", "danger"];
  return types[Math.floor(Math.random() * types.length)];
};

const viewArticle = (id) => {
  `查看文章 ${id}`;
  // 实际项目中这里可以跳转到文章详情页
  router.push({ name: "Details", params: { id } });
};

const logout = () => {
  if (confirm("确定退出吗？")) {
    localStorage.removeItem("id");
    localStorage.removeItem("token");
    localStorage.removeItem("username");
    localStorage.removeItem("media_verified");
    router.push("/login");
  }
};

import { ElMessageBox, ElMessage } from "element-plus";

const correctPassword = "123456"; // 你自己定义的密码

const handlePhotoWallClick = async () => {
  try {
    const { value } = await ElMessageBox.prompt(
      "请输入访问照片墙的密码",
      "身份验证",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputType: "password",
      }
    );
    if (value === correctPassword) {
      router.push("/index/images");
    } else {
      ElMessage.error("密码错误，无法访问照片墙");
    }
  } catch (error) {
    // 用户取消输入，不做处理
  }
};

const isMobile = ref(window.innerWidth <= 768);
const drawerVisible = ref(false);

const handleResize = () => {
  isMobile.value = window.innerWidth <= 768;
};

onMounted(() => {
  window.addEventListener("resize", handleResize);
  handleResize();
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
});

onMounted(() => {
  getArticles();
  getProfile();
});
</script>

<style scoped lang="less">
@media (max-width: 768px) {
  .header .menu {
    display: none !important;
  }

  .mobile-menu-btn {
    display: inline-block !important;
    margin-right: 10px;
    background: #545c64;
    color: #fff;
    border: none;
  }

  .main-content {
    padding: 12px 2px;

    .profile-card {
      padding: 12px 6px;
      border-radius: 8px;
      margin-bottom: 16px;

      .avatar-container {
        margin-bottom: 10px;

        .el-avatar {
          width: 80px !important;
          height: 80px !important;
        }
      }

      h2 {
        font-size: 18px;
        margin: 8px 0;
      }

      .title,
      .location {
        font-size: 13px;
        margin: 2px 0;
      }

      .social-links {
        flex-direction: column;
        gap: 4px;

        .el-link {
          font-size: 14px;
        }
      }

      .tech-stack {
        margin-top: 8px;

        .tech-tag {
          margin: 2px;
          font-size: 12px;
        }
      }
    }

    .intro-card {
      padding: 12px 6px;
      border-radius: 8px;

      h2,
      h3 {
        font-size: 16px;
        margin-bottom: 10px;
      }

      p {
        font-size: 14px;
      }

      .el-timeline-item {
        .el-card {
          padding: 10px;

          h4 {
            font-size: 15px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 160px;
          }

          p {
            font-size: 12px;
            color: #888;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 160px;
            margin-bottom: 6px;
          }

          .el-button {
            font-size: 13px;
          }
        }
      }
    }
  }
}

@media (min-width: 769px) {
  .mobile-menu-btn {
    display: none !important;
  }
}

.blog-container {
  font-family: "Helvetica Neue", Arial, sans-serif;
  background-color: #f5f7fa;
  margin-top: 30px;
  min-height: 100vh;

  .header {
    background-color: #303133;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background-color: #303133;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

    display: flex;
    justify-content: space-between;
    align-items: center;

    .menu {
      background-color: transparent;
      border-bottom: none;

      .el-menu-item {
        font-size: 16px;
        padding: 0 20px;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }
      }
    }
  }

  .mobile-menu-btn {
    position: fixed;
    top: 16px;
    right: 16px;
    z-index: 1100;
  }

  .main-content {
    padding: 40px 20px;
    max-width: 1200px;
    margin: 0 auto;

    .profile-card {
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      text-align: center;

      .avatar-container {
        margin-bottom: 16px;
      }

      h2 {
        font-size: 22px;
        margin: 10px 0;
        color: #303133;
      }

      .title,
      .location {
        color: #909399;
        margin: 4px 0;
      }

      .social-links {
        display: flex;
        justify-content: space-around;
        margin-top: 12px;

        .el-link {
          display: flex;
          align-items: center;
          gap: 5px;
        }
      }

      .tech-stack {
        margin-top: 12px;

        .tech-tag {
          margin: 4px;
        }
      }
    }

    .intro-card {
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

      h2,
      h3 {
        color: #303133;
        margin-bottom: 16px;
      }

      p {
        line-height: 1.6;
        color: #606266;
      }

      .el-timeline-item {
        .el-card {
          background-color: #fff;
          border-radius: 8px;
          padding: 16px;

          h4 {
            font-size: 18px;
            margin-bottom: 8px;
            color: #409eff;
          }

          p {
            color: #606266;
            font-size: 14px;
            margin-bottom: 8px;
          }

          .el-button {
            padding: 0;
            font-size: 14px;
            color: #409eff;
          }
        }
      }
    }
  }
}
</style>
