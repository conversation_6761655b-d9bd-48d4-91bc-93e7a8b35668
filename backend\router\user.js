const Router = require("koa-router");
const user = new Router();
const bodyParser = require("koa-bodyparser");
const auth = require("../utils/sql/authService");
const { handleResponse } = require("./responseHandler");
const { generateToken } = require("../utils/jwtUtils");
const { validateUsername, validatePassword } = require("../plugin/validators"); // 引入验证函数
const fs = require("fs");
const path = require("path");
const { console } = require("inspector");

user.use(bodyParser());

// 验证用户名和密码
const validateCredentials = (username, password) => {
  if (!validateUsername(username)) {
    return { valid: false, message: "用户名格式不正确" };
  }
  if (!validatePassword(password)) {
    return { valid: false, message: "密码格式不正确" };
  }
  return { valid: true };
};

// 登录接口
user.post("/login", async (ctx) => {
  try {
    const { username, password } = ctx.request.body;

    // 输入验证
    const validation = validateCredentials(username, password);
    if (!validation.valid) {
      return handleResponse(ctx, 400, { error: validation.message });
    }

    const loginResult = await auth.login(username, password);
    if (!loginResult.success) {
      logger.warn(`登录失败: 用户 ${username} 认证失败 - IP: ${ctx.ip}`);
      return handleResponse(ctx, 401, { error: "用户名或密码错误" });
    }

    const user = { id: loginResult.id, username: loginResult.username };
    const token = generateToken(user);

    return handleResponse(ctx, 200, {
      token,
      user: { username: user.username, id: user.id, role: user.role },
    });
  } catch (error) {
    logger.error(`登录接口出错: ${error.message}`, {
      method: ctx.method,
      url: ctx.url,
      ip: ctx.ip,
      stack: error.stack,
    });
    return handleResponse(ctx, 500, { error: "服务器内部错误" });
  }
});

// 注册接口

user.post("/register", async (ctx) => {
  const { username, email, password } = ctx.request.body;
  console.log(username, email, password);

  // 输入验证
  const validation = validateCredentials(username, password);
  if (!validation.valid) {
    return handleResponse(ctx, 400, { error: validation.message });
  }

  // 执行注册
  const result = await auth.register(
    encodeURIComponent(username),
    email,
    password
  );

  if (result.success) {
    return handleResponse(ctx, 200, {
      userId: result.userId,
      message: "注册成功",
    });
  } else {
    return handleResponse(ctx, 400, { error: result.message });
  }
});

// 修改密码接口
user.post("/changePassword", async (ctx) => {
  const { userId, oldPassword, newPassword } = ctx.request.body;
  const user = await auth.changePassword(userId, oldPassword, newPassword);
  if (user.success) {
    // 更新 token
    const token = generateToken(user.user);
    return handleResponse(ctx, 200, { token, user: user.user });
  } else {
    return handleResponse(ctx, 400, { error: user.message });
  }
});

// 退出登录接口
user.post("/logout", async (ctx) => {
  // 处理 token
  const token = ctx.request.header.authorization.split(" ")[1];
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    const user = { id: decoded.id, username: decoded.username };

    return handleResponse(ctx, 200, { message: "退出登录成功" });
  } catch (error) {
    return handleResponse(ctx, 401, { error: "退出登录失败" });
  }
});

// 获取个人信息接口 通过id获取用户信息 /user/profile
user.post("/profile", async (ctx) => {
  const { id } = ctx.request.body;
  console.log(id);
  const user = await auth.getUserInfo(id);
  console.log(user);
  if (user.success) {
    return handleResponse(ctx, 200, { user: user.user });
  } else {
    return handleResponse(ctx, 400, { error: user.message });
  }
});

// 更新用户信息接口updateUserInfo() 参数:
user.post("/updateUser", async (ctx) => {
  const { id, username, email, avatar, address, position, intro, tech_tags } =
    ctx.request.body;
  let avatarFilename = avatar; // 默认用前端传来的avatar字段

  // 头像上传处理（如果有文件则保存新头像，只存文件名）
  if (ctx.request.files && ctx.request.files.file) {
    const file = ctx.request.files.file;
    const uploadDir = path.join(__dirname, "../uploads/avatars");
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    avatarFilename = `avatar_${id}_${Date.now()}${path.extname(file.name)}`;
    const filePath = path.join(uploadDir, avatarFilename);
    const reader = fs.createReadStream(file.path);
    const stream = fs.createWriteStream(filePath);
    reader.pipe(stream);
    await new Promise((resolve, reject) => {
      stream.on("finish", resolve);
      stream.on("error", reject);
    });

    // 删除旧头像（如果有且不是默认头像）
    if (avatar && avatar !== avatarFilename && avatar !== "moren.png") {
      const oldAvatarPath = path.join(uploadDir, avatar);
      if (fs.existsSync(oldAvatarPath)) {
        try {
          fs.unlinkSync(oldAvatarPath);
        } catch (err) {}
      }
    }
  }

  // 调用authService更新用户信息
  const result = await auth.updateUserInfo(
    id,
    username,
    email,
    avatarFilename, // 只存文件名
    address,
    position,
    intro,
    tech_tags
  );

  if (result.success) {
    // 返回新头像文件名，便于前端更新
    return handleResponse(ctx, 200, {
      user: result.user,
      avatar: avatarFilename,
    });
  } else {
    return handleResponse(ctx, 400, { error: result.message });
  }
});

// tech-tags 接口
user.get("/tech-tags", async (ctx) => {
  const tags = await auth.tags();
  if (tags.success) {
    return handleResponse(ctx, 200, { tags: tags.data });
  }
  return handleResponse(ctx, 500, { error: "获取技术标签列表失败" });
});

module.exports = user;
