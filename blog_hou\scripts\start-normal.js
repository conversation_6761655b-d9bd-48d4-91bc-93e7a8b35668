#!/usr/bin/env node

/**
 * 正常启动脚本
 * 使用合理的内存设置，避免过度限制
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动正常模式...');

// 合理的Node.js参数
const nodeArgs = [
  // 基础优化
  '--expose-gc',                    // 暴露gc()函数（可选）
  '--max-old-space-size=1024',      // 设置合理的内存限制（1GB）
  
  // 其他优化
  '--no-warnings',                  // 减少警告输出
];

// 环境变量
const env = {
  ...process.env,
  // 正常的缓存设置
  CACHE_MAX_ENTRIES: '1000',
  CACHE_MAX_MEMORY: '100MB',
  // 启用内存监控但不过于激进
  MEMORY_MONITORING: 'true'
};

console.log('📊 Node.js 参数:', nodeArgs.join(' '));

// 启动应用
const appPath = path.join(__dirname, '..', 'index.js');
const child = spawn('node', [...nodeArgs, appPath], {
  stdio: 'inherit',
  env: env,
  cwd: path.dirname(__dirname)
});

// 错误处理
child.on('error', (error) => {
  console.error('❌ 启动失败:', error.message);
  process.exit(1);
});

child.on('exit', (code, signal) => {
  if (code === 0) {
    console.log('✅ 进程正常退出');
  } else {
    console.log(`❌ 进程异常退出: code=${code}, signal=${signal}`);
  }
  process.exit(code || 0);
});

// 优雅退出
process.on('SIGINT', () => {
  console.log('\n🛑 收到退出信号，正在关闭...');
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 收到终止信号，正在关闭...');
  child.kill('SIGTERM');
});
