// 全局特效配置管理
export class EffectConfig {
  constructor() {
    this.storageKey = 'globalEffectSettings'
    this.defaultSettings = {
      enabled: true,
      currentEffect: 'none',
      autoSwitch: false,
      switchInterval: 30000, // 30秒
      effects: {
        meteor: {
          count: 25,
          speed: 4,
          colorStart: '#ffffff',
          colorEnd: '#00ffff'
        },
        nightsky: {
          starCount: 150,
          meteorCount: 20
        },
        particle: {
          particleCount: 60,
          linkDistance: 120
        }
      }
    }
  }

  // 获取当前设置
  getSettings() {
    try {
      const saved = localStorage.getItem(this.storageKey)
      if (saved) {
        return { ...this.defaultSettings, ...JSON.parse(saved) }
      }
    } catch (e) {
      console.warn('读取特效设置失败:', e)
    }
    return { ...this.defaultSettings }
  }

  // 保存设置
  saveSettings(settings) {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(settings))
      return true
    } catch (e) {
      console.error('保存特效设置失败:', e)
      return false
    }
  }

  // 获取当前特效类型
  getCurrentEffect() {
    return this.getSettings().currentEffect
  }

  // 设置当前特效
  setCurrentEffect(effect) {
    const settings = this.getSettings()
    settings.currentEffect = effect
    this.saveSettings(settings)
  }

  // 获取特效配置
  getEffectConfig(effectType) {
    const settings = this.getSettings()
    return settings.effects[effectType] || {}
  }

  // 更新特效配置
  updateEffectConfig(effectType, config) {
    const settings = this.getSettings()
    settings.effects[effectType] = { ...settings.effects[effectType], ...config }
    this.saveSettings(settings)
  }

  // 重置为默认设置
  reset() {
    localStorage.removeItem(this.storageKey)
  }

  // 检查是否启用特效
  isEnabled() {
    return this.getSettings().enabled
  }

  // 启用/禁用特效
  setEnabled(enabled) {
    const settings = this.getSettings()
    settings.enabled = enabled
    this.saveSettings(settings)
  }
}

// 创建全局实例
export const effectConfig = new EffectConfig()

// 特效类型定义
export const EFFECT_TYPES = {
  NONE: 'none',
  METEOR: 'meteor',
  NIGHTSKY: 'nightsky',
  PARTICLE: 'particle'
}

// 特效名称映射
export const EFFECT_NAMES = {
  [EFFECT_TYPES.NONE]: '无特效',
  [EFFECT_TYPES.METEOR]: '流星雨',
  [EFFECT_TYPES.NIGHTSKY]: '夜空星辰',
  [EFFECT_TYPES.PARTICLE]: '粒子连线'
}

// 特效描述
export const EFFECT_DESCRIPTIONS = {
  [EFFECT_TYPES.NONE]: '关闭所有页面特效',
  [EFFECT_TYPES.METEOR]: '美丽的流星雨划过天空',
  [EFFECT_TYPES.NIGHTSKY]: '璀璨星空与流星，点击产生爆炸效果',
  [EFFECT_TYPES.PARTICLE]: '动态粒子连线，鼠标交互效果'
}

// 特效性能等级
export const EFFECT_PERFORMANCE = {
  [EFFECT_TYPES.NONE]: 0,
  [EFFECT_TYPES.METEOR]: 2,
  [EFFECT_TYPES.NIGHTSKY]: 3,
  [EFFECT_TYPES.PARTICLE]: 2
}

// 根据设备性能推荐特效
export function getRecommendedEffect() {
  const canvas = document.createElement('canvas')
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
  
  if (!gl) {
    return EFFECT_TYPES.NONE
  }

  // 检测设备性能
  const renderer = gl.getParameter(gl.RENDERER)
  const vendor = gl.getParameter(gl.VENDOR)
  
  // 移动设备检测
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  
  if (isMobile) {
    return EFFECT_TYPES.METEOR // 移动设备推荐轻量特效
  }

  // 桌面设备根据性能推荐
  if (renderer.includes('Intel')) {
    return EFFECT_TYPES.PARTICLE
  } else {
    return EFFECT_TYPES.NIGHTSKY
  }
}

// 特效切换动画
export function createEffectTransition(fromEffect, toEffect, duration = 1000) {
  return new Promise((resolve) => {
    if (fromEffect === toEffect) {
      resolve()
      return
    }

    // 创建过渡效果
    const overlay = document.createElement('div')
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0);
      z-index: 9999;
      pointer-events: none;
      transition: background ${duration / 2}ms ease-in-out;
    `
    
    document.body.appendChild(overlay)
    
    // 淡入
    setTimeout(() => {
      overlay.style.background = 'rgba(0, 0, 0, 0.3)'
    }, 10)
    
    // 切换特效
    setTimeout(() => {
      overlay.style.background = 'rgba(0, 0, 0, 0)'
    }, duration / 2)
    
    // 清理
    setTimeout(() => {
      document.body.removeChild(overlay)
      resolve()
    }, duration)
  })
}

// 性能监控
export class EffectPerformanceMonitor {
  constructor() {
    this.frameCount = 0
    this.lastTime = performance.now()
    this.fps = 60
    this.isMonitoring = false
  }

  start() {
    this.isMonitoring = true
    this.monitor()
  }

  stop() {
    this.isMonitoring = false
  }

  monitor() {
    if (!this.isMonitoring) return

    this.frameCount++
    const currentTime = performance.now()
    
    if (currentTime - this.lastTime >= 1000) {
      this.fps = this.frameCount
      this.frameCount = 0
      this.lastTime = currentTime
      
      // 如果FPS过低，建议降级特效
      if (this.fps < 30) {
        this.suggestDowngrade()
      }
    }
    
    requestAnimationFrame(() => this.monitor())
  }

  suggestDowngrade() {
    const currentEffect = effectConfig.getCurrentEffect()
    const performance = EFFECT_PERFORMANCE[currentEffect]
    
    if (performance > 1) {
      console.warn('检测到性能问题，建议使用更轻量的特效')
      // 可以在这里触发用户提示
    }
  }

  getFPS() {
    return this.fps
  }
}

export const performanceMonitor = new EffectPerformanceMonitor()
