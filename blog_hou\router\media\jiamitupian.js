const Router = require("koa-router");
const fs = require("fs");
const path = require("path");
const crypto = require("crypto");

const jiami = new Router();
const bodyParser = require("koa-bodyparser");
jiami.use(bodyParser());

const algorithm = "aes-256-cbc";
const password = "mysecret"; // 建议使用 process.env.PASSWORD
const key = crypto.createHash("sha256").update(password).digest();

jiami.post("/images", async (ctx) => {
  const { type } = ctx.request.body;
  console.log("请求类型：", type);

  const baseDir = path.join(__dirname, "../../public/images");

  const files = fs
    .readdirSync(baseDir)
    .filter((f) =>
      type === "1" ? /\.(jpg|jpeg|png)$/i.test(f) : /\.enc$/i.test(f)
    );

  try {
    for (const file of files) {
      const inputPath = path.join(baseDir, file);
      const outputFileName =
        type === "1" ? file + ".enc" : file.replace(/\.enc$/, "");
      const outputPath = path.join(baseDir, outputFileName);

      if (type === "1") {
        // 加密
        const iv = crypto.randomBytes(16);
        const readStream = fs.createReadStream(inputPath);
        const cipher = crypto.createCipheriv(algorithm, key, iv);
        const writeStream = fs.createWriteStream(outputPath);
        writeStream.write(iv);
        await new Promise((resolve, reject) => {
          readStream
            .pipe(cipher)
            .pipe(writeStream)
            .on("finish", resolve)
            .on("error", reject);
        });
        fs.unlinkSync(inputPath); // 删除原图
      } else if (type === "0") {
        // 解密
        const inputBuffer = fs.readFileSync(inputPath);
        const iv = inputBuffer.slice(0, 16);
        const encryptedData = inputBuffer.slice(16);
        const decipher = crypto.createDecipheriv(algorithm, key, iv);
        const decrypted = Buffer.concat([
          decipher.update(encryptedData),
          decipher.final(),
        ]);
        fs.writeFileSync(outputPath, decrypted);
        fs.unlinkSync(inputPath); // 删除加密文件
      } else {
        ctx.status = 400;
        ctx.body = { error: "参数 type 必须为 1（加密）或 0（解密）" };
        return;
      }
    }

    ctx.body = {
      success: true,
      type: type === "1" ? "加密" : "解密",
      message: `${
        type === "1" ? "加密并删除原图" : "解密并删除加密文件"
      }，共处理 ${files.length} 个文件。`,
    };
  } catch (err) {
    console.error(err);
    ctx.status = 500;
    ctx.body = {
      error: `${type === "1" ? "加密" : "解密"}失败`,
      detail: err.message,
    };
  }
});

module.exports = jiami;
