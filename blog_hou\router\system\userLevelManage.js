const Router = require("koa-router");
const db = require("../../utils/db");
const verifyToken = require("../../middlewares/koaJwtMiddleware");

// 简单的响应处理函数
function handleResponse(ctx, code, data) {
  ctx.status = code;
  const responseBody = {
    success: code >= 200 && code < 300,
    code,
    message: data.message || (code >= 200 && code < 300 ? 'Success' : 'Error'),
    data: data.data || data,
    timestamp: new Date().toISOString()
  };

  // 设置响应头
  ctx.set('Content-Type', 'application/json');

  // 设置响应体
  ctx.body = responseBody;

  // 手动计算并设置响应长度
  const responseString = JSON.stringify(responseBody);
  ctx.length = Buffer.byteLength(responseString, 'utf8');

  console.log("响应处理完成:", {
    status: code,
    bodyLength: ctx.length,
    hasData: !!(data.data || data)
  });
}

const router = new Router();

// 管理员权限验证中间件
async function requireAdmin(ctx, next) {
  try {
    const userId = ctx.state.user?.userId || ctx.state.user?.id;

    console.log("管理员权限验证 - 用户ID:", userId);
    console.log("管理员权限验证 - ctx.state.user:", ctx.state.user);

    if (!userId) {
      console.log("管理员权限验证失败: 用户ID为空");
      return handleResponse(ctx, 401, { message: "未登录或token无效" });
    }

    // 检查管理员权限
    const adminResult = await db.query('SELECT role FROM users WHERE id = ?', [userId]);
    console.log("管理员权限查询结果:", adminResult);

    if (adminResult.length === 0 || adminResult[0].role !== 'admin') {
      console.log("管理员权限验证失败: 非管理员用户");
      return handleResponse(ctx, 403, { message: "权限不足，只有管理员可以访问此功能" });
    }

    console.log("管理员权限验证成功");
    await next();
  } catch (error) {
    console.error("管理员权限验证失败:", error);
    return handleResponse(ctx, 500, { message: "权限验证失败" });
  }
}

// 获取用户等级列表
router.get("/users", verifyToken, requireAdmin, async (ctx) => {
  try {
    const { page = 1, limit = 20, level, search } = ctx.query;
    const offset = (page - 1) * limit;

    console.log(`获取用户列表请求: page=${page}, limit=${limit}, level=${level}, search=${search}`);

    // 构建查询条件
    let whereClause = "WHERE 1=1";
    let params = [];

    if (level && level !== 'all') {
      whereClause += " AND u.level = ?";
      params.push(level);
    }

    if (search) {
      whereClause += " AND (u.username LIKE ? OR u.email LIKE ?)";
      params.push(`%${search}%`, `%${search}%`);
    }

    // 用户查询
    const usersQuery = `
      SELECT
        u.id,
        u.username,
        u.email,
        u.level,
        u.role,
        u.status,
        u.created_at,
        u.updated_at,
        slc.download_speed,
        slc.concurrent_downloads,
        slc.daily_download_limit,
        slc.description
      FROM users u
      LEFT JOIN speed_limit_configs slc ON u.level COLLATE utf8mb4_unicode_ci = slc.level COLLATE utf8mb4_unicode_ci
      ${whereClause}
      ORDER BY u.created_at DESC
      LIMIT ? OFFSET ?
    `;

    params.push(parseInt(limit), parseInt(offset));

    console.log("执行用户查询SQL:", usersQuery);
    console.log("查询参数:", params);

    const users = await db.query(usersQuery, params);
    console.log("用户查询结果数量:", users.length);
    console.log("用户查询结果示例:", users.slice(0, 2));

    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM users u
      ${whereClause}
    `;

    const countParams = params.slice(0, -2); // 移除 limit 和 offset
    console.log("执行计数查询SQL:", countQuery);
    console.log("计数查询参数:", countParams);

    const totalResult = await db.query(countQuery, countParams);
    const total = totalResult[0]?.total || 0;
    console.log("用户总数:", total);

    const responseData = {
      message: "获取用户列表成功",
      data: {
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    };

    console.log("返回响应数据:", JSON.stringify(responseData, null, 2));
    return handleResponse(ctx, 200, responseData);

  } catch (error) {
    console.error("获取用户列表失败:", error);
    return handleResponse(ctx, 500, { message: "获取用户列表失败" });
  }
});

// 获取等级统计信息
router.get("/level-stats", verifyToken, requireAdmin, async (ctx) => {
  try {
    // 获取各等级用户数量
    const levelStats = await db.query(`
      SELECT 
        u.level,
        COUNT(*) as user_count,
        slc.description,
        slc.download_speed,
        slc.daily_download_limit
      FROM users u
      LEFT JOIN speed_limit_configs slc ON u.level COLLATE utf8mb4_unicode_ci = slc.level COLLATE utf8mb4_unicode_ci
      GROUP BY u.level, slc.description, slc.download_speed, slc.daily_download_limit
      ORDER BY FIELD(u.level, 'free', 'basic', 'premium', 'vip')
    `);
    
    // 获取今日下载统计（如果download_logs表存在）
    let todayStats = [];
    try {
      todayStats = await db.query(`
        SELECT 
          u.level,
          COUNT(DISTINCT dl.user_id) as active_users,
          COUNT(dl.id) as download_count,
          COALESCE(SUM(dl.file_size), 0) as total_downloaded
        FROM download_logs dl
        JOIN users u ON dl.user_id = u.id
        WHERE DATE(dl.download_time) = CURDATE()
        GROUP BY u.level
      `);
    } catch (error) {
      console.log("download_logs表不存在，跳过今日统计");
    }

    // 合并统计数据
    const statsWithToday = levelStats.map(stat => {
      const todayStat = todayStats.find(t => t.level === stat.level);
      return {
        ...stat,
        today_active_users: todayStat?.active_users || 0,
        today_download_count: todayStat?.download_count || 0,
        today_total_downloaded: todayStat?.total_downloaded || 0
      };
    });

    return handleResponse(ctx, 200, {
      message: "获取等级统计成功",
      data: statsWithToday
    });

  } catch (error) {
    console.error("获取等级统计失败:", error);
    return handleResponse(ctx, 500, { message: "获取等级统计失败" });
  }
});

// 修改用户等级
router.post("/change-level", verifyToken, requireAdmin, async (ctx) => {
  try {
    const { userId, newLevel, reason } = ctx.request.body;
    const adminId = ctx.state.user?.id || ctx.state.user?.userId;
    
    if (!userId || !newLevel) {
      return handleResponse(ctx, 400, { message: "用户ID和新等级不能为空" });
    }
    
    // 验证新等级是否有效
    const validLevels = ['free', 'basic', 'premium', 'vip'];
    if (!validLevels.includes(newLevel)) {
      return handleResponse(ctx, 400, { message: "无效的用户等级" });
    }
    
    // 获取用户当前信息
    const userResult = await db.query('SELECT * FROM users WHERE id = ?', [userId]);
    if (userResult.length === 0) {
      return handleResponse(ctx, 404, { message: "用户不存在" });
    }
    
    const user = userResult[0];
    const oldLevel = user.level;
    
    if (oldLevel === newLevel) {
      return handleResponse(ctx, 400, { message: "用户等级未发生变化" });
    }
    
    // 更新用户等级
    await db.query(
      'UPDATE users SET level = ?, updated_at = NOW() WHERE id = ?',
      [newLevel, userId]
    );
    
    // 记录等级变更日志（如果表存在）
    try {
      await db.query(`
        INSERT INTO user_level_logs (
          user_id, old_level, new_level, changed_by, reason, created_at
        ) VALUES (?, ?, ?, ?, ?, NOW())
      `, [userId, oldLevel, newLevel, adminId, reason || '管理员修改']);
    } catch (error) {
      console.log("user_level_logs表不存在，跳过日志记录");
    }
    
    return handleResponse(ctx, 200, {
      message: "用户等级修改成功",
      data: {
        userId,
        username: user.username,
        oldLevel,
        newLevel,
        changedBy: adminId
      }
    });
    
  } catch (error) {
    console.error("修改用户等级失败:", error);
    return handleResponse(ctx, 500, { message: "修改用户等级失败" });
  }
});

// 批量修改用户等级
router.post("/batch-change-level", verifyToken, requireAdmin, async (ctx) => {
  try {
    const { userIds, newLevel, reason } = ctx.request.body;
    const adminId = ctx.state.user?.id || ctx.state.user?.userId;
    
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return handleResponse(ctx, 400, { message: "请选择要修改的用户" });
    }
    
    if (!newLevel) {
      return handleResponse(ctx, 400, { message: "请选择新等级" });
    }
    
    // 验证新等级是否有效
    const validLevels = ['free', 'basic', 'premium', 'vip'];
    if (!validLevels.includes(newLevel)) {
      return handleResponse(ctx, 400, { message: "无效的用户等级" });
    }
    
    // 获取要修改的用户信息
    const placeholders = userIds.map(() => '?').join(',');
    const usersResult = await db.query(
      `SELECT id, username, level FROM users WHERE id IN (${placeholders})`,
      userIds
    );
    
    if (usersResult.length === 0) {
      return handleResponse(ctx, 404, { message: "未找到要修改的用户" });
    }
    
    // 开始事务
    await db.query('START TRANSACTION');
    
    try {
      const updatedUsers = [];
      
      for (const user of usersResult) {
        if (user.level !== newLevel) {
          // 更新用户等级
          await db.query(
            'UPDATE users SET level = ?, updated_at = NOW() WHERE id = ?',
            [newLevel, user.id]
          );
          
          // 记录等级变更日志（如果表存在）
          try {
            await db.query(`
              INSERT INTO user_level_logs (
                user_id, old_level, new_level, changed_by, reason, created_at
              ) VALUES (?, ?, ?, ?, ?, NOW())
            `, [user.id, user.level, newLevel, adminId, reason || '批量修改']);
          } catch (error) {
            console.log("user_level_logs表不存在，跳过日志记录");
          }
          
          updatedUsers.push({
            id: user.id,
            username: user.username,
            oldLevel: user.level,
            newLevel
          });
        }
      }
      
      await db.query('COMMIT');
      
      return handleResponse(ctx, 200, {
        message: `成功修改 ${updatedUsers.length} 个用户的等级`,
        data: {
          updatedCount: updatedUsers.length,
          updatedUsers,
          changedBy: adminId
        }
      });
      
    } catch (error) {
      await db.query('ROLLBACK');
      throw error;
    }
    
  } catch (error) {
    console.error("批量修改用户等级失败:", error);
    return handleResponse(ctx, 500, { message: "批量修改用户等级失败" });
  }
});

// 获取等级变更日志
router.get("/level-logs", verifyToken, requireAdmin, async (ctx) => {
  try {
    const { page = 1, limit = 20, userId } = ctx.query;
    const offset = (page - 1) * limit;
    
    let whereClause = "WHERE 1=1";
    let params = [];
    
    if (userId) {
      whereClause += " AND ull.user_id = ?";
      params.push(userId);
    }
    
    // 检查user_level_logs表是否存在
    const tableExists = await db.query("SHOW TABLES LIKE 'user_level_logs'");
    if (tableExists.length === 0) {
      return handleResponse(ctx, 200, {
        message: "等级变更日志表不存在",
        data: {
          logs: [],
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: 0,
            pages: 0
          }
        }
      });
    }
    
    const logsQuery = `
      SELECT 
        ull.*,
        u.username,
        admin.username as admin_username
      FROM user_level_logs ull
      LEFT JOIN users u ON ull.user_id = u.id
      LEFT JOIN users admin ON ull.changed_by = admin.id
      ${whereClause}
      ORDER BY ull.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(parseInt(limit), parseInt(offset));
    
    const logs = await db.query(logsQuery, params);
    
    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM user_level_logs ull
      ${whereClause}
    `;
    
    const countParams = params.slice(0, -2);
    const totalResult = await db.query(countQuery, countParams);
    const total = totalResult[0]?.total || 0;
    
    return handleResponse(ctx, 200, {
      message: "获取等级变更日志成功",
      data: {
        logs,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
    
  } catch (error) {
    console.error("获取等级变更日志失败:", error);
    return handleResponse(ctx, 500, { message: "获取等级变更日志失败" });
  }
});

module.exports = router;
