<template>
  <div class="style-guide-test">
    <div class="page-container">
      <el-card class="test-card">
        <template #header>
          <h1 class="page-title">🎨 全局样式规范测试</h1>
        </template>
        
        <div class="test-content">
          <!-- 颜色系统测试 -->
          <section class="test-section">
            <h2>🎨 颜色系统</h2>
            <div class="color-grid">
              <div class="color-item">
                <div class="color-swatch primary"></div>
                <span>主色调</span>
              </div>
              <div class="color-item">
                <div class="color-swatch success"></div>
                <span>成功色</span>
              </div>
              <div class="color-item">
                <div class="color-swatch warning"></div>
                <span>警告色</span>
              </div>
              <div class="color-item">
                <div class="color-swatch error"></div>
                <span>错误色</span>
              </div>
              <div class="color-item">
                <div class="color-swatch info"></div>
                <span>信息色</span>
              </div>
            </div>
          </section>

          <!-- 字体系统测试 -->
          <section class="test-section">
            <h2>📝 字体系统</h2>
            <div class="typography-demo">
              <h1>H1 主标题 - 36px</h1>
              <h2>H2 大标题 - 30px</h2>
              <h3>H3 中标题 - 24px</h3>
              <h4>H4 小标题 - 20px</h4>
              <h5>H5 子标题 - 18px</h5>
              <h6>H6 标题 - 16px</h6>
              <p class="text-lg">大文字 - 18px</p>
              <p class="text-base">基础文字 - 16px</p>
              <p class="text-sm">小文字 - 14px</p>
              <p class="text-xs">极小文字 - 12px</p>
            </div>
          </section>

          <!-- 按钮组件测试 -->
          <section class="test-section">
            <h2>🔘 按钮组件</h2>
            <div class="button-demo">
              <el-button type="primary" size="large">主要按钮</el-button>
              <el-button type="success">成功按钮</el-button>
              <el-button type="warning">警告按钮</el-button>
              <el-button type="danger">危险按钮</el-button>
              <el-button type="info">信息按钮</el-button>
              <el-button>默认按钮</el-button>
              <el-button plain>朴素按钮</el-button>
              <el-button round>圆角按钮</el-button>
              <el-button circle :icon="Plus"></el-button>
            </div>
          </section>

          <!-- 表单组件测试 -->
          <section class="test-section">
            <h2>📝 表单组件</h2>
            <el-form :model="testForm" label-width="100px" class="form-demo">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="用户名">
                    <el-input v-model="testForm.username" placeholder="请输入用户名" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="邮箱">
                    <el-input v-model="testForm.email" placeholder="请输入邮箱" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="选择器">
                    <el-select v-model="testForm.select" placeholder="请选择">
                      <el-option label="选项1" value="1" />
                      <el-option label="选项2" value="2" />
                      <el-option label="选项3" value="3" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="日期">
                    <el-date-picker v-model="testForm.date" type="date" placeholder="选择日期" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="开关">
                <el-switch v-model="testForm.switch" />
              </el-form-item>
              <el-form-item label="描述">
                <el-input v-model="testForm.description" type="textarea" rows="3" placeholder="请输入描述" />
              </el-form-item>
            </el-form>
          </section>

          <!-- 卡片组件测试 -->
          <section class="test-section">
            <h2>🃏 卡片组件</h2>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-card shadow="hover">
                  <template #header>
                    <span>悬停阴影</span>
                  </template>
                  <p>这是一个带悬停效果的卡片</p>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="always">
                  <template #header>
                    <span>始终阴影</span>
                  </template>
                  <p>这是一个始终有阴影的卡片</p>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="never">
                  <template #header>
                    <span>无阴影</span>
                  </template>
                  <p>这是一个无阴影的卡片</p>
                </el-card>
              </el-col>
            </el-row>
          </section>

          <!-- 标签组件测试 -->
          <section class="test-section">
            <h2>🏷️ 标签组件</h2>
            <div class="tag-demo">
              <el-tag>默认标签</el-tag>
              <el-tag type="success">成功标签</el-tag>
              <el-tag type="info">信息标签</el-tag>
              <el-tag type="warning">警告标签</el-tag>
              <el-tag type="danger">危险标签</el-tag>
              <el-tag effect="dark">深色标签</el-tag>
              <el-tag effect="light">浅色标签</el-tag>
              <el-tag effect="plain">朴素标签</el-tag>
            </div>
          </section>

          <!-- 表格组件测试 -->
          <section class="test-section">
            <h2>📊 表格组件</h2>
            <el-table :data="tableData" stripe>
              <el-table-column prop="name" label="姓名" width="120" />
              <el-table-column prop="age" label="年龄" width="80" />
              <el-table-column prop="email" label="邮箱" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.status === '活跃' ? 'success' : 'info'">
                    {{ row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template #default>
                  <el-button size="small" type="primary">编辑</el-button>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </section>

          <!-- 工具类测试 -->
          <section class="test-section">
            <h2>🛠️ 工具类测试</h2>
            <div class="utility-demo">
              <div class="flex items-center justify-between p-4 rounded-lg shadow mb-4">
                <span class="font-semibold">Flex 布局示例</span>
                <el-button size="small">操作</el-button>
              </div>
              <div class="grid grid-cols-3 gap-4">
                <div class="p-4 bg-primary text-white rounded text-center">网格 1</div>
                <div class="p-4 bg-success text-white rounded text-center">网格 2</div>
                <div class="p-4 bg-warning text-white rounded text-center">网格 3</div>
              </div>
            </div>
          </section>

          <!-- 操作按钮 -->
          <div class="test-actions">
            <el-button type="primary" @click="showMessage">显示消息</el-button>
            <el-button type="success" @click="showNotification">显示通知</el-button>
            <el-button type="warning" @click="showDialog">显示对话框</el-button>
            <el-button @click="goBack">返回</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 测试对话框 -->
    <el-dialog v-model="dialogVisible" title="样式测试对话框" width="500px">
      <p>这是一个测试对话框，用于验证对话框的样式效果。</p>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElNotification } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';

const router = useRouter();

// 表单数据
const testForm = ref({
  username: '',
  email: '',
  select: '',
  date: '',
  switch: false,
  description: ''
});

// 表格数据
const tableData = ref([
  { name: '张三', age: 25, email: '<EMAIL>', status: '活跃' },
  { name: '李四', age: 30, email: '<EMAIL>', status: '活跃' },
  { name: '王五', age: 28, email: '<EMAIL>', status: '离线' },
]);

// 对话框状态
const dialogVisible = ref(false);

// 方法
const showMessage = () => {
  ElMessage.success('这是一个成功消息！');
};

const showNotification = () => {
  ElNotification({
    title: '通知标题',
    message: '这是一个通知消息，用于测试通知组件的样式效果。',
    type: 'success'
  });
};

const showDialog = () => {
  dialogVisible.value = true;
};

const goBack = () => {
  router.go(-1);
};
</script>

<style scoped>
.style-guide-test {
  min-height: 100vh;
  background: var(--bg-secondary);
}

.page-container {
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.page-title {
  color: var(--text-primary);
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  margin: 0;
  text-align: center;
}

.test-content {
  padding: var(--spacing-lg);
}

.test-section {
  margin-bottom: var(--spacing-2xl);
}

.test-section h2 {
  color: var(--text-primary);
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border-light);
}

/* 颜色系统 */
.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
}

.color-item {
  text-align: center;
}

.color-swatch {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-lg);
  margin: 0 auto var(--spacing-sm);
  box-shadow: var(--shadow-sm);
}

.color-swatch.primary { background: var(--primary-color); }
.color-swatch.success { background: var(--success-color); }
.color-swatch.warning { background: var(--warning-color); }
.color-swatch.error { background: var(--error-color); }
.color-swatch.info { background: var(--info-color); }

/* 字体系统 */
.typography-demo > * {
  margin-bottom: var(--spacing-sm);
}

/* 按钮演示 */
.button-demo {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

/* 表单演示 */
.form-demo {
  background: var(--bg-tertiary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
}

/* 标签演示 */
.tag-demo {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

/* 工具类演示 */
.utility-demo .grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-md);
}

.bg-primary { background: var(--primary-color); }
.bg-success { background: var(--success-color); }
.bg-warning { background: var(--warning-color); }
.text-white { color: white; }
.text-center { text-align: center; }

/* 操作按钮 */
.test-actions {
  text-align: center;
  margin-top: var(--spacing-2xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
}

.test-actions .el-button {
  margin: 0 var(--spacing-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: var(--spacing-md);
  }
  
  .color-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
  
  .button-demo {
    flex-direction: column;
  }
  
  .test-actions .el-button {
    width: 100%;
    margin: var(--spacing-xs) 0;
  }
}
</style>
