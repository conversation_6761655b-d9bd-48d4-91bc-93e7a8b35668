<template>
  <div class="upload-container">
    <el-card class="upload-card">
      <h2 class="title">上传文件</h2>

      <el-upload :auto-upload="false" :show-file-list="false" :on-change="onFileChange" multiple class="upload-area">
        <el-button type="primary" size="large" icon="el-icon-upload">
          选择文件
        </el-button>
      </el-upload>

      <!-- 文件列表 -->
      <div class="file-list">
        <transition-group name="fade" tag="div">
          <div class="file-item" v-for="(item, idx) in fileList" :key="item.name + item.file.size">
            <div class="left-icon">
              <el-icon>
                <Document />
              </el-icon>
            </div>
            <div class="file-content">
              <div class="file-name">{{ item.name }}</div>
              <div v-if="item.previewUrl" class="image-preview">
                <img :src="item.previewUrl" alt="预览" />
              </div>
            </div>
            <div class="file-action">
              <el-progress :percentage="item.percent" status="active" :stroke-width="6" style="width: 300px"
                color="#67c23a" />
              <el-button type="danger" size="small" icon="el-icon-delete" circle @click="removeFile(idx)" />
            </div>
          </div>
        </transition-group>
      </div>

      <!-- 上传按钮 -->
      <el-button type="success" size="large" :loading="uploading" class="upload-btn" icon="el-icon-upload-filled"
        @click="handleUpload">
        上传
      </el-button>

      <!-- 成功提示 -->
      <el-alert v-if="urlList.length" type="success" :closable="false" class="success-alert" title="上传成功" show-icon>
        <template #default>
          <div v-for="(item, idx) in urlList" :key="item">
            <a :href="item" target="_blank">{{ item }}</a>
          </div>
        </template>
      </el-alert>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { Document } from '@element-plus/icons-vue'
import { UploadApi, UploadChunkApi, MergeChunksApi } from "../utils/api";

// 状态变量
const fileList = ref([]);
const urlList = ref([]);
const uploading = ref(false);
const user_id = localStorage.getItem("id");
const username = localStorage.getItem("username");

// 删除文件
const removeFile = (index) => {
  const item = fileList.value[index];
  if (item.previewUrl) {
    URL.revokeObjectURL(item.previewUrl);
  }
  fileList.value.splice(index, 1);
};

// 选择文件处理
const onFileChange = (uploadFile) => {
  let files = [];

  if (Array.isArray(uploadFile)) {
    files = uploadFile.map(f => f.raw);
  } else if (uploadFile.raw) {
    files = [uploadFile.raw];
  }

  for (const file of files) {
    const exists = fileList.value.some(
      (item) => item.name === file.name && item.file.size === file.size
    );
    if (!exists) {
      fileList.value.push({
        file,
        name: file.name,
        previewUrl: file.type.startsWith("image/")
          ? URL.createObjectURL(file)
          : "",
        percent: 0,
      });
    }
  }
};

// 上传逻辑
const handleUpload = async () => {
  if (!fileList.value.length) {
    ElMessage.warning("请先选择文件");
    return;
  }

  // 打印所有待上传文件的信息
  console.log("待上传文件列表：", fileList.value.map(f => ({
    name: f.name,
    size: f.file.size,
    type: f.file.type,
    
  })));
  uploading.value = true;
  urlList.value = [];

  for (let idx = 0; idx < fileList.value.length; idx++) {
    const cur = fileList.value[idx];
    if (cur.file.size < 10 * 1024 * 1024) {
      await normalUpload(cur, idx);
    } else {
      await chunkUpload(cur, idx);
    }
  }

  // 上传全部完成后
  uploading.value = false;
  ElMessage.success("全部上传完成！");
  // 清空上传列表
  fileList.value = [];
};

// 分片逻辑
function createChunks(file, size = 2 * 1024 * 1024) {
  const result = [];
  let cur = 0;
  while (cur < file.size) {
    result.push(file.slice(cur, cur + size));
    cur += size;
  }
  return result;
}

function getFileHash(file) {
  return `${file.name}_${file.size}_${file.lastModified}`;
}

// 普通上传
const normalUpload = async (cur, idx) => {
  const formData = new FormData();
  formData.append("file", cur.file);
  formData.append("user_id", user_id);
  formData.append("username", username);

  const res = await UploadApi(formData, {
    onUploadProgress: (progressEvent) => {
      if (progressEvent.total) {
        cur.percent = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        );
      }
    },
  });
  urlList.value.push(res.files?.[0]?.filePath || "");
  // 不要在这里移除文件
};

// 分片上传
const chunkUpload = async (cur, idx) => {
  const chunkSize = 2 * 1024 * 1024;
  const chunks = createChunks(cur.file, chunkSize);
  const fileHash = getFileHash(cur.file);
  const totalChunks = chunks.length;

  for (let i = 0; i < totalChunks; i++) {
    const form = new FormData();
    form.append("chunk", chunks[i]);
    form.append("chunkIndex", i);
    form.append("fileHash", fileHash);
    form.append("fileName", cur.file.name);
    form.append("user_id", user_id);
    form.append("totalChunks", totalChunks);

    await UploadChunkApi(form);
    cur.percent = Math.round(((i + 1) / totalChunks) * 100);
  }

  const mergeRes = await MergeChunksApi({
    fileHash,
    fileName: cur.file.name,
    user_id,
    totalChunks,
    fileType: cur.file.type,
    fileSize: cur.file.size,
  });

  urlList.value.push(mergeRes.filePath || "");
  // 不要在这里移除文件
};
</script>

<style scoped>
.upload-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background: #f6f8fa;
  min-height: 100vh;
  padding: 20px 0;
  box-sizing: border-box;
}

.upload-card {
  width: 100%;
  /* 修改为100%，以适应父容器 */
  max-width: 1200px;
  /* 设置最大宽度，防止过大 */
  padding: 32px 28px 24px 28px;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.07);
  box-sizing: border-box;
}

.title {
  font-size: 22px;
  margin-bottom: 24px;
  text-align: center;
  color: #222;
  letter-spacing: 1px;
}

.upload-area {
  display: flex;
  justify-content: center;
  margin-bottom: 18px;
}

.file-list {
  margin: 18px 0 10px 0;
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 10px 14px;
  background: #f9fafb;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
  transition: box-shadow 0.2s;
  box-sizing: border-box;
}

.file-item:hover {
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.08);
}

.left-icon {
  font-size: 26px;
  margin-right: 14px;
  color: #409eff;
  display: flex;
  align-items: center;
}

.file-content {
  flex: 1;
}

.file-name {
  font-weight: 500;
  font-size: 15px;
  margin-bottom: 6px;
  color: #333;
}

.image-preview img {
  max-height: 50px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.file-action {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-btn {
  width: 100%;
  margin-top: 18px;
  font-size: 17px;
  letter-spacing: 2px;
}

.success-alert {
  margin-top: 18px;
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s cubic-bezier(.55, 0, .1, 1);
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(12px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-12px);
}

/* 媒体查询以适应不同屏幕尺寸 */
@media (max-width: 600px) {
  .upload-container {
    padding: 8px 0;
  }
  .upload-card {
    padding: 12px 4px 10px 4px;
    max-width: 100vw;
    border-radius: 8px;
  }
  .title {
    font-size: 17px;
    margin-bottom: 14px;
  }
  .upload-area {
    margin-bottom: 10px;
  }
  .file-list {
    gap: 8px;
  }
  .file-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 8px 6px;
  }
  .left-icon {
    font-size: 18px;
    margin-right: 6px;
    margin-bottom: 4px;
  }
  .file-content {
    width: 100%;
  }
  .file-name {
    font-size: 13px;
    margin-bottom: 4px;
  }
  .image-preview img {
    max-height: 36px;
  }
  .file-action {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    margin-top: 4px;
  }
  .upload-btn {
    font-size: 14px;
    margin-top: 10px;
    letter-spacing: 0.5px;
  }
  .success-alert {
    margin-top: 10px;
    font-size: 13px;
  }
}

/* 可选：让按钮和进度条在小屏下自适应 */
.upload-btn,
.el-progress {
  max-width: 100%;
  box-sizing: border-box;
}
</style>
