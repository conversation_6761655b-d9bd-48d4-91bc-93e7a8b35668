import { createRequire } from 'module';
const require = createRequire(import.meta.url);

const db = require('../blog_hou/utils/db');
const jwt = require('jsonwebtoken');

async function checkCurrentUserFavorites() {
  try {
    console.log('🔍 检查当前用户收藏数据...\n');

    // 1. 模拟前端的token（从前端获取）
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MjEsInVzZXJJZCI6MjEsInVzZXJuYW1lIjoibGlvbiIsInJvbGUiOiJ1c2VyIiwiaWF0IjoxNzUxNjQ5MzIzLCJleHAiOjE3NTE2NjczMjN9.38Mx-3GXu57IL__-eqkAGoTQufnHXhOzIRob2MyM2mA';
    
    // 2. 解析token
    const JWT_SECRET = process.env.JWT_SECRET || "liumingyuan";
    const decoded = jwt.verify(token, JWT_SECRET);
    console.log('1. Token解析结果:', decoded);
    
    const user_id = decoded.userId || decoded.id;
    console.log('2. 提取的用户ID:', user_id);

    // 3. 检查用户信息
    const userInfo = await db.query('SELECT id, username, role FROM users WHERE id = ?', [user_id]);
    console.log('3. 用户信息:', userInfo);

    // 4. 检查所有收藏记录
    console.log('\n4. 检查所有收藏记录...');
    const allFavorites = await db.query('SELECT * FROM article_favorites ORDER BY created_at DESC LIMIT 10');
    console.log('所有收藏记录（最新10条）:');
    allFavorites.forEach(fav => {
      console.log(`- ID: ${fav.id}, 用户ID: ${fav.user_id}, 文章ID: ${fav.article_id}, 时间: ${fav.created_at}`);
    });

    // 5. 检查当前用户的收藏记录
    console.log('\n5. 检查当前用户的收藏记录...');
    const userFavorites = await db.query('SELECT * FROM article_favorites WHERE user_id = ?', [user_id]);
    console.log(`用户 ${user_id} 的收藏记录数量:`, userFavorites.length);
    userFavorites.forEach(fav => {
      console.log(`- ID: ${fav.id}, 文章ID: ${fav.article_id}, 时间: ${fav.created_at}`);
    });

    // 6. 检查文章是否存在且未删除
    if (userFavorites.length > 0) {
      console.log('\n6. 检查收藏的文章状态...');
      for (const fav of userFavorites) {
        const article = await db.query('SELECT id, title, is_deleted FROM articles WHERE id = ?', [fav.article_id]);
        if (article.length > 0) {
          console.log(`- 文章 ${fav.article_id}: ${article[0].title}, 删除状态: ${article[0].is_deleted}`);
        } else {
          console.log(`- 文章 ${fav.article_id}: 不存在`);
        }
      }
    }

    // 7. 模拟后端查询逻辑
    console.log('\n7. 模拟后端查询逻辑...');
    const history = await db.query(`
      SELECT af.*, a.title, a.summary, a.cover_image, a.user_id as author_id,
             u.username as author_name, u.avatar as author_avatar
      FROM article_favorites af
      LEFT JOIN articles a ON af.article_id = a.id
      LEFT JOIN users u ON a.user_id = u.id
      WHERE af.user_id = ? AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
      ORDER BY af.created_at DESC
      LIMIT 20 OFFSET 0
    `, [user_id]);
    
    console.log('后端查询结果数量:', history.length);
    history.forEach(item => {
      console.log(`- 收藏ID: ${item.id}, 文章: ${item.title}, 作者: ${item.author_name}`);
    });

    // 8. 检查总数查询
    const totalResult = await db.query(`
      SELECT COUNT(*) as total 
      FROM article_favorites af
      LEFT JOIN articles a ON af.article_id = a.id
      WHERE af.user_id = ? AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
    `, [user_id]);
    console.log('\n8. 总数查询结果:', totalResult[0].total);

    console.log('\n✅ 检查完成!');

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    if (error.name === 'TokenExpiredError') {
      console.log('Token已过期，请重新登录');
    }
  }
}

checkCurrentUserFavorites();
