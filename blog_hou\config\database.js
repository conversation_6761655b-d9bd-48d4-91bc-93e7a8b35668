/**
 * 数据库配置管理
 * 支持多环境配置和环境变量
 */

const logger = require("../plugin/logger");

// 环境配置映射
const environments = {
  development: {
    host: process.env.DB_HOST || "localhost",
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "0519",
    database: process.env.DB_NAME || "myblog",
    // 连接池配置
    connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 10,
    acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000,
    timeout: parseInt(process.env.DB_TIMEOUT) || 60000,
    reconnect: true,
    multipleStatements: false,
    queueLimit: 0,
    charset: 'utf8mb4',
    ssl: false,
    // 开发环境特定配置
    debug: process.env.DB_DEBUG === 'true',
    slowQueryThreshold: parseInt(process.env.SLOW_QUERY_THRESHOLD) || 1000
  },

  test: {
    host: process.env.TEST_DB_HOST || "localhost",
    user: process.env.TEST_DB_USER || "root",
    password: process.env.TEST_DB_PASSWORD || "0519",
    database: process.env.TEST_DB_NAME || "myblog_test",
    connectionLimit: 5,
    acquireTimeout: 30000,
    timeout: 30000,
    reconnect: true,
    multipleStatements: false,
    queueLimit: 0,
    charset: 'utf8mb4',
    ssl: false,
    debug: false,
    slowQueryThreshold: 500
  },

  production: {
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    // 生产环境更大的连接池
    connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 20,
    acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000,
    timeout: parseInt(process.env.DB_TIMEOUT) || 60000,
    reconnect: true,
    multipleStatements: false,
    queueLimit: 0,
    charset: 'utf8mb4',
    // 生产环境启用SSL
    ssl: process.env.DB_SSL === 'true' ? {
      rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false'
    } : false,
    debug: false,
    slowQueryThreshold: parseInt(process.env.SLOW_QUERY_THRESHOLD) || 2000
  }
};

/**
 * 获取当前环境的数据库配置
 */
function getDatabaseConfig() {
  const env = process.env.NODE_ENV || 'development';
  const config = environments[env];

  if (!config) {
    throw new Error(`不支持的环境: ${env}`);
  }

  // 验证必需的配置项
  validateConfig(config, env);

  logger.info(`数据库配置加载完成`, {
    environment: env,
    host: config.host,
    database: config.database,
    connectionLimit: config.connectionLimit,
    ssl: !!config.ssl
  });

  return config;
}

/**
 * 验证数据库配置
 */
function validateConfig(config, env) {
  const requiredFields = ['host', 'user', 'password', 'database'];
  const missing = requiredFields.filter(field => !config[field]);

  if (missing.length > 0) {
    const error = `数据库配置缺少必需字段 (${env}): ${missing.join(', ')}`;
    logger.error(error);
    throw new Error(error);
  }

  // 生产环境额外验证
  if (env === 'production') {
    if (config.password === '0519' || config.password.length < 8) {
      logger.warn('生产环境使用了弱密码，建议更换');
    }

    if (!config.ssl) {
      logger.warn('生产环境未启用SSL连接，存在安全风险');
    }
  }
}

/**
 * 获取导出服务专用配置（兼容旧版本）
 */
function getExportConfig() {
  const config = getDatabaseConfig();
  return {
    host: config.host,
    user: config.user,
    password: config.password,
    database: config.database
  };
}

/**
 * 获取数据库健康检查配置
 */
function getHealthCheckConfig() {
  return {
    enabled: process.env.DB_HEALTH_CHECK !== 'false',
    interval: parseInt(process.env.DB_HEALTH_CHECK_INTERVAL) || 30000, // 30秒
    timeout: parseInt(process.env.DB_HEALTH_CHECK_TIMEOUT) || 5000,     // 5秒
    retries: parseInt(process.env.DB_HEALTH_CHECK_RETRIES) || 3
  };
}

/**
 * 获取缓存配置
 */
function getCacheConfig() {
  return {
    enabled: process.env.CACHE_ENABLED !== 'false',
    defaultTTL: parseInt(process.env.CACHE_DEFAULT_TTL) || 300000, // 5分钟
    maxSize: parseInt(process.env.CACHE_MAX_SIZE) || 1000,
    cleanupInterval: parseInt(process.env.CACHE_CLEANUP_INTERVAL) || 600000 // 10分钟
  };
}

module.exports = {
  getDatabaseConfig,
  getExportConfig,
  getHealthCheckConfig,
  getCacheConfig,
  environments
};
