// 统一日志记录中间件
// 合并了 loggerMiddleware.js 和 enhancedLoggerMiddleware.js 的功能
const logger = require('../plugin/logger');

// 请求统计
const requestStats = {
  total: 0,
  byMethod: new Map(),
  byStatus: new Map(),
  totalResponseTime: 0,
  slowRequests: [],
  recentRequests: []
};

// 生成请求ID
function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 敏感参数过滤
function sanitizeQuery(query) {
  const sanitized = { ...query };
  const sensitiveKeys = ['password', 'token', 'secret', 'key', 'authorization'];
  
  sensitiveKeys.forEach(key => {
    if (sanitized[key]) {
      sanitized[key] = '[REDACTED]';
    }
  });
  
  return sanitized;
}

// 敏感请求体过滤
function sanitizeBody(body) {
  if (!body || typeof body !== 'object') return body;
  
  const sanitized = { ...body };
  const sensitiveKeys = ['password', 'token', 'secret', 'key', 'authorization'];
  
  sensitiveKeys.forEach(key => {
    if (sanitized[key]) {
      sanitized[key] = '[REDACTED]';
    }
  });
  
  return sanitized;
}

// 更新统计信息
function updateStats(method, status, responseTime) {
  requestStats.total++;
  
  // 按方法统计
  const methodCount = requestStats.byMethod.get(method) || 0;
  requestStats.byMethod.set(method, methodCount + 1);
  
  // 按状态码统计
  const statusCount = requestStats.byStatus.get(status) || 0;
  requestStats.byStatus.set(status, statusCount + 1);
  
  // 响应时间统计
  requestStats.totalResponseTime += responseTime;
  
  // 记录慢请求
  if (responseTime > 1000) {
    requestStats.slowRequests.push({
      timestamp: new Date().toISOString(),
      method,
      status,
      responseTime
    });
    
    // 只保留最近50个慢请求
    if (requestStats.slowRequests.length > 50) {
      requestStats.slowRequests = requestStats.slowRequests.slice(-50);
    }
  }
  
  // 记录最近请求
  requestStats.recentRequests.push({
    timestamp: new Date().toISOString(),
    method,
    status,
    responseTime
  });
  
  // 只保留最近100个请求
  if (requestStats.recentRequests.length > 100) {
    requestStats.recentRequests = requestStats.recentRequests.slice(-100);
  }
}

// 获取统计信息
function getStats() {
  const avgResponseTime = requestStats.total > 0 
    ? Math.round(requestStats.totalResponseTime / requestStats.total) 
    : 0;
    
  return {
    ...requestStats,
    byMethod: Object.fromEntries(requestStats.byMethod),
    byStatus: Object.fromEntries(requestStats.byStatus),
    avgResponseTime,
    slowRequests: requestStats.slowRequests.slice(-10), // 只返回最近10个
    recentRequests: requestStats.recentRequests.slice(-20) // 只返回最近20个
  };
}

/**
 * 统一日志中间件
 * @param {Object} options 配置选项
 * @param {Array} options.skipPaths 跳过日志记录的路径
 * @param {Array} options.detailedPaths 需要详细日志的路径
 * @param {string} options.logLevel 日志级别
 * @param {number} options.slowRequestThreshold 慢请求阈值(ms)
 */
module.exports = (options = {}) => {
  const {
    skipPaths = ['/health', '/favicon.ico', '/ping'],
    detailedPaths = ['/user/login', '/user/register', '/articles', '/dashboard', '/api/upload'],
    logLevel = 'info',
    slowRequestThreshold = 1000
  } = options;

  return async (ctx, next) => {
    const start = Date.now();
    const method = ctx.method;
    const url = ctx.url;
    const path = ctx.path;
    
    // 生成请求ID
    const requestId = generateRequestId();
    ctx.state.requestId = requestId;
    
    // 判断是否跳过日志
    const shouldSkip = skipPaths.some(skipPath => path.includes(skipPath));
    const shouldDetailed = detailedPaths.some(detailPath => path.includes(detailPath));
    
    // 基础请求信息
    const baseLogData = {
      requestId,
      method,
      url,
      path,
      ip: ctx.ip,
      userAgent: ctx.get("User-Agent"),
      userId: ctx.state.user?.id
    };
    
    // 记录请求开始（除非跳过）
    if (!shouldSkip) {
      const logData = { ...baseLogData };
      
      // 详细路径记录更多信息
      if (shouldDetailed) {
        logData.query = sanitizeQuery(ctx.query);
        logData.headers = {
          'content-type': ctx.get('content-type'),
          'accept': ctx.get('accept'),
          'referer': ctx.get('referer')
        };
        
        // 记录请求体（仅POST/PUT/PATCH）
        if (['POST', 'PUT', 'PATCH'].includes(method) && ctx.request.body) {
          logData.requestBody = JSON.stringify(sanitizeBody(ctx.request.body)).substring(0, 500);
        }
      }
      
      logger[logLevel]("请求开始", logData);
    }

    try {
      await next();

      const duration = Date.now() - start;
      const status = ctx.status;

      // 计算响应大小
      let responseSize = 0;
      if (ctx.body) {
        if (typeof ctx.body === 'string') {
          responseSize = Buffer.byteLength(ctx.body, 'utf8');
        } else if (Buffer.isBuffer(ctx.body)) {
          responseSize = ctx.body.length;
        } else if (typeof ctx.body === 'object') {
          responseSize = Buffer.byteLength(JSON.stringify(ctx.body), 'utf8');
        }
      }

      // 更新统计信息
      updateStats(method, status, duration);

      // 记录请求完成（除非跳过）
      if (!shouldSkip) {
        const logData = {
          ...baseLogData,
          status,
          duration,
          responseSize
        };
        
        // 慢请求警告
        if (duration > slowRequestThreshold) {
          logger.warn(`慢请求检测 - ${status} - ${duration}ms`, logData);
        } else {
          logger[logLevel](`请求完成 - ${status} - ${duration}ms`, logData);
        }
      }
      
    } catch (error) {
      const duration = Date.now() - start;
      const status = error.status || 500;
      
      // 更新统计信息
      updateStats(method, status, duration);
      
      // 记录错误（不跳过）
      const errorLogData = {
        ...baseLogData,
        status,
        duration,
        error: error.message,
        stack: status >= 500 ? error.stack : undefined
      };
      
      logger.error("请求处理错误", errorLogData);
      throw error;
    }
  };
};

// 导出统计信息获取函数
module.exports.getStats = getStats;
