// 中间件管理器
// 统一导出所有中间件，提供配置化的中间件管理

const unifiedLogger = require('./unifiedLogger');
const unifiedErrorHandler = require('./unifiedErrorHandler');
const { responseMiddleware } = require('./responseStandardizer');
const { performanceMiddleware } = require('./performanceMiddleware');
const { cacheMiddleware } = require('./cacheMiddleware');
const { generalRateLimit } = require('./rateLimitMiddleware');
const authMiddleware = require('./koaJwtMiddleware');

/**
 * 中间件配置
 */
const middlewareConfig = {
  // 日志中间件配置
  logger: {
    skipPaths: ['/health', '/favicon.ico', '/ping'],
    detailedPaths: ['/user/login', '/user/register', '/articles', '/dashboard', '/api/upload'],
    logLevel: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
    slowRequestThreshold: 1000
  },

  // 错误处理中间件配置
  errorHandler: {
    exposeStack: process.env.NODE_ENV === 'development',
    logClientErrors: true,
    sensitiveHeaders: ['authorization', 'cookie', 'x-api-key']
  },

  // 响应标准化中间件配置
  response: {
    enableResponseTime: true,
    slowResponseThreshold: 1000
  },

  // 性能监控中间件配置
  performance: {
    enabled: true,
    collectMetrics: true
  },

  // 缓存中间件配置
  cache: {
    enabled: true,
    defaultTTL: 300000, // 5分钟
    maxSize: 1000
  },

  // 限流中间件配置
  rateLimit: {
    enabled: true,
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100 // 每个IP最多100个请求
  }
};

/**
 * 获取配置化的中间件实例
 */
function getMiddlewares(customConfig = {}) {
  const config = { ...middlewareConfig, ...customConfig };

  return {
    // 统一日志中间件
    logger: unifiedLogger(config.logger),

    // 统一错误处理中间件
    errorHandler: unifiedErrorHandler(config.errorHandler),

    // 响应标准化中间件
    response: responseMiddleware(config.response),

    // 性能监控中间件
    performance: performanceMiddleware(config.performance),

    // 缓存中间件
    cache: cacheMiddleware,

    // 限流中间件
    rateLimit: generalRateLimit,

    // JWT认证中间件
    auth: authMiddleware
  };
}

/**
 * 获取中间件应用顺序
 * 按照正确的顺序返回中间件数组
 */
function getMiddlewareStack(customConfig = {}) {
  const middlewares = getMiddlewares(customConfig);

  return [
    // 1. 错误处理（最先，捕获所有错误）
    middlewares.errorHandler,

    // 2. 响应标准化（早期设置响应方法）
    middlewares.response,

    // 3. 性能监控（监控整个请求周期）
    middlewares.performance,

    // 4. 限流（防止滥用）
    middlewares.rateLimit,

    // 5. 日志记录（记录请求信息）
    middlewares.logger,

    // 6. 缓存（在业务逻辑之前）
    middlewares.cache,

    // 7. JWT认证（最后，在路由之前）
    middlewares.auth
  ];
}

/**
 * 获取中间件统计信息
 */
function getMiddlewareStats() {
  return {
    logger: unifiedLogger.getStats ? unifiedLogger.getStats() : null,
    timestamp: new Date().toISOString()
  };
}

/**
 * 重置中间件统计
 */
function resetMiddlewareStats() {
  // 如果中间件支持重置统计，可以在这里实现
  console.log('中间件统计已重置');
}

/**
 * 验证中间件配置
 */
function validateMiddlewareConfig(config) {
  const errors = [];

  // 验证日志配置
  if (config.logger) {
    if (config.logger.skipPaths && !Array.isArray(config.logger.skipPaths)) {
      errors.push('logger.skipPaths 必须是数组');
    }
    if (config.logger.detailedPaths && !Array.isArray(config.logger.detailedPaths)) {
      errors.push('logger.detailedPaths 必须是数组');
    }
    if (config.logger.slowRequestThreshold && typeof config.logger.slowRequestThreshold !== 'number') {
      errors.push('logger.slowRequestThreshold 必须是数字');
    }
  }

  // 验证错误处理配置
  if (config.errorHandler) {
    if (config.errorHandler.exposeStack && typeof config.errorHandler.exposeStack !== 'boolean') {
      errors.push('errorHandler.exposeStack 必须是布尔值');
    }
  }

  // 验证响应配置
  if (config.response) {
    if (config.response.slowResponseThreshold && typeof config.response.slowResponseThreshold !== 'number') {
      errors.push('response.slowResponseThreshold 必须是数字');
    }
  }

  if (errors.length > 0) {
    throw new Error(`中间件配置验证失败: ${errors.join(', ')}`);
  }

  return true;
}

module.exports = {
  // 主要导出
  getMiddlewares,
  getMiddlewareStack,
  
  // 配置管理
  middlewareConfig,
  validateMiddlewareConfig,
  
  // 统计信息
  getMiddlewareStats,
  resetMiddlewareStats,
  
  // 直接导出中间件（向后兼容）
  unifiedLogger,
  unifiedErrorHandler,
  responseMiddleware,
  performanceMiddleware,
  cacheMiddleware,
  generalRateLimit,
  authMiddleware
};
