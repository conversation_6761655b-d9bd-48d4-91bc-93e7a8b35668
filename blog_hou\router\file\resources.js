// 资源管理，是否共享资源，是否永久删除资源
const Router = require("koa-router");
const resources = new Router();
const {
  getShallFilesByUserId,
  cancelShareOrShare,
  deleteFile,
  permanentlyDeleteFile,
} = require("../../utils/sql/resourcesService");
const { handleResponse } = require("../../middlewares/responseHandler");
const bodyParser = require("koa-bodyparser");
const fs = require("fs");
const path = require("path");

resources.use(bodyParser());

// 获取资源列表
resources.post("/list", async (ctx) => {
  const { user_id } = ctx.request.body;
  try {
    const result = await getShallFilesByUserId(user_id);
    handleResponse(ctx, 200, { data: result });
  } catch (err) {
    handleResponse(ctx, 500, { message: "获取资源列表失败" });
  }
});

// 取消分享或分享资源
resources.post("/cancelShareOrShare", async (ctx) => {
  const { user_id, file_id, is_share } = ctx.request.body;
  try {
    const result = await cancelShareOrShare(user_id, file_id, is_share);
    handleResponse(ctx, 200, { message: "操作成功" });
  } catch (err) {
    handleResponse(ctx, 500, { message: "操作失败" });
  }
});

// 删除资源
resources.post("/delete", async (ctx) => {
  const { user_id, file_id } = ctx.request.body;
  try {
    const result = await deleteFile(user_id, file_id);
    handleResponse(ctx, 200, { message: "删除成功" });
  } catch (err) {
    handleResponse(ctx, 500, { message: "删除失败" });
  }
});

// 永久删除资源
resources.post("/permanentlyDelete", async (ctx) => {
  const { user_id, file_id, file_name } = ctx.request.body;
  console.log("permanentlyDelete", user_id, file_id, file_name);
  try {
    // 删除系统的资源../../uploads/resources或者在../../public/media
    const del = fs.unlinkSync(
      path.join(__dirname, `../../uploads/resource/${file_name}`)
    );
    const result = await permanentlyDeleteFile(user_id, file_id);
    // 删除文件系统中的实际文件
    handleResponse(ctx, 200, { message: "永久删除成功" });
  } catch (err) {
    handleResponse(ctx, 500, { message: "永久删除失败" });
  }
});

module.exports = resources;
