// 创建浏览记录表的数据库迁移脚本
const db = require("../utils/db");
const logger = require("../plugin/logger");

async function createViewHistoryTable() {
  try {
    console.log("🚀 开始创建浏览记录表...");

    // 检查表是否已存在
    const checkTableSql = `
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'view_history'
    `;
    
    const existingTable = await db.query(checkTableSql);
    
    if (existingTable.length > 0) {
      console.log("✅ view_history表已存在，无需创建");
      return;
    }

    // 创建浏览记录表
    const createTableSql = `
      CREATE TABLE view_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT DEFAULT NULL COMMENT '用户ID，NULL表示匿名用户',
        article_id INT NOT NULL COMMENT '文章ID',
        ip_address VARCHAR(45) NOT NULL COMMENT 'IP地址',
        user_agent TEXT DEFAULT NULL COMMENT '用户代理',
        referrer VARCHAR(500) DEFAULT NULL COMMENT '来源页面',
        session_id VARCHAR(100) DEFAULT NULL COMMENT '会话ID',
        view_duration INT DEFAULT 0 COMMENT '浏览时长(秒)',
        scroll_depth DECIMAL(5,2) DEFAULT 0 COMMENT '滚动深度百分比',
        device_type ENUM('desktop', 'mobile', 'tablet') DEFAULT 'desktop' COMMENT '设备类型',
        browser VARCHAR(50) DEFAULT NULL COMMENT '浏览器',
        os VARCHAR(50) DEFAULT NULL COMMENT '操作系统',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '浏览时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_user_id (user_id),
        INDEX idx_article_id (article_id),
        INDEX idx_ip_address (ip_address),
        INDEX idx_created_at (created_at),
        INDEX idx_session_id (session_id),
        
        FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
      COMMENT='文章浏览记录表'
    `;
    
    await db.query(createTableSql);
    console.log("✅ 成功创建view_history表");

    // 创建用户浏览统计表
    const createUserStatsTableSql = `
      CREATE TABLE user_view_stats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT DEFAULT NULL COMMENT '用户ID，NULL表示匿名统计',
        total_views INT DEFAULT 0 COMMENT '总浏览量',
        unique_articles INT DEFAULT 0 COMMENT '浏览过的文章数',
        avg_view_duration DECIMAL(8,2) DEFAULT 0 COMMENT '平均浏览时长',
        last_view_at TIMESTAMP DEFAULT NULL COMMENT '最后浏览时间',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        UNIQUE KEY uk_user_id (user_id),
        INDEX idx_total_views (total_views),
        INDEX idx_last_view_at (last_view_at),
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
      COMMENT='用户浏览统计表'
    `;
    
    await db.query(createUserStatsTableSql);
    console.log("✅ 成功创建user_view_stats表");

    // 创建文章浏览统计表
    const createArticleStatsTableSql = `
      CREATE TABLE article_view_stats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        article_id INT NOT NULL COMMENT '文章ID',
        total_views INT DEFAULT 0 COMMENT '总浏览量',
        unique_visitors INT DEFAULT 0 COMMENT '独立访客数',
        avg_view_duration DECIMAL(8,2) DEFAULT 0 COMMENT '平均浏览时长',
        bounce_rate DECIMAL(5,2) DEFAULT 0 COMMENT '跳出率',
        last_view_at TIMESTAMP DEFAULT NULL COMMENT '最后浏览时间',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        UNIQUE KEY uk_article_id (article_id),
        INDEX idx_total_views (total_views),
        INDEX idx_unique_visitors (unique_visitors),
        INDEX idx_last_view_at (last_view_at),
        
        FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
      COMMENT='文章浏览统计表'
    `;
    
    await db.query(createArticleStatsTableSql);
    console.log("✅ 成功创建article_view_stats表");

    console.log("🎉 浏览记录相关表创建完成！");

  } catch (error) {
    console.error("❌ 创建浏览记录表失败:", error);
    logger.error("创建浏览记录表失败", { error: error.message });
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createViewHistoryTable()
    .then(() => {
      console.log("✅ 迁移完成");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ 迁移失败:", error);
      process.exit(1);
    });
}

module.exports = { createViewHistoryTable };
