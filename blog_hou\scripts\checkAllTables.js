const db = require("../utils/db");

async function checkAllTables() {
  try {
    console.log("🔍 检查数据库所有表...\n");
    console.log("=".repeat(60));

    // 1. 获取所有表
    const allTables = await db.query(`
      SELECT TABLE_NAME, TABLE_ROWS, DATA_LENGTH, INDEX_LENGTH
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = DATABASE()
      ORDER BY TABLE_NAME
    `);

    console.log(`📊 数据库中共有 ${allTables.length} 个表:\n`);

    // 按类别分组显示
    const tableGroups = {
      '用户相关': ['users', 'friends', 'friend_request'],
      '文章相关': ['articles', 'comments', 'tech_tags'],
      '媒体相关': ['media_stats', 'media_likes', 'media_collections', 'media_play_history'],
      '音乐相关': ['music', 'likes', 'collections', 'play_history'],
      '聊天相关': ['chat_message', 'chat_record', 'offline_messages', 'private_chat_sessions', 'message_status'],
      '文件相关': ['files', 'download_logs'],
      '系统相关': ['bug_record', 'recycle_bin', 'access_passwords'],
      '创意相关': ['creative'],
      'AI相关': ['ai_chat_history', 'ai_content_analysis', 'ai_recommendations', 'ai_model_configs', 'ai_usage_stats']
    };

    for (const [category, tableNames] of Object.entries(tableGroups)) {
      console.log(`\n📁 ${category}:`);
      
      const categoryTables = allTables.filter(table => 
        tableNames.includes(table.TABLE_NAME)
      );

      if (categoryTables.length === 0) {
        console.log("   (无相关表)");
        continue;
      }

      for (const table of categoryTables) {
        const dataSize = table.DATA_LENGTH ? (table.DATA_LENGTH / 1024).toFixed(2) + ' KB' : '0 KB';
        const indexSize = table.INDEX_LENGTH ? (table.INDEX_LENGTH / 1024).toFixed(2) + ' KB' : '0 KB';
        
        console.log(`   ✅ ${table.TABLE_NAME.padEnd(25)} | ${String(table.TABLE_ROWS || 0).padStart(6)} 行 | 数据: ${dataSize.padStart(8)} | 索引: ${indexSize.padStart(8)}`);
      }
    }

    // 显示未分类的表
    const categorizedTableNames = Object.values(tableGroups).flat();
    const uncategorizedTables = allTables.filter(table => 
      !categorizedTableNames.includes(table.TABLE_NAME)
    );

    if (uncategorizedTables.length > 0) {
      console.log(`\n📁 其他表:`);
      for (const table of uncategorizedTables) {
        const dataSize = table.DATA_LENGTH ? (table.DATA_LENGTH / 1024).toFixed(2) + ' KB' : '0 KB';
        const indexSize = table.INDEX_LENGTH ? (table.INDEX_LENGTH / 1024).toFixed(2) + ' KB' : '0 KB';
        
        console.log(`   ✅ ${table.TABLE_NAME.padEnd(25)} | ${String(table.TABLE_ROWS || 0).padStart(6)} 行 | 数据: ${dataSize.padStart(8)} | 索引: ${indexSize.padStart(8)}`);
      }
    }

    // 2. 统计信息
    console.log("\n" + "=".repeat(60));
    console.log("📈 数据库统计:");
    
    const totalRows = allTables.reduce((sum, table) => sum + (table.TABLE_ROWS || 0), 0);
    const totalDataSize = allTables.reduce((sum, table) => sum + (table.DATA_LENGTH || 0), 0);
    const totalIndexSize = allTables.reduce((sum, table) => sum + (table.INDEX_LENGTH || 0), 0);
    
    console.log(`   总表数: ${allTables.length}`);
    console.log(`   总行数: ${totalRows.toLocaleString()}`);
    console.log(`   数据大小: ${(totalDataSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   索引大小: ${(totalIndexSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   总大小: ${((totalDataSize + totalIndexSize) / 1024 / 1024).toFixed(2)} MB`);

    // 3. 检查媒体功能表的详细信息
    console.log("\n" + "=".repeat(60));
    console.log("🎬 媒体功能表详细检查:");
    
    const mediaFunctionTables = ['media_stats', 'media_likes', 'media_collections', 'media_play_history'];
    
    for (const tableName of mediaFunctionTables) {
      try {
        const exists = await db.query(`
          SELECT TABLE_NAME 
          FROM INFORMATION_SCHEMA.TABLES 
          WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
        `, [tableName]);

        if (exists.length > 0) {
          const count = await db.query(`SELECT COUNT(*) as count FROM ${tableName}`);
          console.log(`\n📋 ${tableName}:`);
          console.log(`   状态: ✅ 存在`);
          console.log(`   记录数: ${count[0].count}`);
          
          // 显示表结构
          const structure = await db.query(`DESCRIBE ${tableName}`);
          console.log(`   字段: ${structure.map(col => col.Field).join(', ')}`);
          
          // 显示示例数据
          if (count[0].count > 0) {
            const sample = await db.query(`SELECT * FROM ${tableName} LIMIT 3`);
            console.log(`   示例数据:`);
            sample.forEach((row, index) => {
              const rowData = Object.entries(row)
                .filter(([key, value]) => !['created_at', 'updated_at'].includes(key))
                .map(([key, value]) => `${key}:${value}`)
                .join(', ');
              console.log(`     ${index + 1}. ${rowData}`);
            });
          }
        } else {
          console.log(`\n📋 ${tableName}:`);
          console.log(`   状态: ❌ 不存在`);
        }
      } catch (error) {
        console.log(`\n📋 ${tableName}:`);
        console.log(`   状态: ❌ 检查失败 - ${error.message}`);
      }
    }

    console.log("\n" + "=".repeat(60));
    console.log("🎉 数据库检查完成！");
    
  } catch (error) {
    console.error("❌ 检查失败:", error);
  } finally {
    process.exit(0);
  }
}

// 运行检查
checkAllTables();
