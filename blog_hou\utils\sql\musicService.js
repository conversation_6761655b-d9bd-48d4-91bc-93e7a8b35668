const db = require("../db");

// 添加播放历史记录
async function addPlayHistory(userId, musicId) {
  return new Promise((resolve, reject) => {
    const sql = `
      INSERT INTO play_history (user_id, music_id)
      VALUES (?, ?)
      ON DUPLICATE KEY UPDATE timestamp = CURRENT_TIMESTAMP
    `;
    db.query(sql, [userId, musicId], (err, results) => {
      if (err) {
        console.error("插入播放历史记录失败:", err);
        reject(err);
      } else {
        resolve(results);
      }
    });
  });
}

// 添加喜欢记录
async function addLike(userId, musicId) {
  return new Promise((resolve, reject) => {
    const sql = 'INSERT IGNORE INTO likes (user_id, music_id) VALUES (?, ?)';
    db.query(sql, [userId, musicId], (err, results) => {
      if (err) {
        console.error("插入喜欢记录失败:", err);
        reject(err);
      } else {
        resolve(results);
      }
    });
  });
}

// 添加收藏记录
async function addCollection(userId, musicId) {
  return new Promise((resolve, reject) => {
    const sql = 'INSERT IGNORE INTO collections (user_id, music_id) VALUES (?, ?)';
    db.query(sql, [userId, musicId], (err, results) => {
      if (err) {
        console.error("插入收藏记录失败:", err);
        reject(err);
      } else {
        resolve(results);
      }
    });
  });
}

// 获取播放历史记录
async function getPlayHistory(userId) {
  return new Promise((resolve, reject) => {
    const sql = "SELECT * FROM play_history WHERE user_id = ? ORDER BY timestamp DESC";
    db.query(sql, [userId], (err, results) => {
      if (err) {
        reject(err);
      } else {
        resolve(results);
      }
    });
  });
}

module.exports = {
  addPlayHistory,
  addLike,
  addCollection,
  getPlayHistory,
};
