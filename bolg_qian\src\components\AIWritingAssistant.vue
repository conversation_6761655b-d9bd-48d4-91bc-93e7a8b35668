<template>
  <div class="ai-assistant">
    <!-- AI助手状态指示器 -->
    <div class="ai-status" :class="{ 'available': aiStatus.available, 'unavailable': !aiStatus.available }">
      <el-icon><Cpu /></el-icon>
      <span>{{ aiStatus.available ? 'AI助手已就绪' : 'AI服务不可用' }}</span>
      <el-tag v-if="aiStatus.available" size="small" type="success">{{ aiStatus.currentModel }}</el-tag>
    </div>

    <!-- AI功能按钮组 -->
    <div class="ai-functions" v-if="aiStatus.available">
      <el-button-group>
        <el-button
          size="small"
          :loading="loading.title"
          @click="generateTitles"
          :disabled="!canGenerateTitle"
        >
          <el-icon><Star /></el-icon>
          生成标题
        </el-button>
        
        <el-button 
          size="small"
          :loading="loading.tags"
          @click="suggestTags"
          :disabled="!canSuggestTags"
        >
          <el-icon><PriceTag /></el-icon>
          推荐标签
        </el-button>
        
        <el-button
          size="small"
          :loading="loading.optimize"
          @click="showOptimizeDialog"
          :disabled="!canOptimizeContent"
        >
          <el-icon><Tools /></el-icon>
          优化内容
        </el-button>

        <el-button
          size="small"
          :loading="loading.outline"
          @click="showOutlineDialog"
        >
          <el-icon><List /></el-icon>
          生成大纲
        </el-button>
      </el-button-group>
    </div>

    <!-- 标题生成结果 -->
    <el-dialog v-model="dialogs.titles" title="AI生成的标题建议" width="600px">
      <div v-if="results.titles.length > 0">
        <p class="dialog-tip">点击标题可直接应用到文章中：</p>
        <div class="title-suggestions">
          <div 
            v-for="(title, index) in results.titles" 
            :key="index"
            class="title-item"
            @click="applyTitle(title)"
          >
            <el-icon><Document /></el-icon>
            <span>{{ title }}</span>
            <el-button size="small" text @click.stop="applyTitle(title)">应用</el-button>
          </div>
        </div>
        <div class="response-info">
          <el-text size="small" type="info">
            响应时间: {{ results.responseTime }}ms | 模型: {{ results.model }}
          </el-text>
        </div>
      </div>
      <template #footer>
        <el-button @click="dialogs.titles = false">关闭</el-button>
        <el-button type="primary" @click="generateTitles">重新生成</el-button>
      </template>
    </el-dialog>

    <!-- 内容优化对话框 -->
    <el-dialog v-model="dialogs.optimize" title="内容优化" width="800px">
      <el-form :model="optimizeForm" label-width="100px">
        <el-form-item label="优化风格">
          <el-select v-model="optimizeForm.style" placeholder="选择写作风格">
            <el-option label="专业严谨" value="professional" />
            <el-option label="轻松随意" value="casual" />
            <el-option label="学术正式" value="academic" />
            <el-option label="创意生动" value="creative" />
          </el-select>
        </el-form-item>
        <el-form-item label="目标读者">
          <el-select v-model="optimizeForm.target" placeholder="选择目标读者">
            <el-option label="一般读者" value="general" />
            <el-option label="技术人员" value="technical" />
            <el-option label="学生" value="students" />
            <el-option label="专业人士" value="professionals" />
          </el-select>
        </el-form-item>
        <el-form-item label="待优化内容">
          <el-input 
            v-model="optimizeForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入需要优化的内容"
          />
        </el-form-item>
      </el-form>
      
      <div v-if="results.optimizedContent" class="optimization-result">
        <h4>优化结果：</h4>
        <div class="optimized-content">
          {{ results.optimizedContent }}
        </div>
        <div class="optimization-stats">
          <el-text size="small" type="info">
            原文: {{ results.originalLength }}字 → 优化后: {{ results.optimizedLength }}字 
            | 响应时间: {{ results.responseTime }}ms
          </el-text>
        </div>
      </div>

      <template #footer>
        <el-button @click="dialogs.optimize = false">关闭</el-button>
        <el-button type="primary" :loading="loading.optimize" @click="optimizeContent">
          开始优化
        </el-button>
        <el-button 
          v-if="results.optimizedContent" 
          type="success" 
          @click="applyOptimizedContent"
        >
          应用优化结果
        </el-button>
      </template>
    </el-dialog>

    <!-- 大纲生成对话框 -->
    <el-dialog v-model="dialogs.outline" title="生成文章大纲" width="700px">
      <el-form :model="outlineForm" label-width="100px">
        <el-form-item label="文章主题" required>
          <el-input v-model="outlineForm.topic" placeholder="请输入文章主题" />
        </el-form-item>
        <el-form-item label="文章分类">
          <el-select v-model="outlineForm.category" placeholder="选择分类">
            <el-option label="技术" value="技术" />
            <el-option label="艺术" value="艺术" />
            <el-option label="生活" value="生活" />
            <el-option label="教程" value="教程" />
            <el-option label="经验分享" value="经验分享" />
          </el-select>
        </el-form-item>
        <el-form-item label="大纲深度">
          <el-radio-group v-model="outlineForm.depth">
            <el-radio label="simple">简单 (3-5个要点)</el-radio>
            <el-radio label="medium">中等 (5-8个要点)</el-radio>
            <el-radio label="detailed">详细 (8-12个要点)</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div v-if="results.outline" class="outline-result">
        <h4>生成的大纲：</h4>
        <div class="outline-content" v-html="formatOutline(results.outline)"></div>
        <div class="response-info">
          <el-text size="small" type="info">
            响应时间: {{ results.responseTime }}ms | 模型: {{ results.model }}
          </el-text>
        </div>
      </div>

      <template #footer>
        <el-button @click="dialogs.outline = false">关闭</el-button>
        <el-button 
          type="primary" 
          :loading="loading.outline" 
          @click="generateOutline"
          :disabled="!outlineForm.topic"
        >
          生成大纲
        </el-button>
        <el-button 
          v-if="results.outline" 
          type="success" 
          @click="applyOutline"
        >
          应用到编辑器
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Cpu,
  Star,
  PriceTag,
  Edit,
  List,
  Document,
  Tools,
  Promotion
} from '@element-plus/icons-vue'
import { aiWritingApi } from '@/utils/aiApi'

// Props
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  content: {
    type: String,
    default: ''
  },
  category: {
    type: String,
    default: ''
  },
  tags: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:title', 'update:content', 'update:tags', 'update:category'])

// 响应式数据
const aiStatus = reactive({
  available: false,
  models: [],
  currentModel: ''
})

const loading = reactive({
  title: false,
  tags: false,
  optimize: false,
  outline: false
})

const dialogs = reactive({
  titles: false,
  optimize: false,
  outline: false
})

const results = reactive({
  titles: [],
  tags: [],
  optimizedContent: '',
  originalLength: 0,
  optimizedLength: 0,
  outline: '',
  responseTime: 0,
  model: ''
})

const optimizeForm = reactive({
  style: 'professional',
  target: 'general',
  content: ''
})

const outlineForm = reactive({
  topic: '',
  category: '',
  depth: 'medium'
})

// 计算属性
const canGenerateTitle = computed(() => {
  return props.content && props.content.length > 50
})

const canSuggestTags = computed(() => {
  return props.title || (props.content && props.content.length > 20)
})

const canOptimizeContent = computed(() => {
  return props.content && props.content.length > 10
})

// 方法
const checkAIStatus = async () => {
  try {
    console.log('🔍 检查AI服务状态...')

    // 检查是否有token
    const token = localStorage.getItem('token')
    if (!token) {
      console.warn('⚠️ 未找到登录token')
      aiStatus.available = false
      return
    }

    const response = await aiWritingApi.getStatus()
    console.log('AI状态响应:', response)

    if (response.code === 200) {
      aiStatus.available = response.data.available
      aiStatus.models = response.data.models
      aiStatus.currentModel = response.data.currentModel
      console.log('✅ AI服务状态检查成功:', {
        available: aiStatus.available,
        modelCount: aiStatus.models.length,
        currentModel: aiStatus.currentModel
      })
    } else {
      console.error('❌ AI服务响应错误:', response)
      aiStatus.available = false
    }
  } catch (error) {
    console.error('❌ 检查AI状态失败:', error)
    console.error('错误详情:', error.response?.data || error.message)
    aiStatus.available = false

    // 如果是401错误，提示用户重新登录
    if (error.response?.status === 401) {
      ElMessage.warning('登录已过期，请重新登录后使用AI功能')
    }
  }
}

const generateTitles = async () => {
  if (!canGenerateTitle.value) {
    ElMessage.warning('请先输入足够的文章内容')
    return
  }

  loading.title = true
  try {
    const response = await aiWritingApi.generateTitle({
      content: props.content,
      category: props.category,
      count: 5
    })

    if (response.code === 200) {
      results.titles = response.data.titles
      results.responseTime = response.data.responseTime
      results.model = response.data.model
      dialogs.titles = true
      ElMessage.success('标题生成成功')
    } else {
      ElMessage.error(response.message || '标题生成失败')
    }
  } catch (error) {
    console.error('生成标题失败:', error)
    ElMessage.error('生成标题失败')
  } finally {
    loading.title = false
  }
}

const suggestTags = async () => {
  if (!canSuggestTags.value) {
    ElMessage.warning('请先输入标题或内容')
    return
  }

  loading.tags = true
  try {
    const response = await aiWritingApi.suggestTags({
      title: props.title,
      content: props.content,
      category: props.category,
      maxTags: 8
    })

    if (response.code === 200) {
      results.tags = response.data.tags
      results.responseTime = response.data.responseTime
      results.model = response.data.model
      
      // 自动应用标签建议
      const newTags = [...props.tags, ...results.tags].filter((tag, index, arr) => arr.indexOf(tag) === index)
      emit('update:tags', newTags)
      
      ElMessage.success(`推荐了 ${results.tags.length} 个标签`)
    } else {
      ElMessage.error(response.message || '标签推荐失败')
    }
  } catch (error) {
    console.error('推荐标签失败:', error)
    ElMessage.error('推荐标签失败')
  } finally {
    loading.tags = false
  }
}

const showOptimizeDialog = () => {
  optimizeForm.content = props.content
  dialogs.optimize = true
}

const optimizeContent = async () => {
  if (!optimizeForm.content) {
    ElMessage.warning('请输入需要优化的内容')
    return
  }

  loading.optimize = true
  try {
    const response = await aiWritingApi.optimizeContent({
      content: optimizeForm.content,
      style: optimizeForm.style,
      target: optimizeForm.target
    })

    if (response.code === 200) {
      results.optimizedContent = response.data.optimizedContent
      results.originalLength = response.data.originalLength
      results.optimizedLength = response.data.optimizedLength
      results.responseTime = response.data.responseTime
      results.model = response.data.model
      
      ElMessage.success('内容优化完成')
    } else {
      ElMessage.error(response.message || '内容优化失败')
    }
  } catch (error) {
    console.error('优化内容失败:', error)
    ElMessage.error('优化内容失败')
  } finally {
    loading.optimize = false
  }
}

const showOutlineDialog = () => {
  outlineForm.topic = props.title
  outlineForm.category = props.category
  dialogs.outline = true
}

const generateOutline = async () => {
  if (!outlineForm.topic) {
    ElMessage.warning('请输入文章主题')
    return
  }

  loading.outline = true
  try {
    const response = await aiWritingApi.generateOutline({
      topic: outlineForm.topic,
      category: outlineForm.category,
      depth: outlineForm.depth
    })

    if (response.code === 200) {
      results.outline = response.data.outline
      results.responseTime = response.data.responseTime
      results.model = response.data.model
      
      ElMessage.success('大纲生成完成')
    } else {
      ElMessage.error(response.message || '大纲生成失败')
    }
  } catch (error) {
    console.error('生成大纲失败:', error)
    ElMessage.error('生成大纲失败')
  } finally {
    loading.outline = false
  }
}

const applyTitle = (title) => {
  emit('update:title', title)
  dialogs.titles = false
  ElMessage.success('标题已应用')
}

const applyOptimizedContent = () => {
  emit('update:content', results.optimizedContent)
  dialogs.optimize = false
  ElMessage.success('优化内容已应用')
}

const applyOutline = () => {
  emit('update:content', results.outline)
  dialogs.outline = false
  ElMessage.success('大纲已应用到编辑器')
}

const formatOutline = (outline) => {
  // 简单的markdown转HTML
  return outline
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^\* (.*$)/gim, '<li>$1</li>')
    .replace(/^\- (.*$)/gim, '<li>$1</li>')
    .replace(/\n/g, '<br>')
}

// 生命周期
onMounted(() => {
  checkAIStatus()
})
</script>

<style scoped lang="less">
.ai-assistant {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;

  .ai-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 14px;

    &.available {
      color: #67c23a;
    }

    &.unavailable {
      color: #f56c6c;
    }

    .el-tag {
      margin-left: auto;
    }
  }

  .ai-functions {
    .el-button-group {
      .el-button {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
}

.dialog-tip {
  margin-bottom: 16px;
  color: #606266;
  font-size: 14px;
}

.title-suggestions {
  .title-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background: #e3f2fd;
      transform: translateY(-1px);
    }

    span {
      flex: 1;
      font-size: 14px;
    }
  }
}

.optimization-result,
.outline-result {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;

  h4 {
    margin-bottom: 12px;
    color: #303133;
  }

  .optimized-content,
  .outline-content {
    padding: 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
    line-height: 1.6;
    white-space: pre-wrap;
  }

  .optimization-stats,
  .response-info {
    margin-top: 8px;
    text-align: right;
  }
}

.response-info {
  margin-top: 12px;
  text-align: right;
}
</style>
