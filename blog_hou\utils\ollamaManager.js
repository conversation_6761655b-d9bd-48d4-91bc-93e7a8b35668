const { spawn, exec } = require('child_process');
const axios = require('axios');
const path = require('path');
const fs = require('fs');

class OllamaManager {
  constructor() {
    this.ollamaProcess = null;
    this.isStarting = false;
    this.startupPromise = null;
    this.config = {
      baseURL: 'http://localhost:11434',
      maxStartupTime: 30000, // 30秒超时
      checkInterval: 1000, // 1秒检查间隔
      logFile: path.join(__dirname, '../logs/ollama.log')
    };
    
    // 确保日志目录存在
    const logDir = path.dirname(this.config.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  // 检查Ollama是否正在运行
  async isOllamaRunning() {
    try {
      const response = await axios.get(`${this.config.baseURL}/api/tags`, {
        timeout: 3000
      });
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  // 检查Ollama是否已安装
  async isOllamaInstalled() {
    return new Promise((resolve) => {
      exec('ollama --version', (error) => {
        resolve(!error);
      });
    });
  }

  // 启动Ollama服务
  async startOllama() {
    console.log('🚀 尝试启动Ollama服务...');
    
    // 如果正在启动，返回现有的Promise
    if (this.isStarting && this.startupPromise) {
      console.log('⏳ Ollama正在启动中，等待完成...');
      return this.startupPromise;
    }

    // 如果已经在运行，直接返回
    if (await this.isOllamaRunning()) {
      console.log('✅ Ollama服务已在运行');
      return { success: true, message: 'Ollama服务已在运行' };
    }

    // 检查是否已安装
    if (!(await this.isOllamaInstalled())) {
      console.log('❌ Ollama未安装');
      return { 
        success: false, 
        message: 'Ollama未安装，请先安装Ollama: https://ollama.ai' 
      };
    }

    this.isStarting = true;
    
    this.startupPromise = new Promise(async (resolve) => {
      try {
        console.log('🔧 启动Ollama进程...');
        
        // 启动Ollama服务
        this.ollamaProcess = spawn('ollama', ['serve'], {
          detached: true,
          stdio: ['ignore', 'pipe', 'pipe']
        });

        // 记录日志
        const logStream = fs.createWriteStream(this.config.logFile, { flags: 'a' });
        logStream.write(`\n=== Ollama启动 ${new Date().toISOString()} ===\n`);
        
        this.ollamaProcess.stdout.on('data', (data) => {
          logStream.write(`[STDOUT] ${data}`);
        });

        this.ollamaProcess.stderr.on('data', (data) => {
          logStream.write(`[STDERR] ${data}`);
        });

        this.ollamaProcess.on('error', (error) => {
          console.error('❌ Ollama进程启动失败:', error);
          logStream.write(`[ERROR] ${error.message}\n`);
          this.isStarting = false;
          resolve({ 
            success: false, 
            message: `Ollama启动失败: ${error.message}` 
          });
        });

        this.ollamaProcess.on('exit', (code) => {
          console.log(`🔄 Ollama进程退出，代码: ${code}`);
          logStream.write(`[EXIT] Process exited with code ${code}\n`);
          this.ollamaProcess = null;
        });

        // 等待服务启动
        const startTime = Date.now();
        let attempts = 0;
        const maxAttempts = this.config.maxStartupTime / this.config.checkInterval;

        const checkService = async () => {
          attempts++;
          console.log(`🔍 检查Ollama服务状态 (${attempts}/${maxAttempts})...`);
          
          if (await this.isOllamaRunning()) {
            console.log('✅ Ollama服务启动成功！');
            this.isStarting = false;
            logStream.write(`[SUCCESS] Ollama started successfully after ${Date.now() - startTime}ms\n`);
            logStream.end();
            
            resolve({ 
              success: true, 
              message: 'Ollama服务启动成功',
              startupTime: Date.now() - startTime
            });
            return;
          }

          if (attempts >= maxAttempts) {
            console.log('⏰ Ollama启动超时');
            this.isStarting = false;
            logStream.write(`[TIMEOUT] Ollama startup timeout after ${this.config.maxStartupTime}ms\n`);
            logStream.end();
            
            resolve({ 
              success: false, 
              message: 'Ollama启动超时，请检查日志' 
            });
            return;
          }

          setTimeout(checkService, this.config.checkInterval);
        };

        // 开始检查
        setTimeout(checkService, this.config.checkInterval);

      } catch (error) {
        console.error('❌ 启动Ollama时发生错误:', error);
        this.isStarting = false;
        resolve({ 
          success: false, 
          message: `启动失败: ${error.message}` 
        });
      }
    });

    return this.startupPromise;
  }

  // 停止Ollama服务
  async stopOllama() {
    if (this.ollamaProcess) {
      console.log('🛑 停止Ollama服务...');
      this.ollamaProcess.kill('SIGTERM');
      this.ollamaProcess = null;
      return { success: true, message: 'Ollama服务已停止' };
    }
    return { success: true, message: 'Ollama服务未在运行' };
  }

  // 获取Ollama状态
  async getStatus() {
    const isRunning = await this.isOllamaRunning();
    const isInstalled = await this.isOllamaInstalled();
    
    let models = [];
    if (isRunning) {
      try {
        const response = await axios.get(`${this.config.baseURL}/api/tags`);
        models = response.data.models || [];
      } catch (error) {
        console.error('获取模型列表失败:', error.message);
      }
    }

    return {
      installed: isInstalled,
      running: isRunning,
      starting: this.isStarting,
      models: models,
      modelCount: models.length,
      processId: this.ollamaProcess?.pid || null
    };
  }

  // 获取日志
  getLog(lines = 50) {
    try {
      if (fs.existsSync(this.config.logFile)) {
        const content = fs.readFileSync(this.config.logFile, 'utf8');
        const logLines = content.split('\n');
        return logLines.slice(-lines).join('\n');
      }
      return '暂无日志';
    } catch (error) {
      return `读取日志失败: ${error.message}`;
    }
  }

  // 清理资源
  cleanup() {
    if (this.ollamaProcess) {
      this.ollamaProcess.kill('SIGTERM');
    }
  }
}

// 创建单例
const ollamaManager = new OllamaManager();

// 进程退出时清理
process.on('exit', () => {
  ollamaManager.cleanup();
});

process.on('SIGINT', () => {
  ollamaManager.cleanup();
  process.exit(0);
});

process.on('SIGTERM', () => {
  ollamaManager.cleanup();
  process.exit(0);
});

module.exports = ollamaManager;
