import request from './index.ts'

// AI写作助手API
export const aiWritingApi = {
  // 获取AI服务状态
  getStatus() {
    return request({
      url: '/ai-writing/status',
      method: 'GET'
    })
  },

  // 生成文章标题
  generateTitle(data) {
    return request({
      url: '/ai-writing/generate-title',
      method: 'POST',
      data: {
        content: data.content,
        category: data.category,
        keywords: data.keywords,
        count: data.count || 3
      }
    })
  },

  // 优化内容
  optimizeContent(data) {
    return request({
      url: '/ai-writing/optimize-content',
      method: 'POST',
      data: {
        content: data.content,
        style: data.style || 'professional',
        target: data.target || 'general'
      }
    })
  },

  // 生成文章大纲
  generateOutline(data) {
    return request({
      url: '/ai-writing/generate-outline',
      method: 'POST',
      data: {
        topic: data.topic,
        category: data.category,
        depth: data.depth || 'medium'
      }
    })
  },

  // 推荐标签
  suggestTags(data) {
    return request({
      url: '/ai-writing/suggest-tags',
      method: 'POST',
      data: {
        title: data.title,
        content: data.content,
        category: data.category,
        maxTags: data.maxTags || 5
      }
    })
  }
}

// 原有的AI API (保持兼容性)
export const aiApi = {
  // 检查AI服务状态
  getStatus() {
    return request.get('/ai/status')
  },

  // 获取可用模型列表
  getModels() {
    return request.get('/ai/models')
  },

  // 设置当前使用的模型
  setModel(model) {
    return request.post('/ai/set-model', { model })
  },

  // 获取AI功能配置
  getFeatures() {
    return request.get('/ai/features')
  },

  // 智能聊天
  chat(data) {
    return request.post('/ai/chat', data)
  },

  // 通用AI对话
  chatGeneral(data) {
    return request.post('/ai/chat-general', data)
  },

  // 自动标签生成
  generateTags(data) {
    return request.post('/ai/generate-tags', data)
  },

  // 内容摘要生成
  generateSummary(data) {
    return request.post('/ai/summarize', data)
  },

  // 标题生成
  generateTitles(data) {
    return request.post('/ai/generate-titles', data)
  },

  // 内容审核
  moderateContent(data) {
    return request.post('/ai/moderate', data)
  },

  // 内容推荐
  getRecommendations(data) {
    return request.post('/ai/recommend', data)
  },

  // AI帮写文章
  generateArticle(data) {
    return request.post('/ai/generate-article', data)
  },

  // 续写文章
  continueArticle(data) {
    return request.post('/ai/continue-article', data)
  },

  // 生成文章大纲
  generateOutline(data) {
    return request.post('/ai/generate-outline', data)
  },

  // 批量处理
  batchProcess(data) {
    return request.post('/ai/batch-process', data)
  }
}

// Ollama管理API
export const ollamaApi = {
  // 自动启动Ollama服务
  autoStart() {
    return request.post('/ollama/auto-start')
  },

  // 手动启动Ollama服务
  start() {
    return request.post('/ollama/start')
  },

  // 停止Ollama服务
  stop() {
    return request.post('/ollama/stop')
  },

  // 重启Ollama服务
  restart() {
    return request.post('/ollama/restart')
  },

  // 获取Ollama状态
  getStatus() {
    return request.get('/ollama/status')
  },

  // 获取Ollama日志
  getLogs(lines = 50) {
    return request.get('/ollama/logs', { params: { lines } })
  },

  // 健康检查
  healthCheck() {
    return request.get('/ollama/health')
  }
}

// AI助手工具类
export class AIWritingAssistant {
  constructor() {
    this.isAvailable = false
    this.models = []
    this.currentModel = ''
    this.checkStatus()
  }

  // 检查AI服务状态
  async checkStatus() {
    try {
      const response = await aiWritingApi.getStatus()
      if (response.code === 200) {
        this.isAvailable = response.data.available
        this.models = response.data.models
        this.currentModel = response.data.currentModel
      }
      return this.isAvailable
    } catch (error) {
      console.error('检查AI服务状态失败:', error)
      this.isAvailable = false
      return false
    }
  }

  // 生成标题建议
  async generateTitles(content, category = '', keywords = '', count = 3) {
    if (!this.isAvailable) {
      throw new Error('AI服务不可用')
    }

    try {
      const response = await aiWritingApi.generateTitle({
        content,
        category,
        keywords,
        count
      })

      if (response.code === 200) {
        return {
          success: true,
          titles: response.data.titles,
          responseTime: response.data.responseTime,
          model: response.data.model
        }
      } else {
        throw new Error(response.message || '生成标题失败')
      }
    } catch (error) {
      console.error('生成标题失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 优化内容
  async optimizeContent(content, style = 'professional', target = 'general') {
    if (!this.isAvailable) {
      throw new Error('AI服务不可用')
    }

    try {
      const response = await aiWritingApi.optimizeContent({
        content,
        style,
        target
      })

      if (response.code === 200) {
        return {
          success: true,
          optimizedContent: response.data.optimizedContent,
          originalLength: response.data.originalLength,
          optimizedLength: response.data.optimizedLength,
          responseTime: response.data.responseTime,
          model: response.data.model
        }
      } else {
        throw new Error(response.message || '内容优化失败')
      }
    } catch (error) {
      console.error('内容优化失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 推荐标签
  async suggestTags(title = '', content = '', category = '', maxTags = 5) {
    if (!this.isAvailable) {
      throw new Error('AI服务不可用')
    }

    try {
      const response = await aiWritingApi.suggestTags({
        title,
        content,
        category,
        maxTags
      })

      if (response.code === 200) {
        return {
          success: true,
          tags: response.data.tags,
          responseTime: response.data.responseTime,
          model: response.data.model
        }
      } else {
        throw new Error(response.message || '标签推荐失败')
      }
    } catch (error) {
      console.error('标签推荐失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 获取服务信息
  getServiceInfo() {
    return {
      isAvailable: this.isAvailable,
      models: this.models,
      currentModel: this.currentModel
    }
  }
}

// 创建全局AI助手实例
export const aiAssistant = new AIWritingAssistant()

export default aiApi
