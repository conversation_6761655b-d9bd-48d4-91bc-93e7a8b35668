/// <reference types="vite/client" />

// 声明axios模块
declare module 'axios' {
  export interface AxiosRequestConfig {
    baseURL?: string;
    withCredentials?: boolean;
    timeout?: number;
    headers?: any;
    [key: string]: any;
  }

  export interface AxiosResponse<T = any> {
    data: T;
    status: number;
    statusText: string;
    headers: any;
    config: AxiosRequestConfig;
  }

  export interface AxiosInstance {
    interceptors: {
      request: any;
      response: any;
    };
    create(config?: AxiosRequestConfig): AxiosInstance;
    get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T>;
    post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
    put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
    delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T>;
  }

  const axios: AxiosInstance & {
    create(config?: AxiosRequestConfig): AxiosInstance;
  };

  export default axios;
}
