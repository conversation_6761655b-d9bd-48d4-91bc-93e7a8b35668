/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AIHelper: typeof import('./src/components/AIHelper.vue')['default']
    AIWriter: typeof import('./src/components/AIWriter.vue')['default']
    AIWritingAssistant: typeof import('./src/components/AIWritingAssistant.vue')['default']
    ArticleAITools: typeof import('./src/components/ArticleAITools.vue')['default']
    ArticleFavorite: typeof import('./src/components/ArticleFavorite.vue')['default']
    ArticleLike: typeof import('./src/components/ArticleLike.vue')['default']
    BoFang: typeof import('./src/components/BoFang.vue')['default']
    ChartTypeSwitcher: typeof import('./src/components/ChartTypeSwitcher.vue')['default']
    CommentItem: typeof import('./src/components/CommentItem.vue')['default']
    copy: typeof import('./src/components/EnglishTranslator copy.vue')['default']
    Daochusql: typeof import('./src/components/Daochusql.vue')['default']
    EffectManager: typeof import('./src/components/texiao/EffectManager.vue')['default']
    EffectPreview: typeof import('./src/components/EffectPreview.vue')['default']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElTag: typeof import('element-plus/es')['ElTag']
    EnglishTranslator: typeof import('./src/components/EnglishTranslator.vue')['default']
    'EnglishTranslator copy': typeof import('./src/components/EnglishTranslator copy.vue')['default']
    EnhancedUpload: typeof import('./src/components/EnhancedUpload.vue')['default']
    FileManager: typeof import('./src/components/FileManager.vue')['default']
    FileManagerDebug: typeof import('./src/components/FileManagerDebug.vue')['default']
    FileTransferMonitor: typeof import('./src/components/dashboard/FileTransferMonitor.vue')['default']
    Jiami: typeof import('./src/components/Jiami.vue')['default']
    Meteor: typeof import('./src/components/texiao/Meteor.vue')['default']
    NightSky: typeof import('./src/components/texiao/NightSky.vue')['default']
    Particle: typeof import('./src/components/texiao/Particle.vue')['default']
    ParticleExplosion: typeof import('./src/components/texiao/ParticleExplosion.vue')['default']
    PasswordManage: typeof import('./src/components/PasswordManage.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Share: typeof import('./src/components/Share.vue')['default']
    Shipinjiami: typeof import('./src/components/Shipinjiami.vue')['default']
    SpeedLimitConfig: typeof import('./src/components/dashboard/SpeedLimitConfig.vue')['default']
    UserLevelManagement: typeof import('./src/components/dashboard/UserLevelManagement.vue')['default']
  }
}
