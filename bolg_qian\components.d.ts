/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AIHelper: typeof import('./src/components/AIHelper.vue')['default']
    AIWriter: typeof import('./src/components/AIWriter.vue')['default']
    AIWritingAssistant: typeof import('./src/components/AIWritingAssistant.vue')['default']
    ArticleAITools: typeof import('./src/components/ArticleAITools.vue')['default']
    ArticleFavorite: typeof import('./src/components/ArticleFavorite.vue')['default']
    ArticleLike: typeof import('./src/components/ArticleLike.vue')['default']
    BoFang: typeof import('./src/components/BoFang.vue')['default']
    ChartTypeSwitcher: typeof import('./src/components/ChartTypeSwitcher.vue')['default']
    CommentItem: typeof import('./src/components/CommentItem.vue')['default']
    Daochusql: typeof import('./src/components/Daochusql.vue')['default']
    EffectManager: typeof import('./src/components/texiao/EffectManager.vue')['default']
    EffectPreview: typeof import('./src/components/EffectPreview.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    EnglishTranslator: typeof import('./src/components/EnglishTranslator.vue')['default']
    EnhancedUpload: typeof import('./src/components/EnhancedUpload.vue')['default']
    FileManager: typeof import('./src/components/FileManager.vue')['default']
    FileTransferMonitor: typeof import('./src/components/dashboard/FileTransferMonitor.vue')['default']
    Jiami: typeof import('./src/components/Jiami.vue')['default']
    LazyImage: typeof import('./src/components/LazyImage.vue')['default']
    MemoryMonitor: typeof import('./src/components/MemoryMonitor.vue')['default']
    Meteor: typeof import('./src/components/texiao/Meteor.vue')['default']
    NightSky: typeof import('./src/components/texiao/NightSky.vue')['default']
    Particle: typeof import('./src/components/texiao/Particle.vue')['default']
    ParticleExplosion: typeof import('./src/components/texiao/ParticleExplosion.vue')['default']
    PasswordManage: typeof import('./src/components/PasswordManage.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Share: typeof import('./src/components/Share.vue')['default']
    Shipinjiami: typeof import('./src/components/Shipinjiami.vue')['default']
    SpeedLimitConfig: typeof import('./src/components/dashboard/SpeedLimitConfig.vue')['default']
    UserLevelManagement: typeof import('./src/components/dashboard/UserLevelManagement.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanList: typeof import('vant/es')['List']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanUploader: typeof import('vant/es')['Uploader']
    VirtualList: typeof import('./src/components/VirtualList.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
