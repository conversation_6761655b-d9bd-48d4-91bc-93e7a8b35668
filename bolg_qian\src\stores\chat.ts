import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { io } from 'socket.io-client'
import { ElMessage } from 'element-plus'
import {
  GetChatRecordApi,
  UploadChatImageApi,
  GetFriendListApi,
  MarkOfflineMessagesReadApi,
  GetUnreadMessageCountApi,
  websocketApi
} from '@/utils/api'
import { useUserStore } from './user'

// 常量
const API_BASE_URL = 'http://192.168.31.222:3000'
const SOCKET_URL = 'http://192.168.31.222:3000'

export const useChatStore = defineStore('chat', () => {
  // 获取用户信息
  const userStore = useUserStore()
  
  // 状态
  const messages = ref([])
  const onlineUsers = ref([])
  const friends = ref([])
  const privateTarget = ref(null)
  const socket = ref(null)
  const isConnected = ref(false)
  const isReconnecting = ref(false)
  const unreadCount = ref(0)
  
  // 内部状态
  let socketIOServerStarted = false
  let reconnectTimer = null
  let unreadCountTimer = null
  let leaved = false
  
  // 计算属性
  const filteredMessages = computed(() => {
    let msgs = []
    
    if (!privateTarget.value) {
      // 公共聊天：只显示群聊消息或系统消息
      msgs = messages.value.filter(msg => {
        const isGroupChat = (!msg.to || msg.to === null || msg.to === '' || msg.to === undefined)
        const isSystem = msg.id === 'system'
        return isGroupChat || isSystem
      })
    } else {
      // 私聊：显示与当前私聊对象的消息
      msgs = messages.value.filter(msg => {
        // 系统消息始终显示
        if (msg.id === 'system') return true
        
        // 私聊消息：检查是否是当前私聊对象的消息
        if (msg.to) {
          const currentUserId = String(userStore.id)
          const currentTargetId = String(privateTarget.value.id)
          const messageFromId = String(msg.id)
          const messageToId = String(msg.to)
          
          const isFromTarget = messageFromId === currentTargetId && messageToId === currentUserId
          const isToTarget = messageFromId === currentUserId && messageToId === currentTargetId
          return isFromTarget || isToTarget
        }
        
        // 群聊消息不显示在私聊中
        return false
      })
    }
    
    return msgs.slice().sort((a, b) => a.time - b.time)
  })
  
  // 工具函数 - 处理静态资源URL
  function getStaticUrl(url) {
    if (!url) {
      return `/api/avatars/moren.png`
    }

    // 如果已经是完整URL，直接返回
    if (url.startsWith('http')) {
      return url
    }

    // 清理URL
    let cleanUrl = url.trim()
    
    // 处理嵌套路径问题
    cleanUrl = cleanUrl.replace(/\/+/g, '/')
    
    // 移除路径中的/uploads/avatars/部分
    if (cleanUrl.includes('/uploads/avatars/')) {
      const fileName = cleanUrl.split('/uploads/avatars/').pop()
      cleanUrl = `/avatars/${fileName}`
    }
    
    // 处理/api/前缀
    if (cleanUrl.startsWith('/api/')) {
      cleanUrl = cleanUrl.substring(4)
    }
    
    // 确保avatars路径正确
    if (cleanUrl.includes('avatars/') && !cleanUrl.startsWith('/avatars/')) {
      cleanUrl = `/avatars/${cleanUrl.split('avatars/').pop()}`
    }
    
    // 确保以/开头
    if (!cleanUrl.startsWith('/')) {
      cleanUrl = '/' + cleanUrl
    }

    // 如果包含chat_images，使用完整URL
    if (cleanUrl.includes('chat_images/')) {
      const imgName = cleanUrl.includes('/') ? cleanUrl.split('/').pop() : cleanUrl
      return `${API_BASE_URL}/chat_images/${imgName}`
    }

    // 使用/api代理
    return `/api${cleanUrl}`
  }
  
  // 消息去重函数
  function isMessageDuplicate(newMsg, existingMessages) {
    return existingMessages.some(existingMsg => {
      // 基本字段匹配
      const basicMatch = existingMsg.id === newMsg.id && 
                        existingMsg.time === newMsg.time && 
                        existingMsg.message === newMsg.message &&
                        existingMsg.to === newMsg.to
      
      // 如果是图片消息，还要检查URL
      if (newMsg.type === 'image' && existingMsg.type === 'image') {
        return basicMatch && existingMsg.url === newMsg.url
      }
      
      return basicMatch
    })
  }
  
  // 动作
  async function connectSocket() {
    if (leaved) return

    // 先确保Socket.IO服务器已启动
    const serverStarted = await startSocketIOServer()
    if (!serverStarted) {
      console.error('Socket.IO服务器启动失败，无法建立连接')
      ElMessage.error('聊天服务器启动失败，请稍后重试')
      return
    }

    // 等待一小段时间确保服务器完全启动
    await new Promise(resolve => setTimeout(resolve, 1000))

    try {
      console.log('正在连接Socket.IO服务器...')
      socket.value = io(SOCKET_URL, {
        autoConnect: true,
        reconnection: true,
        reconnectionDelay: 3000,
        reconnectionAttempts: 5,
        timeout: 20000
      })

      // Socket.IO 连接成功
      socket.value.on('connect', () => {
        console.log('Socket.IO连接成功')
        isConnected.value = true
        isReconnecting.value = false
        clearTimeout(reconnectTimer)

        // 获取用户头像文件名
        let avatarFileName = 'moren.png'
        if (userStore.avatar && userStore.avatar.includes('/')) {
          avatarFileName = userStore.avatar.split('/').pop()
        }
        
        // 加入聊天室
        socket.value.emit('join', {
          id: String(userStore.id),
          nickname: userStore.nickname || userStore.username,
          avatar: avatarFileName // 只发送文件名
        })
      })

      // 接收消息
      socket.value.on('message', (data) => {
        console.log('收到Socket.IO消息:', data)
        handleIncomingMessage(data)
      })

      // 接收在线用户列表
      socket.value.on('onlineUsers', (data) => {
        console.log('收到在线用户列表:', data.users)
        onlineUsers.value = (data.users || []).map(user => {
          return {
            ...user,
            avatar: user.avatar ? getStaticUrl('avatars/' + user.avatar) : getStaticUrl(null)
          }
        })
      })

      // 断开连接
      socket.value.on('disconnect', (reason) => {
        console.log('Socket.IO断开连接:', reason)
        isConnected.value = false
        if (!leaved) {
          isReconnecting.value = true
          ElMessage.warning('连接已断开，正在重连...')
        }
      })

      // 重连成功
      socket.value.on('reconnect', () => {
        console.log('Socket.IO重连成功')
        isReconnecting.value = false
        ElMessage.success('重连成功')
      })

      // 连接错误
      socket.value.on('connect_error', (error) => {
        console.error('Socket.IO连接错误:', error)
        isConnected.value = false
        if (!leaved) {
          isReconnecting.value = true
        }
      })

    } catch (error) {
      console.error('连接Socket.IO失败:', error)
      ElMessage.error('连接聊天服务器失败')
    }
  }

  async function startSocketIOServer() {
    if (socketIOServerStarted) {
      console.log('Socket.IO服务器已经启动')
      return true
    }

    try {
      console.log('正在启动Socket.IO服务器...')
      const response = await websocketApi.start()

      if (response && response.code === 200) {
        socketIOServerStarted = true
        console.log('Socket.IO服务器启动成功:', response.data)
        ElMessage.success('聊天服务器已启动')
        return true
      } else {
        console.error('Socket.IO服务器启动失败:', response)
        ElMessage.error('聊天服务器启动失败')
        return false
      }
    } catch (error) {
      console.error('启动Socket.IO服务器异常:', error)
      ElMessage.error('启动聊天服务器时发生错误')
      return false
    }
  }
  
  function handleIncomingMessage(data) {
    try {
      console.log('收到消息原始数据:', data)
      
      // 系统消息始终显示
      if (data.id === 'system') {
        console.log('处理系统消息')
        messages.value.push(data)
        return
      }

      // 调试 - 分析头像数据格式
      console.log('头像分析:', {
        avatar字段: data.avatar,
        avatar类型: typeof data.avatar,
        avatar字段路径: data.avatar ? (data.avatar.startsWith('http') ? '完整URL' : '相对路径') : '无'
      })
      
      // 处理嵌套路径问题
      if (data.avatar && data.avatar.includes('//')) {
        const originalAvatar = data.avatar
        data.avatar = data.avatar.replace(/\/+/g, '/')
        console.log('修复嵌套路径:', { 原始: originalAvatar, 修复后: data.avatar })
      }
      
      // 处理错误的uploads路径
      if (data.avatar && data.avatar.includes('/uploads/avatars/')) {
        const originalAvatar = data.avatar
        const fileName = data.avatar.split('/uploads/avatars/').pop()
        data.avatar = `/avatars/${fileName}`
        console.log('修复uploads路径:', { 原始: originalAvatar, 修复后: data.avatar })
      }
      
      // 处理消息头像URL
      if (data.avatar) {
        const originalAvatar = data.avatar
        
        // 如果是文件名而不是路径，添加前缀
        if (!data.avatar.includes('/') && !data.avatar.startsWith('http')) {
          data.avatar = `/avatars/${data.avatar}`
        }
        
        // 确保使用完整URL，但使用/api代理路径避免CORS问题
        if (!data.avatar.startsWith('http')) {
          // 使用/api代理而不是直接使用API_BASE_URL
          data.avatar = `/api${data.avatar.startsWith('/') ? data.avatar : '/' + data.avatar}`
        }
        
        console.log('头像处理完成:', { 原始: originalAvatar, 处理后: data.avatar })
      } else {
        // 使用代理路径的默认头像
        data.avatar = '/api/avatars/moren.png'
        console.log('使用默认头像:', data.avatar)
      }

      // 处理图片消息URL
      if (data.type === 'image' && data.url) {
        const originalUrl = data.url
        console.log('原始图片URL:', originalUrl)
        
        // 移除可能存在的重复/api前缀
        let cleanUrl = originalUrl
        if (cleanUrl.startsWith('/api/api/')) {
          cleanUrl = cleanUrl.replace('/api/api/', '/api/')
        }
        
        // 如果包含chat_images但不是完整URL，直接使用完整URL
        if (cleanUrl.includes('chat_images/') && !cleanUrl.startsWith('http')) {
          const imgName = cleanUrl.includes('/') ? cleanUrl.split('/').pop() : cleanUrl
          data.url = `${API_BASE_URL}/chat_images/${imgName}`
        }
        // 如果不是完整URL也不包含chat_images，使用代理路径
        else if (!cleanUrl.startsWith('http')) {
          // 避免重复的/api前缀
          if (cleanUrl.startsWith('/api/')) {
            data.url = cleanUrl
          } else {
            data.url = `/api${cleanUrl.startsWith('/') ? cleanUrl : '/' + cleanUrl}`
          }
        }
        
        console.log('图片URL处理完成:', { 原始: originalUrl, 处理后: data.url })
      }

      console.log('最终处理后的消息:', data)

      // 消息去重
      if (isMessageDuplicate(data, messages.value)) {
        console.log('检测到重复消息，跳过')
        return
      }

      // 添加消息到列表
      messages.value.push(data)
      
    } catch (error) {
      console.error('处理消息时出错:', error)
    }
  }
  
  async function loadChatHistory() {
    try {
      const res = await GetChatRecordApi({
        userId: userStore.id,
        to: privateTarget.value?.id || null
      })
      
      if (!res || !res.data) return
      
      const processedMessages = (res.data || []).map(msg => {
        // 处理群聊消息
        if (msg.from_nickname && (msg.to_id === null || msg.to_id === '' || msg.to_id === undefined)) {
          let avatarUrl = getStaticUrl(null)
          if (msg.from_avatar) {
            avatarUrl = getStaticUrl('avatars/' + msg.from_avatar)
          }
          
          let imageUrl = msg.url
          if (msg.type === 'image' && msg.url) {
            imageUrl = getStaticUrl(msg.url)
          }

          return {
            id: msg.from_id,
            nickname: msg.from_nickname,
            avatar: avatarUrl,
            message: msg.message,
            type: msg.type || 'text',
            url: imageUrl,
            filename: msg.filename,
            time: msg.time,
            to: null,
            is_read: true
          }
        }
        // 处理私聊消息
        else if (msg.from_nickname && msg.to_id) {
          let avatarUrl = getStaticUrl(null)
          if (msg.from_avatar) {
            avatarUrl = getStaticUrl('avatars/' + msg.from_avatar)
          }
          
          let imageUrl = msg.url
          if (msg.type === 'image' && msg.url) {
            imageUrl = getStaticUrl(msg.url)
          }

          return {
            id: msg.from_id,
            nickname: msg.from_nickname,
            avatar: avatarUrl,
            message: msg.message,
            type: msg.type || 'text',
            url: imageUrl,
            filename: msg.filename,
            time: msg.time,
            to: msg.to_id,
            is_read: msg.is_read
          }
        }
        // 保持原样
        else {
          return msg
        }
      })
      
      // 更新消息列表
      messages.value = processedMessages
      
      // 如果有私聊对象，标记离线消息为已读
      if (privateTarget.value) {
        try {
          await MarkOfflineMessagesReadApi(userStore.id)
          // 更新未读消息数量
          await loadUnreadCount()
        } catch (err) {
          console.error('标记已读失败:', err)
        }
      }
    } catch (err) {
      ElMessage.error('加载聊天记录失败')
      console.error(err)
    }
  }
  
  async function loadFriends() {
    try {
      const res = await GetFriendListApi(Number(userStore.id))
      
      friends.value = (res?.data || []).map(friend => {
        return {
          ...friend,
          id: String(friend.id || friend.user_id || friend.friend_id),
          nickname: friend.nickname || friend.username,
          avatar: friend.avatar ? getStaticUrl('avatars/' + friend.avatar) : getStaticUrl(null)
        }
      })
    } catch (err) {
      ElMessage.error('加载好友列表失败')
      console.error(err)
    }
  }
  
  async function loadUnreadCount() {
    try {
      const res = await GetUnreadMessageCountApi(userStore.id)
      if (res.code === 0) {
        unreadCount.value = res.data.count
      }
    } catch (err) {
      console.error('获取未读消息数量失败:', err)
    }
  }
  
  function sendMessage(content, type = 'text', url = null) {
    if (!content && type === 'text') return
    
    // 获取用户头像文件名
    let avatarFileName = 'moren.png'
    if (userStore.avatar && userStore.avatar.includes('/')) {
      avatarFileName = userStore.avatar.split('/').pop()
    }

    const msg = {
      id: String(userStore.id),
      nickname: userStore.nickname || userStore.username,
      avatar: avatarFileName,
      message: content,
      url: url,
      time: Date.now(),
      type: type,
      to: privateTarget.value?.id ? String(privateTarget.value.id) : null
    }

    console.log('发送消息:', msg)
    socket.value?.emit('message', msg)
  }
  
  async function uploadImage(file) {
    const formData = new FormData()
    formData.append("file", file)
    
    try {
      const res = await UploadChatImageApi(formData)
      if (res.code === 0) {
        console.log('图片上传成功，原始URL:', res.url)
        
        // 处理图片URL
        let imgUrl = res.url
        
        // 如果包含chat_images但不是完整URL，使用完整URL
        if (imgUrl.includes('chat_images/') && !imgUrl.startsWith('http')) {
          const imgName = imgUrl.includes('/') ? imgUrl.split('/').pop() : imgUrl
          imgUrl = `${API_BASE_URL}/chat_images/${imgName}`
        }
        
        console.log('处理后的图片URL:', imgUrl)
        
        // 发送图片消息
        sendMessage('', 'image', imgUrl)
        return imgUrl
      } else {
        ElMessage.error('图片上传失败')
        return null
      }
    } catch (err) {
      ElMessage.error('图片上传失败')
      console.error(err)
      return null
    }
  }
  
  function setPrivateTarget(user) {
    if (!user) {
      privateTarget.value = null
    } else {
      privateTarget.value = {
        ...user,
        id: String(user.id)
      }
    }
    
    // 重新加载历史记录
    loadChatHistory()
  }
  
  function disconnect() {
    leaved = true
    socket.value?.disconnect()
    clearTimeout(reconnectTimer)
    if (unreadCountTimer) {
      clearInterval(unreadCountTimer)
      unreadCountTimer = null
    }
  }
  
  function startUnreadCountTimer() {
    unreadCountTimer = setInterval(() => {
      if (!leaved) {
        loadUnreadCount()
      }
    }, 10000) // 每10秒更新一次
  }

  function clearMessages() {
    messages.value = []
  }

  return {
    // 状态
    messages,
    onlineUsers,
    friends,
    privateTarget,
    isConnected,
    isReconnecting,
    unreadCount,
    
    // 计算属性
    filteredMessages,
    
    // 动作
    connectSocket,
    loadChatHistory,
    loadFriends,
    loadUnreadCount,
    sendMessage,
    uploadImage,
    setPrivateTarget,
    disconnect,
    startUnreadCountTimer,
    clearMessages,
    
    // 工具函数
    getStaticUrl
  }
}) 