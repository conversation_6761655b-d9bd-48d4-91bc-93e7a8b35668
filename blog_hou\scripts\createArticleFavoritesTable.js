const db = require("../utils/db");

async function createArticleFavoritesTable() {
  try {
    console.log('🔧 开始创建文章收藏相关表...\n');

    // 1. 创建文章收藏表
    console.log('📝 创建文章收藏表 (article_favorites)...');
    const createFavoritesTable = `
      CREATE TABLE IF NOT EXISTS article_favorites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT DEFAULT NULL COMMENT '用户ID，NULL表示匿名用户',
        article_id INT NOT NULL COMMENT '文章ID',
        ip_address VARCHAR(45) NOT NULL COMMENT 'IP地址',
        user_agent TEXT COMMENT '用户代理',
        session_id VARCHAR(64) COMMENT '会话ID（用于匿名用户）',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        -- 索引
        INDEX idx_article_id (article_id),
        INDEX idx_user_id (user_id),
        INDEX idx_ip_address (ip_address),
        INDEX idx_session_id (session_id),
        INDEX idx_created_at (created_at),
        
        -- 外键约束
        FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        
        -- 唯一约束：防止重复收藏
        UNIQUE KEY unique_user_article (user_id, article_id),
        UNIQUE KEY unique_anonymous_article (ip_address, session_id, article_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
        COMMENT='文章收藏表';
    `;
    
    await db.query(createFavoritesTable);
    console.log('✅ 文章收藏表创建成功');

    // 2. 为articles表添加收藏统计字段（如果不存在）
    console.log('\n📝 检查并添加文章收藏统计字段...');
    
    try {
      // 检查favorites_count字段是否存在
      const checkFavoritesCount = `
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'articles' 
        AND COLUMN_NAME = 'favorites_count'
      `;
      const favoritesCountExists = await db.query(checkFavoritesCount);
      
      if (favoritesCountExists.length === 0) {
        const addFavoritesCount = `
          ALTER TABLE articles 
          ADD COLUMN favorites_count INT DEFAULT 0 COMMENT '收藏数量'
        `;
        await db.query(addFavoritesCount);
        console.log('✅ 添加 favorites_count 字段成功');
      } else {
        console.log('✅ favorites_count 字段已存在');
      }

    } catch (error) {
      console.log('⚠️ 添加统计字段时出错:', error.message);
    }

    // 3. 创建触发器来自动更新收藏统计
    console.log('\n📝 创建收藏统计触发器...');
    
    try {
      // 删除已存在的触发器
      await db.query('DROP TRIGGER IF EXISTS update_article_favorites_after_insert');
      await db.query('DROP TRIGGER IF EXISTS update_article_favorites_after_delete');

      // 创建插入触发器
      const createInsertTrigger = `
        CREATE TRIGGER update_article_favorites_after_insert
        AFTER INSERT ON article_favorites
        FOR EACH ROW
        BEGIN
          UPDATE articles SET favorites_count = favorites_count + 1 WHERE id = NEW.article_id;
        END
      `;
      await db.query(createInsertTrigger);

      // 创建删除触发器
      const createDeleteTrigger = `
        CREATE TRIGGER update_article_favorites_after_delete
        AFTER DELETE ON article_favorites
        FOR EACH ROW
        BEGIN
          UPDATE articles SET favorites_count = favorites_count - 1 WHERE id = OLD.article_id;
        END
      `;
      await db.query(createDeleteTrigger);

      console.log('✅ 收藏统计触发器创建成功');
    } catch (error) {
      console.log('⚠️ 创建触发器时出错:', error.message);
    }

    // 4. 初始化现有文章的收藏统计
    console.log('\n📝 初始化现有文章的收藏统计...');
    try {
      const initStats = `
        UPDATE articles a 
        SET favorites_count = (
          SELECT COUNT(*) FROM article_favorites af 
          WHERE af.article_id = a.id
        )
      `;
      await db.query(initStats);
      console.log('✅ 现有文章收藏统计初始化成功');
    } catch (error) {
      console.log('⚠️ 初始化统计时出错:', error.message);
    }

    console.log('\n🎉 文章收藏功能数据库初始化完成！');
    
    // 显示表结构
    console.log('\n📋 article_favorites 表结构:');
    const tableStructure = await db.query('DESCRIBE article_favorites');
    tableStructure.forEach(col => {
      console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(可空)' : '(非空)'} ${col.Key ? `[${col.Key}]` : ''}`);
    });

    // 显示当前收藏统计
    console.log('\n📊 当前收藏统计:');
    const stats = await db.query(`
      SELECT 
        COUNT(*) as total_favorites,
        COUNT(DISTINCT article_id) as articles_with_favorites,
        COUNT(DISTINCT user_id) as users_with_favorites
      FROM article_favorites
    `);
    console.log(`  总收藏数: ${stats[0].total_favorites}`);
    console.log(`  被收藏文章数: ${stats[0].articles_with_favorites}`);
    console.log(`  收藏用户数: ${stats[0].users_with_favorites}`);

  } catch (error) {
    console.error('❌ 创建文章收藏表失败:', error.message);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createArticleFavoritesTable()
    .then(() => {
      console.log('✅ 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { createArticleFavoritesTable };
