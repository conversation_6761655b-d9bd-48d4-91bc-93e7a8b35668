const Router = require("koa-router");
const fs = require("fs");
const path = require("path");
const bodyParser = require("koa-bodyparser");
const { processFiles } = require("../../utils/bingfa/threadPool");

const jiamiRouter = new Router();
jiamiRouter.use(bodyParser());

const baseDir = path.join(__dirname, "../../public/media");

jiamiRouter.post("/videos", async (ctx) => {
  const { type, filename } = ctx.request.body;
  if (type !== "1" && type !== "0") {
    ctx.status = 400;
    ctx.body = { error: "参数 type 必须为 1（加密）或 0（解密）" };
    return;
  }

  const isEncrypt = type === "1";
  let files;

  if (filename) {
    // 指定单文件处理，先检查文件是否存在且格式正确
    const fullPath = path.join(baseDir, filename);
    if (!fs.existsSync(fullPath)) {
      ctx.status = 404;
      ctx.body = { error: "指定文件不存在" };
      return;
    }
    const valid = isEncrypt
      ? /\.(mp4|avi|mov)$/i.test(filename)
      : /\.enc$/i.test(filename);
    if (!valid) {
      ctx.status = 400;
      ctx.body = { error: "文件格式不符合要求" };
      return;
    }
    files = [filename];
  } else {
    // 没指定文件则全部符合格式的处理
    files = fs
      .readdirSync(baseDir)
      .filter((f) =>
        isEncrypt ? /\.(mp4|avi|mov)$/i.test(f) : /\.enc$/i.test(f)
      );
  }

  if (!files.length) {
    ctx.body = {
      success: true,
      message: "没有符合条件的文件可以处理。",
    };
    return;
  }

  // 这里的进度回调，会在 processFiles 内触发，打印日志并收集进度数据
  const logs = [];
  const onProgress = ({ current, total, filename, status }) => {
    const msg = `[${current}/${total}] ${filename} ${status}`;
    logs.push(msg);
    console.log(msg);
  };

  try {
    const result = await processFiles(
      files,
      baseDir,
      isEncrypt ? "encrypt" : "decrypt",
      onProgress
    );
    ctx.body = {
      success: true,
      type: isEncrypt ? "加密" : "解密",
      message: `${isEncrypt ? "加密" : "解密"}完成，共处理 ${
        result.length
      } 个文件`,
      files: result,
      logs,
    };
  } catch (err) {
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: `${isEncrypt ? "加密" : "解密"}失败: ${err.message}`,
      logs,
    };
  }
});

// 单文件加密解密接口
// route.js 继续写在文件里（与之前 /videos 平级）
jiamiRouter.post("/video1", async (ctx) => {
  let { type, filename } = ctx.request.body;
  console.log(type, filename);
  if (type !== "1" && type !== "0") {
    ctx.status = 400;
    ctx.body = { error: "参数 type 必须为 1（加密）或 0（解密）" };
    return;
  }

  if (!filename) {
    ctx.status = 400;
    ctx.body = { error: "必须指定 filename 参数" };
    return;
  }

  const isEncrypt = type === "1";
  // 自动补全后缀
  if (isEncrypt && !/\.(mp4|avi|mov)$/i.test(filename)) {
    filename += ".mp4"; // 你也可以根据实际情况选择默认后缀
  }
  if (!isEncrypt && !/\.enc$/i.test(filename)) {
    filename += ".mp4.enc";
  }

  const baseDir = path.join(__dirname, "../../public/media");
  const fullPath = path.join(baseDir, filename);

  if (!fs.existsSync(fullPath)) {
    ctx.status = 404;
    ctx.body = { error: "指定文件不存在" };
    return;
  }

  const valid = isEncrypt
    ? /\.(mp4|avi|mov)$/i.test(filename)
    : /\.enc$/i.test(filename);
  if (!valid) {
    ctx.status = 400;
    ctx.body = { error: "文件格式不符合要求" };
    return;
  }

  const logs = [];
  const onProgress = ({ current, total, filename, status }) => {
    const msg = `[进度] ${filename} ${status} (${current}/${total})`;
    logs.push(msg);
    console.log(msg);
  };

  try {
    const result = await require("../../utils/bingfa/threadPool").processFiles(
      [filename],
      baseDir,
      isEncrypt ? "encrypt" : "decrypt",
      onProgress
    );
    ctx.body = {
      success: true,
      type: isEncrypt ? "加密" : "解密",
      message: `${isEncrypt ? "加密" : "解密"}成功`,
      files: result,
      logs,
    };
  } catch (err) {
    console.error('单文件加密解密异常:', err);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: err.message,
      logs,
    };
  }
});

// 获取可加密/解密的文件列表
jiamiRouter.get("/files", async (ctx) => {
  try {
    const { type } = ctx.query;

    if (type !== "1" && type !== "0") {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: "参数 type 必须为 1（加密）或 0（解密）"
      };
      return;
    }

    const isEncrypt = type === "1";

    // 读取目录中的文件
    const files = fs.readdirSync(baseDir)
      .filter((f) => {
        // 根据模式过滤文件
        if (isEncrypt) {
          // 加密模式：显示视频文件
          return /\.(mp4|avi|mov|mkv|wmv)$/i.test(f);
        } else {
          // 解密模式：显示加密文件
          return /\.enc$/i.test(f);
        }
      })
      .map(filename => {
        const filePath = path.join(baseDir, filename);
        const stats = fs.statSync(filePath);

        return {
          name: filename,
          size: stats.size,
          modified: stats.mtime.toISOString(),
          canEncrypt: isEncrypt && /\.(mp4|avi|mov|mkv|wmv)$/i.test(filename),
          canDecrypt: !isEncrypt && /\.enc$/i.test(filename),
          type: isEncrypt ? 'video' : 'encrypted'
        };
      })
      .sort((a, b) => new Date(b.modified) - new Date(a.modified)); // 按修改时间倒序

    ctx.body = {
      success: true,
      files,
      total: files.length,
      mode: isEncrypt ? 'encrypt' : 'decrypt'
    };

  } catch (error) {
    console.error('获取文件列表失败:', error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: error.message || "获取文件列表失败"
    };
  }
});

module.exports = jiamiRouter;
