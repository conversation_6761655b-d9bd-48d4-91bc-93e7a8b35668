// 文章服务类
const BaseService = require('./BaseService');

class ArticleService extends BaseService {
  constructor() {
    super('articles');
  }

  // 获取文章列表（带分页和筛选）
  async getArticles(options = {}) {
    const {
      page = 1,
      limit = 10,
      status = null,
      userId = null,
      tags = null,
      search = null,
      orderBy = 'created_at DESC'
    } = options;

    try {
      let whereConditions = ['is_deleted = 0'];
      let params = [];

      // 状态筛选
      if (status !== null) {
        whereConditions.push('status = ?');
        params.push(status);
      }

      // 用户筛选
      if (userId) {
        whereConditions.push('user_id = ?');
        params.push(userId);
      }

      // 标签筛选
      if (tags) {
        whereConditions.push('tags LIKE ?');
        params.push(`%${tags}%`);
      }

      // 搜索功能
      if (search) {
        whereConditions.push('(title LIKE ? OR content LIKE ?)');
        params.push(`%${search}%`, `%${search}%`);
      }

      const whereClause = whereConditions.join(' AND ');
      const baseQuery = `
        SELECT a.*, u.username, u.nickname, u.avatar 
        FROM ${this.tableName} a 
        LEFT JOIN users u ON a.user_id = u.id 
        WHERE ${whereClause}
        ORDER BY ${orderBy}
      `;

      const countQuery = `
        SELECT COUNT(*) as total 
        FROM ${this.tableName} a 
        WHERE ${whereClause}
      `;

      const result = await this.paginate(baseQuery, params, page, limit, countQuery);

      // 处理文章数据
      result.data = result.data.map(article => ({
        ...article,
        tags: article.tags ? article.tags.split(',').map(tag => tag.trim()) : [],
        content_preview: article.content ? article.content.substring(0, 200) + '...' : '',
        created_at: new Date(article.created_at).toISOString(),
        updated_at: new Date(article.updated_at).toISOString()
      }));

      return result;
    } catch (error) {
      this.logger.error('获取文章列表失败', { options, error: error.message });
      throw error;
    }
  }

  // 获取文章详情
  async getArticleById(id, includeContent = true) {
    try {
      const fields = includeContent 
        ? 'a.*, u.username, u.nickname, u.avatar'
        : 'a.id, a.title, a.tags, a.status, a.created_at, a.updated_at, u.username, u.nickname, u.avatar';

      const sql = `
        SELECT ${fields}
        FROM ${this.tableName} a 
        LEFT JOIN users u ON a.user_id = u.id 
        WHERE a.id = ? AND a.is_deleted = 0
      `;

      const result = await this.query(sql, [id]);

      if (result.length === 0) {
        return null;
      }

      const article = result[0];

      // 处理标签
      article.tags = article.tags ? article.tags.split(',').map(tag => tag.trim()) : [];

      // 格式化时间
      article.created_at = new Date(article.created_at).toISOString();
      article.updated_at = new Date(article.updated_at).toISOString();

      return article;
    } catch (error) {
      this.logger.error('获取文章详情失败', { id, error: error.message });
      throw error;
    }
  }

  // 创建文章
  async createArticle(articleData) {
    try {
      const {
        title,
        content,
        tags = [],
        status = 1,
        user_id
      } = articleData;

      const data = {
        title,
        content,
        tags: Array.isArray(tags) ? tags.join(', ') : tags,
        status,
        user_id,
        created_at: new Date(),
        updated_at: new Date()
      };

      const articleId = await this.create(data);

      this.logger.info('文章创建成功', { articleId, title, user_id });

      return articleId;
    } catch (error) {
      this.logger.error('创建文章失败', { articleData, error: error.message });
      throw error;
    }
  }

  // 更新文章
  async updateArticle(id, updateData) {
    try {
      const {
        title,
        content,
        tags,
        status
      } = updateData;

      const data = {
        updated_at: new Date()
      };

      if (title !== undefined) data.title = title;
      if (content !== undefined) data.content = content;
      if (tags !== undefined) {
        data.tags = Array.isArray(tags) ? tags.join(', ') : tags;
      }
      if (status !== undefined) data.status = status;

      const success = await this.update(id, data);

      if (success) {
        this.logger.info('文章更新成功', { id, updateData });
      }

      return success;
    } catch (error) {
      this.logger.error('更新文章失败', { id, updateData, error: error.message });
      throw error;
    }
  }

  // 删除文章（软删除）
  async deleteArticle(id) {
    try {
      const success = await this.softDelete(id);

      if (success) {
        this.logger.info('文章删除成功', { id });
      }

      return success;
    } catch (error) {
      this.logger.error('删除文章失败', { id, error: error.message });
      throw error;
    }
  }

  // 获取用户文章统计
  async getUserArticleStats(userId) {
    try {
      const sql = `
        SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as published,
          SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as draft,
          MAX(created_at) as latest_article
        FROM ${this.tableName} 
        WHERE user_id = ? AND is_deleted = 0
      `;

      const result = await this.queryWithCache(sql, [userId], `user_article_stats_${userId}`, 300000);

      return result[0];
    } catch (error) {
      this.logger.error('获取用户文章统计失败', { userId, error: error.message });
      throw error;
    }
  }

  // 获取热门文章
  async getPopularArticles(limit = 10) {
    try {
      const sql = `
        SELECT a.id, a.title, a.tags, a.created_at, u.username, u.nickname
        FROM ${this.tableName} a 
        LEFT JOIN users u ON a.user_id = u.id 
        WHERE a.status = 1 AND a.is_deleted = 0
        ORDER BY a.views DESC, a.created_at DESC
        LIMIT ?
      `;

      const result = await this.queryWithCache(sql, [limit], `popular_articles_${limit}`, 600000);

      return result.map(article => ({
        ...article,
        tags: article.tags ? article.tags.split(',').map(tag => tag.trim()) : [],
        created_at: new Date(article.created_at).toISOString()
      }));
    } catch (error) {
      this.logger.error('获取热门文章失败', { limit, error: error.message });
      throw error;
    }
  }

  // 获取最新文章
  async getLatestArticles(limit = 10) {
    try {
      const sql = `
        SELECT a.id, a.title, a.tags, a.created_at, u.username, u.nickname
        FROM ${this.tableName} a 
        LEFT JOIN users u ON a.user_id = u.id 
        WHERE a.status = 1 AND a.is_deleted = 0
        ORDER BY a.created_at DESC
        LIMIT ?
      `;

      const result = await this.queryWithCache(sql, [limit], `latest_articles_${limit}`, 300000);

      return result.map(article => ({
        ...article,
        tags: article.tags ? article.tags.split(',').map(tag => tag.trim()) : [],
        created_at: new Date(article.created_at).toISOString()
      }));
    } catch (error) {
      this.logger.error('获取最新文章失败', { limit, error: error.message });
      throw error;
    }
  }

  // 搜索文章
  async searchArticles(keyword, options = {}) {
    const {
      page = 1,
      limit = 10,
      orderBy = 'created_at DESC'
    } = options;

    try {
      const baseQuery = `
        SELECT a.id, a.title, a.tags, a.created_at, u.username, u.nickname,
               MATCH(a.title, a.content) AGAINST(? IN NATURAL LANGUAGE MODE) as relevance
        FROM ${this.tableName} a 
        LEFT JOIN users u ON a.user_id = u.id 
        WHERE a.status = 1 AND a.is_deleted = 0
        AND (a.title LIKE ? OR a.content LIKE ? OR MATCH(a.title, a.content) AGAINST(? IN NATURAL LANGUAGE MODE))
        ORDER BY relevance DESC, ${orderBy}
      `;

      const searchParam = `%${keyword}%`;
      const params = [keyword, searchParam, searchParam, keyword];

      const result = await this.paginate(baseQuery, params, page, limit);

      // 处理搜索结果
      result.data = result.data.map(article => ({
        ...article,
        tags: article.tags ? article.tags.split(',').map(tag => tag.trim()) : [],
        created_at: new Date(article.created_at).toISOString(),
        relevance: parseFloat(article.relevance || 0)
      }));

      return result;
    } catch (error) {
      this.logger.error('搜索文章失败', { keyword, options, error: error.message });
      throw error;
    }
  }

  // 增加文章浏览量
  async incrementViews(id) {
    try {
      const sql = `UPDATE ${this.tableName} SET views = views + 1 WHERE id = ?`;
      await this.query(sql, [id]);

      this.logger.debug('文章浏览量增加', { id });
    } catch (error) {
      this.logger.error('增加文章浏览量失败', { id, error: error.message });
      // 不抛出错误，因为这不是关键操作
    }
  }
}

module.exports = ArticleService;
