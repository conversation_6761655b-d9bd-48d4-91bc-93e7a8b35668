<template>
  <div class="upload-form">
    <el-card>
      <div class="editor-container">
        <v-md-editor v-model="article.content" :height="500" preview />
      </div>

      <el-form :model="article" label-width="120px" class="form-container">
        <!-- 表单区域 -->
        <el-row :gutter="20" class="section">
          <el-col :span="24">
            <el-form-item label="封面图片">
              <!-- 上传按钮，没上传时显示 -->
              <el-upload v-if="!file" ref="uploadRef" class="upload-cover" :limit="1" :auto-upload="false"
                :on-change="onFileChange" :on-remove="onRemove" :on-exceed="handleExceed" accept="image/*"
                list-type="picture-card">
                <el-button icon="el-icon-plus">上传封面</el-button>
              </el-upload>

              <!-- 上传后显示预览和删除 -->
              <div v-else class="cover-preview">
                <img :src="previewUrl" alt="封面预览" />
                <el-button type="text" icon="el-icon-delete" circle @click="removeFile" title="删除图片" />
              </div>
            </el-form-item>

            <el-form-item label="文章标题">
              <el-input v-model="article.title" placeholder="请输入标题" />
            </el-form-item>

            <el-form-item label="文章分类">
              <el-select v-model="article.category" placeholder="请选择">
                <el-option v-for="c in categories" :key="c" :label="c" :value="c" />
              </el-select>
            </el-form-item>

            <el-form-item label="文章标签">
              <el-input v-model="article.tags" placeholder="多个标签用逗号分隔" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 提交按钮 -->
        <el-row justify="center">
          <el-col :span="24" class="btn-wrapper">
            <el-button type="primary" size="large" @click="handleUpload">发布文章</el-button>
            <el-button @click="$router.push('/index')">返回首页</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { PublishArticleApi } from "../utils/api";
import VMdEditor from "@kangc/v-md-editor";
import "@kangc/v-md-editor/lib/style/base-editor.css";
import githubTheme from "@kangc/v-md-editor/lib/theme/github.js";
import "@kangc/v-md-editor/lib/theme/style/github.css";
import hljs from "highlight.js";

VMdEditor.use(githubTheme, { Hljs: hljs });

const router = useRouter();
const categories = ref(["技术", "艺术", "生活"]);
const uploadRef = ref(null);

const file = ref(null);
const previewUrl = ref("");

const article = ref({
  title: "",
  category: "",
  tags: "",
  content: "",
});

const onFileChange = (fileItem) => {
  file.value = fileItem.raw;
  file.value.originalName = fileItem.name;
  previewUrl.value = URL.createObjectURL(file.value);
};
const onRemove = () => {
  file.value = null;
  previewUrl.value = "";
};
const handleExceed = () => {
  ElMessage.warning("只能上传一张封面图片");
};

const removeFile = () => {
  file.value = null;
  previewUrl.value = "";
};

const user_id = localStorage.getItem("id");
const handleUpload = async () => {
  if (!file.value) return ElMessage.error("请上传封面图片");
  if (!article.value.title || !article.value.content)
    return ElMessage.warning("标题和内容不能为空");

  const fd = new FormData();
  fd.append("file", file.value);
  fd.append("user_id", user_id || "");
  fd.append("title", article.value.title);
  fd.append("category", article.value.category);
  fd.append("tags", article.value.tags);
  fd.append("content", article.value.content);
  if (file.value.originalName) {
    fd.append("cover_image", file.value.originalName);
  }


  const res = await PublishArticleApi(fd);
  if (res.code === 200) {
    ElMessage.success("文章发布成功");
    setTimeout(() => router.push("/index/home"), 1500);
  } else {
    ElMessage.error(res.message || "发布失败");
  }
};
</script>

<style scoped lang="less">
.upload-form {
  max-width: 1400px;
  margin: 10px auto;
  padding: 24px;
  background: #f9f9f9;
  border-radius: 12px;

  .el-card {
    padding: 32px;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
    background-color: #ffffff;
  }

  .section {
    margin-bottom: 32px;

    .el-form-item {
      margin-bottom: 24px;

      .el-input,
      .el-select {
        width: 100%;
      }
    }
  }

  .upload-cover {
    width: 160px;

    .el-upload--picture-card {
      width: 160px;
      height: 160px;
      line-height: 160px;
    }

    .el-upload-list__item {
      width: 160px;
      height: 160px;
    }
  }

  .cover-preview {
    position: relative;
    width: 160px;
    height: 160px;
  }

  .cover-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
  }

  .cover-preview .el-button {
    position: absolute;
    top: 4px;
    right: 4px;
    color: red;
  }

  .editor-container {
    margin-bottom: 32px;

    .v-md-editor {
      border: 1px solid #e5e5e5;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    }
  }

  .btn-wrapper {
    text-align: center;
    margin-top: 24px;

    .el-button {
      padding: 10px 28px;
      font-size: 16px;
      border-radius: 6px;
    }
  }
}
</style>
