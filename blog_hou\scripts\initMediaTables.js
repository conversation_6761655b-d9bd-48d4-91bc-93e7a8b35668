const db = require("../utils/db");

async function initMediaTables() {
  try {
    console.log("🚀 开始初始化媒体相关数据表...");

    // 1. 媒体播放历史表
    const createPlayHistoryTable = `
      CREATE TABLE IF NOT EXISTS \`media_play_history\` (
        \`id\` int(11) NOT NULL AUTO_INCREMENT,
        \`user_id\` int(11) NOT NULL COMMENT '用户ID',
        \`file_name\` varchar(255) NOT NULL COMMENT '文件名',
        \`progress\` decimal(5,2) DEFAULT 0.00 COMMENT '播放进度百分比',
        \`current_time\` decimal(10,2) DEFAULT 0.00 COMMENT '当前播放时间(秒)',
        \`duration\` decimal(10,2) DEFAULT 0.00 COMMENT '总时长(秒)',
        \`play_time\` bigint(20) NOT NULL COMMENT '播放时间戳',
        \`created_at\` timestamp DEFAULT CURRENT_TIMESTAMP,
        \`updated_at\` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (\`id\`),
        UNIQUE KEY \`uk_user_file\` (\`user_id\`, \`file_name\`),
        KEY \`idx_user_id\` (\`user_id\`),
        KEY \`idx_play_time\` (\`play_time\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='媒体播放历史表';
    `;

    await db.query(createPlayHistoryTable);
    console.log("✅ 媒体播放历史表创建成功");

    // 2. 媒体喜欢表
    const createLikesTable = `
      CREATE TABLE IF NOT EXISTS \`media_likes\` (
        \`id\` int(11) NOT NULL AUTO_INCREMENT,
        \`user_id\` int(11) NOT NULL COMMENT '用户ID',
        \`file_name\` varchar(255) NOT NULL COMMENT '文件名',
        \`created_at\` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (\`id\`),
        UNIQUE KEY \`uk_user_file\` (\`user_id\`, \`file_name\`),
        KEY \`idx_user_id\` (\`user_id\`),
        KEY \`idx_file_name\` (\`file_name\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='媒体喜欢表';
    `;

    await db.query(createLikesTable);
    console.log("✅ 媒体喜欢表创建成功");

    // 3. 媒体收藏表
    const createCollectionsTable = `
      CREATE TABLE IF NOT EXISTS \`media_collections\` (
        \`id\` int(11) NOT NULL AUTO_INCREMENT,
        \`user_id\` int(11) NOT NULL COMMENT '用户ID',
        \`file_name\` varchar(255) NOT NULL COMMENT '文件名',
        \`created_at\` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (\`id\`),
        UNIQUE KEY \`uk_user_file\` (\`user_id\`, \`file_name\`),
        KEY \`idx_user_id\` (\`user_id\`),
        KEY \`idx_file_name\` (\`file_name\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='媒体收藏表';
    `;

    await db.query(createCollectionsTable);
    console.log("✅ 媒体收藏表创建成功");

    // 4. 媒体统计表
    const createStatsTable = `
      CREATE TABLE IF NOT EXISTS \`media_stats\` (
        \`id\` int(11) NOT NULL AUTO_INCREMENT,
        \`file_name\` varchar(255) NOT NULL COMMENT '文件名',
        \`play_count\` int(11) DEFAULT 0 COMMENT '播放次数',
        \`like_count\` int(11) DEFAULT 0 COMMENT '喜欢次数',
        \`collect_count\` int(11) DEFAULT 0 COMMENT '收藏次数',
        \`file_size\` bigint(20) DEFAULT 0 COMMENT '文件大小(字节)',
        \`duration\` decimal(10,2) DEFAULT 0.00 COMMENT '时长(秒)',
        \`resolution\` varchar(20) DEFAULT NULL COMMENT '分辨率',
        \`codec\` varchar(50) DEFAULT NULL COMMENT '编码格式',
        \`category\` varchar(50) DEFAULT '其他' COMMENT '分类',
        \`upload_time\` bigint(20) DEFAULT NULL COMMENT '上传时间戳',
        \`created_at\` timestamp DEFAULT CURRENT_TIMESTAMP,
        \`updated_at\` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (\`id\`),
        UNIQUE KEY \`uk_file_name\` (\`file_name\`),
        KEY \`idx_category\` (\`category\`),
        KEY \`idx_upload_time\` (\`upload_time\`),
        KEY \`idx_play_count\` (\`play_count\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='媒体统计表';
    `;

    await db.query(createStatsTable);
    console.log("✅ 媒体统计表创建成功");

    // 5. 验证表创建结果
    console.log("\n🔍 验证媒体表创建结果...");
    const mediaTables = [
      "media_play_history",
      "media_likes", 
      "media_collections",
      "media_stats"
    ];

    for (const tableName of mediaTables) {
      const tableExists = await db.query(`
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
      `, [tableName]);

      if (tableExists.length > 0) {
        const countResult = await db.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`✅ ${tableName}: 存在，${countResult[0].count} 行数据`);
      } else {
        console.log(`❌ ${tableName}: 不存在`);
      }
    }

    console.log("\n🎉 媒体数据表初始化完成！");
  } catch (error) {
    console.error("❌ 初始化失败:", error);
  } finally {
    process.exit(0);
  }
}

// 运行初始化
initMediaTables();
