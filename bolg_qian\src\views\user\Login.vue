<template>
  <div class="login-page">
    <div class="login-container">
      <h2 class="login-title">用户登录</h2>
      <van-form @submit="onSubmit">
        <van-cell-group inset>
          <van-field v-model="username" name="用户名" label="用户名" placeholder="请输入用户名"
            :rules="[{ required: true, message: '请填写用户名' }]" />
          <van-field v-model="password" type="password" name="密码" label="密码" placeholder="请输入密码"
            :rules="[{ required: true, message: '请填写密码' }]" />
        </van-cell-group>

        <div class="btn-group">
          <van-button round block type="primary" native-type="submit" :loading="loading">
            登录
          </van-button>
          <van-button round block type="default" @click="$router.push('/register')">
            注册
          </van-button>
        </div>
      </van-form>
    </div>

    <ParticleExplosion v-if="showExplosion" @done="onExplosionDone" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import { loginApi } from "@/utils/api";
import type { LoginResponse } from "@/types/props";
import { useUserStore } from "@/stores/user";

const router = useRouter();
const userStore = useUserStore();
const username = ref("");
const password = ref("");
const loading = ref(false);
const showExplosion = ref(false);

const onSubmit = async () => {
  loading.value = true;
  try {
    const res = (await loginApi({
      username: username.value,
      password: password.value,
    })) as unknown as LoginResponse;

    // 使用Pinia Store保存用户信息
    userStore.setToken(res.token);
    userStore.setUser({
      id: res.user.id,
      username: res.user.username,
      nickname: res.user.nickname || res.user.username,
      avatar: res.user.avatar || ''
    });

    // 显示爆炸动画，隐藏登录表单
    showExplosion.value = true;
  } catch (err: any) {
    alert("登录失败：" + (err.message || "网络错误"));
  } finally {
    loading.value = false;
  }
};

function onExplosionDone() {
  router.push({ name: "Home" });
}
</script>

<style scoped>
.login-page {
  position: relative;
  z-index: 2;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;

  /* 合并背景图和颜色，解决 background 被覆盖的问题 */
  background: rgba(255, 255, 255, 0.5) url("/api/avatars/hun.jpg") center/cover no-repeat;
}

.login-container {
  width: 90%;
  max-width: 380px;
  padding: 32px 24px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16px;
  backdrop-filter: blur(12px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  opacity: 0.75;
}

.login-title {
  text-align: center;
  font-size: 22px;
  margin-bottom: 24px;
  color: #333;
  font-weight: 600;
}

.btn-group {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}
</style>
