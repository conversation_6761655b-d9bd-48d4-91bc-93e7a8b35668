const db = require("../utils/db");

async function createArticleLikesTable() {
  try {
    console.log('🔧 开始创建文章点赞相关表...\n');

    // 1. 创建文章点赞表
    console.log('📝 创建文章点赞表 (article_likes)...');
    const createLikesTable = `
      CREATE TABLE IF NOT EXISTS article_likes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT DEFAULT NULL COMMENT '用户ID，NULL表示匿名用户',
        article_id INT NOT NULL COMMENT '文章ID',
        ip_address VARCHAR(45) NOT NULL COMMENT 'IP地址',
        user_agent TEXT COMMENT '用户代理',
        session_id VARCHAR(64) COMMENT '会话ID（用于匿名用户）',
        like_type ENUM('like', 'dislike') NOT NULL DEFAULT 'like' COMMENT '点赞类型',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        -- 索引
        INDEX idx_article_id (article_id),
        INDEX idx_user_id (user_id),
        INDEX idx_ip_address (ip_address),
        INDEX idx_session_id (session_id),
        INDEX idx_created_at (created_at),
        
        -- 外键约束
        FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        
        -- 唯一约束：防止重复点赞
        UNIQUE KEY unique_user_article (user_id, article_id),
        UNIQUE KEY unique_anonymous_article (ip_address, session_id, article_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
        COMMENT='文章点赞表';
    `;
    
    await db.query(createLikesTable);
    console.log('✅ 文章点赞表创建成功');

    // 2. 为articles表添加点赞统计字段（如果不存在）
    console.log('\n📝 检查并添加文章点赞统计字段...');
    
    try {
      // 检查likes_count字段是否存在
      const checkLikesCount = `
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'articles' 
        AND COLUMN_NAME = 'likes_count'
      `;
      const likesCountExists = await db.query(checkLikesCount);
      
      if (likesCountExists.length === 0) {
        const addLikesCount = `
          ALTER TABLE articles 
          ADD COLUMN likes_count INT DEFAULT 0 COMMENT '点赞数量'
        `;
        await db.query(addLikesCount);
        console.log('✅ 添加 likes_count 字段成功');
      } else {
        console.log('✅ likes_count 字段已存在');
      }

      // 检查dislikes_count字段是否存在
      const checkDislikesCount = `
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'articles' 
        AND COLUMN_NAME = 'dislikes_count'
      `;
      const dislikesCountExists = await db.query(checkDislikesCount);
      
      if (dislikesCountExists.length === 0) {
        const addDislikesCount = `
          ALTER TABLE articles 
          ADD COLUMN dislikes_count INT DEFAULT 0 COMMENT '踩数量'
        `;
        await db.query(addDislikesCount);
        console.log('✅ 添加 dislikes_count 字段成功');
      } else {
        console.log('✅ dislikes_count 字段已存在');
      }

    } catch (error) {
      console.log('⚠️ 添加统计字段时出错:', error.message);
    }

    // 3. 创建触发器来自动更新点赞统计
    console.log('\n📝 创建点赞统计触发器...');
    
    try {
      // 删除已存在的触发器
      await db.query('DROP TRIGGER IF EXISTS update_article_likes_after_insert');
      await db.query('DROP TRIGGER IF EXISTS update_article_likes_after_update');
      await db.query('DROP TRIGGER IF EXISTS update_article_likes_after_delete');

      // 创建插入触发器
      const createInsertTrigger = `
        CREATE TRIGGER update_article_likes_after_insert
        AFTER INSERT ON article_likes
        FOR EACH ROW
        BEGIN
          IF NEW.like_type = 'like' THEN
            UPDATE articles SET likes_count = likes_count + 1 WHERE id = NEW.article_id;
          ELSE
            UPDATE articles SET dislikes_count = dislikes_count + 1 WHERE id = NEW.article_id;
          END IF;
        END
      `;
      await db.query(createInsertTrigger);

      // 创建更新触发器
      const createUpdateTrigger = `
        CREATE TRIGGER update_article_likes_after_update
        AFTER UPDATE ON article_likes
        FOR EACH ROW
        BEGIN
          -- 减少旧的计数
          IF OLD.like_type = 'like' THEN
            UPDATE articles SET likes_count = likes_count - 1 WHERE id = OLD.article_id;
          ELSE
            UPDATE articles SET dislikes_count = dislikes_count - 1 WHERE id = OLD.article_id;
          END IF;
          
          -- 增加新的计数
          IF NEW.like_type = 'like' THEN
            UPDATE articles SET likes_count = likes_count + 1 WHERE id = NEW.article_id;
          ELSE
            UPDATE articles SET dislikes_count = dislikes_count + 1 WHERE id = NEW.article_id;
          END IF;
        END
      `;
      await db.query(createUpdateTrigger);

      // 创建删除触发器
      const createDeleteTrigger = `
        CREATE TRIGGER update_article_likes_after_delete
        AFTER DELETE ON article_likes
        FOR EACH ROW
        BEGIN
          IF OLD.like_type = 'like' THEN
            UPDATE articles SET likes_count = likes_count - 1 WHERE id = OLD.article_id;
          ELSE
            UPDATE articles SET dislikes_count = dislikes_count - 1 WHERE id = OLD.article_id;
          END IF;
        END
      `;
      await db.query(createDeleteTrigger);

      console.log('✅ 点赞统计触发器创建成功');
    } catch (error) {
      console.log('⚠️ 创建触发器时出错:', error.message);
    }

    // 4. 初始化现有文章的点赞统计
    console.log('\n📝 初始化现有文章的点赞统计...');
    try {
      const initStats = `
        UPDATE articles a 
        SET 
          likes_count = (
            SELECT COUNT(*) FROM article_likes al 
            WHERE al.article_id = a.id AND al.like_type = 'like'
          ),
          dislikes_count = (
            SELECT COUNT(*) FROM article_likes al 
            WHERE al.article_id = a.id AND al.like_type = 'dislike'
          )
      `;
      await db.query(initStats);
      console.log('✅ 现有文章点赞统计初始化成功');
    } catch (error) {
      console.log('⚠️ 初始化统计时出错:', error.message);
    }

    console.log('\n🎉 文章点赞功能数据库初始化完成！');
    
    // 显示表结构
    console.log('\n📋 article_likes 表结构:');
    const tableStructure = await db.query('DESCRIBE article_likes');
    tableStructure.forEach(col => {
      console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(可空)' : '(非空)'} ${col.Key ? `[${col.Key}]` : ''}`);
    });

  } catch (error) {
    console.error('❌ 创建文章点赞表失败:', error.message);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createArticleLikesTable()
    .then(() => {
      console.log('✅ 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { createArticleLikesTable };
