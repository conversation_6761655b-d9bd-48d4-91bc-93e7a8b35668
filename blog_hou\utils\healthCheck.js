// 假设你的任务队列是全局变量 global.coverTaskQueue
const queue = global.coverTaskQueue || [];

// 检查事件循环是否卡顿
function isBlocked() {
  const start = Date.now();
  // 利用 setTimeout 检测事件循环延迟
  setTimeout(() => {}, 0);
  return Date.now() - start > 5000; // 超过5秒认为卡顿
}

// 定时健康检查
setInterval(() => {
  if (queue.length > 1000 || isBlocked()) {
    console.error('服务卡死，自动退出重启');
    process.exit(1);
  }
}, 60000);

console.log('健康检查已启动');