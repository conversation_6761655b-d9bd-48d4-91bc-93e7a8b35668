/**
 * 视频URL测试工具
 */

export async function testVideoUrl(filename: string): Promise<boolean> {
  const videoUrl = `/api/media/videos/${encodeURIComponent(filename.trim())}`
  
  console.log(`🧪 测试视频URL: ${videoUrl}`)
  
  try {
    const response = await fetch(videoUrl, {
      method: 'HEAD', // 只获取头部信息，不下载内容
      headers: {
        'Range': 'bytes=0-1' // 请求第一个字节来测试
      }
    })
    
    console.log(`📊 视频URL测试结果:`, {
      url: videoUrl,
      status: response.status,
      statusText: response.statusText,
      contentType: response.headers.get('content-type'),
      contentLength: response.headers.get('content-length'),
      acceptRanges: response.headers.get('accept-ranges')
    })
    
    return response.ok || response.status === 206 // 200 或 206 都表示成功
  } catch (error) {
    console.error(`❌ 视频URL测试失败:`, {
      url: videoUrl,
      error: error.message
    })
    return false
  }
}

export async function testMultipleVideoUrls(filenames: string[]): Promise<Record<string, boolean>> {
  const results: Record<string, boolean> = {}
  
  for (const filename of filenames) {
    results[filename] = await testVideoUrl(filename)
    // 添加小延迟避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  return results
}

export function logVideoUrlInfo(filename: string) {
  const videoUrl = `/api/media/videos/${encodeURIComponent(filename.trim())}`
  
  console.group(`🎬 视频URL信息: ${filename}`)
  console.log('原始文件名:', filename)
  console.log('编码后文件名:', encodeURIComponent(filename.trim()))
  console.log('完整URL:', videoUrl)
  console.log('当前域名:', window.location.origin)
  console.log('完整绝对URL:', window.location.origin + videoUrl)
  console.groupEnd()
}
