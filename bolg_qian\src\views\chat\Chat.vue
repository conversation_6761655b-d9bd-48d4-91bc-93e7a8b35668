<template>
  <div class="chat-layout">
    <!-- 左侧用户列表 -->
    <div class="user-list">
      <h4>好友列表</h4>
      <!-- 搜索框，回车触发搜索 -->
      <el-input 
        v-model="friendSearch" 
        placeholder="搜索好友" 
        size="small" 
        clearable 
        @keyup.enter="onFriendSearch"
        style="margin-bottom: 10px;" 
      />
      <el-scrollbar height="320">
        <!-- 好友或搜索结果循环渲染 -->
        <div 
          v-for="(friend, idx) in filteredFriends" 
          :key="friend.id || idx" 
          class="user-item"
          :class="{ active: privateTarget && String(privateTarget.id) === String(friend.id) }" 
          @click="selectPrivate(friend)"
        >
          <el-avatar :size="32" :src="friend.avatar || defaultAvatar" />
          <span class="user-name">{{ friend.nickname || friend.username || '好友' }}</span>
          <!-- 搜索结果时，显示添加好友按钮 -->
          <el-button 
            v-if="searching && !friends.some(f => String(f.id) === String(friend.id)) && String(friend.id) !== String(userId)" 
            size="small"
            type="primary" 
            style="margin-left: auto" 
            @click.stop="applyFriend(friend.id)"
          >
            添加好友
          </el-button>
        </div>
      </el-scrollbar>
    </div>

    <!-- 在线用户列表 -->
    <div class="friend-list">
      <h4>在线用户</h4>
      <el-scrollbar height="350">
        <!-- 在线用户循环渲染 -->
        <div 
          v-for="(user, idx) in filteredOnlineUsers" 
          :key="user.id || idx" 
          class="user-item" 
          :class="{
            self: String(user.id) === String(userId),
            active: privateTarget && String(privateTarget.id) === String(user.id)
          }" 
          @click="selectPrivate(user)"
        >
          <el-avatar :size="32" :src="user.avatar || defaultAvatar" />
          <span class="user-name">{{ user.nickname || '匿名' }}</span>
          <!-- 调试信息 -->
          <span style="font-size: 10px; color: #999; display: block;">
            头像: {{ user.avatar || '无' }}
          </span>
        </div>
      </el-scrollbar>
    </div>

    <!-- 右侧聊天内容 -->
    <div class="chat-box">
      <h3 class="chat-title">
        <div class="title-content">
          {{ privateTarget ? `与 ${privateTarget.nickname} 私聊` : '聊天室（公共聊天）' }}
          <span v-if="unreadCount > 0" class="unread-badge">{{ unreadCount }}</span>
        </div>
        <!-- 公共聊天时显示操作按钮 -->
        <div v-if="!privateTarget" class="chat-actions">
          <el-button 
            size="small" 
            type="info" 
            @click="testMessageSending"
          >
            测试发送
          </el-button>
          <el-button 
            size="small" 
            type="success" 
            @click="exportPublicChatHistory"
          >
            导出
          </el-button>
          <el-button 
            size="small" 
            type="warning" 
            @click="clearPublicChatHistory"
          >
            清理
          </el-button>
        </div>
        <!-- 私聊时显示头像测试按钮 -->
        <div v-if="privateTarget" class="chat-actions">
          <el-button 
            size="small" 
            type="info" 
            @click="testPrivateChatAvatar"
          >
            测试头像
          </el-button>
        </div>
      </h3>
      <div v-if="privateTarget" style="margin-bottom: 8px; color: #409eff;">
        正在私聊：{{ privateTarget.nickname }}
        <el-button size="small" type="text" @click="cancelPrivate">取消</el-button>
      </div>
      
      <!-- 公共聊天时显示存储状态 -->
      <div v-if="!privateTarget" style="margin-bottom: 8px; color: #909399; font-size: 12px;">
        💾 聊天记录已保存到本地存储
        <span v-if="localStorageMessageCount > 0">({{ localStorageMessageCount }}条)</span>
      </div>
      
      <!-- 消息列表 -->
      <div class="messages" ref="messageContainer">
        <div 
          v-for="(msg, idx) in filteredMessages" 
          :key="idx" 
          class="message-item" 
          :class="{
            self: String(msg.id) === String(userId),
            system: msg.id === 'system',
            private: msg.to
          }"
        >
          <el-avatar :size="32" :src="msg.avatar || defaultAvatar" />
          <div class="message-content">
            <div class="meta">
              <span class="username">{{ msg.nickname || '匿名' }}</span>
              <span class="time">{{ formatTime(msg.time) }}</span>
              <span v-if="msg.to" class="private-tag">[私聊]</span>
              <!-- 调试信息：显示头像和图片字段 -->
              <span style="font-size: 10px; color: #999; margin-left: 5px;">
                [头像: {{ msg.avatar ? '有' : '无' }}{{ msg.type === 'image' ? ', 图片: ' + (msg.url ? '有' : '无') : '' }}]
              </span>
            </div>
            <div class="text">
              <template v-if="msg.type === 'image'">
                <div style="margin-bottom: 4px;">
                  <img
                    :src="msg.url"
                    style="max-width:180px;max-height:120px;border-radius:6px;"
                    @error="(e) => console.error('图片加载失败:', msg.url, e)"
                    @load="() => console.log('图片加载成功:', msg.url)"
                    crossorigin="anonymous"
                  />
                </div>
                <!-- 调试信息 -->
                <div style="font-size: 10px; color: #999; word-break: break-all;">
                  图片URL: {{ msg.url }}
                </div>
              </template>
              <template v-else>
                <span v-html="parseEmoji(msg.message || '')"></span>
              </template>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 输入区 -->
      <div class="input-area">
        <!-- 图片上传按钮 -->
        <el-upload 
          :show-file-list="false" 
          :before-upload="beforeImgUpload" 
          accept="image/*"
          :disabled="sending"
        >
          <el-button 
            icon="el-icon-picture" 
            circle 
            size="small"
            :disabled="sending"
          >
            文件
          </el-button>
        </el-upload>
        
        <!-- 消息输入框 -->
        <el-input 
          v-model="input" 
          ref="inputRef" 
          @keyup.enter.native="sendMessage" 
          placeholder="输入消息，回车发送😊" 
          clearable
          :disabled="sending" 
        />
        
        <!-- 发送按钮 -->
        <el-button 
          type="primary" 
          @click="sendMessage" 
          :loading="sending"
          :disabled="!input.trim()"
        >
          发送
        </el-button>
      </div>
      
      <!-- 好友申请列表 -->
      <div class="friend-apply-list" v-if="friendApplyList.length">
        <h4>好友申请</h4>
        <el-scrollbar height="120">
          <div v-for="item in friendApplyList" :key="item.id" class="apply-item">
            <el-avatar :src="item.avatar" :size="36" style="margin-right: 12px;" />
            <span class="apply-username">{{ item.username }}</span>
            <el-button 
              size="small" 
              type="success" 
              @click="handleAddFriend(item.id, true)"
              class="apply-btn"
            >
              同意
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleAddFriend(item.id, false)"
              class="apply-btn"
            >
              拒绝
            </el-button>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { io } from 'socket.io-client'
import {
  GetProfileApi,
  GetChatRecordApi,
  UploadChatImageApi,
  GetFriendListApi,
  SearchUserApi,
  ApplyFriendApi,
  GetFriendApplyListApi,
  HandleAddFriendApi,
  MarkOfflineMessagesReadApi,
  GetUnreadMessageCountApi,
  websocketApi
} from '@/utils/api'
import { onBeforeRouteLeave, useRoute } from 'vue-router'
import { useChatStore } from '@/stores/chat'
import { useUserStore } from '@/stores/user'
import { formatDate } from '@/utils/date'

// 使用Pinia Store
const chatStore = useChatStore()
const userStore = useUserStore()

// 常量定义
const BASE_URL = '/api' // 修改为空，因为静态资源直接从根路径提供
const SOCKET_URL = 'http://**************:3000'
const API_BASE_URL = 'http://**************:3000' // 添加API基础URL常量
const MAX_MESSAGE_LENGTH = 1000
const RECONNECT_INTERVAL = 3000

// 路由
const route = useRoute()

// localStorage 相关常量
const LOCAL_STORAGE_KEYS = {
  PUBLIC_CHAT_MESSAGES: 'public_chat_messages',
  MAX_STORED_MESSAGES: 200 // 最多存储200条消息
}

// 用户信息
const userId = computed(() => userStore.id)
const username = localStorage.getItem('username') || '系统'
const defaultAvatar = ref('/api/avatars/moren.png')

// 响应式数据
const onlineUsers = computed(() => chatStore.onlineUsers)
const privateTarget = computed(() => chatStore.privateTarget)
const input = ref('')
const messages = ref([])
const socket = ref(null)
const sending = ref(false)
const messageContainer = ref(null)
const inputRef = ref(null)
const friends = computed(() => chatStore.friends)
const friendSearch = ref('')
const searchResults = ref([])
const searching = ref(false)
const friendApplyList = ref([])
const techStack = ref([])
const unreadCount = computed(() => chatStore.unreadCount)

// 消息状态管理
const messageStatus = ref(new Map()) // 存储消息发送状态
const pendingMessages = ref(new Set()) // 待确认的消息ID

// 连接状态管理
let reconnectTimer = null
let unreadCountTimer = null
let leaved = false
let socketIOServerStarted = false
const isConnected = ref(false)
const isReconnecting = ref(false)

// 计算属性
const filteredFriends = computed(() => {
  if (!friendSearch.value.trim() || !searching.value) {
    return friends.value
  }
  return friends.value.filter(friend => {
    const nickname = (friend.nickname || '').toLowerCase()
    const username = (friend.username || '').toLowerCase()
    const search = friendSearch.value.toLowerCase()
    return nickname.includes(search) || username.includes(search)
  })
})

const filteredOnlineUsers = computed(() => {
  return onlineUsers.value.filter(user => {
    return String(user.id) !== 'system'
  })
})

const filteredMessages = computed(() => {
  console.log('filteredMessages计算 - 当前状态:', {
    privateTarget: privateTarget.value,
    messagesCount: messages.value.length,
    messages: messages.value
  })
  
  let msgs
  if (!privateTarget.value) {
    // 公共聊天：只显示群聊消息或系统消息
    console.log('当前在公共聊天模式，过滤群聊消息...')
    msgs = messages.value.filter(
      msg => {
        const isGroupChat = (!msg.to || msg.to === null || msg.to === '' || msg.to === undefined)
        const isSystem = msg.id === 'system'
        console.log('群聊消息过滤:', { 
          msg, 
          msgTo: msg.to, 
          isGroupChat, 
          isSystem,
          willShow: isGroupChat || isSystem 
        })
        return isGroupChat || isSystem
      }
    )
  } else {
    // 私聊：显示与当前私聊对象的消息
    msgs = messages.value.filter(msg => {
      // 系统消息始终显示
      if (msg.id === 'system') return true
      
      // 私聊消息：检查是否是当前私聊对象的消息
      if (msg.to) {
        const currentUserId = String(userId.value)
        const currentTargetId = String(privateTarget.value.id)
        const messageFromId = String(msg.id)
        const messageToId = String(msg.to)
        
        const isFromTarget = messageFromId === currentTargetId && messageToId === currentUserId
        const isToTarget = messageFromId === currentUserId && messageToId === currentTargetId
        return isFromTarget || isToTarget
      }
      
      // 群聊消息不显示在私聊中
      return false
    })
  }
  
  console.log('过滤后的消息:', msgs)
  return msgs.slice().sort((a, b) => a.time - b.time)
})

// localStorage中存储的消息数量
const localStorageMessageCount = ref(0)

// 消息去重函数
const isMessageDuplicate = (newMsg, existingMessages) => {
  return existingMessages.some(existingMsg => {
    // 基本字段匹配
    const basicMatch = existingMsg.id === newMsg.id && 
                      existingMsg.time === newMsg.time && 
                      existingMsg.message === newMsg.message &&
                      existingMsg.to === newMsg.to
    
    // 如果是图片消息，还要检查URL
    if (newMsg.type === 'image' && existingMsg.type === 'image') {
      return basicMatch && existingMsg.url === newMsg.url
    }
    
    return basicMatch
  })
}

// 工具函数 - 处理静态资源URL（图片、头像等）
const getStaticUrl = (url) => {
  console.log('🔧 getStaticUrl 输入:', url)

  if (!url) {
    const defaultUrl = `/api/avatars/moren.png`
    console.log('🔧 getStaticUrl 输出 (空URL):', defaultUrl)
    return defaultUrl
  }

  // 如果已经是完整URL，直接返回
  if (url.startsWith('http')) {
    console.log('🔧 getStaticUrl 输出 (已是http):', url)
    return url
  }

  // 清理URL
  let cleanUrl = url.trim()
  
  // 处理嵌套路径问题 (如 /avatars//uploads/avatars/test_2.png)
  cleanUrl = cleanUrl.replace(/\/+/g, '/')
  
  // 移除路径中的/uploads/avatars/部分
  if (cleanUrl.includes('/uploads/avatars/')) {
    const fileName = cleanUrl.split('/uploads/avatars/').pop()
    cleanUrl = `/avatars/${fileName}`
  }
  
  // 处理/api/前缀
  if (cleanUrl.startsWith('/api/')) {
    cleanUrl = cleanUrl.substring(4) // 移除/api/
  }
  
  // 确保avatars路径正确
  if (cleanUrl.includes('avatars/') && !cleanUrl.startsWith('/avatars/')) {
    cleanUrl = `/avatars/${cleanUrl.split('avatars/').pop()}`
  }
  
  // 确保以/开头
  if (!cleanUrl.startsWith('/')) {
    cleanUrl = '/' + cleanUrl
  }

  // 使用/api代理而不是直接使用API_BASE_URL
  const fullUrl = `/api${cleanUrl}`
  console.log('🔧 getStaticUrl 输出 (完整URL):', fullUrl)
  return fullUrl
}

// 兼容性别名
const getFullUrl = getStaticUrl

// localStorage 相关工具函数
const savePublicChatToLocalStorage = (messages) => {
  try {
    // 只保存群聊消息和系统消息
    const publicMessages = messages.filter(msg => 
      !msg.to || msg.to === null || msg.to === '' || msg.to === undefined || msg.id === 'system'
    )
    
    // 限制存储数量，保留最新的消息
    const messagesToStore = publicMessages
      .slice()
      .sort((a, b) => a.time - b.time)
      .slice(-LOCAL_STORAGE_KEYS.MAX_STORED_MESSAGES)
    
    localStorage.setItem(LOCAL_STORAGE_KEYS.PUBLIC_CHAT_MESSAGES, JSON.stringify(messagesToStore))
    console.log('公共聊天消息已保存到localStorage，数量:', messagesToStore.length)
  } catch (error) {
    console.error('保存公共聊天消息到localStorage失败:', error)
  }
}

const loadPublicChatFromLocalStorage = () => {
  try {
    const stored = localStorage.getItem(LOCAL_STORAGE_KEYS.PUBLIC_CHAT_MESSAGES)
    if (stored) {
      const messages = JSON.parse(stored)
      console.log('从localStorage加载公共聊天消息，数量:', messages.length)
      return messages
    }
  } catch (error) {
    console.error('从localStorage加载公共聊天消息失败:', error)
  }
  return []
}

const clearOldPublicChatMessages = () => {
  try {
    const stored = localStorage.getItem(LOCAL_STORAGE_KEYS.PUBLIC_CHAT_MESSAGES)
    if (stored) {
      const messages = JSON.parse(stored)
      const now = Date.now()
      const oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000) // 一周前
      
      // 删除一周前的消息
      const recentMessages = messages.filter(msg => msg.time > oneWeekAgo)
      
      if (recentMessages.length < messages.length) {
        localStorage.setItem(LOCAL_STORAGE_KEYS.PUBLIC_CHAT_MESSAGES, JSON.stringify(recentMessages))
        console.log('清理过期公共聊天消息，删除数量:', messages.length - recentMessages.length)
      }
    }
  } catch (error) {
    console.error('清理过期公共聊天消息失败:', error)
  }
}

const formatTime = (ts) => {
  const date = new Date(ts)
  const now = new Date()
  if (now.toDateString() === date.toDateString()) {
    return `${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`
  }
  return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`
}

const parseEmoji = (text) => {
  return text
    .replace(/:\)/g, '😊')
    .replace(/:\(/g, '😢')
    .replace(/:D/g, '😄')
    .replace(/;\)/g, '😉')
    .replace(/:\|/g, '😐')
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messageContainer.value) {
      messageContainer.value.scrollTop = messageContainer.value.scrollHeight
    }
  })
}

// 启动Socket.IO服务器
const startSocketIOServer = async () => {
  if (socketIOServerStarted) {
    console.log('Socket.IO服务器已经启动')
    return true
  }

  try {
    console.log('🚀 正在启动Socket.IO服务器...')
    const response = await websocketApi.start()

    console.log('Socket.IO启动API响应:', response)

    if (response && response.code === 200) {
      socketIOServerStarted = true
      console.log('✅ Socket.IO服务器启动成功:', response.data)
      ElMessage.success('聊天服务器已启动')
      return true
    } else {
      console.error('❌ Socket.IO服务器启动失败:', response)
      ElMessage.error('聊天服务器启动失败')
      return false
    }
  } catch (error) {
    console.error('❌ 启动Socket.IO服务器异常:', error)
    ElMessage.error('启动聊天服务器时发生错误')
    return false
  }
}

// 检查Socket.IO服务器状态
const checkSocketIOStatus = async () => {
  try {
    const response = await websocketApi.getStatus()
    console.log('Socket.IO状态API响应:', response)

    if (response && response.code === 200) {
      socketIOServerStarted = response.data.isStarted
      console.log('Socket.IO服务器状态:', response.data)
      return response.data.isStarted
    }
  } catch (error) {
    console.error('检查Socket.IO状态失败:', error)
  }
  return false
}

// 消息验证
const validateMessage = (message) => {
  if (!message.trim()) {
    throw new Error('消息不能为空')
  }
  if (message.length > MAX_MESSAGE_LENGTH) {
    throw new Error(`消息长度不能超过${MAX_MESSAGE_LENGTH}个字符`)
  }
  return true
}

// API 调用函数
const getProfile = async () => {
  try {
    const res = await GetProfileApi({ id: userId.value })
    if (res?.user) {
      // 使用修复后的getStaticUrl函数来处理头像URL
      defaultAvatar.value = getStaticUrl(`avatars/${res.user.avatar}`)
      techStack.value = JSON.parse(res.user.tech_tags || '[]')
      onlineUsers.value[0].avatar = defaultAvatar.value
      onlineUsers.value[0].nickname = res.user.nickname || username
    }
  } catch (err) {
    ElMessage.error('获取用户信息失败')
    console.error(err)
  }
}

const loadHistory = async () => {
  try {
    // 如果是公共聊天，优先从localStorage加载
    if (!privateTarget.value) {
      const localMessages = loadPublicChatFromLocalStorage()
      if (localMessages.length > 0) {
        console.log('从localStorage加载公共聊天消息:', localMessages.length, '条')
        messages.value = localMessages
        scrollToBottom()
      }
    }

    const res = await GetChatRecordApi({
      userId: userId.value,
      to: privateTarget.value?.id || null
    })
    
    console.log('原始聊天记录数据:', res?.data)
    
    // 处理从数据库返回的消息，统一字段格式
    const processedMessages = (res?.data || []).map(msg => {
      console.log('处理消息:', msg)
      
      // 如果是来自chat_message表的群聊消息（to_id为null或空字符串）
      if (msg.from_nickname && (msg.to_id === null || msg.to_id === '' || msg.to_id === undefined)) {
        console.log('识别为群聊消息:', msg)
        
        // 处理头像URL：如果已经是完整URL就直接使用，否则拼接
        let avatarUrl = defaultAvatar.value
        if (msg.from_avatar) {
          if (msg.from_avatar.startsWith('http')) {
            // 已经是完整URL
            avatarUrl = msg.from_avatar
          } else {
            // 需要拼接URL
            avatarUrl = getStaticUrl('avatars/' + msg.from_avatar)
          }
        }
        
        // 处理图片URL
        let imageUrl = msg.url
        if (msg.type === 'image' && msg.url) {
          imageUrl = getStaticUrl(msg.url)
          console.log('🖼️ 历史群聊图片URL处理:', {
            原始URL: msg.url,
            处理后URL: imageUrl
          })
        }

        return {
          id: msg.from_id,
          nickname: msg.from_nickname,
          avatar: avatarUrl,
          message: msg.message,
          type: msg.type || 'text',
          url: imageUrl,
          filename: msg.filename,
          time: msg.time,
          to: null, // 群聊消息的to字段为null
          is_read: true // 群聊消息默认已读
        }
      }
      // 如果是来自offline_messages表的私聊消息（to_id有值）
      else if (msg.from_nickname && msg.to_id) {
        console.log('识别为私聊消息:', msg)
        console.log('私聊消息头像字段:', {
          from_avatar: msg.from_avatar,
          has_avatar: !!msg.from_avatar,
          full_url: msg.from_avatar ? getStaticUrl('avatars/' + msg.from_avatar) : defaultAvatar.value
        })
        
        // 处理头像URL：如果已经是完整URL就直接使用，否则拼接
        let avatarUrl = defaultAvatar.value
        if (msg.from_avatar) {
          if (msg.from_avatar.startsWith('http')) {
            // 已经是完整URL
            avatarUrl = msg.from_avatar
          } else {
            // 需要拼接URL
            avatarUrl = getStaticUrl('avatars/' + msg.from_avatar)
          }
        }
        
        // 处理图片URL
        let imageUrl = msg.url
        if (msg.type === 'image' && msg.url) {
          imageUrl = getStaticUrl(msg.url)
          console.log('🖼️ 历史私聊图片URL处理:', {
            原始URL: msg.url,
            处理后URL: imageUrl
          })
        }

        return {
          id: msg.from_id,
          nickname: msg.from_nickname,
          avatar: avatarUrl,
          message: msg.message,
          type: msg.type || 'text',
          url: imageUrl,
          filename: msg.filename,
          time: msg.time,
          to: msg.to_id,
          is_read: msg.is_read
        }
      }
      // 如果是实时消息或其他格式，保持原样
      else {
        console.log('保持原样的消息:', msg)
        return msg
      }
    })
    
    console.log('处理后的消息:', processedMessages)
    
    // 如果是公共聊天，合并localStorage和服务器消息
    if (!privateTarget.value) {
      const localMessages = loadPublicChatFromLocalStorage()
      const allMessages = [...localMessages, ...processedMessages]
      
      // 使用更精确的去重逻辑
      const uniqueMessages = allMessages.filter((msg, index, self) => {
        // 查找相同消息的索引
        const firstIndex = self.findIndex(m => isMessageDuplicate(msg, [m]))
        return index === firstIndex
      }).sort((a, b) => a.time - b.time)
      
      messages.value = uniqueMessages
      
      // 保存到localStorage
      savePublicChatToLocalStorage(uniqueMessages)
    } else {
      // 私聊消息直接使用服务器数据
      messages.value = processedMessages
    }
    
    scrollToBottom()
    
    // 如果有私聊对象，标记离线消息为已读
    if (privateTarget.value) {
      try {
        await MarkOfflineMessagesReadApi(userId.value)
        // 更新未读消息数量
        await loadUnreadCount()
      } catch (err) {
        console.error('标记已读失败:', err)
      }
    }
  } catch (err) {
    ElMessage.error('加载聊天记录失败')
    console.error(err)
  }
}

const loadFriends = async () => {
  try {
    const res = await GetFriendListApi(Number(userId.value))
    console.log('好友列表原始数据:', res?.data)
    
    chatStore.friends = (res?.data || []).map(friend => {
      const processedFriend = {
        ...friend,
        // 确保ID是字符串类型，与在线用户保持一致
        id: String(friend.id || friend.user_id || friend.friend_id),
        nickname: friend.nickname || friend.username,
        avatar: friend.avatar ? getStaticUrl('avatars/' + friend.avatar) : defaultAvatar.value
      }
      console.log('处理后的好友数据:', processedFriend)
      return processedFriend
    })
    
    console.log('最终好友列表:', chatStore.friends)
  } catch (err) {
    ElMessage.error('加载好友列表失败')
    console.error(err)
  }
}

const loadFriendApplyList = async () => {
  try {
    const res = await GetFriendApplyListApi(Number(userId.value))
    if (res.code === 0) {
      friendApplyList.value = (res.data || []).map(item => ({
        ...item,
        avatar: item.avatar ? getStaticUrl('avatars/' + item.avatar) : defaultAvatar.value
      }))
    } else {
      friendApplyList.value = []
    }
  } catch (err) {
    friendApplyList.value = []
  }
}

const loadUnreadCount = async () => {
  try {
    const res = await GetUnreadMessageCountApi(userId.value)
    if (res.code === 0) {
      chatStore.unreadCount = res.data.count
    }
  } catch (err) {
    console.error('获取未读消息数量失败:', err)
  }
}

// 用户交互函数
const selectPrivate = async (user) => {
  console.log('选择私聊对象:', user)
  console.log('当前用户ID:', userId.value, '类型:', typeof userId.value)
  console.log('目标用户ID:', user.id, '类型:', typeof user.id)
  
  if (user.id === userId.value) {
    chatStore.setPrivateTarget(null)
  } else {
    // 确保用户ID是字符串类型
    chatStore.setPrivateTarget({
      ...user,
      id: String(user.id)
    })
  }

  console.log('设置私聊目标:', chatStore.privateTarget)

  // 清空当前消息，重新加载历史记录
  messages.value = []
  await loadHistory()
  scrollToBottom()
}

const cancelPrivate = async () => {
  console.log('取消私聊')
  chatStore.setPrivateTarget(null)
  await loadHistory()
  scrollToBottom()
}

const onFriendSearch = async () => {
  const keyword = friendSearch.value.trim()
  if (!keyword) {
    searchResults.value = []
    searching.value = false
    return
  }
  
  try {
    const res = await SearchUserApi(keyword)
    if (res.code === 0) {
      searchResults.value = (res.data || []).map(user => ({
        ...user,
        nickname: user.username,
        avatar: user.avatar ? getStaticUrl('avatars/' + user.avatar) : defaultAvatar.value
      }))
      searching.value = true
    } else {
      searchResults.value = []
      searching.value = false
    }
  } catch (err) {
    searchResults.value = []
    searching.value = false
    ElMessage.error('搜索失败')
  }
}

const sendMessage = async () => {
  if (sending.value) return
  
  try {
    validateMessage(input.value)
  } catch (error) {
    ElMessage.warning(error.message)
    return
  }
  
  sending.value = true

  // 获取用户完整头像URL
  const avatarUrl = defaultAvatar.value
  
  // 提取文件名 - 只发送文件名，服务器会拼接完整路径
  let avatarFileName = 'moren.png'
  if (avatarUrl.includes('/')) {
    avatarFileName = avatarUrl.split('/').pop()
  }
  
  console.log('发送消息时的头像处理:', {
    原始头像URL: avatarUrl,
    提取的文件名: avatarFileName
  })

  const msg = {
    id: String(userId.value),
    nickname: username,
    avatar: avatarFileName, // 只发送文件名
    message: input.value,
    time: Date.now(),
    type: 'text',
    to: chatStore.privateTarget?.id ? String(chatStore.privateTarget.id) : null
  }

  console.log('发送消息:', msg)

  try {
    socket.value?.emit('message', msg)
    input.value = ''
    inputRef.value?.focus()

    // 移除立即添加消息的逻辑，避免重复显示
    // 消息将通过Socket.IO接收后统一添加
  } catch (err) {
    ElMessage.error('发送失败')
    console.error(err)
  } finally {
    sending.value = false
  }
}

const sendImageMessage = async (url) => {
  // 获取用户完整头像URL
  const avatarUrl = defaultAvatar.value
  
  // 提取文件名 - 只发送文件名，服务器会拼接完整路径
  let avatarFileName = 'moren.png'
  if (avatarUrl.includes('/')) {
    avatarFileName = avatarUrl.split('/').pop()
  }
  
  console.log('发送图片消息时的头像处理:', {
    原始头像URL: avatarUrl,
    提取的文件名: avatarFileName
  })

  const msg = {
    id: String(userId.value),
    nickname: username,
    avatar: avatarFileName, // 只发送文件名
    message: '',
    url,
    time: Date.now(),
    type: 'image',
    to: chatStore.privateTarget?.id ? String(chatStore.privateTarget.id) : null
  }

  try {
    socket.value?.emit('message', msg)

    // 移除立即添加消息的逻辑，避免重复显示
    // 消息将通过Socket.IO接收后统一添加
  } catch (err) {
    ElMessage.error('图片发送失败')
    console.error(err)
  }
}

const beforeImgUpload = async (file) => {
  const formData = new FormData()
  formData.append("file", file)
  
  try {
    const res = await UploadChatImageApi(formData)
    if (res.code === 0) {
      console.log('图片上传成功，原始URL:', res.url)
      
      // 处理图片URL
      let imgUrl = res.url
      
      // 如果包含chat_images但不是完整URL，使用完整URL
      if (imgUrl.includes('chat_images/') && !imgUrl.startsWith('http')) {
        const imgName = imgUrl.includes('/') ? imgUrl.split('/').pop() : imgUrl
        imgUrl = `${API_BASE_URL}/chat_images/${imgName}`
      }
      
      console.log('处理后的图片URL:', imgUrl)
      sendImageMessage(imgUrl)
    } else {
      ElMessage.error('图片上传失败')
    }
  } catch (err) {
    ElMessage.error('图片上传失败')
    console.error(err)
  }
  
  return false
}

const applyFriend = async (to_id) => {
  try {
    const res = await ApplyFriendApi(Number(userId.value), to_id)
    if (res.code === 0) {
      ElMessage.success('好友申请已发送')
    } else if (res.code === 200) {
      ElMessage.info('对方已是你的好友')
    } else if (res.code === 1) {
      ElMessage.warning('已发送申请或已是好友')
    } else {
      ElMessage.warning(res.msg || '申请失败')
    }
  } catch (err) {
    ElMessage.error('申请失败')
  }
}

const handleAddFriend = async (request_id, accept) => {
  try {
    const res = await HandleAddFriendApi(request_id, accept)
    if (res.code === 0) {
      ElMessage.success(accept ? '已同意' : '已拒绝')
      await loadFriendApplyList()
      await loadFriends()
      
      if (accept) {
        const applyItem = friendApplyList.value.find(item => item.id === request_id)
        if (applyItem) {
          chatStore.setPrivateTarget({
            id: applyItem.from_id,
            nickname: applyItem.username,
            avatar: getStaticUrl('avatars/' + applyItem.avatar)
          })
        }
      }
    } else {
      ElMessage.warning(res.msg || '操作失败')
    }
  } catch (err) {
    ElMessage.error('操作失败')
  }
}

// 测试消息发送功能
const testMessageSending = () => {
  console.log('🧪 开始测试消息发送功能...')
  console.log('当前消息列表长度:', messages.value.length)
  
  // 模拟发送一条测试消息
  const testMsg = {
    id: String(userId.value),
    nickname: username,
    avatar: defaultAvatar.value,
    message: `测试消息 ${new Date().toLocaleTimeString()}`,
    time: Date.now(),
    type: 'text',
    to: null
  }
  
  console.log('发送测试消息:', testMsg)
  socket.value?.emit('message', testMsg)

  ElMessage.info('测试消息已发送，请观察控制台输出')
}

// 测试私聊消息头像功能
const testPrivateChatAvatar = () => {
  if (!chatStore.privateTarget) {
    ElMessage.warning('请先选择一个私聊对象')
    return
  }
  
  console.log('🧪 开始测试私聊消息头像功能...')
  console.log('当前私聊对象:', chatStore.privateTarget)
  console.log('当前用户头像:', defaultAvatar.value)
  
  // 模拟发送一条私聊测试消息
  const testPrivateMsg = {
    id: String(userId.value),
    nickname: username,
    avatar: defaultAvatar.value,
    message: `私聊头像测试 ${new Date().toLocaleTimeString()}`,
    time: Date.now(),
    type: 'text',
    to: String(chatStore.privateTarget.id)
  }
  
  console.log('发送私聊测试消息:', testPrivateMsg)
  socket.value?.emit('message', testPrivateMsg)

  ElMessage.info('私聊头像测试消息已发送，请观察控制台输出和消息显示')
}

// 清理公共聊天记录
const clearPublicChatHistory = () => {
  ElMessageBox.confirm(
    '确定要清理所有公共聊天记录吗？此操作不可恢复。',
    '确认清理',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    try {
      // 清空localStorage中的公共聊天记录
      localStorage.removeItem(LOCAL_STORAGE_KEYS.PUBLIC_CHAT_MESSAGES)
      
      // 清空当前显示的公共聊天消息
      if (!chatStore.privateTarget) {
        messages.value = []
        scrollToBottom()
      }
      
      ElMessage.success('公共聊天记录已清理')
      console.log('公共聊天记录已清理')
    } catch (error) {
      console.error('清理公共聊天记录失败:', error)
      ElMessage.error('清理失败')
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 导出公共聊天记录
const exportPublicChatHistory = () => {
  try {
    const stored = localStorage.getItem(LOCAL_STORAGE_KEYS.PUBLIC_CHAT_MESSAGES)
    if (!stored) {
      ElMessage.warning('没有可导出的聊天记录')
      return
    }
    
    const messages = JSON.parse(stored)
    if (messages.length === 0) {
      ElMessage.warning('没有可导出的聊天记录')
      return
    }
    
    // 格式化聊天记录
    const formattedMessages = messages.map(msg => {
      const time = new Date(msg.time).toLocaleString()
      const sender = msg.nickname || '匿名'
      const content = msg.type === 'image' ? `[图片] ${msg.url || ''}` : msg.message
      return `[${time}] ${sender}: ${content}`
    }).join('\n')
    
    // 创建下载链接
    const blob = new Blob([formattedMessages], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `公共聊天记录_${new Date().toISOString().slice(0, 10)}.txt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success(`已导出 ${messages.length} 条聊天记录`)
    console.log('聊天记录导出成功')
  } catch (error) {
    console.error('导出聊天记录失败:', error)
    ElMessage.error('导出失败')
  }
}

// Socket.IO 连接管理
const connectSocket = async () => {
  if (leaved) return

  // 先确保Socket.IO服务器已启动
  const serverStarted = await startSocketIOServer()
  if (!serverStarted) {
    console.error('Socket.IO服务器启动失败，无法建立连接')
    ElMessage.error('聊天服务器启动失败，请稍后重试')
    return
  }

  // 等待一小段时间确保服务器完全启动
  await new Promise(resolve => setTimeout(resolve, 1000))

  try {
    console.log('🔗 正在连接Socket.IO服务器...')
    socket.value = io(SOCKET_URL, {
      autoConnect: true,
      reconnection: true,
      reconnectionDelay: RECONNECT_INTERVAL,
      reconnectionAttempts: 5,
      timeout: 20000
    })

    // Socket.IO 连接成功
    socket.value.on('connect', () => {
      console.log('Socket.IO连接成功')
      isConnected.value = true
      isReconnecting.value = false
      clearTimeout(reconnectTimer)

      // 获取用户头像文件名
      let avatarFileName = 'moren.png'
      if (defaultAvatar.value.includes('/')) {
        avatarFileName = defaultAvatar.value.split('/').pop()
      }
      
      console.log('Socket.IO连接时的头像处理:', {
        原始头像URL: defaultAvatar.value,
        提取的文件名: avatarFileName
      })

      // 加入聊天室
      socket.value.emit('join', {
        id: String(userId.value),
        nickname: username,
        avatar: avatarFileName // 只发送文件名
      })
    })

    // 接收消息
    socket.value.on('message', (data) => {
      console.log('收到Socket.IO消息:', data)
      handleIncomingMessage(data)
    })

    // 接收在线用户列表
    socket.value.on('onlineUsers', (data) => {
      console.log('收到在线用户列表:', data.users)
      onlineUsers.value = (data.users || []).map(user => {
        const processedUser = {
          ...user,
          avatar: user.avatar ? getStaticUrl('avatars/' + user.avatar) : defaultAvatar.value
        }
        console.log('处理在线用户:', user, '→', processedUser)
        return processedUser
      })
      console.log('最终在线用户列表:', onlineUsers.value)
    })

    // 断开连接
    socket.value.on('disconnect', (reason) => {
      console.log('Socket.IO断开连接:', reason)
      isConnected.value = false
      if (!leaved) {
        isReconnecting.value = true
        ElMessage.warning('连接已断开，正在重连...')
      }
    })

    // 重连成功
    socket.value.on('reconnect', () => {
      console.log('Socket.IO重连成功')
      isReconnecting.value = false
      ElMessage.success('重连成功')
    })

    // 连接错误
    socket.value.on('connect_error', (error) => {
      console.error('Socket.IO连接错误:', error)
      isConnected.value = false
      if (!leaved) {
        isReconnecting.value = true
      }
    })

    // 接收错误消息
    socket.value.on('error', (error) => {
      console.error('Socket.IO服务器错误:', error)
      ElMessage.error(error.message || '服务器错误')
    })

  } catch (error) {
    console.error('Failed to connect Socket.IO:', error)
    ElMessage.error('连接聊天服务器失败')
  }
}

// 处理接收到的消息
const handleIncomingMessage = (data) => {
  try {
    console.log('收到消息原始数据:', data)
    
    // 系统消息始终显示
    if (data.id === 'system') {
      console.log('处理系统消息')
      messages.value.push(data)
      nextTick(() => scrollToBottom())
      // 系统消息也保存到localStorage
      if (!chatStore.privateTarget) {
        savePublicChatToLocalStorage(messages.value)
      }
      return
    }

    // 调试 - 重点分析头像数据格式
    console.log('头像分析:', {
      avatar字段: data.avatar,
      avatar类型: typeof data.avatar,
      avatar字段路径: data.avatar ? (data.avatar.startsWith('http') ? '完整URL' : '相对路径') : '无',
      嵌套路径检测: data.avatar && data.avatar.includes('//') ? '存在嵌套路径' : '无嵌套路径',
      uploads检测: data.avatar && data.avatar.includes('/uploads/') ? '包含uploads路径' : '不含uploads路径'
    })
    
    // 处理嵌套路径问题
    if (data.avatar && data.avatar.includes('//')) {
      const originalAvatar = data.avatar
      data.avatar = data.avatar.replace(/\/+/g, '/')
      console.log('修复嵌套路径:', { 原始: originalAvatar, 修复后: data.avatar })
    }
    
    // 处理错误的uploads路径
    if (data.avatar && data.avatar.includes('/uploads/avatars/')) {
      const originalAvatar = data.avatar
      const fileName = data.avatar.split('/uploads/avatars/').pop()
      data.avatar = `/avatars/${fileName}`
      console.log('修复uploads路径:', { 原始: originalAvatar, 修复后: data.avatar })
    }
    
    // 处理消息头像URL
    if (data.avatar) {
      const originalAvatar = data.avatar
      
      // 如果是文件名而不是路径，添加前缀
      if (!data.avatar.includes('/') && !data.avatar.startsWith('http')) {
        data.avatar = `/avatars/${data.avatar}`
      }
      
      // 确保使用完整URL，但使用/api代理路径避免CORS问题
      if (!data.avatar.startsWith('http')) {
        // 使用/api代理而不是直接使用API_BASE_URL
        data.avatar = `/api${data.avatar.startsWith('/') ? data.avatar : '/' + data.avatar}`
      }
      
      console.log('头像处理完成:', { 原始: originalAvatar, 处理后: data.avatar })
    } else {
      // 使用代理路径的默认头像
      data.avatar = '/api/avatars/moren.png'
      console.log('使用默认头像:', data.avatar)
    }

    // 处理图片消息URL
    if (data.type === 'image' && data.url) {
      const originalUrl = data.url
      console.log('原始图片URL:', originalUrl)
      
      // 移除可能存在的重复/api前缀
      let cleanUrl = originalUrl
      if (cleanUrl.startsWith('/api/api/')) {
        cleanUrl = cleanUrl.replace('/api/api/', '/api/')
      }
      
      // 如果包含chat_images但不是完整URL，直接使用完整URL
      if (cleanUrl.includes('chat_images/') && !cleanUrl.startsWith('http')) {
        const imgName = cleanUrl.includes('/') ? cleanUrl.split('/').pop() : cleanUrl
        data.url = `${API_BASE_URL}/chat_images/${imgName}`
      }
      // 如果不是完整URL也不包含chat_images，使用代理路径
      else if (!cleanUrl.startsWith('http')) {
        // 避免重复的/api前缀
        if (cleanUrl.startsWith('/api/')) {
          data.url = cleanUrl
        } else {
          data.url = `/api${cleanUrl.startsWith('/') ? cleanUrl : '/' + cleanUrl}`
        }
      }
      
      console.log('图片URL处理完成:', { 原始: originalUrl, 处理后: data.url })
    }

    console.log('最终处理后的消息:', data)

    // 消息去重
    if (isMessageDuplicate(data, messages.value)) {
      console.log('检测到重复消息，跳过')
      return
    }

    // 私聊消息处理
    if (data.to) {
      // 确保ID比较时类型一致
      const currentUserId = String(userId.value)
      const currentTargetId = chatStore.privateTarget ? String(chatStore.privateTarget.id) : null
      const messageFromId = String(data.id)
      const messageToId = String(data.to)
      
      const isFromTarget = messageFromId === currentTargetId && messageToId === currentUserId
      const isToTarget = messageFromId === currentUserId && messageToId === currentTargetId
      
      console.log('私聊消息检查:', {
        isFromTarget,
        isToTarget,
        messageFromId,
        messageToId,
        currentTargetId,
        currentUserId
      })
      
      if (isFromTarget || isToTarget) {
        console.log('添加私聊消息到列表')
        messages.value.push(data)
        nextTick(() => scrollToBottom())
      } else {
        console.log('私聊消息不匹配当前聊天对象，忽略')
      }
      return
    }

    // 群聊消息处理
    if (!chatStore.privateTarget) {
      console.log('添加群聊消息到列表')
      messages.value.push(data)
      nextTick(() => scrollToBottom())
      
      // 保存公共聊天消息到localStorage
      savePublicChatToLocalStorage(messages.value)
    } else {
      console.log('当前在私聊模式，忽略群聊消息')
    }
  } catch (error) {
    console.error('处理消息时出错:', error)
  }
}

// Socket.IO自带心跳机制，不需要手动实现

const startUnreadCountTimer = () => {
  unreadCountTimer = setInterval(() => {
    if (!leaved) {
      loadUnreadCount()
    }
  }, 10000) // 每10秒更新一次
}

const stopUnreadCountTimer = () => {
  if (unreadCountTimer) {
    clearInterval(unreadCountTimer)
    unreadCountTimer = null
  }
}

// 生命周期
onMounted(async () => {
  leaved = false

  // 清理过期的localStorage消息
  clearOldPublicChatMessages()

  await getProfile()
  await loadHistory()
  await loadFriends()
  await loadFriendApplyList()
  await loadUnreadCount()

  // 检查路由参数，如果有私聊参数则自动设置私聊对象
  const privateUserId = route.query.privateUserId
  const privateUserName = route.query.privateUserName
  const privateUserAvatar = route.query.privateUserAvatar
  if (privateUserId && privateUserName) {
    // 先尝试从好友列表中找到对应的好友信息（包含头像）
    const friend = chatStore.friends.find(f => String(f.id) === String(privateUserId))
    if (friend) {
      // 如果在好友列表中找到了，使用完整的好友信息
      chatStore.setPrivateTarget({
        ...friend,
        id: String(privateUserId)
      })
    } else {
      // 如果没找到，使用传递过来的信息或默认信息
      const avatarUrl = privateUserAvatar && privateUserAvatar !== 'moren.png'
        ? getStaticUrl('avatars/' + privateUserAvatar)
        : defaultAvatar.value

      chatStore.setPrivateTarget({
        id: String(privateUserId),
        nickname: String(privateUserName),
        username: String(privateUserName),
        avatar: avatarUrl
      })
    }
    // 重新加载历史记录
    await loadHistory()
  }

  // 检查Socket.IO服务器状态，然后连接
  console.log('🔍 检查Socket.IO服务器状态...')
  const isServerRunning = await checkSocketIOStatus()
  if (isServerRunning) {
    console.log('✅ Socket.IO服务器已运行，直接连接')
  } else {
    console.log('⚠️ Socket.IO服务器未运行，将在连接时启动')
  }

  connectSocket()
  startUnreadCountTimer()
  inputRef.value?.focus()
})

onBeforeUnmount(() => {
  leaved = true
  socket.value?.disconnect()
  clearTimeout(reconnectTimer)
  stopUnreadCountTimer()
})

onBeforeRouteLeave(() => {
  leaved = true
  socket.value?.disconnect()
  clearTimeout(reconnectTimer)
  stopUnreadCountTimer()
})

// 监听器
watch([chatStore.privateTarget, messages], () => {
  scrollToBottom()
})

watch(
  () => messages.value.length,
  () => {
    scrollToBottom()
  }
)

// 监听公共聊天消息变化，自动保存到localStorage
watch(
  () => messages.value,
  (newMessages) => {
    // 只在公共聊天模式下保存
    if (!chatStore.privateTarget && newMessages.length > 0) {
      // 使用防抖，避免频繁保存
      clearTimeout(window.saveTimeout)
      window.saveTimeout = setTimeout(() => {
        savePublicChatToLocalStorage(newMessages)
      }, 1000)
    }
  },
  { deep: true }
)
</script>

<style scoped>
.chat-layout {
  display: flex;
  max-width: 1200px;
  margin: 50px auto;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px #eee;
  min-height: 500px;
}

.user-list {
  width: 220px;
  border-right: 1px solid #f0f0f0;
  padding: 24px 12px 0 12px;
  background: #fafbfc;
}

.user-list h4 {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 12px;
}

.user-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 4px 0;
  border-radius: 4px;
  transition: background 0.2s;
  cursor: pointer;
}

.user-item.self {
  background: #e6f7ff;
}

.user-item.active {
  background: #ffe58f;
}

.user-name {
  margin-left: 10px;
  font-size: 14px;
  color: #333;
}

.friend-list {
  width: 220px;
  border-right: 1px solid #f0f0f0;
  padding: 24px 12px 0 12px;
  background: #f7fafc;
}

.friend-list h4 {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 12px;
}

.chat-box {
  flex: 1;
  padding: 24px 32px 16px 32px;
  display: flex;
  flex-direction: column;
}

.chat-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: space-between;
}

.chat-title .title-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.unread-badge {
  background: #f56c6c;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.messages {
  flex: 1;
  min-height: 300px;
  max-height: 350px;
  overflow-y: auto;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 8px;
}

.message-item {
  display: flex;
  margin-bottom: 12px;
}

.message-item.self {
  flex-direction: row-reverse;
  text-align: right;
}

.message-item.system .text {
  background: #e6f7ff;
  color: #1890ff;
  font-style: italic;
}

.message-item.private .text {
  background: #fffbe6;
  border: 1px solid #ffe58f;
}

.message-content {
  max-width: 80%;
  margin: 0 10px;
}

.meta {
  font-size: 12px;
  color: #888;
}

.username {
  font-weight: bold;
  margin-right: 5px;
}

.private-tag {
  color: #faad14;
  margin-left: 6px;
}

.text {
  background: #fff;
  padding: 8px 12px;
  border-radius: 16px;
  margin-top: 2px;
  word-break: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.input-area {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.friend-apply-list {
  margin-top: 16px;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  min-width: 220px;
}

.friend-apply-list h4 {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 10px;
}

.apply-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
  min-height: 48px;
}

.apply-item:last-child {
  border-bottom: none;
}

.apply-username {
  flex: 1;
  font-size: 14px;
  color: #333;
  margin-left: 4px;
  margin-right: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.apply-btn {
  margin-left: 6px;
  min-width: 48px;
  padding: 0 8px;
}

@media (max-width: 700px) {
  .friend-apply-list {
    min-width: 0;
    padding: 8px;
  }

  .apply-item {
    min-height: 40px;
    padding: 4px 0;
  }

  .apply-btn {
    min-width: 36px;
    font-size: 12px;
    padding: 0 4px;
  }
}
</style>