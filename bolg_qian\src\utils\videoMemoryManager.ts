/**
 * 视频内存管理工具
 * 用于优化视频播放的内存使用
 */

interface VideoElement extends HTMLVideoElement {
  _memoryManagerId?: string;
}

interface VideoInfo {
  element: VideoElement;
  blobUrl?: string;
  lastActiveTime: number;
  isPlaying: boolean;
}

class VideoMemoryManager {
  private videos: Map<string, VideoInfo> = new Map();
  private maxVideos: number = 5; // 最大同时存在的视频数量
  private cleanupInterval: number = 30000; // 30秒清理一次
  private inactiveThreshold: number = 60000; // 60秒未活动则清理
  private cleanupTimer?: number;

  constructor() {
    this.startCleanupTimer();
    this.setupMemoryMonitoring();
  }

  /**
   * 注册视频元素
   */
  registerVideo(id: string, element: VideoElement, blobUrl?: string): void {
    // 如果超过最大数量，清理最旧的视频
    if (this.videos.size >= this.maxVideos) {
      this.cleanupOldestVideo();
    }

    // 清理已存在的同ID视频
    if (this.videos.has(id)) {
      this.unregisterVideo(id);
    }

    element._memoryManagerId = id;
    
    // 添加事件监听器
    this.addVideoEventListeners(element, id);

    this.videos.set(id, {
      element,
      blobUrl,
      lastActiveTime: Date.now(),
      isPlaying: false
    });

    console.log(`视频已注册: ${id}, 当前视频数量: ${this.videos.size}`);
  }

  /**
   * 注销视频元素
   */
  unregisterVideo(id: string): void {
    const videoInfo = this.videos.get(id);
    if (videoInfo) {
      this.cleanupVideo(videoInfo);
      this.videos.delete(id);
      console.log(`视频已注销: ${id}, 剩余视频数量: ${this.videos.size}`);
    }
  }

  /**
   * 更新视频活动时间
   */
  updateActivity(id: string): void {
    const videoInfo = this.videos.get(id);
    if (videoInfo) {
      videoInfo.lastActiveTime = Date.now();
    }
  }

  /**
   * 设置视频播放状态
   */
  setPlayingState(id: string, isPlaying: boolean): void {
    const videoInfo = this.videos.get(id);
    if (videoInfo) {
      videoInfo.isPlaying = isPlaying;
      this.updateActivity(id);
    }
  }

  /**
   * 暂停所有其他视频
   */
  pauseOtherVideos(currentId: string): void {
    this.videos.forEach((videoInfo, id) => {
      if (id !== currentId && videoInfo.isPlaying) {
        videoInfo.element.pause();
        videoInfo.isPlaying = false;
      }
    });
  }

  /**
   * 清理单个视频
   */
  private cleanupVideo(videoInfo: VideoInfo): void {
    const { element, blobUrl } = videoInfo;
    
    try {
      // 移除事件监听器
      this.removeVideoEventListeners(element);
      
      // 停止播放并重置
      element.pause();
      element.currentTime = 0;
      element.src = '';
      element.load();
      
      // 清理 Blob URL
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
      
      // 清理标识
      delete element._memoryManagerId;
      
      console.log('视频已清理');
    } catch (error) {
      console.error('清理视频时出错:', error);
    }
  }

  /**
   * 清理最旧的视频
   */
  private cleanupOldestVideo(): void {
    let oldestId = '';
    let oldestTime = Date.now();

    this.videos.forEach((videoInfo, id) => {
      if (!videoInfo.isPlaying && videoInfo.lastActiveTime < oldestTime) {
        oldestTime = videoInfo.lastActiveTime;
        oldestId = id;
      }
    });

    if (oldestId) {
      this.unregisterVideo(oldestId);
    }
  }

  /**
   * 定期清理不活跃的视频
   */
  private cleanupInactiveVideos(): void {
    const now = Date.now();
    const toCleanup: string[] = [];

    this.videos.forEach((videoInfo, id) => {
      if (!videoInfo.isPlaying && 
          (now - videoInfo.lastActiveTime) > this.inactiveThreshold) {
        toCleanup.push(id);
      }
    });

    toCleanup.forEach(id => this.unregisterVideo(id));
    
    if (toCleanup.length > 0) {
      console.log(`清理了 ${toCleanup.length} 个不活跃的视频`);
    }
  }

  /**
   * 添加视频事件监听器
   */
  private addVideoEventListeners(element: VideoElement, id: string): void {
    const handlers = {
      play: () => this.setPlayingState(id, true),
      pause: () => this.setPlayingState(id, false),
      ended: () => this.setPlayingState(id, false),
      timeupdate: () => this.updateActivity(id),
      error: () => {
        console.error(`视频播放错误: ${id}`);
        this.unregisterVideo(id);
      }
    };

    Object.entries(handlers).forEach(([event, handler]) => {
      element.addEventListener(event, handler);
    });

    // 保存处理器引用以便后续移除
    (element as any)._memoryManagerHandlers = handlers;
  }

  /**
   * 移除视频事件监听器
   */
  private removeVideoEventListeners(element: VideoElement): void {
    const handlers = (element as any)._memoryManagerHandlers;
    if (handlers) {
      Object.entries(handlers).forEach(([event, handler]) => {
        element.removeEventListener(event, handler as EventListener);
      });
      delete (element as any)._memoryManagerHandlers;
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = window.setInterval(() => {
      this.cleanupInactiveVideos();
      this.logMemoryUsage();
    }, this.cleanupInterval);
  }

  /**
   * 停止清理定时器
   */
  private stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
  }

  /**
   * 设置内存监控
   */
  private setupMemoryMonitoring(): void {
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // 页面隐藏时暂停所有视频
        this.videos.forEach((videoInfo) => {
          if (videoInfo.isPlaying) {
            videoInfo.element.pause();
          }
        });
      }
    });

    // 监听内存压力（如果支持）
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        const usageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
        
        if (usageRatio > 0.8) {
          console.warn('内存使用率过高，开始清理视频');
          this.cleanupInactiveVideos();
        }
      }, 10000);
    }
  }

  /**
   * 记录内存使用情况
   */
  private logMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      console.log(`内存使用情况 - 已用: ${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB, 总计: ${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB, 活跃视频: ${this.videos.size}`);
    }
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      totalVideos: this.videos.size,
      playingVideos: Array.from(this.videos.values()).filter(v => v.isPlaying).length,
      memoryUsage: 'memory' in performance ? (performance as any).memory : null
    };
  }

  /**
   * 清理所有视频
   */
  cleanup(): void {
    this.videos.forEach((videoInfo) => {
      this.cleanupVideo(videoInfo);
    });
    this.videos.clear();
    this.stopCleanupTimer();
    console.log('视频内存管理器已清理完成');
  }
}

// 创建全局实例
export const videoMemoryManager = new VideoMemoryManager();

// 导出类型
export type { VideoElement, VideoInfo };
