<template>
  <div class="export-container">
    <h2>数据库导出</h2>
    <button :disabled="loading" @click="exportAndDownload">
      {{ loading ? "导出中..." : "导出并下载 SQL 文件" }}
    </button>
    <p class="status-text">{{ status }}</p>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { ExportSqlApi } from "../utils/api"; // 你封装的导出接口，已经带了 responseType: "blob"

const loading = ref(false);
const status = ref("");

async function exportAndDownload() {
  loading.value = true;
  status.value = "正在导出并下载文件...";

  try {
    // 直接调用你封装的接口，返回 Blob 数据
    const res = await ExportSqlApi();
    console.log("导出结果：", res);
    console.log("响应类型：", typeof res);
    console.log("响应数据类型：", typeof res.data);
    console.log("是否为Blob：", res.data instanceof Blob);

    // 由于axios响应拦截器对Blob类型返回完整response对象，所以blob数据在res.data中
    const blob = new Blob([res.data], { type: "application/sql" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "latest_export.sql";
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();

    status.value = "导出并下载成功！";
  } catch (error) {
    console.error(error);
    status.value = "导出失败，请重试";
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped>
.export-container {
  max-width: 400px;
  margin: 60px auto;
  padding: 30px;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  text-align: center;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.export-container h2 {
  margin-bottom: 20px;
  color: #333;
  font-weight: 600;
}

button {
  padding: 12px 24px;
  background-color: #409eff;
  border: none;
  border-radius: 4px;
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

button:hover:not(:disabled) {
  background-color: #66b1ff;
}

button:disabled {
  background-color: #a0cfff;
  cursor: not-allowed;
}

.status-text {
  margin-top: 20px;
  color: #606266;
  font-size: 14px;
  min-height: 24px;
}
</style>
