<template>
  <div class="dashboard-home">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="24" class="content-row">
        <!-- 左侧个人信息卡片 -->
        <el-col :xs="24" :sm="24" :md="8" :lg="7" :xl="6">
          <el-card class="profile-card" shadow="hover">
            <!-- 个人头像和基本信息 -->
            <div class="profile-header">
              <div class="avatar-wrapper">
                <el-avatar :size="isMobile ? 80 : 100" :src="imgurl" class="profile-avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <div class="online-status">
                  <div class="status-dot"></div>
                </div>
              </div>

              <div class="user-details">
                <h2 class="username">{{ userInfo.username || '未设置用户名' }}</h2>
                <p class="user-role" v-if="userInfo.address">
                  <el-icon><Briefcase /></el-icon>
                  {{ userInfo.address }}
                </p>
                <p class="user-location" v-if="userInfo.position">
                  <el-icon><Location /></el-icon>
                  {{ userInfo.position }}
                </p>
              </div>
            </div>

            <!-- 个人简介 -->
            <div class="profile-bio">
              <h3 class="section-title">
                <el-icon><Document /></el-icon>
                个人简介
              </h3>
              <div class="bio-content">
                <p class="bio-text" v-if="userInfo.intro">{{ userInfo.intro }}</p>
                <div v-else class="no-bio">
                  <el-icon class="no-bio-icon"><EditPen /></el-icon>
                  <span>暂无个人简介</span>
                </div>
              </div>
            </div>

            <el-divider />

            <!-- 快速操作 -->
            <div class="quick-actions">
              <h3 class="section-title">
                <el-icon><Operation /></el-icon>
                快速操作
              </h3>
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="small"
                  class="action-btn"
                  @click="goToCreateArticle"
                >
                  <el-icon><EditPen /></el-icon>
                  写文章
                </el-button>
                <el-button
                  type="success"
                  size="small"
                  class="action-btn"
                  @click="goToArticles"
                >
                  <el-icon><Document /></el-icon>
                  管理文章
                </el-button>
              </div>
            </div>

            <el-divider />

            <!-- 社交链接 -->
            <div class="social-section" v-if="userInfo.gitee || userInfo.bilibili">
              <h3 class="section-title">
                <el-icon><Link /></el-icon>
                社交平台
              </h3>
              <div class="social-links">
                <a
                  v-if="userInfo.gitee"
                  :href="userInfo.gitee"
                  target="_blank"
                  class="social-link gitee"
                >
                  <el-icon><Collection /></el-icon>
                  <span>Gitee</span>
                </a>
                <a
                  v-if="userInfo.bilibili"
                  :href="userInfo.bilibili"
                  target="_blank"
                  class="social-link bilibili"
                >
                  <el-icon><VideoPlay /></el-icon>
                  <span>Bilibili</span>
                </a>
              </div>
            </div>

            <el-divider v-if="techStack.length > 0" />

            <!-- 技术栈 -->
            <div class="tech-section" v-if="techStack.length > 0">
              <h3 class="section-title">
                <el-icon><Cpu /></el-icon>
                技术栈
              </h3>
              <div class="tech-cloud">
                <el-tag
                  v-for="tech in techStack"
                  :key="tech"
                  :type="getRandomTagType()"
                  class="tech-tag"
                  effect="light"
                  size="small"
                  round
                >
                  {{ tech }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
        <!-- 右侧主要内容区域 -->
        <el-col :xs="24" :sm="24" :md="16" :lg="17" :xl="18">
          <!-- 统计数据卡片 -->
          <el-row :gutter="16" class="stats-row">
            <el-col :xs="12" :sm="6" :md="6">
              <el-card class="stat-card" shadow="hover">
                <div class="stat-content">
                  <div class="stat-icon articles">
                    <el-icon><Document /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ latestArticles.length }}</div>
                    <div class="stat-label">文章总数</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :xs="12" :sm="6" :md="6">
              <el-card class="stat-card" shadow="hover">
                <div class="stat-content">
                  <div class="stat-icon views">
                    <el-icon><View /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ formatNumber(totalViews) }}</div>
                    <div class="stat-label">总浏览量</div>
                    <div class="stat-subtitle">全站文章浏览</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :xs="12" :sm="6" :md="6">
              <el-card class="stat-card" shadow="hover">
                <div class="stat-content">
                  <div class="stat-icon tech">
                    <el-icon><Cpu /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ techStack.length }}</div>
                    <div class="stat-label">技术栈</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :xs="12" :sm="6" :md="6">
              <el-card class="stat-card" shadow="hover">
                <div class="stat-content">
                  <div class="stat-icon days">
                    <el-icon><Calendar /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ joinDays }}</div>
                    <div class="stat-label">加入天数</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 最新文章卡片 -->
          <el-card class="articles-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <h2 class="card-title">
                  <el-icon><Reading /></el-icon>
                  最新文章
                </h2>
                <el-button type="primary" link @click="goToArticles">
                  查看全部
                  <el-icon><ArrowRight /></el-icon>
                </el-button>
              </div>
            </template>

            <div class="articles-content">
              <div v-if="latestArticles.length > 0" class="articles-list">
                <div
                  v-for="(article, index) in latestArticles.slice(0, 5)"
                  :key="article.id"
                  class="article-item"
                  @click="viewArticle(article.id)"
                >
                  <div class="article-index">{{ index + 1 }}</div>
                  <div class="article-info">
                    <h4 class="article-title">{{ article.title }}</h4>
                    <p class="article-summary">{{ article.summary || '暂无摘要' }}</p>
                    <div class="article-meta">
                      <el-tag size="small" type="info">
                        {{ dayjs(article.created_at).format('YYYY-MM-DD') }}
                      </el-tag>
                      <span class="article-time">
                        {{ dayjs(article.created_at).fromNow() }}
                      </span>
                    </div>
                  </div>
                  <div class="article-action">
                    <el-button type="primary" link size="small">
                      <el-icon><View /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
              <el-empty v-else description="暂无文章" :image-size="100">
                <el-button type="primary" @click="goToCreateArticle">
                  <el-icon><EditPen /></el-icon>
                  创建第一篇文章
                </el-button>
              </el-empty>
            </div>
          </el-card>


        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import { ShowArticlesByPageApi, GetProfileApi, dashboardApi } from "@/utils/api";
import { useRouter } from "vue-router";
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';
import { GetTagsApi } from '@/utils/api';
import { 
  User, Briefcase, Location, Document, EditPen, Operation, 
  Link, Collection, VideoPlay, Cpu, Calendar, Reading, 
  ArrowRight, View
} from '@element-plus/icons-vue';

// 配置dayjs
dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

const router = useRouter();

// 响应式数据
const userInfo = ref({
  gitee: "https://gitee.com/",
  bilibili: "https://www.bilibili.com/",
});
const techStack = ref([]);
const latestArticles = ref([]);
const imgurl = ref("");
const isMobile = ref(window.innerWidth <= 768);

// 全站统计数据
const globalStats = ref({
  totalViews: 0,
  userCount: 0,
  articleCount: 0,
  resourceCount: 0
});

// 计算属性
const totalViews = computed(() => {
  // 使用全站总浏览量，如果没有则回退到用户文章浏览量
  return globalStats.value.totalViews || latestArticles.value.reduce((total, article) => total + (article.views || 0), 0);
});

const joinDays = computed(() => {
  if (userInfo.value.created_at) {
    return dayjs().diff(dayjs(userInfo.value.created_at), 'day');
  }
  return 0;
});

// 方法
const getRandomTagType = () => {
  const types = ["", "success", "info", "warning", "danger"];
  return types[Math.floor(Math.random() * types.length)];
};

const viewArticle = (id) => {
  router.push({ name: "Details", params: { id } });
};

const goToArticles = () => {
  router.push('/index/allarticles');
};

const goToCreateArticle = () => {
  router.push('/index/smart-edit');
};

const handleResize = () => {
  isMobile.value = window.innerWidth <= 768;
};

// 格式化数字显示
const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

const getArticles = async () => {
  const params = {
    user_id: localStorage.getItem("id"),
    page: 1,
    limit: 100,
  };
  const res = await ShowArticlesByPageApi(params);
  latestArticles.value = (res.data || []).sort(
    (a, b) => new Date(b.created_at) - new Date(a.created_at)
  );
};

const getProfile = async () => {
  const id = localStorage.getItem("id");
  const res = await GetProfileApi({ id });
  userInfo.value = {
    gitee: "https://gitee.com/",
    bilibili: "https://www.bilibili.com/",
    ...res.user
  };
  imgurl.value = `/api/avatars/${res.user.avatar}`;
  techStack.value = JSON.parse(userInfo.value.tech_tags);
};

// 获取全站统计数据
const getGlobalStats = async () => {
  try {
    const res = await dashboardApi();
    if (res.data && res.data.data) {
      globalStats.value = {
        totalViews: res.data.data.totalViews || 0,
        userCount: res.data.data.userCount || 0,
        articleCount: res.data.data.articleCount || 0,
        resourceCount: res.data.data.resourceCount || 0
      };
      console.log('全站统计数据:', globalStats.value);
    }
  } catch (error) {
    console.error('获取全站统计数据失败:', error);
  }
};

onMounted(() => {
  window.addEventListener("resize", handleResize);
  handleResize();
  getArticles();
  getProfile();
  getGlobalStats(); // 获取全站统计数据
});
onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
/* 页面容器 */
.dashboard-home {
  min-height: 100vh;
  background: var(--bg-secondary);
  font-family: var(--font-family-sans);
}

/* 主要内容 */
.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

.content-row {
  margin-bottom: var(--spacing-lg);
}

/* 个人信息卡片 */
.profile-card {
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
  position: relative;
}

.profile-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.profile-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

/* 个人头像和基本信息 */
.profile-header {
  padding: var(--spacing-lg);
  text-align: center;
}

.avatar-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: var(--spacing-md);
}

.profile-avatar {
  border: 3px solid var(--border-light);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  background: var(--primary-gradient);
}

.profile-avatar:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.online-status {
  position: absolute;
  bottom: 4px;
  right: 4px;
}

.status-dot {
  width: 16px;
  height: 16px;
  background: var(--success-color);
  border-radius: var(--radius-full);
  border: 2px solid var(--bg-primary);
  box-shadow: var(--shadow-sm);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
  100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
}

/* 用户详细信息 */
.user-details {
  margin-bottom: var(--spacing-md);
}

.username {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.user-role,
.user-location {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  margin: var(--spacing-xs) 0;
  font-weight: var(--font-medium);
}



/* 个人简介 */
.profile-bio {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.bio-content {
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  border-left: 4px solid var(--primary-color);
}

.bio-text {
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
  margin: 0;
  font-style: italic;
}

.no-bio {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-tertiary);
  font-size: var(--text-sm);
}

.no-bio-icon {
  font-size: var(--text-lg);
  opacity: 0.6;
}

/* 快速操作 */
.quick-actions {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.section-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
}

.action-btn {
  flex: 1;
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  transition: all var(--transition-normal);
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* 社交链接 */
.social-section {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.social-links {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
}

.social-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: all var(--transition-normal);
  border: 1px solid var(--border-light);
  color: var(--text-secondary);
}

.social-link:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
  text-decoration: none;
}

.social-link.gitee:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: var(--primary-color);
  color: white;
}

.social-link.bilibili:hover {
  border-color: var(--error-color);
  color: var(--error-color);
  background: var(--error-color);
  color: white;
}

/* 技术栈 */
.tech-section {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.tech-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  justify-content: center;
}

.tech-tag {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  transition: all var(--transition-normal);
}

.tech-tag:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* 统计卡片 */
.stats-row {
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.articles {
  background: linear-gradient(135deg, var(--primary-color) 0%, #764ba2 100%);
}

.stat-icon.views {
  background: linear-gradient(135deg, var(--info-color) 0%, #06b6d4 100%);
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(6, 182, 212, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.6);
    transform: scale(1.05);
  }
}

.stat-icon.tech {
  background: linear-gradient(135deg, var(--warning-color) 0%, #f97316 100%);
}

.stat-icon.days {
  background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.stat-subtitle {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  margin-top: var(--spacing-xs);
}

/* 内容卡片 */
.intro-card,
.articles-card {
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  margin-bottom: var(--spacing-lg);
}

.intro-card:hover,
.articles-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 简介内容 */
.intro-content {
  padding: var(--spacing-lg) 0;
}

.intro-text {
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
  margin: 0;
  text-align: justify;
}

/* 文章列表 */
.articles-content {
  padding: var(--spacing-md) 0;
}

.articles-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.article-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal);
  cursor: pointer;
}

.article-item:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary-color);
  transform: translateX(4px);
}

.article-index {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background: var(--primary-gradient);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-semibold);
  font-size: var(--text-sm);
}

.article-info {
  flex: 1;
  min-width: 0;
}

.article-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.article-summary {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-sm) 0;
  line-height: var(--leading-normal);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.article-time {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.article-action {
  flex-shrink: 0;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-md);
  }

  .profile-card,
  .intro-card,
  .articles-card {
    margin-bottom: var(--spacing-md);
  }

  .profile-header {
    padding: var(--spacing-md);
  }

  .user-stats {
    flex-direction: row;
    justify-content: space-around;
  }

  .action-buttons {
    flex-direction: column;
  }

  .social-links {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .tech-cloud {
    justify-content: flex-start;
  }

  .article-item {
    flex-direction: column;
    text-align: center;
  }

  .article-index {
    align-self: center;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: var(--spacing-sm);
  }

  .profile-header {
    padding: var(--spacing-sm);
  }

  .username {
    font-size: var(--text-lg);
  }

  .user-stats {
    padding: var(--spacing-sm) 0;
  }

  .stat-number {
    font-size: var(--text-base);
  }

  .action-buttons {
    gap: var(--spacing-xs);
  }

  .social-links {
    gap: var(--spacing-xs);
  }
}
</style>
