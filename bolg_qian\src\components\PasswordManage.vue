<template>
  <div class="password-manage">
    <el-card class="password-card">
      <template #header>
        <div class="card-header">
          <span class="title">🔐 访问密码管理</span>
          <el-tag type="warning" size="small">仅管理员可操作</el-tag>
        </div>
      </template>

      <!-- 密码配置列表 -->
      <div class="password-list">
        <h3>当前密码配置</h3>
        <el-table :data="passwordList" style="width: 100%" v-loading="loading">
          <el-table-column prop="password_type" label="类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getPasswordTypeTag(row.password_type)">
                {{ getPasswordTypeName(row.password_type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="security_question" label="密保问题" />
          <el-table-column prop="description" label="描述" />
          <el-table-column prop="created_by_name" label="设置者" width="120" />
          <el-table-column prop="use_count" label="使用次数" width="100" />
          <el-table-column prop="updated_at" label="更新时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.updated_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="is_active" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.is_active ? 'success' : 'info'">
                {{ row.is_active ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button size="small" @click="editPassword(row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deletePassword(row.password_type)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 设置密码表单 -->
      <div class="password-form">
        <h3>{{ isEdit ? '编辑密码配置' : '设置新密码配置' }}</h3>
        <el-form
          ref="passwordFormRef"
          :model="passwordForm"
          :rules="passwordRules"
          label-width="120px"
          class="form"
        >
          <el-form-item label="密码类型" prop="passwordType">
            <el-select
              v-model="passwordForm.passwordType"
              placeholder="请选择密码类型"
              :disabled="isEdit"
            >
              <el-option label="照片墙密码" value="photo_wall" />
              <el-option label="媒体访问密码" value="media" />
            </el-select>
          </el-form-item>

          <el-form-item label="访问密码" prop="password">
            <el-input
              v-model="passwordForm.password"
              type="password"
              placeholder="请输入访问密码"
              show-password
            />
          </el-form-item>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              show-password
            />
          </el-form-item>

          <el-form-item label="密保问题" prop="securityQuestion">
            <el-input
              v-model="passwordForm.securityQuestion"
              placeholder="请输入密保问题，如：您的生日是？"
            />
          </el-form-item>

          <el-form-item label="密保答案" prop="securityAnswer">
            <el-input
              v-model="passwordForm.securityAnswer"
              type="password"
              placeholder="请输入密保答案"
              show-password
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="submitPassword" :loading="submitting">
              {{ isEdit ? '更新配置' : '设置密码' }}
            </el-button>
            <el-button @click="resetPasswordForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 密保重置密码 -->
      <div class="security-reset">
        <h3>🔑 通过密保重置密码</h3>
        <el-form
          ref="resetFormRef"
          :model="resetForm"
          :rules="resetRules"
          label-width="120px"
          class="form"
        >
          <el-form-item label="密码类型" prop="passwordType">
            <el-select v-model="resetForm.passwordType" placeholder="请选择密码类型" @change="loadSecurityQuestion">
              <el-option label="照片墙密码" value="photo_wall" />
              <el-option label="媒体访问密码" value="media" />
            </el-select>
          </el-form-item>

          <el-form-item label="密保问题" v-if="securityQuestion">
            <el-input v-model="securityQuestion" disabled />
          </el-form-item>

          <el-form-item label="密保答案" prop="securityAnswer">
            <el-input
              v-model="resetForm.securityAnswer"
              type="password"
              placeholder="请输入密保答案"
              show-password
            />
          </el-form-item>

          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="resetForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
            />
          </el-form-item>

          <el-form-item label="确认新密码" prop="confirmNewPassword">
            <el-input
              v-model="resetForm.confirmNewPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
            />
          </el-form-item>

          <el-form-item>
            <el-button type="warning" @click="submitReset" :loading="resetting">
              重置密码
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 修改导入方式，从script标签而不是setup中导入
import request from '../utils/index'

// 直接在组件内定义API函数，避免模块导入问题
const GetPasswordListApi = () => request.get("/password/list");

const SetPasswordApi = (params) => 
  request.post("/password/create", params);

const DeletePasswordApi = (passwordType) =>
  request.delete(`/password/delete/${passwordType}`);

const GetSecurityQuestionApi = (passwordType) =>
  request.get(`/password/security-question/${passwordType}`);

const ResetPasswordBySecurityApi = (params) => 
  request.post("/password/reset-by-security", params);

export default {
  setup() {
    // 数据和响应式对象
    const loading = ref(false)
    const submitting = ref(false)
    const resetting = ref(false)
    const passwordList = ref([])
    const isEdit = ref(false)
    const securityQuestion = ref('')

    const passwordFormRef = ref()
    const resetFormRef = ref()

    const passwordForm = reactive({
      passwordType: '',
      password: '',
      confirmPassword: '',
      securityQuestion: '',
      securityAnswer: ''
    })

    const resetForm = reactive({
      passwordType: '',
      securityAnswer: '',
      newPassword: '',
      confirmNewPassword: ''
    })

    // 表单验证规则
    const passwordRules = {
      passwordType: [{ required: true, message: '请选择密码类型', trigger: 'change' }],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认密码', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (value !== passwordForm.password) {
              callback(new Error('两次输入的密码不一致'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ],
      securityQuestion: [{ required: true, message: '请输入密保问题', trigger: 'blur' }],
      securityAnswer: [{ required: true, message: '请输入密保答案', trigger: 'blur' }]
    }

    const resetRules = {
      passwordType: [{ required: true, message: '请选择密码类型', trigger: 'change' }],
      securityAnswer: [{ required: true, message: '请输入密保答案', trigger: 'blur' }],
      newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
      ],
      confirmNewPassword: [
        { required: true, message: '请确认新密码', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (value !== resetForm.newPassword) {
              callback(new Error('两次输入的密码不一致'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ]
    }

    // 获取密码列表
    const fetchPasswordList = async () => {
      try {
        loading.value = true
        const res = await GetPasswordListApi()
        if (res?.data) {
          passwordList.value = res.data
        }
      } catch (error) {
        console.error('获取密码列表失败:', error)
        ElMessage.error('获取密码列表失败')
      } finally {
        loading.value = false
      }
    }

    // 提交密码设置
    const submitPassword = async () => {
      try {
        await passwordFormRef.value.validate()
        submitting.value = true
        console.log(passwordForm.passwordType);
        
        const res = await SetPasswordApi({
          passwordType: passwordForm.passwordType,
          password: passwordForm.password,
          securityQuestion: passwordForm.securityQuestion,
          securityAnswer: passwordForm.securityAnswer
        })
        
        if (res?.data) {
          ElMessage.success(isEdit.value ? '密码配置更新成功' : '密码配置设置成功')
          resetPasswordForm()
          fetchPasswordList()
        }
      } catch (error) {
        console.error('设置密码失败:', error)
        ElMessage.error('设置密码失败')
      } finally {
        submitting.value = false
      }
    }

    // 编辑密码
    const editPassword = (row) => {
      isEdit.value = true
      passwordForm.passwordType = row.password_type
      passwordForm.securityQuestion = row.security_question
      // 不显示原密码，需要重新设置
      passwordForm.password = ''
      passwordForm.confirmPassword = ''
      passwordForm.securityAnswer = ''
    }

    // 删除密码
    const deletePassword = async (passwordType) => {
      try {
        await ElMessageBox.confirm('确定要删除这个密码配置吗？', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const res = await DeletePasswordApi(passwordType)
        if (res?.data) {
          ElMessage.success('删除成功')
          fetchPasswordList()
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }

    // 加载密保问题
    const loadSecurityQuestion = async () => {
      if (!resetForm.passwordType) return
      
      try {
        const res = await GetSecurityQuestionApi(resetForm.passwordType)
        if (res?.data) {
          securityQuestion.value = res.data.securityQuestion
        }
      } catch (error) {
        console.error('获取密保问题失败:', error)
        ElMessage.error('获取密保问题失败')
      }
    }

    // 提交重置密码
    const submitReset = async () => {
      try {
        await resetFormRef.value.validate()
        resetting.value = true
        
        const res = await ResetPasswordBySecurityApi({
          passwordType: resetForm.passwordType,
          securityAnswer: resetForm.securityAnswer,
          newPassword: resetForm.newPassword
        })
        
        if (res?.data) {
          ElMessage.success('密码重置成功')
          resetSecurityForm()
        }
      } catch (error) {
        console.error('重置密码失败:', error)
        ElMessage.error((error)?.message || '重置密码失败')
      } finally {
        resetting.value = false
      }
    }

    // 重置密码设置表单
    const resetPasswordForm = () => {
      passwordForm.passwordType = ''
      passwordForm.password = ''
      passwordForm.confirmPassword = ''
      passwordForm.securityQuestion = ''
      passwordForm.securityAnswer = ''
      isEdit.value = false
      passwordFormRef.value?.resetFields()
    }

    // 重置密保重置表单
    const resetSecurityForm = () => {
      resetForm.passwordType = ''
      resetForm.securityAnswer = ''
      resetForm.newPassword = ''
      resetForm.confirmNewPassword = ''
      securityQuestion.value = ''
      resetFormRef.value?.resetFields()
    }

    // 工具函数
    const getPasswordTypeName = (type) => {
      const typeMap = {
        photo_wall: '照片墙密码',
        media: '媒体访问密码'
      }
      return typeMap[type] || type
    }

    const getPasswordTypeTag = (type) => {
      const tagMap = {
        photo_wall: 'success',
        media: 'warning'
      }
      return tagMap[type] || 'info'
    }

    const formatDate = (dateStr) => {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return date.toLocaleString('zh-CN')
    }

    onMounted(() => {
      fetchPasswordList()
    })

    return {
      loading,
      submitting,
      resetting,
      passwordList,
      isEdit,
      securityQuestion,
      passwordFormRef,
      resetFormRef,
      passwordForm,
      resetForm,
      passwordRules,
      resetRules,
      fetchPasswordList,
      submitPassword,
      editPassword,
      deletePassword,
      loadSecurityQuestion,
      submitReset,
      resetPasswordForm,
      resetSecurityForm,
      getPasswordTypeName,
      getPasswordTypeTag,
      formatDate
    }
  }
}
</script>

<style scoped>
.password-manage {
  padding: 20px;
}

.password-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.password-list,
.password-form,
.security-reset {
  margin-bottom: 30px;
}

.password-list h3,
.password-form h3,
.security-reset h3 {
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.form {
  max-width: 600px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-button {
  margin-right: 10px;
}
</style> 