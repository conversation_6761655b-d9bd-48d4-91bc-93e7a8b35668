<template>
  <div class="ai-writer">
    <el-card class="writer-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>🖋️ AI智能帮写</span>
          <div class="header-controls">
            <el-tag type="info" size="small" style="margin-right: 8px;">
              qwen2.5:0.5b (轻量级)
            </el-tag>
            <el-tag :type="aiStatus === 'online' ? 'success' : 'danger'" size="small">
              {{ aiStatus === 'online' ? '在线' : '离线' }}
            </el-tag>
          </div>
        </div>
      </template>

      <div class="writer-content">
        <!-- 写作模式选择 -->
        <el-tabs v-model="activeMode" class="writer-tabs">
          <!-- 全文生成 -->
          <el-tab-pane label="📝 全文生成" name="generate">
            <div class="generate-section">
              <el-form :model="generateForm" label-width="100px">
                <el-form-item label="文章标题" required>
                  <el-input
                    v-model="generateForm.title"
                    placeholder="输入文章标题..."
                    clearable
                  />
                </el-form-item>

                <el-form-item label="写作风格">
                  <el-select v-model="generateForm.style" style="width: 100%;">
                    <el-option label="专业严谨" value="professional" />
                    <el-option label="轻松随意" value="casual" />
                    <el-option label="技术性强" value="technical" />
                    <el-option label="创意生动" value="creative" />
                  </el-select>
                </el-form-item>

                <el-form-item label="文章长度">
                  <el-select v-model="generateForm.length" style="width: 100%;">
                    <el-option label="短文 (800-1200字) - 预计15-30秒" value="short" />
                    <el-option label="中等 (1500-2500字) - 预计30-60秒" value="medium" />
                    <el-option label="长文 (3000-5000字) - 预计60-120秒" value="long" />
                  </el-select>
                  <div class="length-tip">
                    <el-text size="small" type="info">
                      💡 {{ getLengthTip(generateForm.length) }}
                    </el-text>
                  </div>
                </el-form-item>

                <el-form-item label="目标读者">
                  <el-select v-model="generateForm.targetAudience" style="width: 100%;">
                    <el-option label="普通读者" value="general" />
                    <el-option label="初学者" value="beginner" />
                    <el-option label="专业人士" value="expert" />
                  </el-select>
                </el-form-item>

                <el-form-item label="关键词">
                  <el-input
                    v-model="generateForm.keywordsInput"
                    placeholder="输入关键词，用逗号分隔..."
                  />
                  <div v-if="generateForm.keywords.length > 0" class="keywords-display">
                    <el-tag
                      v-for="keyword in generateForm.keywords"
                      :key="keyword"
                      closable
                      @close="removeKeyword(keyword)"
                      style="margin: 4px;"
                    >
                      {{ keyword }}
                    </el-tag>
                  </div>
                </el-form-item>

                <el-form-item>
                  <el-button
                    type="primary"
                    @click="generateArticle"
                    :loading="generateLoading"
                    :disabled="!generateForm.title"
                    style="width: 100%;"
                  >
                    <span v-if="!generateLoading">🚀 开始生成文章</span>
                    <span v-else>{{ generateProgress }}</span>
                  </el-button>

                  <!-- 进度条 -->
                  <div v-if="generateLoading" class="progress-container">
                    <el-progress
                      :percentage="progressPercentage"
                      :show-text="false"
                      :stroke-width="4"
                      color="#409eff"
                    />
                    <div class="progress-text">{{ progressText }}</div>
                  </div>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>

          <!-- 大纲生成 -->
          <el-tab-pane label="📋 大纲生成" name="outline">
            <div class="outline-section">
              <el-form :model="outlineForm" label-width="100px">
                <el-form-item label="文章标题" required>
                  <el-input
                    v-model="outlineForm.title"
                    placeholder="输入文章标题..."
                    clearable
                  />
                </el-form-item>

                <el-form-item label="大纲详细度">
                  <el-select v-model="outlineForm.depth" style="width: 100%;">
                    <el-option label="简单大纲" value="simple" />
                    <el-option label="详细大纲" value="detailed" />
                    <el-option label="全面大纲" value="comprehensive" />
                  </el-select>
                </el-form-item>

                <el-form-item label="章节数量">
                  <el-input-number
                    v-model="outlineForm.sections"
                    :min="3"
                    :max="10"
                    style="width: 100%;"
                  />
                </el-form-item>

                <el-form-item>
                  <el-button 
                    type="primary" 
                    @click="generateOutline"
                    :loading="outlineLoading"
                    :disabled="!outlineForm.title"
                    style="width: 100%;"
                  >
                    📋 生成大纲
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>

          <!-- 续写功能 -->
          <el-tab-pane label="➕ 续写" name="continue">
            <div class="continue-section">
              <el-form :model="continueForm" label-width="100px">
                <el-form-item label="现有内容" required>
                  <el-input
                    v-model="continueForm.content"
                    type="textarea"
                    :rows="6"
                    placeholder="粘贴现有文章内容..."
                    maxlength="2000"
                    show-word-limit
                  />
                </el-form-item>

                <el-form-item label="续写要求">
                  <el-input
                    v-model="continueForm.instructions"
                    type="textarea"
                    :rows="2"
                    placeholder="描述续写要求，如：增加案例分析、深入探讨某个观点等..."
                  />
                </el-form-item>

                <el-form-item>
                  <el-button 
                    type="primary" 
                    @click="continueArticle"
                    :loading="continueLoading"
                    :disabled="!continueForm.content"
                    style="width: 100%;"
                  >
                    ➕ 续写文章
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>

        <!-- 生成结果展示 -->
        <div v-if="showResult" class="result-section">
          <div class="result-header">
            <span>{{ resultTitle }}</span>
            <div class="result-actions">
              <el-button size="small" @click="copyResult">复制</el-button>
              <el-button size="small" @click="applyResult" type="primary">应用</el-button>
              <el-button size="small" @click="clearResult">清空</el-button>
            </div>
          </div>
          
          <div class="result-content">
            <el-input
              v-model="generatedResult"
              type="textarea"
              :rows="20"
              readonly
              class="result-textarea"
            />
          </div>

          <!-- 结果统计 -->
          <div class="result-stats">
            <div class="stat-item">
              <span>字数：{{ resultWordCount }}</span>
            </div>
            <div class="stat-item">
              <span>生成时间：{{ resultGenerateTime }}</span>
            </div>
            <div class="stat-item">
              <span>响应时间：{{ resultResponseTime }}ms</span>
            </div>
          </div>

          <!-- 内容质量评估 -->
          <div class="quality-assessment">
            <div class="quality-header">📊 内容质量评估</div>
            <div class="quality-metrics">
              <div class="metric-item">
                <span class="metric-label">结构完整性：</span>
                <el-rate v-model="contentQuality.structure" disabled show-score />
              </div>
              <div class="metric-item">
                <span class="metric-label">语言流畅度：</span>
                <el-rate v-model="contentQuality.fluency" disabled show-score />
              </div>
              <div class="metric-item">
                <span class="metric-label">内容相关性：</span>
                <el-rate v-model="contentQuality.relevance" disabled show-score />
              </div>
            </div>
            <div class="quality-tips">
              <el-text size="small" type="info">
                💡 {{ getQualityTip() }}
              </el-text>
            </div>
          </div>
        </div>

        <!-- 快速模板 -->
        <div class="quick-templates">
          <div class="templates-header">
            <span>🚀 快速模板</span>
          </div>
          <div class="templates-grid">
            <el-button 
              v-for="template in quickTemplates" 
              :key="template.title"
              size="small"
              @click="applyTemplate(template)"
              class="template-btn"
            >
              {{ template.title }}
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { aiApi } from '../utils/aiApi'

// Props
const props = defineProps({
  title: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['apply-content', 'apply-title'])

// 响应式数据
const aiStatus = ref('offline')
const activeMode = ref('generate')
const showResult = ref(false)
const generatedResult = ref('')
const resultTitle = ref('')
const resultResponseTime = ref(0)



// 表单数据
const generateForm = reactive({
  title: '',
  style: 'professional',
  length: 'medium',
  targetAudience: 'general',
  keywordsInput: '',
  keywords: []
})

const outlineForm = reactive({
  title: '',
  depth: 'detailed',
  sections: 5
})

const continueForm = reactive({
  content: '',
  instructions: ''
})

// 加载状态
const generateLoading = ref(false)
const outlineLoading = ref(false)
const continueLoading = ref(false)

// 进度相关
const generateProgress = ref('🚀 开始生成文章')
const progressPercentage = ref(0)
const progressText = ref('')

// 内容质量评估
const contentQuality = reactive({
  structure: 4,
  fluency: 4,
  relevance: 4
})

// 快速模板
const quickTemplates = ref([
  { title: 'Vue.js入门教程', style: 'technical', length: 'medium', targetAudience: 'beginner' },
  { title: '前端开发最佳实践', style: 'professional', length: 'long', targetAudience: 'expert' },
  { title: '我的编程学习心得', style: 'casual', length: 'short', targetAudience: 'general' },
  { title: '人工智能发展趋势', style: 'professional', length: 'medium', targetAudience: 'general' },
  { title: '如何提高工作效率', style: 'casual', length: 'short', targetAudience: 'general' },
  { title: '技术团队管理经验', style: 'professional', length: 'medium', targetAudience: 'expert' }
])

// 计算属性
const resultWordCount = computed(() => {
  return generatedResult.value.length
})

const resultGenerateTime = computed(() => {
  return new Date().toLocaleString()
})

// 监听器
watch(() => props.title, (newTitle) => {
  if (newTitle) {
    generateForm.title = newTitle
    outlineForm.title = newTitle
  }
})

watch(() => generateForm.keywordsInput, (newValue) => {
  if (newValue && newValue.includes(',')) {
    const keywords = newValue.split(',').map(k => k.trim()).filter(k => k)
    generateForm.keywords = [...new Set([...generateForm.keywords, ...keywords])]
    generateForm.keywordsInput = ''
  }
})

// 生命周期
onMounted(() => {
  checkAIStatus()
  loadExampleData()
  if (props.title) {
    generateForm.title = props.title
    outlineForm.title = props.title
  }
})

// 方法
const checkAIStatus = async () => {
  try {
    const response = await aiApi.getStatus()
    aiStatus.value = response.data.status
  } catch (error) {
    console.error('检查AI状态失败:', error)
    aiStatus.value = 'offline'
  }
}



// 生成文章
const generateArticle = async () => {
  if (!generateForm.title.trim()) {
    ElMessage.warning('请输入文章标题')
    return
  }

  generateLoading.value = true
  progressPercentage.value = 0
  const startTime = Date.now()

  // 模拟进度更新
  const progressInterval = setInterval(() => {
    if (progressPercentage.value < 90) {
      progressPercentage.value += Math.random() * 10

      if (progressPercentage.value < 30) {
        progressText.value = '🧠 AI正在理解标题...'
        generateProgress.value = '🧠 分析中...'
      } else if (progressPercentage.value < 60) {
        progressText.value = '📝 AI正在构思内容...'
        generateProgress.value = '📝 构思中...'
      } else {
        progressText.value = '✍️ AI正在生成文章...'
        generateProgress.value = '✍️ 生成中...'
      }
    }
  }, 1000)

  try {
    const options = {
      style: generateForm.style,
      length: generateForm.length,
      targetAudience: generateForm.targetAudience,
      keywords: generateForm.keywords
    }

    const response = await aiApi.generateArticle({
      title: generateForm.title,
      options
    })

    // 完成进度
    clearInterval(progressInterval)
    progressPercentage.value = 100
    progressText.value = '✅ 生成完成！'
    generateProgress.value = '✅ 完成'

    generatedResult.value = response.data.article
    resultTitle.value = '🖋️ AI生成的文章'
    resultResponseTime.value = Date.now() - startTime
    showResult.value = true

    // 评估内容质量
    assessContentQuality(response.data.article)

    ElMessage.success('文章生成成功')
  } catch (error) {
    clearInterval(progressInterval)
    console.error('文章生成失败:', error)
    ElMessage.error('文章生成失败，请稍后重试')
  } finally {
    setTimeout(() => {
      generateLoading.value = false
      progressPercentage.value = 0
      progressText.value = ''
      generateProgress.value = '🚀 开始生成文章'
    }, 1000)
  }
}

// 生成大纲
const generateOutline = async () => {
  if (!outlineForm.title.trim()) {
    ElMessage.warning('请输入文章标题')
    return
  }

  outlineLoading.value = true
  const startTime = Date.now()
  
  try {
    const options = {
      depth: outlineForm.depth,
      sections: outlineForm.sections
    }

    const response = await aiApi.generateOutline({
      title: outlineForm.title,
      options
    })
    
    generatedResult.value = response.data.outline
    resultTitle.value = '📋 AI生成的大纲'
    resultResponseTime.value = Date.now() - startTime
    showResult.value = true
    
    ElMessage.success('大纲生成成功')
  } catch (error) {
    console.error('大纲生成失败:', error)
    ElMessage.error('大纲生成失败，请稍后重试')
  } finally {
    outlineLoading.value = false
  }
}

// 续写文章
const continueArticle = async () => {
  if (!continueForm.content.trim()) {
    ElMessage.warning('请输入现有内容')
    return
  }

  continueLoading.value = true
  const startTime = Date.now()
  
  try {
    const response = await aiApi.continueArticle({
      content: continueForm.content,
      instructions: continueForm.instructions
    })
    
    generatedResult.value = response.data.continuation
    resultTitle.value = '➕ AI续写的内容'
    resultResponseTime.value = Date.now() - startTime
    showResult.value = true
    
    ElMessage.success('文章续写成功')
  } catch (error) {
    console.error('文章续写失败:', error)
    ElMessage.error('文章续写失败，请稍后重试')
  } finally {
    continueLoading.value = false
  }
}

// 应用模板
const applyTemplate = (template) => {
  generateForm.title = template.title
  generateForm.style = template.style
  generateForm.length = template.length
  generateForm.targetAudience = template.targetAudience
  activeMode.value = 'generate'
  ElMessage.success(`已应用模板: ${template.title}`)
}

// 移除关键词
const removeKeyword = (keyword) => {
  const index = generateForm.keywords.indexOf(keyword)
  if (index > -1) {
    generateForm.keywords.splice(index, 1)
  }
}

// 复制结果
const copyResult = () => {
  navigator.clipboard.writeText(generatedResult.value).then(() => {
    ElMessage.success('内容已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

// 应用结果
const applyResult = () => {
  emit('apply-content', generatedResult.value)
  ElMessage.success('内容已应用到编辑器')
}

// 清空结果
const clearResult = () => {
  showResult.value = false
  generatedResult.value = ''
  resultTitle.value = ''
}

// 获取长度提示
const getLengthTip = (length) => {
  const tips = {
    short: '适合快速阅读，要点突出',
    medium: '平衡深度和可读性，推荐选择',
    long: '深入分析，全面覆盖主题'
  }
  return tips[length] || '选择合适的文章长度'
}

// 评估内容质量
const assessContentQuality = (content) => {
  // 简单的质量评估算法
  const wordCount = content.length
  const hasHeaders = content.includes('#') || content.includes('##')
  const hasParagraphs = content.split('\n\n').length > 2
  const hasStructure = hasHeaders && hasParagraphs

  // 结构完整性评分
  contentQuality.structure = hasStructure ? 4 + Math.random() : 3 + Math.random()

  // 语言流畅度评分（基于字数和段落）
  contentQuality.fluency = wordCount > 500 ? 4 + Math.random() : 3 + Math.random()

  // 内容相关性评分（基于标题匹配度）
  contentQuality.relevance = 3.5 + Math.random() * 1.5
}

// 获取质量提示
const getQualityTip = () => {
  const avgScore = (contentQuality.structure + contentQuality.fluency + contentQuality.relevance) / 3

  if (avgScore >= 4.5) {
    return '内容质量优秀，可以直接使用'
  } else if (avgScore >= 4.0) {
    return '内容质量良好，建议稍作修改'
  } else if (avgScore >= 3.5) {
    return '内容质量一般，建议重新生成或大幅修改'
  } else {
    return '内容质量较差，建议重新生成'
  }
}
</script>

<style scoped>
.ai-writer {
  margin-bottom: 20px;
}

.writer-card {
  border: 1px solid #e4e7ed;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.writer-content {
  padding: 0;
}

.writer-tabs {
  margin-bottom: 20px;
}

.keywords-display {
  margin-top: 8px;
}

.length-tip {
  margin-top: 4px;
  padding: 4px 8px;
  background: #f0f9ff;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.result-section {
  margin-top: 20px;
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: bold;
  color: #333;
}

.result-actions {
  display: flex;
  gap: 8px;
}

.result-content {
  margin-bottom: 12px;
}

.result-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.6;
}

.result-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.quick-templates {
  margin-top: 20px;
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.templates-header {
  font-weight: bold;
  margin-bottom: 12px;
  color: #333;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.template-btn {
  width: 100%;
  text-align: left;
}

.progress-container {
  margin-top: 12px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
  color: #666;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.quality-assessment {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.quality-header {
  font-weight: bold;
  margin-bottom: 12px;
  color: #333;
}

.quality-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.metric-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.metric-label {
  font-size: 14px;
  color: #666;
}

.quality-tips {
  padding: 8px;
  background: #f0f9ff;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

@media (max-width: 768px) {
  .result-stats {
    flex-direction: column;
    gap: 4px;
  }

  .templates-grid {
    grid-template-columns: 1fr;
  }
}
</style>
