// 仪表盘接口
// 用户总数 文章数量 分享数量 资源数量 资源下载次数
const Router = require("koa-router");
const dashboard = new Router();
const { handleResponse } = require("../../middlewares/responseHandler");
const dashboardService = require("../../utils/sql/dashboardService");
const cacheManager = require("./cacheManager");

dashboard.get("/data", async (ctx) => {
  try {
    const data = await dashboardService.getDashboardData();
    handleResponse(ctx, 200, { data });
  } catch (err) {
    handleResponse(ctx, 500, { error: "获取仪表盘数据失败" });
  }
});

// 用户增长趋势 文章发布趋势 资源下载量统计
dashboard.get("/qushi", async (ctx) => {
  try {
    const userGrowth = await dashboardService.getUserGrowthTrend();
    const articlePublish = await dashboardService.getArticlePublishTrend();
    const downloadData = await dashboardService.getDownloadTrend();

    const trends = {
      userGrowth,
      articlePublish,
      downloadData,
    };

    handleResponse(ctx, 200, { data: trends });
  } catch (err) {
    handleResponse(ctx, 500, { error: "获取趋势数据失败" });
  }
});

// 注册缓存管理路由
dashboard.use("/cache", cacheManager.routes(), cacheManager.allowedMethods());

module.exports = dashboard;
