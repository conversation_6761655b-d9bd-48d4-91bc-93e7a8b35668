#!/usr/bin/env node

/**
 * 内存优化启动脚本
 * 使用优化的Node.js参数启动应用
 */

const { spawn } = require('child_process');
const path = require('path');

// Node.js 内存优化参数
const nodeArgs = [
  // 垃圾回收优化
  '--expose-gc',                    // 暴露gc()函数
  '--max-old-space-size=512',       // 限制老生代内存为512MB
  '--max-new-space-size=64',        // 限制新生代内存为64MB
  
  // V8 优化
  '--optimize-for-size',            // 优化内存使用而非速度
  '--gc-interval=100',              // 更频繁的垃圾回收
  
  // 其他优化
  '--no-warnings',                  // 减少警告输出
  '--trace-warnings',               // 跟踪警告来源
];

// 环境变量设置
const env = {
  ...process.env,
  NODE_ENV: process.env.NODE_ENV || 'production',
  // 启用内存监控
  MEMORY_MONITORING: 'true',
  // 减少缓存大小
  CACHE_MAX_ENTRIES: '300',
  CACHE_MAX_MEMORY: '30MB'
};

console.log('🚀 启动内存优化模式...');
console.log('📊 Node.js 参数:', nodeArgs.join(' '));

// 启动应用
const appPath = path.join(__dirname, '..', 'app.js');
const child = spawn('node', [...nodeArgs, appPath], {
  env,
  stdio: 'inherit',
  cwd: path.dirname(appPath)
});

// 监听进程事件
child.on('error', (error) => {
  console.error('❌ 启动失败:', error);
  process.exit(1);
});

child.on('exit', (code, signal) => {
  if (code !== 0) {
    console.error(`❌ 进程异常退出: code=${code}, signal=${signal}`);
    process.exit(code || 1);
  } else {
    console.log('✅ 进程正常退出');
  }
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 收到关闭信号，正在优雅关闭...');
  child.kill('SIGTERM');
});

process.on('SIGTERM', () => {
  console.log('🛑 收到终止信号，正在关闭...');
  child.kill('SIGTERM');
});
