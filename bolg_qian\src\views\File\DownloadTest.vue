<template>
  <div class="download-test">
    <el-card>
      <template #header>
        <h3>下载功能测试</h3>
      </template>
      
      <div class="test-section">
        <h4>1. 获取文件列表</h4>
        <el-button @click="getFileList" :loading="loading.list">获取文件列表</el-button>
        
        <div v-if="fileList.length > 0" class="file-list">
          <h5>找到 {{ fileList.length }} 个文件:</h5>
          <div v-for="file in fileList.slice(0, 5)" :key="file.file_name" class="file-item">
            <div class="file-info">
              <strong>{{ file.file_name }}</strong>
              <span class="file-size">{{ formatFileSize(file.file_size) }}</span>
            </div>
            <el-button 
              size="small" 
              @click="testDownload(file)" 
              :loading="downloading.has(file.file_name)"
            >
              测试下载
            </el-button>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h4>2. 下载测试结果</h4>
        <div v-if="testResults.length > 0" class="test-results">
          <div v-for="result in testResults" :key="result.fileName" class="result-item">
            <div class="result-header">
              <strong>{{ result.fileName }}</strong>
              <el-tag :type="result.success ? 'success' : 'danger'">
                {{ result.success ? '成功' : '失败' }}
              </el-tag>
            </div>
            <div class="result-details">
              <p><strong>期望大小:</strong> {{ formatFileSize(result.expectedSize) }}</p>
              <p><strong>实际大小:</strong> {{ formatFileSize(result.actualSize) }}</p>
              <p><strong>响应类型:</strong> {{ result.responseType }}</p>
              <p><strong>是否为Blob:</strong> {{ result.isBlob ? '是' : '否' }}</p>
              <p v-if="result.error"><strong>错误:</strong> {{ result.error }}</p>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { GetResourceListApi, DownloadResourceApi } from '../../utils/api';

// 响应式数据
const fileList = ref<any[]>([]);
const testResults = ref<any[]>([]);
const downloading = ref(new Set<string>());
const loading = ref({
  list: false
});

// 方法
const getFileList = async () => {
  loading.value.list = true;
  try {
    const response = await GetResourceListApi();
    console.log('文件列表响应:', response);
    
    if (response && response.data) {
      fileList.value = response.data;
      ElMessage.success(`获取到 ${response.data.length} 个文件`);
    } else {
      ElMessage.warning('文件列表为空');
    }
  } catch (error: any) {
    console.error('获取文件列表失败:', error);
    ElMessage.error('获取文件列表失败: ' + error.message);
  } finally {
    loading.value.list = false;
  }
};

const testDownload = async (file: any) => {
  if (downloading.value.has(file.file_name)) return;
  
  downloading.value.add(file.file_name);
  
  try {
    console.log('测试下载文件:', file.file_name);
    console.log('期望文件大小:', file.file_size);
    
    const startTime = Date.now();
    const response = await DownloadResourceApi(file.file_name);
    const endTime = Date.now();
    
    console.log('下载响应:', response);
    
    const result = {
      fileName: file.file_name,
      expectedSize: parseInt(file.file_size),
      actualSize: 0,
      responseType: typeof response.data,
      isBlob: response.data instanceof Blob,
      success: false,
      error: null as string | null,
      downloadTime: endTime - startTime
    };
    
    if (response.data instanceof Blob) {
      result.actualSize = response.data.size;
      result.success = result.actualSize === result.expectedSize;
      
      if (!result.success) {
        result.error = `文件大小不匹配：期望 ${result.expectedSize}，实际 ${result.actualSize}`;
      }
      
      // 如果成功，触发实际下载
      if (result.success) {
        const link = document.createElement("a");
        link.href = window.URL.createObjectURL(response.data);
        link.download = file.file_name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(link.href);
        
        ElMessage.success(`文件 ${file.file_name} 下载成功`);
      } else {
        ElMessage.error(`文件 ${file.file_name} 下载失败：${result.error}`);
      }
    } else {
      result.error = '响应数据不是Blob类型';
      result.actualSize = response.data?.length || 0;
      ElMessage.error(`文件 ${file.file_name} 下载失败：${result.error}`);
    }
    
    testResults.value.unshift(result);
    
  } catch (error: any) {
    console.error('下载测试失败:', error);
    
    const result = {
      fileName: file.file_name,
      expectedSize: parseInt(file.file_size),
      actualSize: 0,
      responseType: 'error',
      isBlob: false,
      success: false,
      error: error.message || '未知错误',
      downloadTime: 0
    };
    
    testResults.value.unshift(result);
    ElMessage.error(`文件 ${file.file_name} 下载失败: ${error.message}`);
  } finally {
    downloading.value.delete(file.file_name);
  }
};

const formatFileSize = (size: number): string => {
  if (!size) return '0 B';
  const units = ['B', 'KB', 'MB', 'GB'];
  let index = 0;
  let fileSize = size;
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  return `${fileSize.toFixed(1)} ${units[index]}`;
};
</script>

<style scoped>
.download-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h4 {
  margin-bottom: 16px;
  color: #303133;
}

.file-list {
  margin-top: 16px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
}

.file-info {
  flex: 1;
}

.file-info strong {
  display: block;
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.test-results {
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.result-details p {
  margin: 4px 0;
  font-size: 14px;
}

.result-details strong {
  color: #303133;
}
</style>
