const db = require("../utils/db");

async function createAIUsageTable() {
  try {
    console.log('🔧 创建AI使用统计表...\n');
    
    // 删除旧表（如果存在）
    console.log('删除旧表...');
    await db.query('DROP TABLE IF EXISTS ai_usage_stats');
    
    // 创建新表
    console.log('创建新表...');
    const createTableSQL = `
      CREATE TABLE ai_usage_stats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        function_type VARCHAR(50) NOT NULL,
        prompt_length INT NOT NULL DEFAULT 0,
        response_length INT NOT NULL DEFAULT 0,
        response_time INT NOT NULL DEFAULT 0,
        model_used VARCHAR(50) NOT NULL DEFAULT 'qwen2.5:0.5b',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_function (user_id, function_type),
        INDEX idx_created_at (created_at),
        INDEX idx_user_date (user_id, created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI使用统计表'
    `;
    
    await db.query(createTableSQL);
    console.log('✅ AI使用统计表创建成功');
    
    // 插入一些测试数据
    console.log('\n插入测试数据...');
    const testData = [
      [19, 'title_generation', 150, 45, 2500, 'qwen2.5:0.5b'],
      [19, 'content_optimization', 200, 180, 3200, 'qwen2.5:0.5b'],
      [19, 'tag_suggestion', 100, 25, 1800, 'qwen2.5:0.5b'],
      [19, 'outline_creation', 80, 300, 4100, 'qwen2.5:0.5b']
    ];
    
    for (const data of testData) {
      await db.query(`
        INSERT INTO ai_usage_stats (user_id, function_type, prompt_length, response_length, response_time, model_used)
        VALUES (?, ?, ?, ?, ?, ?)
      `, data);
    }
    
    console.log('✅ 测试数据插入成功');
    
    // 验证表结构
    console.log('\n验证表结构...');
    const tableInfo = await db.query('DESCRIBE ai_usage_stats');
    console.log('表结构:');
    tableInfo.forEach(field => {
      console.log(`  ${field.Field}: ${field.Type} ${field.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${field.Key ? `(${field.Key})` : ''}`);
    });
    
    // 查询测试数据
    console.log('\n查询测试数据...');
    const testQuery = await db.query('SELECT * FROM ai_usage_stats ORDER BY created_at DESC LIMIT 5');
    console.log('测试数据:');
    testQuery.forEach((row, index) => {
      console.log(`  ${index + 1}. 用户${row.user_id} - ${row.function_type} - ${row.response_time}ms`);
    });
    
    console.log('\n🎉 AI使用统计表设置完成！');
    
  } catch (error) {
    console.error('❌ 创建表失败:', error);
  } finally {
    process.exit(0);
  }
}

// 运行创建
createAIUsageTable();
