const Router = require("koa-router");
const db = require("../../utils/db");
const redisCache = require("../../utils/redisCache");
const logger = require("../../plugin/logger");
const { getPerformanceStats } = require("../../middlewares/performanceMiddleware");

const router = new Router();

// 基础健康检查
router.get("/health", async (ctx) => {
  const startTime = Date.now();
  
  const health = {
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || "1.0.0",
    environment: process.env.NODE_ENV || "development",
    checks: {}
  };

  try {
    // 数据库健康检查
    try {
      await db.query("SELECT 1 as health_check");
      health.checks.database = {
        status: "healthy",
        responseTime: Date.now() - startTime
      };
    } catch (error) {
      health.checks.database = {
        status: "unhealthy",
        error: error.message,
        responseTime: Date.now() - startTime
      };
      health.status = "unhealthy";
    }

    // Redis健康检查
    if (redisCache.isAvailable()) {
      try {
        const redisStart = Date.now();
        await redisCache.set("health_check", "ok", 10);
        const result = await redisCache.get("health_check");
        await redisCache.del("health_check");
        
        health.checks.redis = {
          status: result === "ok" ? "healthy" : "unhealthy",
          responseTime: Date.now() - redisStart
        };
      } catch (error) {
        health.checks.redis = {
          status: "unhealthy",
          error: error.message
        };
      }
    } else {
      health.checks.redis = {
        status: "disabled",
        message: "Redis not configured or unavailable"
      };
    }

    // 内存使用检查
    const memUsage = process.memoryUsage();
    const memUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
    
    health.checks.memory = {
      status: memUsagePercent < 80 ? "healthy" : "warning",
      usage: {
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + "MB",
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + "MB",
        percentage: Math.round(memUsagePercent) + "%"
      }
    };

    if (memUsagePercent >= 90) {
      health.status = "unhealthy";
    } else if (memUsagePercent >= 80) {
      health.status = "warning";
    }

    // 磁盘空间检查（简化版）
    health.checks.disk = {
      status: "healthy",
      message: "Disk space check not implemented"
    };

    ctx.status = health.status === "healthy" ? 200 : 503;
    ctx.body = health;

  } catch (error) {
    logger.error("健康检查失败:", error);
    ctx.status = 503;
    ctx.body = {
      status: "unhealthy",
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
});

// 详细健康检查
router.get("/health/detailed", async (ctx) => {
  try {
    const performanceStats = getPerformanceStats();
    const memUsage = process.memoryUsage();
    
    const detailedHealth = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      system: {
        uptime: process.uptime(),
        platform: process.platform,
        nodeVersion: process.version,
        pid: process.pid,
        memory: {
          rss: Math.round(memUsage.rss / 1024 / 1024) + "MB",
          heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + "MB",
          heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + "MB",
          external: Math.round(memUsage.external / 1024 / 1024) + "MB"
        },
        cpu: {
          usage: process.cpuUsage()
        }
      },
      performance: performanceStats,
      database: {
        connectionPool: "active", // 这里可以添加更详细的连接池信息
      },
      cache: {
        redis: redisCache.isAvailable() ? "connected" : "disconnected"
      }
    };

    ctx.body = detailedHealth;
  } catch (error) {
    logger.error("详细健康检查失败:", error);
    ctx.status = 500;
    ctx.body = {
      status: "error",
      message: error.message
    };
  }
});

// 就绪检查（用于容器编排）
router.get("/ready", async (ctx) => {
  try {
    // 检查关键服务是否就绪
    await db.query("SELECT 1");
    
    ctx.status = 200;
    ctx.body = {
      status: "ready",
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    ctx.status = 503;
    ctx.body = {
      status: "not ready",
      error: error.message
    };
  }
});

// 存活检查（用于容器编排）
router.get("/alive", async (ctx) => {
  ctx.status = 200;
  ctx.body = {
    status: "alive",
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  };
});

module.exports = router;
