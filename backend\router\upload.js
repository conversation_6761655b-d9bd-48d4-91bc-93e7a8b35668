const Router = require("koa-router");
const uploads = new Router();
const fs = require("fs");
const path = require("path");
const logger = require("../plugin/logger");
const db = require("../utils/sql/uploadServer");
const { handleResponse } = require("./responseHandler");

// 文件路径这样才能访问到文件：
// http://localhost:3000/articles/1630.jpg
// http://localhost:3000/images/twitter_img_4.jpg
// http://localhost:3000/resource/twitter_img_4.jpg

// 统一资源保存目录
const resourceDir = path.join(__dirname, "../uploads/resource");

// 新增：判断是否为视频文件
function isVideoFile(filename) {
  return /\.(mp4|webm|mov)$/i.test(filename);
}

// 文件上传接口（单文件/多文件）
uploads.post("/uploads", async (ctx) => {
  let files = ctx.request.files.file;
  if (!files) {
    return handleResponse(ctx, 400, { error: "未上传文件" });
  }
  if (!Array.isArray(files)) {
    files = [files];
  }

  const { user_id } = ctx.request.body;
  const mediaDir = path.join(__dirname, "../public/media");
  if (!fs.existsSync(resourceDir)) fs.mkdirSync(resourceDir, { recursive: true });
  if (!fs.existsSync(mediaDir)) fs.mkdirSync(mediaDir, { recursive: true });

  const uploadedFiles = [];
  for (const file of files) {
    const { size: file_size, name: file_name, type: file_type } = file;
    const isVideo = isVideoFile(file_name);
    const saveDir = isVideo ? mediaDir : resourceDir;
    const file_path = isVideo
      ? `http://localhost:3000/media/${file_name}`
      : `http://localhost:3000/resource/${file_name}`;

    // 判断服务器是否已有该文件
    const targetPath = path.join(saveDir, file_name);
    if (fs.existsSync(targetPath)) {
      uploadedFiles.push({
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        filePath: file_path,
        skipped: true,
        message: "文件已存在，未重复上传"
      });
      logger.info(`文件已存在，未重复上传 - 文件名: ${file.name} - IP: ${ctx.ip}`);
      continue;
    }

    const result = await db.uploadFile(
      user_id,
      file_name,
      file_path,
      file_size,
      file_type
    );
    if (result.success) {
      const reader = fs.createReadStream(file.path);
      const stream = fs.createWriteStream(targetPath);
      reader.pipe(stream);
      uploadedFiles.push({
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        filePath: file_path,
      });
      logger.info(`文件上传成功 - 文件名: ${file.name} - IP: ${ctx.ip}`);
    } else {
      logger.error(`文件上传失败 - 文件名: ${file.name} - IP: ${ctx.ip}`);
      return handleResponse(ctx, 500, { error: `文件上传失败: ${file.name}` });
    }
  }

  return handleResponse(ctx, 200, {
    message: "文件上传处理完成",
    files: uploadedFiles,
  });
});

// 分片上传接口
uploads.post("/uploads/chunk", async (ctx) => {
  const { fileHash, chunkIndex, totalChunks, fileName, user_id } =
    ctx.request.body;
  const chunk = ctx.request.files?.chunk;
  if (!fileHash || chunkIndex === undefined || !chunk || !fileName) {
    return handleResponse(ctx, 400, { error: "缺少必要参数" });
  }

  const chunkDir = path.join(resourceDir, "chunks", fileHash);
  if (!fs.existsSync(chunkDir)) fs.mkdirSync(chunkDir, { recursive: true });

  const chunkPath = path.join(chunkDir, `${chunkIndex}`);
  const reader = fs.createReadStream(chunk.path);
  const stream = fs.createWriteStream(chunkPath);
  reader.pipe(stream);

  logger.info(
    `分片上传成功 - 文件: ${fileName} - 分片: ${chunkIndex} - IP: ${ctx.ip}`
  );
  return handleResponse(ctx, 200, { message: "分片上传成功" });
});

// 合并分片接口
uploads.post("/uploads/merge", async (ctx) => {
  const { fileHash, fileName, totalChunks, user_id, fileType, fileSize } = ctx.request.body;
  if (!fileHash || !fileName || !totalChunks) {
    return handleResponse(ctx, 400, { error: "缺少必要参数" });
  }

  const isVideo = isVideoFile(fileName);
  const saveDir = isVideo
    ? path.join(__dirname, "../public/media")
    : resourceDir;
  const file_path = isVideo
    ? `http://localhost:3000/media/${fileName}`
    : `http://localhost:3000/resource/${fileName}`;

  if (!fs.existsSync(saveDir)) fs.mkdirSync(saveDir, { recursive: true });

  const finalPath = path.join(saveDir, fileName);
  const chunkDir = path.join(resourceDir, "chunks", fileHash);

  // 判断服务器是否已有该文件
  if (fs.existsSync(finalPath)) {
    // 清理分片目录
    if (fs.existsSync(chunkDir)) {
      fs.rmSync(chunkDir, { recursive: true, force: true });
    }
    logger.info(`分片合并时文件已存在，未重复合并 - 文件名: ${fileName} - IP: ${ctx.ip}`);
    return handleResponse(ctx, 200, {
      message: "文件已存在，未重复合并",
      filePath: file_path,
    });
  }

  const writeStream = fs.createWriteStream(finalPath);

  for (let i = 0; i < totalChunks; i++) {
    const chunkPath = path.join(chunkDir, `${i}`);
    if (!fs.existsSync(chunkPath)) {
      return handleResponse(ctx, 400, { error: `缺少分片${i}` });
    }
    const data = fs.readFileSync(chunkPath);
    writeStream.write(data);
    fs.unlinkSync(chunkPath);
  }
  writeStream.end();
  fs.rmdirSync(chunkDir);

  await db.uploadFile(user_id, fileName, file_path, fileSize, fileType);

  logger.info(`文件合并成功 - 用户ID: ${user_id} - 文件名: ${fileName} - 路径: ${file_path} - IP: ${ctx.ip}`);
  // 或者
  // console.log({ user_id, fileName, file_path, fileSize, fileType });

  return handleResponse(ctx, 200, {
    message: "文件合并成功",
    filePath: file_path,
  });
});

// 更新用户头像接口
uploads.post("/avatar", async (ctx) => {
  const { file } = ctx.request.files;
  const { userId } = ctx.request.body;

  if (!file || !userId) {
    return handleResponse(ctx, 400, { error: "缺少必要参数" });
  }

  // 上传文件处理
  const uploadDir = path.join(__dirname, "../uploads/avatars");
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  const reader = fs.createReadStream(file.path);
  const avatarFilename = `avatar_${userId}_${Date.now()}${path.extname(
    file.name
  )}`;
  const stream = fs.createWriteStream(path.join(uploadDir, avatarFilename));
  reader.pipe(stream);

  // 更新数据库中的用户 avatar 字段
  const avatarPath = `/uploads/avatars/${avatarFilename}`;
  const sql = "UPDATE users SET avatar = ? WHERE id = ?";
  const result = await db.query(sql, [avatarPath, userId]);

  if (result.affectedRows > 0) {
    logger.info(`用户 ${userId} 更新头像成功 - IP: ${ctx.ip}`);
    return handleResponse(ctx, 200, {
      message: "头像更新成功",
      avatar: avatarPath,
    });
  } else {
    logger.warn(`用户 ${userId} 更新头像失败 - IP: ${ctx.ip}`);
    return handleResponse(ctx, 500, { error: "头像更新失败" });
  }
});

module.exports = uploads;
