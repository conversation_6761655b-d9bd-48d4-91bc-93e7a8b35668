<template>
  <div class="comment-item">
    <el-avatar :size="40" :src="getAvatarUrl(comment.avatar)" />
    <div class="comment-content">
      <div class="comment-meta">
        <span class="comment-author">{{ comment.author || comment.username }}</span>
        <span class="comment-time">{{ formatDate(comment.created_at) }}</span>
      </div>
      <div class="comment-text">{{ comment.content }}</div>
      <el-button
        size="small"
        type="text"
        @click="showReplyInput(comment.id, comment.author || comment.username)"
      >回复</el-button>
      <div v-if="replyToId === comment.id" class="reply-form">
        <el-input
          :model-value="replyContent"
          type="textarea"
          :rows="2"
          :placeholder="`回复 @${replyToAuthor}`"
          @update:model-value="val => $emit('update:replyContent', val)"
        />
        <div class="form-actions">
          <el-button type="primary" size="small" @click="submitReply(comment.id)">发送</el-button>
          <el-button size="small" @click="cancelReply">取消</el-button>
        </div>
      </div>
      <!-- 递归渲染所有子评论 -->
      <div v-if="comment.children && comment.children.length" class="comment-children" style="margin-left: 32px;">
        <CommentItem
          v-for="child in comment.children"
          :key="child.id"
          :comment="child"
          :get-avatar-url="getAvatarUrl"
          :format-date="formatDate"
          :reply-to-id="replyToId"
          :reply-to-author="replyToAuthor"
          :reply-content="replyContent"
          @update:replyContent="$emit('update:replyContent', $event)"
          :show-reply-input="showReplyInput"
          :submit-reply="submitReply"
          :cancel-reply="cancelReply"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps([
  "comment",
  "getAvatarUrl",
  "formatDate",
  "replyToId",
  "replyToAuthor",
  "replyContent",
  "showReplyInput",
  "submitReply",
  "cancelReply"
]);
defineEmits(['update:replyContent']);
import CommentItem from './CommentItem.vue';
</script>

// ...existing code...
<style scoped lang="less">
.comment-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 18px;
  min-height: 48px;

  .el-avatar {
    margin-right: 12px;
    flex-shrink: 0;
  }

  .comment-content {
    flex: 1;
    background: #fafbfc;
    border-radius: 6px;
    padding: 12px 16px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.03);

    .comment-meta {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      font-size: 13px;
      color: #888;

      .comment-author {
        font-weight: bold;
        color: #333;
        margin-right: 10px;
      }
      .comment-time {
        font-size: 12px;
        color: #bbb;
      }
    }

    .comment-text {
      font-size: 15px;
      color: #222;
      margin-bottom: 8px;
      word-break: break-all;
    }

    .el-button[type="text"] {
      font-size: 13px;
      color: #409eff;
      padding: 0 4px;
      margin-bottom: 4px;
    }

    .reply-form {
      margin-top: 8px;
      background: #f4f8fb;
      border-radius: 4px;
      padding: 8px;
      box-shadow: 0 1px 2px rgba(0,0,0,0.02);

      .form-actions {
        margin-top: 6px;
        display: flex;
        gap: 8px;
      }
    }

    .comment-children {
      margin-top: 10px;
      margin-left: 24px;
      border-left: 2px solid #f0f0f0;
      padding-left: 12px;
    }
  }
}
</style>