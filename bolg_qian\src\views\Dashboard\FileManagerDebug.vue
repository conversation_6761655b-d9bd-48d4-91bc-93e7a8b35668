<template>
  <div class="debug-container">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon class="title-icon"><Setting /></el-icon>
        调试工具
      </h1>
      <p class="page-subtitle">文件管理器调试和诊断工具</p>
    </div>

    <div class="debug-content">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="debug-card">
            <template #header>
              <div class="card-header">
                <el-icon><Monitor /></el-icon>
                <span>系统信息</span>
              </div>
            </template>
            <div class="info-list">
              <div class="info-item">
                <span class="label">操作系统:</span>
                <span class="value">{{ systemInfo.os }}</span>
              </div>
              <div class="info-item">
                <span class="label">浏览器:</span>
                <span class="value">{{ systemInfo.browser }}</span>
              </div>
              <div class="info-item">
                <span class="label">屏幕分辨率:</span>
                <span class="value">{{ systemInfo.screen }}</span>
              </div>
              <div class="info-item">
                <span class="label">当前时间:</span>
                <span class="value">{{ currentTime }}</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="debug-card">
            <template #header>
              <div class="card-header">
                <el-icon><Tools /></el-icon>
                <span>调试操作</span>
              </div>
            </template>
            <div class="debug-actions">
              <el-button type="primary" @click="clearCache" :loading="loading">
                <el-icon><Delete /></el-icon>
                清除缓存
              </el-button>
              <el-button type="warning" @click="checkConnectivity" :loading="connectivityLoading">
                <el-icon><Link /></el-icon>
                检查连接
              </el-button>
              <el-button type="info" @click="exportLogs" :loading="exportLoading">
                <el-icon><Download /></el-icon>
                导出日志
              </el-button>
              <el-button type="success" @click="runDiagnostics" :loading="diagnosticsLoading">
                <el-icon><Check /></el-icon>
                运行诊断
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card class="debug-card">
            <template #header>
              <div class="card-header">
                <el-icon><Document /></el-icon>
                <span>调试日志</span>
                <el-button size="small" @click="clearLogs" style="margin-left: auto;">
                  清空日志
                </el-button>
              </div>
            </template>
            <div class="log-container">
              <div v-for="(log, index) in debugLogs" :key="index" class="log-item" :class="log.type">
                <span class="log-time">{{ log.time }}</span>
                <span class="log-type">[{{ log.type.toUpperCase() }}]</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
              <div v-if="debugLogs.length === 0" class="no-logs">
                暂无调试日志
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Setting,
  Monitor,
  Tools,
  Delete,
  Link,
  Download,
  Check,
  Document
} from '@element-plus/icons-vue';

// 响应式数据
const loading = ref(false);
const connectivityLoading = ref(false);
const exportLoading = ref(false);
const diagnosticsLoading = ref(false);
const currentTime = ref('');
const debugLogs = ref<Array<{time: string, type: string, message: string}>>([]);

// 系统信息
const systemInfo = ref({
  os: navigator.platform,
  browser: navigator.userAgent.split(' ').pop() || 'Unknown',
  screen: `${screen.width}x${screen.height}`
});

// 时间更新
let timeInterval: NodeJS.Timeout;

const updateTime = () => {
  currentTime.value = new Date().toLocaleString();
};

// 添加日志
const addLog = (type: string, message: string) => {
  debugLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    type,
    message
  });
  // 限制日志数量
  if (debugLogs.value.length > 100) {
    debugLogs.value = debugLogs.value.slice(0, 100);
  }
};

// 调试操作
const clearCache = async () => {
  loading.value = true;
  try {
    // 清除本地存储
    localStorage.clear();
    sessionStorage.clear();
    addLog('info', '缓存清除成功');
    ElMessage.success('缓存已清除');
  } catch (error) {
    addLog('error', `清除缓存失败: ${error}`);
    ElMessage.error('清除缓存失败');
  } finally {
    loading.value = false;
  }
};

const checkConnectivity = async () => {
  connectivityLoading.value = true;
  try {
    const response = await fetch('/api/health', { method: 'GET' });
    if (response.ok) {
      addLog('success', '服务器连接正常');
      ElMessage.success('连接检查通过');
    } else {
      addLog('warning', `服务器响应异常: ${response.status}`);
      ElMessage.warning('服务器响应异常');
    }
  } catch (error) {
    addLog('error', `连接检查失败: ${error}`);
    ElMessage.error('连接检查失败');
  } finally {
    connectivityLoading.value = false;
  }
};

const exportLogs = async () => {
  exportLoading.value = true;
  try {
    const logData = debugLogs.value.map(log => 
      `${log.time} [${log.type.toUpperCase()}] ${log.message}`
    ).join('\n');
    
    const blob = new Blob([logData], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `debug-logs-${new Date().toISOString().slice(0, 10)}.txt`;
    a.click();
    URL.revokeObjectURL(url);
    
    addLog('info', '日志导出成功');
    ElMessage.success('日志已导出');
  } catch (error) {
    addLog('error', `日志导出失败: ${error}`);
    ElMessage.error('日志导出失败');
  } finally {
    exportLoading.value = false;
  }
};

const runDiagnostics = async () => {
  diagnosticsLoading.value = true;
  try {
    addLog('info', '开始运行诊断...');
    
    // 检查本地存储
    const localStorageSize = JSON.stringify(localStorage).length;
    addLog('info', `本地存储大小: ${(localStorageSize / 1024).toFixed(2)} KB`);
    
    // 检查内存使用
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      addLog('info', `内存使用: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
    }
    
    // 检查网络状态
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      addLog('info', `网络类型: ${connection.effectiveType || 'unknown'}`);
    }
    
    addLog('success', '诊断完成');
    ElMessage.success('诊断运行完成');
  } catch (error) {
    addLog('error', `诊断运行失败: ${error}`);
    ElMessage.error('诊断运行失败');
  } finally {
    diagnosticsLoading.value = false;
  }
};

const clearLogs = () => {
  debugLogs.value = [];
  ElMessage.success('日志已清空');
};

onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
  addLog('info', '调试工具已启动');
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});
</script>

<style scoped>
.debug-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0 0 8px 0;
}

.title-icon {
  margin-right: 8px;
  color: #409eff;
}

.page-subtitle {
  color: #666;
  margin: 0;
}

.debug-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.label {
  font-weight: bold;
  color: #666;
}

.value {
  color: #333;
}

.debug-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.debug-actions .el-button {
  justify-content: flex-start;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  border-bottom: 1px solid #eee;
}

.log-time {
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.log-type {
  margin-right: 8px;
  min-width: 60px;
  font-weight: bold;
}

.log-item.info .log-type {
  color: #409eff;
}

.log-item.success .log-type {
  color: #67c23a;
}

.log-item.warning .log-type {
  color: #e6a23c;
}

.log-item.error .log-type {
  color: #f56c6c;
}

.log-message {
  flex: 1;
}

.no-logs {
  text-align: center;
  color: #999;
  padding: 20px;
}
</style>
