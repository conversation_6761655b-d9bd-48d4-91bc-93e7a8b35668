const db = require("../utils/db");

async function fixCollation() {
  try {
    console.log("🔧 修复数据库字符集冲突...\n");

    // 修复 video_management 表的字符集
    console.log("修复 video_management 表字符集...");
    await db.query("ALTER TABLE video_management CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci");
    console.log("✅ video_management 表字符集修复成功");

    // 修复 video_categories 表的字符集
    console.log("修复 video_categories 表字符集...");
    await db.query("ALTER TABLE video_categories CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci");
    console.log("✅ video_categories 表字符集修复成功");

    // 修复 video_tags 表的字符集
    console.log("修复 video_tags 表字符集...");
    await db.query("ALTER TABLE video_tags CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci");
    console.log("✅ video_tags 表字符集修复成功");

    // 修复 media_stats 表的字符集
    console.log("修复 media_stats 表字符集...");
    await db.query("ALTER TABLE media_stats CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci");
    console.log("✅ media_stats 表字符集修复成功");

    // 修复 users 表的字符集
    console.log("修复 users 表字符集...");
    await db.query("ALTER TABLE users CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci");
    console.log("✅ users 表字符集修复成功");

    console.log("\n🎉 字符集修复完成！");
    
  } catch (error) {
    console.error("❌ 修复失败:", error);
  } finally {
    process.exit(0);
  }
}

// 运行修复
fixCollation();
