<template>
  <div class="logs-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon class="title-icon"><Document /></el-icon>
          系统日志
        </h1>
        <p class="page-description">实时监控系统运行状态和异常信息</p>
      </div>
      <div class="header-actions">
        <el-button
          @click="fetchLogs"
          :loading="loading"
          type="primary"
          icon="Refresh"
        >
          刷新数据
        </el-button>
        <el-button @click="exportLogs" type="success" icon="Download">
          导出日志
        </el-button>
      </div>
    </div>

    <!-- 统计概览卡片 -->
    <div class="stats-overview">
      <div class="stat-card" v-for="stat in statsCards" :key="stat.key">
        <div class="stat-icon" :class="stat.iconClass">
          <el-icon><component :is="stat.icon" /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
        <div class="stat-trend" v-if="stat.trend">
          <el-icon :class="stat.trend > 0 ? 'trend-up' : 'trend-down'">
            <component :is="stat.trend > 0 ? 'TrendCharts' : 'Bottom'" />
          </el-icon>
          <span>{{ Math.abs(stat.trend) }}%</span>
        </div>
      </div>
    </div>

    <!-- 图表与分析区域 -->
    <div class="analysis-section">
      <div class="chart-panel card">
        <div class="card-header">
          <h3>日志趋势分析</h3>
          <div class="chart-controls">
            <el-radio-group v-model="chartType" size="small">
              <el-radio-button label="bar">柱状图</el-radio-button>
              <el-radio-button label="line">折线图</el-radio-button>
              <el-radio-button label="pie">饼图</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="card-body">
          <div class="chart-container">
            <canvas ref="chartRef"></canvas>
          </div>
        </div>
      </div>

      <div class="alert-panel card">
        <div class="card-header">
          <h3>系统状态</h3>
          <el-tag
            :type="systemStatus.type"
            :icon="systemStatus.icon"
            size="small"
          >
            {{ systemStatus.text }}
          </el-tag>
        </div>
        <div class="card-body">
          <div class="alert-list">
            <div
              v-for="alert in recentAlerts"
              :key="alert.id"
              class="alert-item"
              :class="alert.level"
            >
              <el-icon class="alert-icon">
                <component :is="alert.icon" />
              </el-icon>
              <div class="alert-content">
                <div class="alert-message">{{ alert.message }}</div>
                <div class="alert-time">{{ alert.time }}</div>
              </div>
            </div>
            <div v-if="recentAlerts.length === 0" class="no-alerts">
              <el-icon><SuccessFilled /></el-icon>
              <span>暂无异常警报</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 过滤器工具栏 -->
    <div class="filter-toolbar card">
      <div class="filter-row">
        <div class="filter-group">
          <label class="filter-label">搜索</label>
          <el-input
            v-model="searchKeyword"
            placeholder="搜索日志内容..."
            class="filter-input"
            clearable
            prefix-icon="Search"
            @input="handleSearch"
          />
        </div>

        <div class="filter-group">
          <label class="filter-label">级别</label>
          <el-select
            v-model="selectedLevel"
            placeholder="选择日志级别"
            class="filter-select"
            clearable
            @change="handleFilterChange"
          >
            <el-option
              v-for="level in logLevels"
              :key="level.value"
              :label="level.label"
              :value="level.value"
            >
              <span class="level-option">
                <span class="level-dot" :class="level.value"></span>
                {{ level.label }}
              </span>
            </el-option>
          </el-select>
        </div>

        <div class="filter-group">
          <label class="filter-label">时间范围</label>
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            class="filter-date"
            @change="handleFilterChange"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </div>
      </div>

      <div class="filter-actions">
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          @change="handleAutoRefreshChange"
        />
        <el-button @click="resetFilters" icon="RefreshLeft">重置</el-button>
        <el-button @click="toggleRealTime" :type="realTimeMode ? 'danger' : 'primary'">
          <el-icon><component :is="realTimeMode ? 'VideoPause' : 'VideoPlay'" /></el-icon>
          {{ realTimeMode ? '停止实时' : '实时监控' }}
        </el-button>
      </div>
    </div>

    <!-- 日志表格 -->
    <div class="logs-table-section card">
      <div class="card-header">
        <div class="table-header-left">
          <h3>日志列表</h3>
          <el-tag v-if="filteredLogs.length > 0" type="info" size="small">
            共 {{ totalLogs }} 条，显示 {{ filteredLogs.length }} 条
          </el-tag>
        </div>
        <div class="table-header-right">
          <el-button-group size="small">
            <el-button
              :type="viewMode === 'table' ? 'primary' : ''"
              @click="viewMode = 'table'"
              icon="Grid"
            >
              表格视图
            </el-button>
            <el-button
              :type="viewMode === 'card' ? 'primary' : ''"
              @click="viewMode = 'card'"
              icon="Postcard"
            >
              卡片视图
            </el-button>
          </el-button-group>
        </div>
      </div>

      <div class="card-body">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="8" animated />
        </div>

        <!-- 表格视图 -->
        <div v-else-if="viewMode === 'table'" class="table-view">
          <el-table
            :data="paginatedLogs"
            class="logs-table"
            stripe
            :height="tableHeight"
            @sort-change="handleSortChange"
            :default-sort="{ prop: 'timestamp', order: 'descending' }"
          >
            <el-table-column
              prop="timestamp"
              label="时间"
              width="180"
              sortable="custom"
              :formatter="formatTimestamp"
            />
            <el-table-column prop="level" label="级别" width="100" sortable>
              <template #default="{ row }">
                <el-tag
                  :type="getLevelTagType(row.level)"
                  size="small"
                  class="level-tag"
                >
                  {{ row.level }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="source" label="来源" width="120" sortable />
            <el-table-column prop="message" label="消息内容" min-width="300">
              <template #default="{ row }">
                <div class="message-content">
                  <span
                    v-html="highlightKeyword(row.message)"
                    class="message-text"
                  ></span>
                  <el-button
                    v-if="row.message.length > 100"
                    @click="showLogDetail(row)"
                    type="text"
                    size="small"
                    class="expand-btn"
                  >
                    查看详情
                  </el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button-group size="small">
                  <el-button @click="showLogDetail(row)" icon="View" />
                  <el-button @click="copyLogContent(row)" icon="CopyDocument" />
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 卡片视图 -->
        <div v-else class="card-view">
          <div class="log-cards">
            <div
              v-for="log in paginatedLogs"
              :key="log.id"
              class="log-card"
              :class="log.level.toLowerCase()"
            >
              <div class="log-card-header">
                <el-tag :type="getLevelTagType(log.level)" size="small">
                  {{ log.level }}
                </el-tag>
                <span class="log-time">{{ formatTimestamp(log) }}</span>
              </div>
              <div class="log-card-body">
                <div class="log-source">{{ log.source }}</div>
                <div
                  class="log-message"
                  v-html="highlightKeyword(log.message)"
                ></div>
              </div>
              <div class="log-card-actions">
                <el-button @click="showLogDetail(log)" size="small" text>
                  查看详情
                </el-button>
                <el-button @click="copyLogContent(log)" size="small" text>
                  复制内容
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && filteredLogs.length === 0" class="empty-container">
          <el-empty
            :image-size="120"
            description="暂无日志数据"
          >
            <el-button @click="fetchLogs" type="primary">重新加载</el-button>
          </el-empty>
        </div>

        <!-- 分页 -->
        <div v-if="filteredLogs.length > 0" class="pagination-container">
          <el-pagination
            :current-page="currentPage"
            :page-size="pageSize"
            :page-sizes="[20, 50, 100, 200]"
            :total="filteredLogs.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handleCurrentPageChange"
          />
        </div>
      </div>
    </div>

    <!-- 日志详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="日志详情"
      width="60%"
      :before-close="handleDetailClose"
    >
      <div v-if="selectedLog" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="时间">
            {{ formatTimestamp(selectedLog) }}
          </el-descriptions-item>
          <el-descriptions-item label="级别">
            <el-tag :type="getLevelTagType(selectedLog.level)">
              {{ selectedLog.level }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="来源">
            {{ selectedLog.source }}
          </el-descriptions-item>
          <el-descriptions-item label="线程ID" v-if="selectedLog.threadId">
            {{ selectedLog.threadId }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="detail-content">
          <h4>消息内容</h4>
          <el-input
            v-model="selectedLog.message"
            type="textarea"
            :rows="8"
            readonly
            class="detail-message"
          />
        </div>

        <div v-if="selectedLog.stackTrace" class="detail-stack">
          <h4>堆栈跟踪</h4>
          <el-input
            v-model="selectedLog.stackTrace"
            type="textarea"
            :rows="10"
            readonly
            class="detail-stack-trace"
          />
        </div>
      </div>

      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button @click="copyLogContent(selectedLog)" type="primary">
          复制内容
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from "vue";
import { ShowLogsApi } from "../../utils/api.ts";
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Document, Refresh, Download, TrendCharts, Bottom,
  SuccessFilled, Warning, CircleClose, InfoFilled,
  Grid, Postcard, View, CopyDocument, VideoPlay, VideoPause,
  RefreshLeft
} from '@element-plus/icons-vue';
import Chart from "chart.js/auto";
import { debounce } from 'lodash-es';

// 响应式数据
const logs = ref([]);
const loading = ref(false);
const searchKeyword = ref("");
const selectedLevel = ref("");
const autoRefresh = ref(false);
const realTimeMode = ref(false);
const chartType = ref('bar');
const dateRange = ref([]);
const viewMode = ref('table'); // 'table' | 'card'
const currentPage = ref(1);
const pageSize = ref(50);
const tableHeight = ref(400);

// 弹窗相关
const detailDialogVisible = ref(false);
const selectedLog = ref(null);

// 定时器
let autoRefreshTimer = null;
let realTimeTimer = null;
let chartInstance = null;

// 日志级别配置
const logLevels = [
  { label: '全部', value: '', color: '#909399' },
  { label: 'DEBUG', value: 'debug', color: '#909399' },
  { label: 'INFO', value: 'info', color: '#67c23a' },
  { label: 'WARN', value: 'warn', color: '#e6a23c' },
  { label: 'ERROR', value: 'error', color: '#f56c6c' },
  { label: 'FATAL', value: 'fatal', color: '#909399' }
];

// 统计数据
const analysis = ref({
  total: 0,
  debug: 0,
  info: 0,
  warn: 0,
  error: 0,
  fatal: 0
});

// 计算属性
const filteredLogs = computed(() => {
  let result = logs.value;

  // 关键字过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(log =>
      log.message.toLowerCase().includes(keyword) ||
      log.source.toLowerCase().includes(keyword)
    );
  }

  // 级别过滤
  if (selectedLevel.value) {
    result = result.filter(log =>
      log.level.toLowerCase() === selectedLevel.value.toLowerCase()
    );
  }

  // 时间范围过滤
  if (dateRange.value && dateRange.value.length === 2) {
    const [startTime, endTime] = dateRange.value;
    result = result.filter(log => {
      const logTime = new Date(log.timestamp);
      return logTime >= new Date(startTime) && logTime <= new Date(endTime);
    });
  }

  return result;
});

const paginatedLogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredLogs.value.slice(start, end);
});

const totalLogs = computed(() => logs.value.length);

// 统计卡片数据
const statsCards = computed(() => [
  {
    key: 'total',
    label: '总日志数',
    value: analysis.value.total,
    icon: 'Document',
    iconClass: 'stat-total',
    trend: 0
  },
  {
    key: 'info',
    label: 'INFO',
    value: analysis.value.info,
    icon: 'InfoFilled',
    iconClass: 'stat-info',
    trend: 5
  },
  {
    key: 'warn',
    label: 'WARN',
    value: analysis.value.warn,
    icon: 'Warning',
    iconClass: 'stat-warn',
    trend: -2
  },
  {
    key: 'error',
    label: 'ERROR',
    value: analysis.value.error + analysis.value.fatal,
    icon: 'CircleClose',
    iconClass: 'stat-error',
    trend: analysis.value.error + analysis.value.fatal > 0 ? 10 : -5
  }
]);

// 系统状态
const systemStatus = computed(() => {
  const errorCount = analysis.value.error + analysis.value.fatal;
  if (errorCount > 10) {
    return { type: 'danger', icon: 'CircleClose', text: '系统异常' };
  } else if (errorCount > 0) {
    return { type: 'warning', icon: 'Warning', text: '存在警告' };
  } else {
    return { type: 'success', icon: 'SuccessFilled', text: '运行正常' };
  }
});

// 最近警报
const recentAlerts = computed(() => {
  return logs.value
    .filter(log => ['error', 'fatal', 'warn'].includes(log.level.toLowerCase()))
    .slice(0, 5)
    .map(log => ({
      id: log.id,
      level: log.level.toLowerCase(),
      message: log.message.substring(0, 50) + (log.message.length > 50 ? '...' : ''),
      time: formatTimestamp(log),
      icon: log.level.toLowerCase() === 'error' || log.level.toLowerCase() === 'fatal'
        ? 'CircleClose' : 'Warning'
    }));
});

// 工具方法
const formatTimestamp = (log) => {
  if (!log.timestamp) return '';
  const date = new Date(log.timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

const parseLogLine = (line) => {
  // 解析日志行，支持多种格式
  const patterns = [
    // [2024-01-01 12:00:00][INFO][Source] Message
    /\[([\d-\s:]+)\]\s*\[(.*?)\]\s*\[(.*?)\]\s*(.*)/,
    // [2024-01-01 12:00:00][INFO] Message
    /\[([\d-\s:]+)\]\s*\[(.*?)\]\s*(.*)/,
    // 2024-01-01 12:00:00 INFO Message
    /([\d-\s:]+)\s+(INFO|WARN|ERROR|FATAL|DEBUG)\s+(.*)/i
  ];

  for (const pattern of patterns) {
    const match = line.match(pattern);
    if (match) {
      return {
        id: Date.now() + Math.random(),
        timestamp: match[1],
        level: match[2] || 'INFO',
        source: match[3] || 'System',
        message: match[4] || match[3] || line,
        raw: line
      };
    }
  }

  // 默认格式
  return {
    id: Date.now() + Math.random(),
    timestamp: new Date().toISOString(),
    level: 'INFO',
    source: 'Unknown',
    message: line,
    raw: line
  };
};

const getLevelTagType = (level) => {
  const levelMap = {
    'DEBUG': 'info',
    'INFO': 'success',
    'WARN': 'warning',
    'ERROR': 'danger',
    'FATAL': 'danger'
  };
  return levelMap[level.toUpperCase()] || 'info';
};

const highlightKeyword = (text) => {
  if (!searchKeyword.value || !text) return text;
  const regex = new RegExp(`(${searchKeyword.value})`, 'gi');
  return text.replace(regex, '<mark class="highlight">$1</mark>');
};

// 防抖搜索
const handleSearch = debounce(() => {
  currentPage.value = 1; // 重置到第一页
}, 300);

// 事件处理方法
const handleFilterChange = () => {
  currentPage.value = 1;
};

const handleAutoRefreshChange = () => {
  if (autoRefresh.value) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
};

const handlePageSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
};

const handleCurrentPageChange = (page) => {
  currentPage.value = page;
};

const handleSortChange = ({ prop, order }) => {
  // 实现排序逻辑
  if (prop === 'timestamp') {
    logs.value.sort((a, b) => {
      const timeA = new Date(a.timestamp);
      const timeB = new Date(b.timestamp);
      return order === 'ascending' ? timeA - timeB : timeB - timeA;
    });
  }
};

const resetFilters = () => {
  searchKeyword.value = '';
  selectedLevel.value = '';
  dateRange.value = [];
  currentPage.value = 1;
};

const toggleRealTime = () => {
  realTimeMode.value = !realTimeMode.value;
  if (realTimeMode.value) {
    startRealTimeMonitoring();
  } else {
    stopRealTimeMonitoring();
  }
};

const showLogDetail = (log) => {
  selectedLog.value = log;
  detailDialogVisible.value = true;
};

const handleDetailClose = () => {
  detailDialogVisible.value = false;
  selectedLog.value = null;
};

const copyLogContent = async (log) => {
  try {
    const content = `时间: ${formatTimestamp(log)}\n级别: ${log.level}\n来源: ${log.source}\n内容: ${log.message}`;
    await navigator.clipboard.writeText(content);
    ElMessage.success('日志内容已复制到剪贴板');
  } catch (error) {
    ElMessage.error('复制失败，请手动复制');
  }
};

// 数据获取方法
const fetchLogs = async () => {
  loading.value = true;
  try {
    const params = {
      page: 1,
      limit: 1000, // 获取更多数据用于前端分页
      level: selectedLevel.value || undefined
    };

    const response = await ShowLogsApi(params);

    if (response && response.logs) {
      logs.value = response.logs.map(parseLogLine);
      updateAnalysis();
      await nextTick();
      updateChart();
      ElMessage.success(`成功加载 ${logs.value.length} 条日志`);
    } else {
      logs.value = [];
      ElMessage.warning('未获取到日志数据');
    }
  } catch (error) {
    console.error("获取日志失败:", error);
    ElMessage.error('获取日志失败: ' + (error.message || '未知错误'));
    logs.value = [];
  } finally {
    loading.value = false;
  }
};

const updateAnalysis = () => {
  const levelCount = {
    DEBUG: 0,
    INFO: 0,
    WARN: 0,
    ERROR: 0,
    FATAL: 0
  };

  logs.value.forEach(log => {
    const level = log.level.toUpperCase();
    if (levelCount[level] !== undefined) {
      levelCount[level]++;
    }
  });

  analysis.value = {
    total: logs.value.length,
    debug: levelCount.DEBUG,
    info: levelCount.INFO,
    warn: levelCount.WARN,
    error: levelCount.ERROR,
    fatal: levelCount.FATAL
  };
};

// 定时器管理
const startAutoRefresh = () => {
  stopAutoRefresh();
  if (autoRefresh.value) {
    autoRefreshTimer = setInterval(fetchLogs, 30000); // 30秒刷新一次
  }
};

const stopAutoRefresh = () => {
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer);
    autoRefreshTimer = null;
  }
};

const startRealTimeMonitoring = () => {
  stopRealTimeMonitoring();
  realTimeTimer = setInterval(fetchLogs, 5000); // 5秒刷新一次
  ElMessage.success('已开启实时监控模式');
};

const stopRealTimeMonitoring = () => {
  if (realTimeTimer) {
    clearInterval(realTimeTimer);
    realTimeTimer = null;
  }
  ElMessage.info('已停止实时监控');
};

// 导出功能
const exportLogs = async () => {
  try {
    const logsToExport = filteredLogs.value.length > 0 ? filteredLogs.value : logs.value;

    if (logsToExport.length === 0) {
      ElMessage.warning('没有可导出的日志数据');
      return;
    }

    await ElMessageBox.confirm(
      `确定要导出 ${logsToExport.length} 条日志记录吗？`,
      '确认导出',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    );

    const headers = ['时间', '级别', '来源', '消息内容'];
    const csvContent = [
      headers.join(','),
      ...logsToExport.map(log => [
        `"${formatTimestamp(log)}"`,
        `"${log.level}"`,
        `"${log.source}"`,
        `"${log.message.replace(/"/g, '""')}"`
      ].join(','))
    ].join('\n');

    const blob = new Blob(['\ufeff' + csvContent], {
      type: 'text/csv;charset=utf-8;'
    });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `system_logs_${new Date().toISOString().slice(0, 10)}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success('日志导出成功');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('导出失败: ' + (error.message || '未知错误'));
    }
  }
};

// 图表相关
const chartRef = ref(null);

const initChart = () => {
  if (!chartRef.value) return;

  if (chartInstance) {
    chartInstance.destroy();
  }

  const chartConfig = {
    type: chartType.value === 'pie' ? 'pie' : chartType.value,
    data: {
      labels: ["DEBUG", "INFO", "WARN", "ERROR", "FATAL"],
      datasets: [{
        label: "日志数量",
        data: [0, 0, 0, 0, 0],
        backgroundColor: [
          "#909399", // DEBUG - 灰色
          "#67c23a", // INFO - 绿色
          "#e6a23c", // WARN - 橙色
          "#f56c6c", // ERROR - 红色
          "#909399"  // FATAL - 深灰色
        ],
        borderWidth: 1,
        borderColor: "#ffffff"
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: chartType.value === 'pie',
          position: 'bottom'
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const label = context.label || '';
              const value = context.parsed || context.parsed === 0 ? context.parsed : '';
              const total = context.dataset.data.reduce((a, b) => a + b, 0);
              const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
              return `${label}: ${value} (${percentage}%)`;
            }
          }
        }
      },
      scales: chartType.value === 'pie' ? undefined : {
        x: {
          beginAtZero: true,
          grid: { display: false }
        },
        y: {
          beginAtZero: true,
          ticks: { precision: 0 }
        }
      }
    }
  };

  chartInstance = new Chart(chartRef.value, chartConfig);
  updateChart();
};

const updateChart = () => {
  if (!chartInstance) return;

  chartInstance.data.datasets[0].data = [
    analysis.value.debug,
    analysis.value.info,
    analysis.value.warn,
    analysis.value.error,
    analysis.value.fatal
  ];

  chartInstance.update('none'); // 无动画更新，提高性能
};

// 监听器
watch(chartType, () => {
  nextTick(() => {
    initChart();
  });
});

watch(logs, () => {
  updateAnalysis();
  updateChart();
});

watch(autoRefresh, () => {
  handleAutoRefreshChange();
});

// 生命周期
onMounted(() => {
  fetchLogs();
  nextTick(() => {
    initChart();
  });

  // 设置表格高度
  const updateTableHeight = () => {
    const windowHeight = window.innerHeight;
    tableHeight.value = Math.max(400, windowHeight - 600);
  };

  updateTableHeight();
  window.addEventListener('resize', updateTableHeight);
});

onUnmounted(() => {
  // 清理定时器
  stopAutoRefresh();
  stopRealTimeMonitoring();

  // 清理图表
  if (chartInstance) {
    chartInstance.destroy();
  }

  // 清理事件监听器
  window.removeEventListener('resize', () => {});
});
</script>

<style scoped>
/* 主容器优化 */
.logs-page {
  padding: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: var(--font-family-sans);
  position: relative;
  overflow-x: hidden;
}

/* 添加背景装饰 */
.logs-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  opacity: 0.05;
  z-index: 0;
}

.logs-page > * {
  position: relative;
  z-index: 1;
}

/* 页面标题区域优化 */
.page-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  padding: var(--spacing-2xl) var(--spacing-xl);
  margin: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

/* 添加装饰性背景图案 */
.page-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
}

.page-header::after {
  content: '';
  position: absolute;
  bottom: -30%;
  left: -10%;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
  border-radius: 50%;
}

.header-content {
  flex: 1;
  z-index: 2;
}

.page-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 2.5rem;
  font-weight: var(--font-bold);
  color: white;
  margin: 0 0 var(--spacing-sm) 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-icon {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-sm);
  border-radius: var(--radius-lg);
  font-size: 1.8rem;
  backdrop-filter: blur(10px);
}

.page-description {
  color: rgba(255, 255, 255, 0.9);
  font-size: var(--text-lg);
  margin: 0;
  font-weight: var(--font-medium);
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
  z-index: 2;
}

.header-actions .el-button {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  font-weight: var(--font-medium);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 统计概览卡片区域 */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-xl);
  padding: var(--spacing-xl);
  margin: -60px var(--spacing-xl) var(--spacing-xl);
  position: relative;
  z-index: 10;
}

.stat-card {
  background: var(--bg-primary);
  border: none;
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 卡片悬浮效果 */
.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.stat-card:hover::before {
  opacity: 1;
}

/* 图标样式优化 */
.stat-icon {
  width: 64px;
  height: 64px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  position: relative;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transition: all var(--transition-normal);
}

.stat-card:hover .stat-icon {
  transform: scale(1.1) rotate(5deg);
}

.stat-icon.stat-total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.stat-icon.stat-info {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  color: white;
  box-shadow: 0 8px 24px rgba(103, 194, 58, 0.4);
}

.stat-icon.stat-warn {
  background: linear-gradient(135deg, #e6a23c 0%, #f0a020 100%);
  color: white;
  box-shadow: 0 8px 24px rgba(230, 162, 60, 0.4);
}

.stat-icon.stat-error {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
  color: white;
  box-shadow: 0 8px 24px rgba(245, 108, 108, 0.4);
}

/* 内容区域 */
.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.stat-value {
  font-size: 2.5rem;
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: 1;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--text-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: var(--text-base);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 趋势指示器 */
.stat-trend {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  width: fit-content;
  margin-top: var(--spacing-xs);
}

.trend-up {
  color: var(--success-color);
  background: rgba(16, 185, 129, 0.1);
}

.trend-down {
  color: var(--error-color);
  background: rgba(239, 68, 68, 0.1);
}

/* 分析区域优化 */
.analysis-section {
  display: grid;
  grid-template-columns: 1.8fr 1.2fr;
  gap: var(--spacing-xl);
  padding: 0 var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.chart-panel {
  min-height: 450px;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.chart-panel:hover {
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.chart-container {
  height: 350px;
  position: relative;
  padding: var(--spacing-md);
}

.chart-controls {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.chart-controls .el-radio-group {
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xs);
}

.chart-controls .el-radio-button__inner {
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
}

.chart-controls .el-radio-button__inner:hover {
  background: var(--bg-primary);
  color: var(--text-primary);
}

.chart-controls .el-radio-button.is-active .el-radio-button__inner {
  background: var(--primary-color);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 警报面板优化 */
.alert-panel {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.alert-panel:hover {
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.alert-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 350px;
  overflow-y: auto;
  padding-right: var(--spacing-xs);
}

/* 自定义滚动条 */
.alert-list::-webkit-scrollbar {
  width: 4px;
}

.alert-list::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
}

.alert-list::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: var(--radius-sm);
}

.alert-list::-webkit-scrollbar-thumb:hover {
  background: var(--border-dark);
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  border-left: 4px solid;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.alert-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.alert-item:hover::before {
  opacity: 1;
}

.alert-item.error {
  background: linear-gradient(135deg, #fef0f0 0%, #fdf2f2 100%);
  border-left-color: var(--error-color);
}

.alert-item.warn {
  background: linear-gradient(135deg, #fdf6ec 0%, #fef3e2 100%);
  border-left-color: var(--warning-color);
}

.alert-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.alert-icon {
  margin-top: 2px;
  padding: var(--spacing-xs);
  border-radius: var(--radius-md);
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.alert-content {
  flex: 1;
}

.alert-message {
  font-size: var(--text-sm);
  color: var(--text-primary);
  line-height: var(--leading-relaxed);
  font-weight: var(--font-medium);
  margin-bottom: var(--spacing-xs);
}

.alert-time {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  font-weight: var(--font-normal);
  background: rgba(0, 0, 0, 0.05);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
  display: inline-block;
}

.no-alerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
  text-align: center;
  height: 100%;
}

.no-alerts .el-icon {
  font-size: 3rem;
  color: var(--success-color);
  margin-bottom: var(--spacing-sm);
}

/* 过滤器工具栏优化 */
.filter-toolbar {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin: 0 var(--spacing-xl) var(--spacing-xl);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.filter-toolbar:hover {
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  align-items: end;
  margin-bottom: var(--spacing-lg);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  position: relative;
}

.filter-label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-xs);
  position: relative;
}

.filter-label::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 20px;
  height: 2px;
  background: var(--primary-color);
  border-radius: 1px;
}

/* 输入框样式优化 */
.filter-input,
.filter-select,
.filter-date {
  border-radius: var(--radius-lg);
  border: 2px solid var(--border-light);
  transition: all var(--transition-normal);
  background: var(--bg-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.filter-input:focus,
.filter-select:focus,
.filter-date:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.filter-input:hover,
.filter-select:hover,
.filter-date:hover {
  border-color: var(--primary-light);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

/* 下拉选项样式 */
.level-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) 0;
}

.level-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
}

.level-dot::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
}

.level-dot.debug {
  background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
}
.level-dot.info {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}
.level-dot.warn {
  background: linear-gradient(135deg, #e6a23c 0%, #f0a020 100%);
}
.level-dot.error {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
}
.level-dot.fatal {
  background: linear-gradient(135deg, #909399 0%, #73767a 100%);
}

/* 操作按钮区域 */
.filter-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-light);
  margin-top: var(--spacing-lg);
  justify-content: space-between;
  flex-wrap: wrap;
}

.filter-actions .el-switch {
  --el-switch-on-color: var(--primary-color);
  --el-switch-off-color: var(--border-medium);
}

.filter-actions .el-button {
  border-radius: var(--radius-lg);
  font-weight: var(--font-medium);
  padding: var(--spacing-sm) var(--spacing-lg);
  transition: all var(--transition-normal);
  border: 2px solid transparent;
}

.filter-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.filter-actions .el-button--primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-color: var(--primary-color);
}

.filter-actions .el-button--danger {
  background: linear-gradient(135deg, var(--error-color) 0%, #ef4444 100%);
  border-color: var(--error-color);
}

/* 日志表格区域优化 */
.logs-table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin: 0 var(--spacing-xl) var(--spacing-xl);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.logs-table-section:hover {
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.table-header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.table-header-left h3 {
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--font-bold);
}

.table-header-left .el-tag {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  border: none;
  border-radius: var(--radius-full);
  font-weight: var(--font-medium);
  padding: var(--spacing-xs) var(--spacing-md);
}

.table-header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.table-header-right .el-button-group {
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xs);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
}

.table-header-right .el-button {
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
}

.table-header-right .el-button--primary {
  background: var(--primary-color);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.loading-container {
  padding: var(--spacing-2xl);
}

/* 表格视图优化 */
.table-view {
  flex: 1;
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  overflow: hidden;
}

.logs-table {
  width: 100%;
  font-size: var(--text-sm);
  border-radius: 0;
}

.logs-table :deep(.el-table__header) {
  background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
}

.logs-table :deep(.el-table__header th) {
  background: transparent !important;
  color: var(--text-primary);
  font-weight: var(--font-bold);
  border-bottom: 2px solid var(--primary-color);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: var(--text-xs);
  padding: var(--spacing-lg) var(--spacing-md);
}

.logs-table :deep(.el-table__body tr) {
  transition: all var(--transition-normal);
}

.logs-table :deep(.el-table__row:hover > td) {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, rgba(102, 126, 234, 0.05) 100%);
  transform: scale(1.01);
}

.logs-table :deep(.el-table__body td) {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.level-tag {
  font-weight: var(--font-bold);
  font-size: var(--text-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;
}

.message-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.message-text {
  flex: 1;
  line-height: var(--leading-relaxed);
  font-weight: var(--font-normal);
}

.expand-btn {
  flex-shrink: 0;
  color: var(--primary-color);
  font-weight: var(--font-medium);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
}

.expand-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: scale(1.05);
}

/* 卡片视图优化 */
.card-view {
  flex: 1;
  padding: var(--spacing-md);
}

.log-cards {
  display: grid;
  gap: var(--spacing-lg);
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
}

.log-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  border-left: 6px solid;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.log-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.log-card:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-8px) scale(1.02);
}

.log-card:hover::before {
  opacity: 1;
}

.log-card.debug {
  border-left-color: #909399;
  background: linear-gradient(135deg, rgba(144, 147, 153, 0.05) 0%, var(--bg-primary) 100%);
}

.log-card.info {
  border-left-color: var(--success-color);
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.05) 0%, var(--bg-primary) 100%);
}

.log-card.warn {
  border-left-color: var(--warning-color);
  background: linear-gradient(135deg, rgba(230, 162, 60, 0.05) 0%, var(--bg-primary) 100%);
}

.log-card.error,
.log-card.fatal {
  border-left-color: var(--error-color);
  background: linear-gradient(135deg, rgba(245, 108, 108, 0.05) 0%, var(--bg-primary) 100%);
}

.log-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  position: relative;
  z-index: 2;
}

.log-card-header .el-tag {
  font-weight: var(--font-bold);
  border-radius: var(--radius-full);
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--text-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: none;
}

.log-time {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  background: var(--bg-tertiary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-weight: var(--font-medium);
  font-family: var(--font-family-mono);
}

.log-card-body {
  margin-bottom: var(--spacing-lg);
  position: relative;
  z-index: 2;
}

.log-source {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: var(--bg-tertiary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  display: inline-block;
}

.log-message {
  font-size: var(--text-sm);
  color: var(--text-primary);
  line-height: var(--leading-relaxed);
  font-weight: var(--font-normal);
  background: rgba(0, 0, 0, 0.02);
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  font-family: var(--font-family-mono);
}

.log-card-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
  position: relative;
  z-index: 2;
}

.log-card-actions .el-button {
  border-radius: var(--radius-lg);
  font-weight: var(--font-medium);
  transition: all var(--transition-normal);
  border: 2px solid transparent;
}

.log-card-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 空状态 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: var(--spacing-lg) 0;
  border-top: 1px solid var(--border-light);
  margin-top: var(--spacing-lg);
}

/* 日志详情 */
.log-detail {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.detail-content,
.detail-stack {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.detail-content h4,
.detail-stack h4 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.detail-message,
.detail-stack-trace {
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
}

/* 高亮样式 */
:deep(.highlight) {
  background: linear-gradient(120deg, #ffd04b 0%, #ffed4e 100%);
  color: var(--text-primary);
  padding: 2px 4px;
  border-radius: var(--radius-sm);
  font-weight: var(--font-medium);
}

/* 通用卡片样式 */
.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-secondary);
}

.card-header h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.card-body {
  padding: var(--spacing-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 响应式设计优化 */
@media (max-width: 1400px) {
  .analysis-section {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .stats-overview {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    margin: -40px var(--spacing-lg) var(--spacing-lg);
  }
}

@media (max-width: 1024px) {
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }

  .filter-row {
    grid-template-columns: repeat(2, 1fr);
  }

  .log-cards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .logs-page {
    background: var(--bg-secondary);
  }

  .page-header {
    padding: var(--spacing-xl) var(--spacing-lg);
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-lg);
    text-align: center;
  }

  .page-title {
    font-size: 2rem;
    justify-content: center;
  }

  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .stats-overview {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    margin: -30px var(--spacing-md) var(--spacing-lg);
    padding: var(--spacing-lg);
  }

  .stat-card {
    padding: var(--spacing-lg);
    gap: var(--spacing-md);
  }

  .stat-icon {
    width: 56px;
    height: 56px;
    font-size: 24px;
  }

  .stat-value {
    font-size: 2rem;
  }

  .analysis-section,
  .filter-toolbar,
  .logs-table-section {
    margin: 0 var(--spacing-md) var(--spacing-lg);
  }

  .filter-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .filter-actions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .table-header-left,
  .table-header-right {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .logs-table {
    font-size: var(--text-xs);
  }

  .logs-table :deep(.el-table__header th) {
    padding: var(--spacing-sm);
  }

  .logs-table :deep(.el-table__body td) {
    padding: var(--spacing-sm);
  }

  .log-cards {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .log-card {
    padding: var(--spacing-md);
  }

  .chart-container {
    height: 250px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: var(--spacing-lg);
  }

  .page-title {
    font-size: 1.75rem;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .title-icon {
    font-size: 1.5rem;
  }

  .stats-overview {
    margin: -20px var(--spacing-sm) var(--spacing-md);
    padding: var(--spacing-md);
  }

  .stat-card {
    padding: var(--spacing-md);
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
    margin: 0 auto;
  }

  .stat-value {
    font-size: 1.75rem;
  }

  .analysis-section,
  .filter-toolbar,
  .logs-table-section {
    margin: 0 var(--spacing-sm) var(--spacing-md);
  }

  .chart-container {
    height: 200px;
  }

  .alert-list {
    max-height: 200px;
  }

  .logs-table :deep(.el-table__cell) {
    padding: var(--spacing-xs);
    font-size: 11px;
  }

  .message-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-xs);
  }

  .log-card {
    padding: var(--spacing-sm);
  }

  .log-card-header {
    flex-direction: column;
    gap: var(--spacing-xs);
    align-items: stretch;
  }
}

/* 高级动画效果 */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 应用动画 */
.page-header {
  animation: slideInFromTop 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.stats-overview {
  animation: slideInFromTop 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

.stat-card {
  animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

.analysis-section {
  animation: slideInFromLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
}

.filter-toolbar {
  animation: slideInFromRight 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.5s both;
}

.logs-table-section {
  animation: slideInFromTop 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.6s both;
}

.log-card {
  animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.log-card:nth-child(odd) {
  animation-delay: 0.1s;
}

.log-card:nth-child(even) {
  animation-delay: 0.2s;
}

/* 加载状态动画 */
.loading-container {
  animation: pulse 2s infinite;
}

/* 悬浮状态特效 */
.stat-card:hover .stat-icon {
  animation: pulse 0.6s ease-in-out;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .logs-page {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  }

  .logs-page::before {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    opacity: 0.1;
  }

  .page-header {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
  }

  .card,
  .stat-card,
  .chart-panel,
  .alert-panel,
  .filter-toolbar,
  .logs-table-section {
    background: #2d3748;
    border-color: #4a5568;
  }

  .card-header {
    background: #1a202c;
    border-color: #4a5568;
  }

  .logs-table :deep(.el-table__header) {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  }

  .alert-item.error {
    background: linear-gradient(135deg, rgba(245, 108, 108, 0.1) 0%, #2d3748 100%);
  }

  .alert-item.warn {
    background: linear-gradient(135deg, rgba(230, 162, 60, 0.1) 0%, #2d3748 100%);
  }
}

/* 打印样式 */
@media print {
  .logs-page {
    background: white !important;
    color: black !important;
  }

  .page-header {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  .stat-card,
  .chart-panel,
  .alert-panel,
  .filter-toolbar,
  .logs-table-section {
    background: white !important;
    border: 1px solid #ccc !important;
    box-shadow: none !important;
  }

  .header-actions,
  .filter-actions,
  .log-card-actions {
    display: none !important;
  }
}
</style>