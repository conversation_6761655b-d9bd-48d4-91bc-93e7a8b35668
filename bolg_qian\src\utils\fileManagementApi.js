// 文件管理API服务
import request from './index'

// 文件管理API
export const fileManagementApi = {
  // 获取文件管理总览
  getOverview() {
    return request({
      url: '/files/overview',
      method: 'get'
    })
  },

  // 获取下载统计
  getDownloadStats() {
    return request({
      url: '/files/downloads',
      method: 'get'
    })
  },

  // 获取上传统计
  getUploadStats() {
    return request({
      url: '/files/uploads',
      method: 'get'
    })
  },

  // 获取媒体统计
  getMediaStats() {
    return request({
      url: '/files/media',
      method: 'get'
    })
  },

  // 获取限速配置
  getSpeedLimitConfigs() {
    return request({
      url: '/files/speed-limits',
      method: 'get'
    })
  },

  // 获取用户限速信息
  getUserSpeedLimit(userId) {
    return request({
      url: `/files/user-speed-limit/${userId}`,
      method: 'get'
    })
  },

  // 升级用户等级
  upgradeUserLevel(data) {
    return request({
      url: '/files/upgrade-user-level',
      method: 'post',
      data
    })
  },

  // 获取系统传输统计
  getSystemStats() {
    return request({
      url: '/files/system-stats',
      method: 'get'
    })
  },

  // 获取活跃下载列表
  getActiveDownloads() {
    return request({
      url: '/files/active-downloads',
      method: 'get'
    })
  },

  // 获取活跃上传列表
  getActiveUploads() {
    return request({
      url: '/files/active-uploads',
      method: 'get'
    })
  },

  // 获取活跃媒体播放会话
  getActiveMedia() {
    return request({
      url: '/files/active-media',
      method: 'get'
    })
  }
}

// 仪表盘文件管理API
export const dashboardFileApi = {
  // 获取仪表盘文件统计
  getFileStats() {
    return request({
      url: '/dashboard/file-stats',
      method: 'get'
    })
  },

  // 获取限速配置
  getSpeedLimitConfig() {
    return request({
      url: '/dashboard/speed-limit-config',
      method: 'get'
    })
  },

  // 升级用户等级
  upgradeUser(data) {
    return request({
      url: '/dashboard/upgrade-user',
      method: 'post',
      data
    })
  },

  // 获取传输状态
  getTransferStatus() {
    return request({
      url: '/dashboard/transfer-status',
      method: 'get'
    })
  }
}

// 文件上传API
export const fileUploadApi = {
  // 初始化上传
  initUpload(data) {
    return request({
      url: '/upload/init',
      method: 'post',
      data
    })
  },

  // 分片上传
  uploadChunk(data) {
    return request({
      url: '/upload/chunk',
      method: 'post',
      data
    })
  },

  // 完成上传
  completeUpload(data) {
    return request({
      url: '/upload/complete',
      method: 'post',
      data
    })
  }
}

// 工具函数
export const fileUtils = {
  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    if (bytes === -1) return '无限制'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 格式化速度
  formatSpeed(bytesPerSecond) {
    if (bytesPerSecond === 0) return '无限制'
    
    const k = 1024
    const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s']
    const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k))
    
    return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  },

  // 格式化时间
  formatDuration(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    } else {
      return `${seconds}s`
    }
  },

  // 获取文件类型图标
  getFileTypeIcon(fileName) {
    const ext = fileName.split('.').pop().toLowerCase()
    
    const iconMap = {
      // 图片
      jpg: 'el-icon-picture',
      jpeg: 'el-icon-picture',
      png: 'el-icon-picture',
      gif: 'el-icon-picture',
      webp: 'el-icon-picture',
      svg: 'el-icon-picture',
      
      // 音频
      mp3: 'el-icon-headset',
      wav: 'el-icon-headset',
      flac: 'el-icon-headset',
      aac: 'el-icon-headset',
      ogg: 'el-icon-headset',
      
      // 视频
      mp4: 'el-icon-video-camera',
      webm: 'el-icon-video-camera',
      avi: 'el-icon-video-camera',
      mov: 'el-icon-video-camera',
      mkv: 'el-icon-video-camera',
      
      // 文档
      pdf: 'el-icon-document',
      doc: 'el-icon-document',
      docx: 'el-icon-document',
      txt: 'el-icon-document',
      md: 'el-icon-document',
      
      // 压缩文件
      zip: 'el-icon-folder',
      rar: 'el-icon-folder',
      '7z': 'el-icon-folder'
    }
    
    return iconMap[ext] || 'el-icon-document'
  },

  // 获取用户等级颜色
  getUserLevelColor(level) {
    const colorMap = {
      free: '#909399',
      basic: '#67C23A',
      premium: '#E6A23C',
      vip: '#F56C6C',
      unlimited: '#722ED1'
    }
    
    return colorMap[level] || '#909399'
  },

  // 获取用户等级标签
  getUserLevelLabel(level) {
    const labelMap = {
      free: '免费用户',
      basic: '基础会员',
      premium: '高级会员',
      vip: 'VIP会员',
      unlimited: '无限制会员'
    }
    
    return labelMap[level] || '未知等级'
  },

  // 计算进度百分比
  calculateProgress(current, total) {
    if (total === 0) return 0
    return Math.round((current / total) * 100)
  },

  // 验证文件类型
  validateFileType(fileName, allowedTypes = []) {
    if (allowedTypes.length === 0) return true
    
    const ext = fileName.split('.').pop().toLowerCase()
    return allowedTypes.includes(ext)
  },

  // 生成随机颜色
  generateRandomColor() {
    const colors = [
      '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',
      '#722ED1', '#13C2C2', '#52C41A', '#FAAD14', '#F5222D'
    ]
    return colors[Math.floor(Math.random() * colors.length)]
  }
}

export default {
  fileManagementApi,
  dashboardFileApi,
  fileUploadApi,
  fileUtils
}
