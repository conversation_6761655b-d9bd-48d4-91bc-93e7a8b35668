<template>
  <div class="effect-demo">
    <div class="demo-header">
      <h1>✨ 特效演示页面</h1>
      <p>体验不同的页面特效，感受视觉魅力</p>
    </div>

    <!-- 快速切换特效 -->
    <div class="quick-controls">
      <el-card class="controls-card" shadow="hover">
        <template #header>
          <span>快速切换特效</span>
        </template>
        
        <div class="effect-buttons">
          <el-button 
            v-for="effect in quickEffects" 
            :key="effect.value"
            :type="currentEffect === effect.value ? 'primary' : 'default'"
            @click="switchEffect(effect.value)"
            :icon="effect.icon"
          >
            {{ effect.label }}
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 特效信息展示 -->
    <div class="effect-info">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-card class="info-card">
            <div class="info-item">
              <div class="info-label">当前特效</div>
              <div class="info-value">{{ getCurrentEffectName() }}</div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="info-card">
            <div class="info-item">
              <div class="info-label">运行状态</div>
              <div class="info-value">
                <el-tag :type="effectConfig.enabled ? 'success' : 'danger'">
                  {{ effectConfig.enabled ? '运行中' : '已停止' }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="info-card">
            <div class="info-item">
              <div class="info-label">透明度</div>
              <div class="info-value">{{ effectConfig.opacity }}%</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 演示内容 -->
    <div class="demo-content">
      <el-card class="content-card">
        <template #header>
          <span>演示内容</span>
        </template>
        
        <div class="content-text">
          <h2>欢迎来到特效世界</h2>
          <p>
            页面特效能够为用户带来更加丰富的视觉体验。我们提供了多种不同风格的特效：
          </p>
          
          <ul>
            <li><strong>流星雨特效</strong>：浪漫的流星划过夜空，适合营造温馨的氛围</li>
            <li><strong>夜空星辰</strong>：静谧的星空背景，让人感受宁静与美好</li>
            <li><strong>粒子连线</strong>：科技感十足的粒子动画，展现现代感</li>
          </ul>
          
          <p>
            每种特效都经过精心设计，在保证视觉效果的同时，也考虑了性能优化。
            您可以在仪表盘的特效管理页面中进行详细配置。
          </p>
          
          <div class="demo-actions">
            <el-button type="primary" @click="goToEffectManagement">
              前往特效管理
            </el-button>
            <el-button @click="toggleEffect">
              {{ effectConfig.enabled ? '暂停特效' : '启动特效' }}
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 特效预览区域 -->
    <div class="preview-area">
      <el-card class="preview-card">
        <template #header>
          <span>特效预览</span>
        </template>
        
        <div class="preview-grid">
          <div 
            v-for="effect in previewEffects" 
            :key="effect.value"
            class="preview-item"
          >
            <div class="preview-header">
              <h4>{{ effect.label }}</h4>
              <el-button 
                size="small" 
                @click="switchEffect(effect.value)"
                :type="currentEffect === effect.value ? 'primary' : 'default'"
              >
                {{ currentEffect === effect.value ? '当前' : '切换' }}
              </el-button>
            </div>
            
            <div class="preview-container">
              <EffectPreview
                :effect-type="effect.value"
                :config="getEffectConfig(effect.value)"
                :width="300"
                :height="200"
              />
            </div>
            
            <div class="preview-description">
              <p>{{ effect.description }}</p>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 性能提示 -->
    <div class="performance-tips" v-if="showPerformanceTip">
      <el-alert
        title="性能提示"
        description="当前设备性能较低，建议选择轻量级特效以获得更好的体验"
        type="warning"
        :closable="false"
      />
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useEffectManager } from '../composables/useEffectManager'
import EffectPreview from '../components/EffectPreview.vue'

export default {
  name: 'EffectDemo',
  components: {
    EffectPreview
  },
  setup() {
    const router = useRouter()
    const { currentEffect, effectConfig, applyEffect, toggleEffect: toggleEffectManager } = useEffectManager()

    // 快速切换特效选项
    const quickEffects = [
      { value: 'none', label: '无特效', icon: 'Close' },
      { value: 'meteor', label: '流星雨', icon: 'Star' },
      { value: 'nightsky', label: '夜空星辰', icon: 'Moon' },
      { value: 'particle', label: '粒子连线', icon: 'Connection' }
    ]

    // 预览特效选项
    const previewEffects = [
      {
        value: 'meteor',
        label: '流星雨',
        description: '浪漫的流星雨效果，营造温馨浪漫的氛围，适合夜间浏览和情感类内容。'
      },
      {
        value: 'nightsky',
        label: '夜空星辰',
        description: '静谧的星空背景，带来宁静致远的感觉，适合阅读和思考。'
      },
      {
        value: 'particle',
        label: '粒子连线',
        description: '科技感十足的粒子连线动画，展现现代感和科技魅力，适合技术类内容。'
      }
    ]

    // 性能提示
    const showPerformanceTip = ref(false)

    // 获取当前特效名称
    const getCurrentEffectName = () => {
      const effect = quickEffects.find(e => e.value === currentEffect.value)
      return effect ? effect.label : '未知特效'
    }

    // 切换特效
    const switchEffect = (effectType) => {
      applyEffect(effectType)
      ElMessage.success(`已切换到 ${getCurrentEffectName()}`)
    }

    // 切换特效开关
    const toggleEffect = () => {
      toggleEffectManager(!effectConfig.enabled)
      ElMessage.success(effectConfig.enabled ? '特效已启动' : '特效已暂停')
    }

    // 前往特效管理
    const goToEffectManagement = () => {
      router.push('/index/dashboard/effects')
    }

    // 获取特效配置
    const getEffectConfig = (effectType) => {
      // 返回适合预览的配置
      const configs = {
        meteor: { count: 3, size: 2, theme: 'white' },
        nightsky: { starCount: 100, twinkle: true, constellation: false },
        particle: { particleCount: 50, linkDistance: 80, mouseInteraction: false, particleColor: '#409eff' }
      }
      return configs[effectType] || {}
    }

    // 检测设备性能
    const checkPerformance = () => {
      // 简单的性能检测
      const canvas = document.createElement('canvas')
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
      
      if (!gl) {
        showPerformanceTip.value = true
        return
      }
      
      // 检测硬件加速
      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
      if (debugInfo) {
        const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
        if (renderer.includes('Software') || renderer.includes('Microsoft')) {
          showPerformanceTip.value = true
        }
      }
    }

    onMounted(() => {
      checkPerformance()
    })

    return {
      currentEffect,
      effectConfig,
      quickEffects,
      previewEffects,
      showPerformanceTip,
      getCurrentEffectName,
      switchEffect,
      toggleEffect,
      goToEffectManagement,
      getEffectConfig
    }
  }
}
</script>

<style scoped>
.effect-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;
}

.demo-header h1 {
  color: #303133;
  margin-bottom: 8px;
  font-size: 28px;
}

.demo-header p {
  color: #606266;
  font-size: 16px;
}

.quick-controls,
.effect-info,
.demo-content,
.preview-area,
.performance-tips {
  margin-bottom: 24px;
}

.effect-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.info-item {
  text-align: center;
}

.info-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.info-value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.content-text h2 {
  color: #303133;
  margin-bottom: 16px;
}

.content-text p {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 16px;
}

.content-text ul {
  color: #606266;
  line-height: 1.6;
  margin: 16px 0;
  padding-left: 20px;
}

.content-text li {
  margin-bottom: 8px;
}

.demo-actions {
  margin-top: 24px;
  display: flex;
  gap: 12px;
}

.preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
}

.preview-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.preview-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.preview-header h4 {
  margin: 0;
  color: #303133;
}

.preview-container {
  margin-bottom: 12px;
  border-radius: 6px;
  overflow: hidden;
}

.preview-description p {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .effect-demo {
    padding: 16px;
  }
  
  .effect-buttons {
    justify-content: center;
  }
  
  .demo-actions {
    flex-direction: column;
  }
  
  .preview-grid {
    grid-template-columns: 1fr;
  }
}
</style>
