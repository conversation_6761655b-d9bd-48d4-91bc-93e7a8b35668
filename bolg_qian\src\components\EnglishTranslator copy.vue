<template>
  <div class="english-translator">
    <el-card class="translator-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <div class="header-controls">
            <el-tag type="info" size="small" style="margin-right: 8px;">
              翻译模式
            </el-tag>
            <el-tag :type="aiStatus === 'online' ? 'success' : 'danger'" size="small">
              {{ aiStatus === 'online' ? '在线' : '离线' }}
            </el-tag>
          </div>
        </div>
      </template>

      <div class="translator-content">
        <!-- 练习模式选择 -->
        <div class="mode-selection">
          <el-radio-group v-model="practiceMode" size="small">
            <el-radio-button label="view">查看模式</el-radio-button>
            <el-radio-button label="practice">练习模式</el-radio-button>
            <el-radio-button label="test">测试模式</el-radio-button>
            <el-radio-button label="reverse">英文练习</el-radio-button>
          </el-radio-group>
          <el-tooltip content="查看模式：直接显示翻译结果&#10;练习模式：部分单词替换为下划线&#10;测试模式：所有内容为下划线，自己填写&#10;英文练习：系统生成中文，您写英文">
            <el-icon class="help-icon"><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
        
        <!-- 英文练习模式 -->
        <div v-if="practiceMode === 'reverse'" class="reverse-mode">
          <div class="reverse-prompt">
            <div class="section-title dark-title">
              <span>🇨🇳 请将下面的中文翻译成英文</span>
              <el-button type="primary" size="small" @click="generateChinesePrompt" :loading="generating">
                换一个
              </el-button>
            </div>
            <div class="chinese-prompt">{{ chinesePrompt || '点击"换一个"按钮开始练习' }}</div>
          </div>
          
          <div class="sentence-practice" v-if="chinesePrompt">
            <div class="section-title dark-title">
              <span>🇬🇧 在下划线处填写英文单词</span>
              <el-button type="success" size="small" @click="checkSentenceAnswer" :disabled="sentenceAnswerChecked">
                检查答案
              </el-button>
            </div>
            
            <div class="sentence-container">
              <div class="sentence-words">
                <template v-for="(word, index) in sentenceWords" :key="index">
                  <div class="word-container">
                    <input 
                      v-if="word.hidden"
                      type="text"
                      v-model="sentenceAnswers[index]"
                      class="sentence-input"
                      :class="{
                        'correct-word': isWordCorrect(index) || isInputCorrect(index), 
                        'incorrect-word': isWordIncorrect(index)
                      }"
                      :disabled="sentenceAnswerChecked"
                      @input="checkInputCorrectness(index)"
                    />
                    <span v-else class="visible-sentence-word">{{ word.text }}</span>
                    <div class="sentence-underline"></div>
                    <div v-if="sentenceAnswerChecked && word.hidden && !isWordCorrect(index)" class="word-correction">
                      {{ word.text }}
                    </div>
                    <div v-if="word.hidden && !sentenceAnswerChecked" class="word-hint">
                      <el-tooltip :content="getFullHint(index)" placement="bottom">
                        <el-button 
                          type="text" 
                          size="small" 
                          class="hint-button"
                          @click="getHint(index)"
                        >
                          提示
                        </el-button>
                      </el-tooltip>
                    </div>
                  </div>
                  <span class="word-space" v-if="index < sentenceWords.length - 1"> </span>
                </template>
              </div>
            </div>
            
            <div class="sentence-controls" v-if="sentenceAnswerChecked">
              <el-button type="primary" @click="nextSentence">
                下一题
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 中文输入区域 -->
        <div class="input-section" v-if="practiceMode !== 'reverse'">
          <div class="section-title">
            <span>📝 请输入中文句子或段落</span>
            <el-button type="primary" size="small" :loading="translating" @click="translateText">
              翻译
            </el-button>
          </div>
          <el-input
            v-model="chineseText"
            type="textarea"
            :rows="5"
            placeholder="请输入要翻译的中文内容..."
            maxlength="1000"
            show-word-limit
          />
        </div>

        <!-- 翻译结果展示 -->
        <div class="translation-section" v-if="translationResult && practiceMode !== 'reverse'">
          <div class="section-title">
            <span>🔤 英语翻译结果</span>
            <div class="action-buttons">
              <el-button type="text" @click="copyTranslation">复制</el-button>
              <el-button type="text" @click="playAudio" :disabled="!canPlayAudio">
                <el-icon><Headset /></el-icon> 朗读
              </el-button>
            </div>
          </div>
          
          <!-- 查看模式 -->
          <div v-if="practiceMode === 'view'" class="translation-result">
            <p>{{ translationResult }}</p>
          </div>
          
          <!-- 练习模式 -->
          <div v-else-if="practiceMode === 'practice'" class="practice-mode">
            <div class="practice-sentence">
              <template v-for="(word, index) in processedWords" :key="index">
                <template v-if="word.hidden">
                  <div class="blank-word">
                    <input 
                      type="text"
                      v-model="userAnswers[index]"
                      class="word-input"
                      :class="{
                        'correct': isAnswerCorrect(index) || isRealtimeCorrect(index), 
                        'incorrect': isAnswerIncorrect(index)
                      }"
                      :disabled="showAnswers"
                      @input="checkRealtimeCorrectness(index)"
                    />
                    <div class="underline"></div>
                    <div class="correct-answer" v-if="showAnswers">{{ word.text }}</div>
                  </div>
                </template>
                <template v-else>
                  <span class="visible-word">{{ word.text }}</span>
                </template>
              </template>
            </div>
            
            <div class="practice-controls">
              <el-button type="primary" size="small" @click="checkAnswers" :disabled="showAnswers">
                检查答案
              </el-button>
              <el-button type="info" size="small" @click="resetAnswers">
                重置
              </el-button>
              <el-button type="success" size="small" @click="showAllAnswers" :disabled="showAnswers">
                显示答案
              </el-button>
            </div>
          </div>
          
          <!-- 测试模式 -->
          <div v-else-if="practiceMode === 'test'" class="test-mode">
            <div class="test-prompt">
              <p>根据上面的中文，在下方文本框中输入您的英语翻译：</p>
            </div>
            <el-input
              v-model="userTranslation"
              type="textarea"
              :rows="5"
              placeholder="请输入您的英文翻译..."
              :disabled="testSubmitted"
            />
            <div class="test-controls">
              <el-button type="primary" size="small" @click="submitTest" :disabled="testSubmitted">
                提交答案
              </el-button>
              <el-button type="info" size="small" @click="resetTest" :disabled="!testSubmitted">
                重新测试
              </el-button>
            </div>
            
            <!-- 测试结果 -->
            <div v-if="testSubmitted" class="test-result">
              <div class="result-header">
                <span>测试结果</span>
                <el-tag :type="testScore > 80 ? 'success' : testScore > 60 ? 'warning' : 'danger'">
                  {{ testScore }}分
                </el-tag>
              </div>
              <div class="comparison">
                <div class="your-answer">
                  <h4>您的翻译：</h4>
                  <p>{{ userTranslation }}</p>
                </div>
                <div class="correct-answer">
                  <h4>参考翻译：</h4>
                  <p>{{ translationResult }}</p>
                </div>
              </div>
              <div class="feedback">
                <h4>反馈：</h4>
                <p>{{ testFeedback }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 历史记录 -->
        <div class="history-section" v-if="translationHistory.length > 0 && practiceMode !== 'reverse'">
          <div class="section-title">
            <span>📜 历史记录</span>
            <el-button type="text" @click="clearHistory">清空</el-button>
          </div>
          <el-collapse accordion>
            <el-collapse-item v-for="(item, index) in translationHistory" :key="index" :name="index">
              <template #title>
                <div class="history-item-title">
                  <span>{{ truncateText(item.chinese, 30) }}</span>
                  <span class="history-time">{{ formatTime(item.timestamp) }}</span>
                </div>
              </template>
              <div class="history-content">
                <div class="history-chinese">
                  <h4>中文：</h4>
                  <p>{{ item.chinese }}</p>
                </div>
                <div class="history-english">
                  <h4>英文：</h4>
                  <p>{{ item.english }}</p>
                </div>
                <div class="history-actions">
                  <el-button type="primary" size="small" @click="loadFromHistory(item)">
                    加载
                  </el-button>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { QuestionFilled, Headset } from '@element-plus/icons-vue';
import { formatDate } from '@/utils/date';

// AI状态
const aiStatus = ref('online');

// 翻译相关
const chineseText = ref('');
const translationResult = ref('');
const translating = ref(false);

// 练习模式
const practiceMode = ref('view');
const processedWords = ref([]);
const userAnswers = reactive({});
const showAnswers = ref(false);
const realtimeCorrectAnswers = reactive({});

// 测试模式
const userTranslation = ref('');
const testSubmitted = ref(false);
const testScore = ref(0);
const testFeedback = ref('');

// 英文练习模式
const chinesePrompt = ref('');
const englishReference = ref('');
const userEnglish = ref('');
const reverseAnswerChecked = ref(false);
const reverseScore = ref(0);
const reverseFeedback = ref('');
const generating = ref(false);
const sentenceWords = ref([]);
const sentenceAnswers = reactive({});
const sentenceAnswerChecked = ref(false);
const wordHints = reactive({});
const inputCorrectWords = reactive({});

// 历史记录
const translationHistory = ref([]);
// 英文练习历史记录
const practiceHistory = ref([]);

// 音频播放
const canPlayAudio = computed(() => {
  return !!translationResult.value && 'speechSynthesis' in window;
});

// 组件挂载时预加载英文练习内容
onMounted(() => {
  // 从本地存储加载历史记录
  loadHistoryFromLocalStorage();
  
  // 预加载一个英文练习内容，这样切换到英文练习模式时就有内容了
  generateChinesePrompt();
});

// 从localStorage加载历史记录
function loadHistoryFromLocalStorage() {
  try {
    // 加载翻译历史记录
    const savedHistory = localStorage.getItem('translationHistory');
    if (savedHistory) {
      translationHistory.value = JSON.parse(savedHistory);
    }
    
    // 加载练习历史记录
    const savedPracticeHistory = localStorage.getItem('practiceHistory');
    if (savedPracticeHistory) {
      practiceHistory.value = JSON.parse(savedPracticeHistory);
    }
  } catch (error) {
    console.error('从本地存储加载历史记录失败:', error);
  }
}

// 保存历史记录到localStorage
function saveHistoryToLocalStorage() {
  try {
    localStorage.setItem('translationHistory', JSON.stringify(translationHistory.value));
    localStorage.setItem('practiceHistory', JSON.stringify(practiceHistory.value));
  } catch (error) {
    console.error('保存历史记录到本地存储失败:', error);
  }
}

// 监听练习模式变化
watch(practiceMode, (newMode) => {
  if (newMode === 'reverse' && !chinesePrompt.value) {
    // 当切换到英文练习模式且没有中文提示时，自动生成一个
    generateChinesePrompt();
  }
  
  // 重置各种状态
  resetAnswers();
  resetTest();
});

// 翻译文本
async function translateText() {
  if (!chineseText.value.trim()) {
    ElMessage.warning('请输入要翻译的中文内容');
    return;
  }
  
  translating.value = true;
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 这里应该是实际的API调用，现在使用模拟数据
    const mockTranslations = {
      '你好': 'Hello',
      '我爱学习': 'I love studying',
      '今天天气很好': 'The weather is very nice today',
      '我喜欢编程': 'I like programming',
      '这是一个测试': 'This is a test',
      '中国是一个美丽的国家': 'China is a beautiful country',
      '学习英语很重要': 'Learning English is important',
      '我每天都锻炼身体': 'I exercise every day',
      '他是我的好朋友': 'He is my good friend',
      '她喜欢听音乐': 'She likes listening to music'
    };
    
    // 尝试直接匹配，如果没有则使用默认回复
    translationResult.value = mockTranslations[chineseText.value.trim()] || 
      `This is a translation of "${chineseText.value}". In a real application, this would be generated by an API call.`;
    
    // 处理练习模式的单词
    processTranslationForPractice();
    
    // 添加到历史记录
    addToHistory(chineseText.value, translationResult.value);
    
    ElMessage.success('翻译成功');
  } catch (error) {
    console.error('翻译出错:', error);
    ElMessage.error('翻译失败，请稍后重试');
  } finally {
    translating.value = false;
  }
}

// 处理翻译结果，为练习模式做准备
function processTranslationForPractice() {
  if (!translationResult.value) return;
  
  const words = translationResult.value.split(/\s+/);
  const processed = [];
  
  // 重置用户答案
  Object.keys(userAnswers).forEach(key => delete userAnswers[key]);
  Object.keys(realtimeCorrectAnswers).forEach(key => delete realtimeCorrectAnswers[key]);
  
  words.forEach((word, index) => {
    // 移除标点符号进行比较
    const cleanWord = word.replace(/[.,!?;:'"()\[\]{}]/g, '');
    
    // 对于长度大于3的单词，有30%的概率会被隐藏
    const shouldHide = cleanWord.length > 3 && Math.random() < 0.3;
    
    processed.push({
      text: word,
      hidden: shouldHide,
      index
    });
  });
  
  processedWords.value = processed;
}

// 实时检查单词是否正确（练习模式）
function checkRealtimeCorrectness(index) {
  if (!processedWords.value[index] || !processedWords.value[index].hidden) return;
  
  const userAnswer = (userAnswers[index] || '').trim().replace(/[.,!?;:'"()\[\]{}]/g, '');
  const correctAnswer = processedWords.value[index].text.replace(/[.,!?;:'"()\[\]{}]/g, '');
  
  if (userAnswer.toLowerCase() === correctAnswer.toLowerCase()) {
    realtimeCorrectAnswers[index] = true;
  } else {
    realtimeCorrectAnswers[index] = false;
  }
}

// 判断单词是否实时正确（练习模式）
function isRealtimeCorrect(index) {
  return realtimeCorrectAnswers[index] === true;
}

// 检查用户答案
function checkAnswers() {
  let correctCount = 0;
  let totalHidden = 0;
  
  processedWords.value.forEach((word, index) => {
    if (word.hidden) {
      totalHidden++;
      
      // 清除标点符号进行比较
      const userAnswer = (userAnswers[index] || '').trim().replace(/[.,!?;:'"()\[\]{}]/g, '');
      const correctAnswer = word.text.replace(/[.,!?;:'"()\[\]{}]/g, '');
      
      if (userAnswer.toLowerCase() === correctAnswer.toLowerCase()) {
        correctCount++;
      }
    }
  });
  
  showAnswers.value = true;
  
  if (totalHidden === 0) {
    ElMessage.info('没有需要填写的单词');
    return;
  }
  
  const score = Math.round((correctCount / totalHidden) * 100);
  
  if (score === 100) {
    ElMessage.success(`太棒了！所有答案都正确！`);
  } else if (score >= 80) {
    ElMessage.success(`做得很好！正确率: ${score}%`);
  } else if (score >= 60) {
    ElMessage.warning(`还不错！正确率: ${score}%`);
  } else {
    ElMessage.error(`需要更多练习！正确率: ${score}%`);
  }
}

// 判断答案是否正确
function isAnswerCorrect(index) {
  if (!showAnswers.value || !processedWords.value[index]) return false;
  
  const userAnswer = (userAnswers[index] || '').trim().replace(/[.,!?;:'"()\[\]{}]/g, '');
  const correctAnswer = processedWords.value[index].text.replace(/[.,!?;:'"()\[\]{}]/g, '');
  
  return userAnswer.toLowerCase() === correctAnswer.toLowerCase();
}

// 判断答案是否错误
function isAnswerIncorrect(index) {
  if (!showAnswers.value || !processedWords.value[index]) return false;
  
  const userAnswer = (userAnswers[index] || '').trim();
  if (!userAnswer) return false;
  
  return !isAnswerCorrect(index);
}

// 重置答案
function resetAnswers() {
  Object.keys(userAnswers).forEach(key => delete userAnswers[key]);
  Object.keys(realtimeCorrectAnswers).forEach(key => delete realtimeCorrectAnswers[key]);
  showAnswers.value = false;
}

// 显示所有答案
function showAllAnswers() {
  showAnswers.value = true;
}

// 测试模式相关
function submitTest() {
  if (!userTranslation.value.trim()) {
    ElMessage.warning('请输入您的翻译');
    return;
  }
  
  // 这里应该是实际的API调用来评估翻译质量
  // 现在使用模拟数据
  
  // 简单的相似度评分（实际应用中应使用更复杂的算法）
  const similarity = calculateSimilarity(userTranslation.value, translationResult.value);
  testScore.value = Math.round(similarity * 100);
  
  // 生成反馈
  if (testScore.value >= 90) {
    testFeedback.value = "优秀的翻译！语法和用词都很准确。";
  } else if (testScore.value >= 80) {
    testFeedback.value = "很好的翻译！有一些小的改进空间，但整体意思表达清晰。";
  } else if (testScore.value >= 70) {
    testFeedback.value = "不错的翻译。有一些语法或用词可以改进，但基本意思表达出来了。";
  } else if (testScore.value >= 60) {
    testFeedback.value = "基本可以理解，但有一些重要的语法或用词问题需要注意。";
  } else {
    testFeedback.value = "需要更多练习。尝试使用更准确的词汇和语法结构。";
  }
  
  testSubmitted.value = true;
}

// 简单的相似度计算（实际应用中应使用更复杂的算法）
function calculateSimilarity(str1, str2) {
  const words1 = str1.toLowerCase().split(/\s+/);
  const words2 = str2.toLowerCase().split(/\s+/);
  
  const commonWords = words1.filter(word => 
    words2.includes(word.replace(/[.,!?;:'"()\[\]{}]/g, ''))
  );
  
  return commonWords.length / Math.max(words1.length, words2.length);
}

// 重置测试
function resetTest() {
  userTranslation.value = '';
  testSubmitted.value = false;
  testScore.value = 0;
  testFeedback.value = '';
}

// 英文练习模式
async function generateChinesePrompt() {
  generating.value = true;
  sentenceAnswerChecked.value = false;
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // 这里应该是实际的API调用，现在使用模拟数据
    const prompts = [
      '我每天早上跑步锻炼身体',
      '学习英语需要坚持不懈的努力',
      '这本书非常有趣，我很喜欢',
      '周末我计划去公园野餐',
      '我的朋友明天将从北京回来',
      '这家餐厅的食物很美味',
      '我们需要保护环境，减少污染',
      '音乐能让人心情愉快',
      '使用电脑工作提高了效率',
      '旅行是了解不同文化的好方式'
    ];
    
    // 随机选择一个提示
    let randomIndex = Math.floor(Math.random() * prompts.length);
    let selectedPrompt = prompts[randomIndex];
    
    // 检查是否最近已经使用过这个句子，如果是则选择另一个
    const maxAttempts = 10; // 最大尝试次数，防止无限循环
    let attempts = 0;
    
    while (practiceHistory.value.some(item => item.chinese === selectedPrompt) && attempts < maxAttempts) {
      randomIndex = Math.floor(Math.random() * prompts.length);
      selectedPrompt = prompts[randomIndex];
      attempts++;
    }
    
    // 如果尝试了最大次数还是有重复，则生成一个新句子
    if (attempts >= maxAttempts) {
      const customPrompts = [
        '科技发展改变了人们的生活方式',
        '健康饮食对身体有很多好处',
        '阅读书籍可以增长知识',
        '运动是保持健康的重要方式',
        '良好的沟通技巧很重要',
        '坚持努力最终会取得成功',
        '互联网连接了世界各地的人们',
        '学习新技能有助于个人成长',
        '尊重他人是社会和谐的基础',
        '早睡早起使人保持精力充沛'
      ];
      
      randomIndex = Math.floor(Math.random() * customPrompts.length);
      selectedPrompt = customPrompts[randomIndex];
      
      // 再次检查，极小概率下如果还重复，就添加时间戳使其唯一
      if (practiceHistory.value.some(item => item.chinese === selectedPrompt)) {
        selectedPrompt += ` (${new Date().toLocaleTimeString()})`;
      }
    }
    
    chinesePrompt.value = selectedPrompt;
    
    // 模拟英文参考答案
    const references = {
      '我每天早上跑步锻炼身体': 'I go running every morning to exercise my body',
      '学习英语需要坚持不懈的努力': 'Learning English requires persistent effort',
      '这本书非常有趣，我很喜欢': 'This book is very interesting, I like it a lot',
      '周末我计划去公园野餐': 'I plan to have a picnic in the park this weekend',
      '我的朋友明天将从北京回来': 'My friend will return from Beijing tomorrow',
      '这家餐厅的食物很美味': 'The food at this restaurant is delicious',
      '我们需要保护环境，减少污染': 'We need to protect the environment and reduce pollution',
      '音乐能让人心情愉快': 'Music can make people happy',
      '使用电脑工作提高了效率': 'Using computers for work has improved efficiency',
      '旅行是了解不同文化的好方式': 'Traveling is a good way to understand different cultures',
      // 添加新句子对应的翻译
      '科技发展改变了人们的生活方式': 'Technological development has changed people\'s lifestyles',
      '健康饮食对身体有很多好处': 'A healthy diet has many benefits for the body',
      '阅读书籍可以增长知识': 'Reading books can increase knowledge',
      '运动是保持健康的重要方式': 'Exercise is an important way to maintain health',
      '良好的沟通技巧很重要': 'Good communication skills are important',
      '坚持努力最终会取得成功': 'Persistent effort will eventually lead to success',
      '互联网连接了世界各地的人们': 'The Internet connects people around the world',
      '学习新技能有助于个人成长': 'Learning new skills contributes to personal growth',
      '尊重他人是社会和谐的基础': 'Respecting others is the foundation of social harmony',
      '早睡早起使人保持精力充沛': 'Going to bed early and getting up early keeps people energetic'
    };
    
    // 如果是带时间戳的自定义句子，创建一个默认翻译
    if (!references[chinesePrompt.value]) {
      const baseSentence = chinesePrompt.value.split(' (')[0]; // 移除时间戳部分
      englishReference.value = references[baseSentence] || 
        `This is a translation of "${baseSentence}". Please try your best to translate it.`;
    } else {
      englishReference.value = references[chinesePrompt.value];
    }
    
    // 处理句子，随机隐藏一些单词
    processSentenceForPractice();
    
    // 添加到练习历史记录
    addToPracticeHistory(chinesePrompt.value, englishReference.value);
    
    ElMessage.success('已生成新的练习题');
  } catch (error) {
    console.error('生成练习题出错:', error);
    ElMessage.error('生成练习题失败，请稍后重试');
  } finally {
    generating.value = false;
  }
}

// 处理句子，为练习做准备
function processSentenceForPractice() {
  if (!englishReference.value) return;
  
  const words = englishReference.value.split(/\s+/);
  const processed = [];
  
  // 重置用户答案和提示
  Object.keys(sentenceAnswers).forEach(key => delete sentenceAnswers[key]);
  Object.keys(wordHints).forEach(key => delete wordHints[key]);
  Object.keys(inputCorrectWords).forEach(key => delete inputCorrectWords[key]);
  
  words.forEach((word, index) => {
    // 移除标点符号进行比较
    const cleanWord = word.replace(/[.,!?;:'"()\[\]{}]/g, '');
    
    // 对于长度大于3的单词，有50%的概率会被隐藏
    const shouldHide = cleanWord.length > 3 && Math.random() < 0.5;
    
    processed.push({
      text: word,
      hidden: shouldHide,
      index
    });
  });
  
  sentenceWords.value = processed;
}

// 实时检查输入单词是否正确
function checkInputCorrectness(index) {
  if (!sentenceWords.value[index] || !sentenceWords.value[index].hidden) return;
  
  const userAnswer = (sentenceAnswers[index] || '').trim().replace(/[.,!?;:'"()\[\]{}]/g, '');
  const correctAnswer = sentenceWords.value[index].text.replace(/[.,!?;:'"()\[\]{}]/g, '');
  
  if (userAnswer.toLowerCase() === correctAnswer.toLowerCase()) {
    inputCorrectWords[index] = true;
  } else {
    inputCorrectWords[index] = false;
  }
}

// 判断输入是否正确
function isInputCorrect(index) {
  return inputCorrectWords[index] === true;
}

// 检查句子答案
function checkSentenceAnswer() {
  let correctCount = 0;
  let totalHidden = 0;
  
  sentenceWords.value.forEach((word, index) => {
    if (word.hidden) {
      totalHidden++;
      
      // 清除标点符号进行比较
      const userAnswer = (sentenceAnswers[index] || '').trim().replace(/[.,!?;:'"()\[\]{}]/g, '');
      const correctAnswer = word.text.replace(/[.,!?;:'"()\[\]{}]/g, '');
      
      if (userAnswer.toLowerCase() === correctAnswer.toLowerCase()) {
        correctCount++;
      }
    }
  });
  
  sentenceAnswerChecked.value = true;
  
  if (totalHidden === 0) {
    ElMessage.info('没有需要填写的单词');
    return;
  }
  
  const score = Math.round((correctCount / totalHidden) * 100);
  
  if (score === 100) {
    ElMessage.success(`太棒了！所有答案都正确！`);
  } else if (score >= 80) {
    ElMessage.success(`做得很好！正确率: ${score}%`);
  } else if (score >= 60) {
    ElMessage.warning(`还不错！正确率: ${score}%`);
  } else {
    ElMessage.error(`需要更多练习！正确率: ${score}%`);
  }
}

// 判断单词是否正确
function isWordCorrect(index) {
  if (!sentenceAnswerChecked.value || !sentenceWords.value[index]) return false;
  
  const userAnswer = (sentenceAnswers[index] || '').trim().replace(/[.,!?;:'"()\[\]{}]/g, '');
  const correctAnswer = sentenceWords.value[index].text.replace(/[.,!?;:'"()\[\]{}]/g, '');
  
  return userAnswer.toLowerCase() === correctAnswer.toLowerCase();
}

// 判断单词是否错误
function isWordIncorrect(index) {
  if (!sentenceAnswerChecked.value || !sentenceWords.value[index]) return false;
  
  const userAnswer = (sentenceAnswers[index] || '').trim();
  if (!userAnswer) return false;
  
  return !isWordCorrect(index);
}

// 下一个句子
function nextSentence() {
  sentenceAnswerChecked.value = false;
  generateChinesePrompt();
}

// 获取提示
function getHint(index) {
  if (!sentenceWords.value[index] || !sentenceWords.value[index].hidden) return;
  
  const word = sentenceWords.value[index].text;
  
  if (!wordHints[index]) {
    wordHints[index] = 1;
  } else if (wordHints[index] < 3) {
    wordHints[index]++;
  }
  
  const hintLevel = wordHints[index];
  let hint = '';
  
  if (hintLevel === 1) {
    // 第一级提示：显示第一个字母
    hint = word.charAt(0) + '...';
  } else if (hintLevel === 2) {
    // 第二级提示：显示一半
    const halfLength = Math.ceil(word.length / 2);
    hint = word.substring(0, halfLength) + '...';
  } else {
    // 第三级提示：显示除了最后一个字母外的所有字母
    hint = word.substring(0, word.length - 1) + '?';
  }
  
  return hint;
}

// 获取完整提示
function getFullHint(index) {
  if (!sentenceWords.value[index] || !sentenceWords.value[index].hidden) return '';
  
  const word = sentenceWords.value[index].text;
  const hintLevel = wordHints[index] || 0;
  
  if (hintLevel === 0) {
    return '点击获取提示';
  } else if (hintLevel === 1) {
    return `提示: 单词以 "${word.charAt(0)}" 开头`;
  } else if (hintLevel === 2) {
    const halfLength = Math.ceil(word.length / 2);
    return `提示: ${word.substring(0, halfLength)}...`;
  } else {
    return `提示: ${word.substring(0, word.length - 1)}?`;
  }
}

// 历史记录相关
function addToHistory(chinese, english) {
  translationHistory.value.unshift({
    chinese,
    english,
    timestamp: new Date()
  });
  
  // 限制历史记录数量
  if (translationHistory.value.length > 10) {
    translationHistory.value.pop();
  }
  
  // 保存到本地存储
  saveHistoryToLocalStorage();
}

// 添加到练习历史记录
function addToPracticeHistory(chinese, english) {
  practiceHistory.value.unshift({
    chinese,
    english,
    timestamp: new Date()
  });
  
  // 限制练习历史记录数量
  if (practiceHistory.value.length > 20) {
    practiceHistory.value.pop();
  }
  
  // 保存到本地存储
  saveHistoryToLocalStorage();
}

function loadFromHistory(item) {
  chineseText.value = item.chinese;
  translationResult.value = item.english;
  processTranslationForPractice();
}

function clearHistory() {
  ElMessageBox.confirm('确定要清空所有历史记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    translationHistory.value = [];
    // 同时清空本地存储
    localStorage.removeItem('translationHistory');
    ElMessage.success('历史记录已清空');
  }).catch(() => {});
}

// 格式化时间
function formatTime(date) {
  if (!(date instanceof Date)) {
    date = new Date(date);
  }
  
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// 截断文本
function truncateText(text, maxLength) {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

// 复制翻译结果
function copyTranslation() {
  if (!translationResult.value) return;
  
  navigator.clipboard.writeText(translationResult.value)
    .then(() => {
      ElMessage.success('已复制到剪贴板');
    })
    .catch(() => {
      ElMessage.error('复制失败');
    });
}

// 朗读英文
function playAudio() {
  if (!translationResult.value || !canPlayAudio.value) return;
  
  const utterance = new SpeechSynthesisUtterance(translationResult.value);
  utterance.lang = 'en-US';
  
  window.speechSynthesis.speak(utterance);
}
</script>

<style scoped>
.english-translator {
  margin: 0 auto;
  max-width: 900px;
}

.translator-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.translator-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: bold;
}

.mode-selection {
  display: flex;
  align-items: center;
  gap: 10px;
}

.help-icon {
  cursor: pointer;
  color: #909399;
}

.practice-mode, .test-mode {
  margin-top: 15px;
  padding: 15px;
  border-radius: 5px;
  background-color: #f9f9f9;
}

.practice-sentence {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 15px;
  line-height: 1.8;
}

.visible-word {
  margin-right: 5px;
}

.blank-word {
  position: relative;
  display: inline-block;
  margin-right: 5px;
}

.word-input {
  width: 80px;
  background: transparent;
  border: none;
  outline: none;
  text-align: center;
  padding: 0 0 2px 0;
}

.underline {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #409EFF;
}

.word-input.correct {
  color: #67C23A;
}

.word-input.incorrect {
  color: #F56C6C;
}

.correct-answer {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  font-size: 12px;
  color: #67C23A;
  text-align: center;
}

.practice-controls, .test-controls {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.test-result {
  margin-top: 20px;
  padding: 15px;
  border-radius: 5px;
  background-color: #f5f7fa;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: bold;
}

.comparison {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 15px;
}

.your-answer, .correct-answer, .feedback {
  padding: 10px;
  border-radius: 4px;
  background-color: white;
}

.your-answer h4, .correct-answer h4, .feedback h4 {
  margin-top: 0;
  margin-bottom: 5px;
  color: #606266;
}

.history-section {
  margin-top: 20px;
}

.history-item-title {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.history-time {
  font-size: 12px;
  color: #909399;
}

.history-content {
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.history-chinese, .history-english {
  margin-bottom: 10px;
}

.history-chinese h4, .history-english h4 {
  margin-top: 0;
  margin-bottom: 5px;
  color: #606266;
}

.history-actions {
  display: flex;
  justify-content: flex-end;
}

.reverse-mode {
  margin-top: 15px;
  padding: 15px;
  border-radius: 5px;
  background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.reverse-prompt {
  padding: 20px;
  border-radius: 5px;
  background-color: #0d0d0d;
  color: white;
}

.dark-title {
  color: white;
  font-weight: bold;
}

.chinese-prompt {
  font-size: 1.5rem;
  margin-top: 10px;
  padding: 20px;
  text-align: center;
}

.sentence-practice {
  margin-top: 15px;
  padding: 20px;
  border-radius: 5px;
  background-color: #0d0d0d;
  color: white;
}

.sentence-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.sentence-words {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
  margin-bottom: 30px;
  line-height: 2;
  font-size: 24px;
}

.word-container {
  position: relative;
  display: inline-block;
  margin: 0 5px;
}

.sentence-input {
  width: 100px;
  background: transparent;
  border: none;
  outline: none;
  text-align: center;
  padding: 0 0 5px 0;
  font-size: 24px;
  color: white;
}

.visible-sentence-word {
  margin-right: 5px;
  font-size: 24px;
}

.sentence-underline {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: white;
}

.sentence-input.correct-word {
  color: #67C23A;
}

.sentence-input.incorrect-word {
  color: #F56C6C;
}

.word-correction {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  font-size: 16px;
  color: #F56C6C;
  text-align: center;
}

.word-hint {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.hint-button {
  padding: 2px 5px;
  font-size: 12px;
  color: #909399;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

.hint-button:hover {
  color: #409EFF;
  background-color: rgba(255, 255, 255, 0.3);
}

.sentence-controls {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

.word-space {
  width: 8px;
}

.reverse-input {
  background-color: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.reverse-result {
  background-color: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.reverse-controls {
  margin-top: 15px;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .comparison {
    flex-direction: column;
  }
  
  .your-answer, .correct-answer {
    width: 100%;
  }
}
</style> 