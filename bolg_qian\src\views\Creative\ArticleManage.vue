<template>
  <div style="width: 80%; margin: 50px auto;">
    <el-table :data="pagedArticles">
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="title" label="标题" />
      <el-table-column prop="updated_at" label="删除时间" width="180">
        <template #default="scope">
          {{ formatTime(scope.row.updated_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="220">
        <template #default="scope">
          <el-button type="success" @click="restoreArticle(scope.row.id)">恢复</el-button>
          <el-button type="danger" @click="permanentDeleteArticle(scope.row.id)"
            style="margin-left: 8px">永久删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="margin: 16px 0; text-align: right;">
      <el-pagination background layout="prev, pager, next, jumper" :total="deletedArticles.length" :page-size="pageSize"
        :current-page="currentPage" @current-change="handlePageChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { RestoreArticleApi, DeleteRestoredArticleApi, DeleteArticleApi } from "../../utils/api";

const deletedArticles = ref<any[]>([]);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const pagedArticles = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  return deletedArticles.value.slice(start, start + pageSize.value);
});
const handlePageChange = (page: number) => {
  currentPage.value = page;
};

function formatTime(ts: string | number) {
  if (!ts) return "";
  const date = new Date(ts);
  return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, "0")}`;
}

async function fetchDeletedArticles(user_id: string) {
  const res = await DeleteRestoredArticleApi({ user_id });
  console.log("已删除文章列表", res);
  if (res?.data) {
    deletedArticles.value = res.data;
    currentPage.value = 1;
  }
}

async function restoreArticle(id: number) {
  const res = await RestoreArticleApi({ id });
  if (res?.message === "文章恢复成功") {
    ElMessage.success("恢复成功");
    fetchDeletedArticles(localStorage.getItem("id")!);
  } else {
    ElMessage.error("恢复失败");
  }
}

async function permanentDeleteArticle(id: number) {
  try {
    await ElMessageBox.confirm("确定永久删除该文章吗?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    const res = await DeleteArticleApi({ id });
    if (res?.code === 200) {
      ElMessage.success("永久删除成功");
      fetchDeletedArticles(localStorage.getItem("id")!);
    } else {
      ElMessage.error("永久删除失败");
    }
  } catch {
    ElMessage.info("已取消永久删除");
  }
}

function refreshList() {
  fetchDeletedArticles(localStorage.getItem("id")!);
}

// 页面加载时获取已删除文章
fetchDeletedArticles(localStorage.getItem("id")!);
</script>
<style scoped lang="less">
/* 可根据需要自定义样式 */
</style>