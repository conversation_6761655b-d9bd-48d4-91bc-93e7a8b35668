// JavaScript 测试文件
console.log('开始文本预览测试');

// 定义测试函数
function testTextPreview() {
  const features = [
    '支持多种文本格式',
    '语法高亮显示', 
    '复制功能',
    '全屏预览'
  ];
  
  features.forEach((feature, index) => {
    console.log(`${index + 1}. ${feature}`);
  });
}

// 执行测试
testTextPreview();

// 异步函数示例
async function fetchData() {
  try {
    const response = await fetch('/api/data');
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('获取数据失败:', error);
  }
}

console.log('文本预览测试完成');
