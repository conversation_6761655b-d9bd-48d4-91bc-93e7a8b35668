const Router = require("koa-router");
const articles = new Router();
const bodyParser = require("koa-bodyparser");
const article = require("../utils/sql/articleService");
const { handleResponse } = require("./responseHandler");
const path = require("path");
const fs = require("fs");

articles.use(bodyParser());

// 获取文章列表
articles.post("/pages", async (ctx) => {
  const { user_id } = ctx.request.body;
  const result = await article.getArticleById(user_id);
  if (result) {
    return handleResponse(ctx, 200, {
      data: result,
    });
  }
});

// 分页获取文章列表
articles.post("/pagesNumber", async (ctx) => {
  const { user_id, page, limit } = ctx.request.body;
  // 参数验证
  if (isNaN(page) || isNaN(limit) || page <= 0 || limit <= 0) {
    return handleResponse(ctx, 400, {
      message: "Invalid page or limit value",
    });
  }

  const result = await article.getArticleByPage({ user_id, page, limit });
  if (result) {
    return handleResponse(ctx, 200, {
      data: result,
    });
  } else {
    return handleResponse(ctx, 400, {
      message: "获取文章列表失败",
    });
  }
});

articles.post("/add", async (ctx) => {
  const { file } = ctx.request.files;
  const { user_id, title, tags, category, content, cover_image } =
    ctx.request.body;

  if (!file || !user_id || !title || !tags || !category || !content) {
    return handleResponse(ctx, 400, { error: "缺少必要参数" });
  }

  // 上传文件处理
  const uploadDir = path.join(__dirname, "../uploads/articles");
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  const reader = fs.createReadStream(file.path);
  const avatarFilename = file.name;
  const avatarPath = path.join(uploadDir, avatarFilename);
  const stream = fs.createWriteStream(path.join(uploadDir, avatarFilename));
  reader.pipe(stream);

  // 保存文章
  const summary = content.slice(0, 100);
  const result = await article.addArticle(
    user_id,
    title,
    summary,
    tags,
    category,
    content,
    cover_image
  );

  if (result) {
    return handleResponse(ctx, 200, {
      message: "文章发布成功",
    });
  }

  return handleResponse(ctx, 500, {
    message: "文章发布失败",
  });
});

// 更新文章
articles.post("/update", async (ctx) => {
  const { id, user_id, title, summary, content, cover_image } =
    ctx.request.body;
  const result = await article.updateArticle(
    id,
    user_id,
    title,
    summary,
    content,
    cover_image
  );
  if (result) {
    return handleResponse(ctx, 200, {
      message: "文章更新成功",
    });
  }
});

// 真删除文章
articles.post("/delete", async (ctx) => {
  const { id } = ctx.request.body;
  const result = await article.deleteArticle(id);
  if (result) {
    return handleResponse(ctx, 200, {
      message: "文章删除成功",
    });
  }
});

// 假删除文章 将文章的is_delete字段设置为1 获取文章参数id
articles.post("/fakeDelete", async (ctx) => {
  const { id } = ctx.request.body;

  const result = await article.fakeDeleteArticle(id);
  if (result) {
    return handleResponse(ctx, 200, {
      message: "文章假删除成功",
    });
  }
});

// 搜索文章
articles.post("/search", async (ctx) => {
  const { keyword } = ctx.request.body;
  const result = await article.searchArticle(keyword);
  if (result) {
    return handleResponse(ctx, 200, {
      data: result,
    });
  }
});

// 获取文章的评论
articles.post("/comments", async (ctx) => {
  const { article_id } = ctx.request.body;

  const result = await article.commentArticle(article_id);
  if (result) {
    return handleResponse(ctx, 200, {
      data: result,
    });
  }
});

// 添加评论 addComment(username,article_id, content,parent_id)
articles.post("/addComment", async (ctx) => {
  const { username, article_id, content, parent_id } = ctx.request.body;
  const result = await article.addComment(
    username,
    article_id,
    content,
    parent_id
  );
  if (result) {
    return handleResponse(ctx, 200, {
      message: "评论添加成功",
    });
  }
});
//  文章详情 文章id
articles.post("/detail", async (ctx) => {
  const { id } = ctx.request.body;
  const result = await article.getArticleById(id);
  if (result) {
    return handleResponse(ctx, 200, {
      data: result,
    });
  }
});

// 设置文章共享状态
articles.post("/share", async (ctx) => {
  const { id, is_share } = ctx.request.body;
  if (typeof id === "undefined" || typeof is_share === "undefined") {
    return handleResponse(ctx, 400, { message: "缺少必要参数" });
  }
  const result = await article.setArticleShare(id, is_share);
  if (result) {
    return handleResponse(ctx, 200, {
      message: "共享状态更新成功",
    });
  } else {
    return handleResponse(ctx, 500, {
      message: "共享状态更新失败",
    });
  }
});

// 获取所有共享文章
articles.get("/shared", async (ctx) => {
  const result = await article.getSharedArticles();
  if (result) {
    return handleResponse(ctx, 200, {
      data: result,
    });
  } else {
    return handleResponse(ctx, 500, {
      message: "获取共享文章失败",
    });
  }
});

// 恢复文章
articles.post("/restore", async (ctx) => {
  const { id } = ctx.request.body;
  if (!id) {
    return handleResponse(ctx, 400, { message: "缺少文章ID" });
  }
  const result = await article.restoreArticle(id);
  if (result) {
    return handleResponse(ctx, 200, {
      message: "文章恢复成功",
    });
  } else {
    return handleResponse(ctx, 500, {
      message: "文章恢复失败",
    });
  }
});
// 获取用户删除的文章
articles.post("/deletedByUser", async (ctx) => {
  const { user_id } = ctx.request.body;
  if (!user_id) {
    return handleResponse(ctx, 400, { message: "缺少用户ID" });
  }
  const result = await article.getDeletedArticlesByUserId(user_id);
  if (result) {
    return handleResponse(ctx, 200, {
      data: result,
    });
  } else {
    return handleResponse(ctx, 500, {
      message: "获取用户删除的文章失败",
    });
  }
});

module.exports = articles;
