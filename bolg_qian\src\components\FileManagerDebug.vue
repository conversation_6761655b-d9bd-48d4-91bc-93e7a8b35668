<template>
  <div class="file-manager-debug">
    <el-card>
      <template #header>
        <h3>文件管理器调试</h3>
      </template>
      
      <div class="debug-section">
        <h4>1. API测试</h4>
        <el-button @click="testApi" type="primary">测试API</el-button>
        <div v-if="apiResponse" class="debug-info">
          <h5>API响应:</h5>
          <pre>{{ JSON.stringify(apiResponse, null, 2) }}</pre>
        </div>
      </div>

      <div class="debug-section">
        <h4>2. 数据状态</h4>
        <p><strong>当前分类:</strong> {{ currentCategory }}</p>
        <p><strong>文件结构:</strong> {{ Object.keys(fileStructure) }}</p>
        <p><strong>树形数据:</strong> {{ treeData.length }} 项</p>
        <p><strong>当前文件:</strong> {{ currentFiles.length }} 个</p>
      </div>

      <div class="debug-section">
        <h4>3. 分类切换</h4>
        <el-select v-model="currentCategory" @change="onCategoryChange">
          <el-option label="图片" value="images" />
          <el-option label="文档" value="documents" />
          <el-option label="视频" value="videos" />
        </el-select>
      </div>

      <div class="debug-section">
        <h4>4. 文件列表</h4>
        <div v-if="currentFiles.length > 0">
          <div v-for="file in currentFiles" :key="file.path" class="file-item">
            <span>{{ file.name }} ({{ file.type }})</span>
          </div>
        </div>
        <div v-else>
          <p>没有文件数据</p>
        </div>
      </div>

      <div class="debug-section">
        <h4>5. 原始数据</h4>
        <el-collapse>
          <el-collapse-item title="查看原始fileStructure数据">
            <pre>{{ JSON.stringify(fileStructure, null, 2) }}</pre>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getFileStructureApi } from '../utils/fileManagerApi';

// 响应式数据
const currentCategory = ref('images');
const fileStructure = ref<any>({});
const apiResponse = ref<any>(null);

// 计算属性
const treeData = computed(() => {
  const structure = fileStructure.value[currentCategory.value];
  return structure ? [structure] : [];
});

const currentFiles = computed(() => {
  const structure = fileStructure.value[currentCategory.value];
  if (!structure || !structure.children) return [];
  return structure.children;
});

// 方法
const testApi = async () => {
  try {
    console.log('开始测试API...');
    const response = await getFileStructureApi(currentCategory.value);
    console.log('API响应:', response);
    
    apiResponse.value = response;
    
    // 解析数据
    if (response && response.data) {
      fileStructure.value = response.data;
      ElMessage.success('API测试成功');
    } else {
      ElMessage.warning('API响应格式异常');
    }
  } catch (error) {
    console.error('API测试失败:', error);
    ElMessage.error('API测试失败');
    apiResponse.value = { error: error.message };
  }
};

const onCategoryChange = async () => {
  console.log('分类切换到:', currentCategory.value);
  await testApi();
};

// 生命周期
onMounted(() => {
  testApi();
});
</script>

<style scoped>
.file-manager-debug {
  padding: 20px;
}

.debug-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.debug-section h4 {
  margin-top: 0;
  color: #303133;
}

.debug-info {
  margin-top: 12px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.debug-info pre {
  margin: 0;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.file-item {
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
}

.file-item:last-child {
  border-bottom: none;
}
</style>
