<template>
  <div class="speed-limit-config">
    <el-card class="config-card">
      <div slot="header" class="card-header">
        <span class="card-title">
          <i class="el-icon-lightning"></i>
          下载限速等级配置
        </span>
        <el-button 
          type="primary" 
          size="small" 
          @click="refreshConfigs"
          :loading="loading"
        >
          <i class="el-icon-refresh"></i>
          刷新
        </el-button>
      </div>

      <!-- 配置列表 -->
      <div class="config-list" v-loading="loading">
        <div 
          v-for="config in speedConfigs" 
          :key="config.level"
          class="config-item"
          :class="getConfigClass(config.level)"
        >
          <div class="config-header">
            <div class="level-info">
              <el-tag 
                :type="getLevelTagType(config.level)"
                size="medium"
                class="level-tag"
              >
                {{ config.description }}
              </el-tag>
              <span class="level-name">{{ config.level.toUpperCase() }}</span>
            </div>
            <div class="config-actions">
              <el-button 
                type="text" 
                size="small"
                @click="showConfigDetails(config)"
              >
                <i class="el-icon-view"></i>
                详情
              </el-button>
            </div>
          </div>

          <div class="config-content">
            <div class="config-row">
              <div class="config-item-detail">
                <div class="detail-label">
                  <i class="el-icon-download"></i>
                  下载速度
                </div>
                <div class="detail-value">
                  {{ formatSpeed(config.downloadSpeed) }}
                </div>
              </div>
              <div class="config-item-detail">
                <div class="detail-label">
                  <i class="el-icon-upload2"></i>
                  上传速度
                </div>
                <div class="detail-value">
                  {{ formatSpeed(config.uploadSpeed) }}
                </div>
              </div>
            </div>

            <div class="config-row">
              <div class="config-item-detail">
                <div class="detail-label">
                  <i class="el-icon-connection"></i>
                  并发下载
                </div>
                <div class="detail-value">
                  {{ config.concurrentDownloads }} 个
                </div>
              </div>
              <div class="config-item-detail">
                <div class="detail-label">
                  <i class="el-icon-date"></i>
                  每日限额
                </div>
                <div class="detail-value">
                  {{ formatFileSize(config.dailyDownloadLimit) }}
                </div>
              </div>
            </div>

            <div class="features-section">
              <div class="features-label">功能特权：</div>
              <div class="features-list">
                <el-tag 
                  v-for="feature in config.features" 
                  :key="feature"
                  size="mini"
                  class="feature-tag"
                >
                  {{ feature }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && speedConfigs.length === 0" class="empty-state">
        <i class="el-icon-warning-outline"></i>
        <p>暂无限速配置数据</p>
      </div>
    </el-card>

    <!-- 配置详情对话框 -->
    <el-dialog
      title="限速配置详情"
      :visible.sync="detailDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedConfig" class="config-detail">
        <div class="detail-header">
          <el-tag 
            :type="getLevelTagType(selectedConfig.level)"
            size="large"
          >
            {{ selectedConfig.description }}
          </el-tag>
          <span class="detail-level">{{ selectedConfig.level.toUpperCase() }}</span>
        </div>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="下载速度">
            <span class="speed-value">{{ formatSpeed(selectedConfig.downloadSpeed) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="上传速度">
            <span class="speed-value">{{ formatSpeed(selectedConfig.uploadSpeed) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="并发下载">
            {{ selectedConfig.concurrentDownloads }} 个
          </el-descriptions-item>
          <el-descriptions-item label="并发上传">
            {{ selectedConfig.concurrentUploads }} 个
          </el-descriptions-item>
          <el-descriptions-item label="每日下载限额">
            {{ formatFileSize(selectedConfig.dailyDownloadLimit) }}
          </el-descriptions-item>
          <el-descriptions-item label="每日上传限额">
            {{ formatFileSize(selectedConfig.dailyUploadLimit) }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="features-detail">
          <h4>功能特权</h4>
          <ul class="features-list-detail">
            <li v-for="feature in selectedConfig.features" :key="feature">
              <i class="el-icon-check"></i>
              {{ feature }}
            </li>
          </ul>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fileManagementApi, fileUtils } from '@/utils/fileManagementApi'

export default {
  name: 'SpeedLimitConfig',
  data() {
    return {
      loading: false,
      speedConfigs: [],
      detailDialogVisible: false,
      selectedConfig: null
    }
  },
  mounted() {
    this.loadSpeedConfigs()
  },
  methods: {
    // 加载限速配置
    async loadSpeedConfigs() {
      this.loading = true
      try {
        const response = await fileManagementApi.getSpeedLimitConfigs()
        if (response.code === 200) {
          this.speedConfigs = response.data || []
        } else {
          this.$message.error(response.message || '获取限速配置失败')
        }
      } catch (error) {
        console.error('获取限速配置失败:', error)
        this.$message.error('获取限速配置失败')
      } finally {
        this.loading = false
      }
    },

    // 刷新配置
    refreshConfigs() {
      this.loadSpeedConfigs()
    },

    // 显示配置详情
    showConfigDetails(config) {
      this.selectedConfig = config
      this.detailDialogVisible = true
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      return fileUtils.formatFileSize(bytes)
    },

    // 格式化速度
    formatSpeed(bytesPerSecond) {
      return fileUtils.formatSpeed(bytesPerSecond)
    },

    // 获取等级标签类型
    getLevelTagType(level) {
      const typeMap = {
        free: 'info',
        basic: 'success',
        premium: 'warning',
        vip: 'danger',
        unlimited: ''
      }
      return typeMap[level] || 'info'
    },

    // 获取配置项样式类
    getConfigClass(level) {
      return `config-${level}`
    }
  }
}
</script>

<style scoped>
.speed-limit-config {
  height: 100%;
}

.config-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-title i {
  margin-right: 8px;
  color: #409EFF;
}

.config-list {
  max-height: 600px;
  overflow-y: auto;
}

.config-item {
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 16px;
  transition: all 0.3s ease;
}

.config-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.config-free {
  border-left: 4px solid #909399;
}

.config-basic {
  border-left: 4px solid #67C23A;
}

.config-premium {
  border-left: 4px solid #E6A23C;
}

.config-vip {
  border-left: 4px solid #F56C6C;
}

.config-unlimited {
  border-left: 4px solid #722ED1;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.level-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.level-tag {
  font-weight: 600;
}

.level-name {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.config-content {
  space-y: 12px;
}

.config-row {
  display: flex;
  gap: 24px;
  margin-bottom: 12px;
}

.config-item-detail {
  flex: 1;
}

.detail-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.detail-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.features-section {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #F2F6FC;
}

.features-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.features-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.feature-tag {
  background-color: #F4F4F5;
  border-color: #E9E9EB;
  color: #606266;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
}

/* 对话框样式 */
.config-detail {
  padding: 16px 0;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
}

.detail-level {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.speed-value {
  font-weight: 600;
  color: #409EFF;
}

.features-detail {
  margin-top: 24px;
}

.features-detail h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}

.features-list-detail {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features-list-detail li {
  padding: 6px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
}

.features-list-detail i {
  color: #67C23A;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}
</style>
