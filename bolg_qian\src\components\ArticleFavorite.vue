<template>
  <div class="article-favorite-container">
    <!-- 收藏按钮 -->
    <el-button
      :loading="loading"
      @click="toggleFavorite"
      class="favorite-btn"
      :class="{ 'is-favorited': isFavorited }"
      :size="size"
    >
      <span class="favorite-icon">
        <span v-if="isFavorited" class="heart-filled">❤️</span>
        <span v-else class="heart-outline">🤍</span>
      </span>
      <span v-if="showText" class="favorite-text">
        {{ isFavorited ? '已收藏' : '收藏' }}
      </span>
      <span v-if="showCount && favoritesCount > 0" class="favorite-count">
        {{ formatCount(favoritesCount) }}
      </span>
    </el-button>

    <!-- 收藏用户列表弹窗 -->
    <el-dialog
      v-model="showFavoriteUsers"
      title="收藏用户列表"
      width="500px"
      :before-close="closeFavoriteUsersDialog"
      v-if="showUsersList"
    >
      <div class="favorite-users-content">
        <div class="users-list">
          <div 
            v-for="user in favoriteUsers" 
            :key="user.id"
            class="user-item"
          >
            <el-avatar 
              :src="user.avatar" 
              :size="32"
              class="user-avatar"
            >
              {{ user.username }}
            </el-avatar>
            <div class="user-info">
              <div class="user-name">{{ user.username }}</div>
              <div class="favorite-time">{{ formatTime(user.created_at) }}</div>
            </div>
          </div>
          
          <div v-if="favoriteUsers.length === 0" class="empty-state">
            <el-empty description="暂无收藏用户" />
          </div>
        </div>

        <!-- 分页 -->
        <el-pagination
          v-if="pagination.total > 0"
          :current-page="pagination.page"
          :page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          @current-change="loadFavoriteUsers"
          @size-change="loadFavoriteUsers"
          class="pagination"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
// 不需要导入图标，使用CSS和Unicode字符
import request from '../utils/index'

export default {
  name: 'ArticleFavorite',
  components: {
    // 不需要注册图标组件
  },
  props: {
    articleId: {
      type: [Number, String],
      required: true
    },
    showText: {
      type: Boolean,
      default: true
    },
    showCount: {
      type: Boolean,
      default: true
    },
    showUsersList: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'default',
      validator: (value) => ['large', 'default', 'small'].includes(value)
    }
  },
  emits: ['favorite-changed'],
  setup(props, { emit }) {
    const loading = ref(false)
    const isFavorited = ref(false)
    const favoritesCount = ref(0)
    const showFavoriteUsers = ref(false)
    const favoriteUsers = ref([])
    
    const pagination = reactive({
      page: 1,
      limit: 20,
      total: 0
    })

    // 获取收藏统计
    const loadFavoriteStats = async () => {
      try {
        const response = await request.get(`/article-favorites/stats/${props.articleId}`)
        if (response.code === 200) {
          const data = response.data
          favoritesCount.value = data.favorites_count || 0
          isFavorited.value = data.isFavorited || false
        }
      } catch (error) {
        console.error('获取收藏统计失败:', error)
      }
    }

    // 切换收藏状态
    const toggleFavorite = async () => {
      console.log('toggleFavorite 被调用，articleId:', props.articleId)
      if (loading.value) return

      loading.value = true
      try {
        console.log('发送收藏请求...')
        const response = await request.post('/article-favorites/toggle', {
          article_id: props.articleId
        })
        console.log('收藏请求响应:', response)

        if (response.code === 200) {
          const { action, isFavorited: newFavoriteStatus, stats } = response.data

          // 更新状态
          isFavorited.value = newFavoriteStatus
          favoritesCount.value = stats.favorites_count || 0

          ElMessage.success(response.message)

          // 触发事件
          emit('favorite-changed', {
            action,
            isFavorited: isFavorited.value,
            stats: {
              favorites_count: favoritesCount.value
            }
          })
        } else {
          ElMessage.error(response.message || '操作失败')
        }
      } catch (error) {
        console.error('收藏操作失败:', error)
        ElMessage.error('操作失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 加载收藏用户列表
    const loadFavoriteUsers = async () => {
      if (!props.showUsersList) return

      try {
        const response = await request.get(`/article-favorites/users/${props.articleId}`, {
          params: {
            page: pagination.page,
            limit: pagination.limit
          }
        })

        if (response.code === 200) {
          const data = response.data
          favoriteUsers.value = data.users || []
          pagination.total = data.pagination?.total || 0
        }
      } catch (error) {
        console.error('获取收藏用户列表失败:', error)
        if (error.response?.status === 401) {
          ElMessage.warning('请先登录')
        }
      }
    }

    // 显示收藏用户列表
    const showFavoriteUsersList = () => {
      if (!props.showUsersList) return
      showFavoriteUsers.value = true
      loadFavoriteUsers()
    }

    // 关闭收藏用户列表弹窗
    const closeFavoriteUsersDialog = () => {
      showFavoriteUsers.value = false
      favoriteUsers.value = []
      pagination.page = 1
      pagination.total = 0
    }

    // 格式化数量显示
    const formatCount = (count) => {
      if (count < 1000) return count.toString()
      if (count < 10000) return (count / 1000).toFixed(1) + 'k'
      return (count / 10000).toFixed(1) + 'w'
    }

    // 格式化时间
    const formatTime = (time) => {
      if (!time) return ''
      const date = new Date(time)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) { // 1天内
        return `${Math.floor(diff / 3600000)}小时前`
      } else {
        return date.toLocaleDateString()
      }
    }

    // 监听文章ID变化
    watch(() => props.articleId, () => {
      if (props.articleId) {
        loadFavoriteStats()
      }
    }, { immediate: true })

    onMounted(() => {
      if (props.articleId) {
        loadFavoriteStats()
      }
    })

    return {
      loading,
      isFavorited,
      favoritesCount,
      showFavoriteUsers,
      favoriteUsers,
      pagination,
      toggleFavorite,
      showFavoriteUsersList,
      closeFavoriteUsersDialog,
      loadFavoriteUsers,
      formatCount,
      formatTime
    }
  }
}
</script>

<style scoped>
.article-favorite-container {
  display: inline-block;
}

.favorite-btn {
  min-width: 90px;
  min-height: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #e4e7ed;
  background: #ffffff;
  color: #606266;
  padding: 0 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.favorite-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #f56c6c;
  color: #f56c6c;
}

.favorite-btn.is-favorited {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border-color: #ff6b6b;
  color: white;
}

.favorite-btn.is-favorited:hover {
  background: linear-gradient(135deg, #ff5252, #e53935);
  border-color: #ff5252;
  color: white;
}

.favorite-icon {
  font-size: 16px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.heart-filled,
.heart-outline {
  font-size: 16px;
  transition: all 0.3s ease;
}

.heart-filled {
  animation: heartBeat 0.6s ease-in-out;
}

.favorite-text {
  font-size: 14px;
  font-weight: 500;
}

.favorite-count {
  font-size: 12px;
  opacity: 0.8;
  margin-left: 2px;
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.favorite-btn:hover .favorite-icon {
  transform: scale(1.1);
}

.favorite-users-content {
  max-height: 400px;
}

.users-list {
  max-height: 300px;
  overflow-y: auto;
}

.user-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.user-item:last-child {
  border-bottom: none;
}

.user-avatar {
  flex-shrink: 0;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.favorite-time {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.empty-state {
  text-align: center;
  padding: 20px;
}

.pagination {
  margin-top: 16px;
  text-align: center;
}

/* 动画效果 */
.favorite-btn {
  position: relative;
}

.favorite-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.favorite-btn:active::before {
  width: 100px;
  height: 100px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .favorite-btn {
    min-width: 80px;
    min-height: 32px;
    height: 32px;
    font-size: 12px;
    padding: 0 12px;
  }

  .favorite-icon {
    font-size: 14px;
  }

  .heart-filled,
  .heart-outline {
    font-size: 14px;
  }

  .favorite-text {
    font-size: 12px;
  }

  .favorite-count {
    font-size: 10px;
  }
}
</style>
