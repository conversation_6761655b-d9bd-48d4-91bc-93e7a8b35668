// WebRTC API 服务
import request from './index'

// WebRTC服务器管理
export const webrtcApi = {
  // 启动WebRTC服务器
  start: () => {
    return request.post('/webrtc/start')
  },

  // 停止WebRTC服务器
  stop: () => {
    return request.post('/webrtc/stop')
  },

  // 获取WebRTC服务器状态
  getStatus: () => {
    return request.get('/webrtc/status')
  },

  // 获取活跃通话列表
  getCalls: () => {
    return request.get('/webrtc/calls')
  },

  // 强制结束通话
  endCall: (callId) => {
    return request.post(`/webrtc/calls/${callId}/end`)
  },

  // 获取统计信息
  getStats: () => {
    return request.get('/webrtc/stats')
  },

  // 健康检查
  health: () => {
    return request.get('/webrtc/health')
  }
}

export default webrtcApi
