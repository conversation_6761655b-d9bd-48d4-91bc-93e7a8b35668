const db = require("../db");

// 获取聊天历史记录（私聊）
async function getPrivateChatHistory(userId, toId) {
  const sql = `
    SELECT * FROM offline_messages 
    WHERE (from_id=? AND to_id=?) OR (from_id=? AND to_id=?) 
    ORDER BY time ASC LIMIT 100
  `;
  const result = await db.query(sql, [userId, toId, toId, userId]);
  return result;
}

// 获取群聊历史记录
async function getGroupChatHistory() {
  const sql = `
    SELECT * FROM chat_message 
    WHERE to_id IS NULL 
    ORDER BY time ASC LIMIT 100
  `;
  const result = await db.query(sql);
  return result;
}

// 获取最新的聊天消息（用于调试）
async function getLatestChatMessages() {
  const sql = `
    SELECT id, from_id, from_nickname, from_avatar, type, message, url, time 
    FROM chat_message 
    ORDER BY id DESC LIMIT 5
  `;
  const result = await db.query(sql);
  return result;
}

// 搜索注册用户
async function searchUsers(keyword) {
  const sql = `
    SELECT id, username, avatar 
    FROM users 
    WHERE username LIKE ? 
    LIMIT 10
  `;
  const result = await db.query(sql, [`%${keyword}%`]);
  return result;
}

// 获取未读消息数量
async function getUnreadMessageCount(userId) {
  const sql = `
    SELECT COUNT(*) as count
    FROM offline_messages
    WHERE to_id = ? AND is_read = 0
  `;
  const result = await db.query(sql, [userId]);
  return result;
}

// 检查好友申请是否存在
async function checkFriendRequest(from_id, to_id) {
  const sql = 'SELECT * FROM friend_request WHERE from_id=? AND to_id=?';
  const result = await db.query(sql, [from_id, to_id]);
  return result;
}

// 检查是否已是好友
async function checkExistingFriend(from_id, to_id) {
  const sql = 'SELECT * FROM friends WHERE (user_id=? AND friend_id=?) OR (user_id=? AND friend_id=?)';
  const result = await db.query(sql, [from_id, to_id, to_id, from_id]);
  return result;
}

// 插入好友申请记录
async function insertFriendRequest(from_id, to_id) {
  const sql = "INSERT INTO friend_request (from_id, to_id, status) VALUES (?, ?, 'pending')";
  const result = await db.query(sql, [from_id, to_id]);
  return result;
}

// 获取好友申请列表
async function getFriendRequestList(user_id) {
  const sql = "SELECT fr.id, fr.from_id, u.username, u.avatar FROM friend_request fr JOIN users u ON fr.from_id=u.id WHERE fr.to_id=? AND fr.status='pending'";
  const result = await db.query(sql, [user_id]);
  return result;
}

// 获取好友申请详情
async function getFriendRequestById(request_id) {
  const sql = 'SELECT * FROM friend_request WHERE id=?';
  const result = await db.query(sql, [request_id]);
  return result;
}

// 添加好友关系（双向）
async function addFriendRelation(from_id, to_id) {
  const sql1 = 'INSERT IGNORE INTO friends (user_id, friend_id) VALUES (?, ?)';
  const sql2 = 'INSERT IGNORE INTO friends (user_id, friend_id) VALUES (?, ?)';
  await db.query(sql1, [from_id, to_id]);
  await db.query(sql2, [to_id, from_id]);
}

// 更新好友申请状态
async function updateFriendRequestStatus(request_id, status) {
  const sql = 'UPDATE friend_request SET status=? WHERE id=?';
  const result = await db.query(sql, [status, request_id]);
  return result;
}

// 获取好友列表
async function getFriendList(user_id) {
  const sql = 'SELECT u.id, u.username, u.avatar FROM friends f JOIN users u ON f.friend_id=u.id WHERE f.user_id=?';
  const result = await db.query(sql, [user_id]);
  return result;
}

// 标记消息为已读
async function markMessagesAsRead(userId) {
  const sql = 'UPDATE offline_messages SET is_read = 1 WHERE to_id = ? AND is_read = 0';
  const result = await db.query(sql, [userId]);
  return result;
}

module.exports = {
  getPrivateChatHistory,
  getGroupChatHistory,
  getLatestChatMessages,
  searchUsers,
  getUnreadMessageCount,
  checkFriendRequest,
  checkExistingFriend,
  insertFriendRequest,
  getFriendRequestList,
  getFriendRequestById,
  addFriendRelation,
  updateFriendRequestStatus,
  getFriendList,
  markMessagesAsRead,
};
