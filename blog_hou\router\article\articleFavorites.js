const Router = require("koa-router");
const { handleResponse } = require("../../middlewares/responseHandler");
const verifyToken = require("../../middlewares/koaJwtMiddleware");
const articleFavoritesService = require("../../utils/sql/articleFavoritesService");
const UAParser = require('ua-parser-js');

const articleFavorites = new Router();

// 收藏/取消收藏文章（支持匿名用户）
articleFavorites.post("/toggle", async (ctx) => {
  try {
    const { article_id } = ctx.request.body;
    
    if (!article_id) {
      return handleResponse(ctx, 400, { error: "文章ID不能为空" });
    }

    // 获取用户信息
    const user = ctx.state.user;
    const user_id = user?.userId || user?.id || null;
    
    // 获取IP和用户代理信息
    const ip_address = ctx.request.ip || ctx.ip;
    const user_agent = ctx.request.header['user-agent'] || '';
    
    // 生成会话ID（用于匿名用户）
    const crypto = require('crypto');
    const session_id = crypto.createHash('md5')
      .update(ip_address + user_agent + new Date().toDateString())
      .digest('hex');

    // 检查文章是否存在
    const articleExists = await articleFavoritesService.checkArticleExists(article_id);

    if (!articleExists) {
      return handleResponse(ctx, 404, { error: "文章不存在" });
    }

    // 检查是否已经收藏过
    let existingFavorite;
    if (user_id) {
      // 登录用户
      existingFavorite = await articleFavoritesService.checkUserFavorite(user_id, article_id);
    } else {
      // 匿名用户
      existingFavorite = await articleFavoritesService.checkAnonymousFavorite(ip_address, session_id, article_id);
    }

    let action = '';
    let message = '';
    let isFavorited = false;

    if (existingFavorite) {
      // 取消收藏
      await articleFavoritesService.removeFavorite(existingFavorite.id);
      action = 'removed';
      message = '已取消收藏';
      isFavorited = false;
    } else {
      // 新增收藏
      await articleFavoritesService.addFavorite(user_id, article_id, ip_address, user_agent, session_id);

      action = 'added';
      message = '收藏成功';
      isFavorited = true;
    }

    // 获取最新的收藏统计
    const stats = await getArticleFavoriteStats(article_id);

    return handleResponse(ctx, 200, {
      message,
      data: {
        action,
        isFavorited,
        stats
      }
    });

  } catch (error) {
    console.error('收藏操作失败:', error);
    return handleResponse(ctx, 500, { error: "收藏操作失败" });
  }
});

// 获取文章收藏统计
articleFavorites.get("/stats/:articleId", async (ctx) => {
  try {
    const { articleId } = ctx.params;
    
    if (!articleId) {
      return handleResponse(ctx, 400, { error: "文章ID不能为空" });
    }

    const stats = await getArticleFavoriteStats(articleId);
    
    // 如果用户已登录，检查用户的收藏状态
    let isFavorited = false;
    const user = ctx.state.user;
    const user_id = user?.userId || user?.id;
    
    if (user_id) {
      isFavorited = await articleFavoritesService.getUserFavoriteStatus(user_id, articleId);
    } else {
      // 匿名用户，通过IP和session检查
      const ip_address = ctx.request.ip || ctx.ip;
      const user_agent = ctx.request.header['user-agent'] || '';
      const crypto = require('crypto');
      const session_id = crypto.createHash('md5')
        .update(ip_address + user_agent + new Date().toDateString())
        .digest('hex');

      isFavorited = await articleFavoritesService.getAnonymousFavoriteStatus(ip_address, session_id, articleId);
    }

    return handleResponse(ctx, 200, {
      data: {
        ...stats,
        isFavorited
      }
    });

  } catch (error) {
    console.error('获取收藏统计失败:', error);
    return handleResponse(ctx, 500, { error: "获取收藏统计失败" });
  }
});

// 获取文章的收藏用户列表（需要登录）
articleFavorites.get("/users/:articleId", verifyToken, async (ctx) => {
  try {
    const { articleId } = ctx.params;
    const { page = 1, limit = 20 } = ctx.query;
    const offset = (page - 1) * limit;

    if (!articleId) {
      return handleResponse(ctx, 400, { error: "文章ID不能为空" });
    }

    // 获取收藏用户列表（只显示登录用户）
    const users = await articleFavoritesService.getArticleFavoriteUsers(articleId, limit, offset);

    // 获取总数
    const total = await articleFavoritesService.getArticleFavoriteUsersCount(articleId);

    return handleResponse(ctx, 200, {
      data: {
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取收藏用户列表失败:', error);
    return handleResponse(ctx, 500, { error: "获取收藏用户列表失败" });
  }
});

// 获取用户的收藏历史（需要登录）
articleFavorites.get("/history", verifyToken, async (ctx) => {
  try {
    const user = ctx.state.user;
    const user_id = user?.userId || user?.id;
    const { page = 1, limit = 20 } = ctx.query;
    const offset = (page - 1) * limit;

    console.log('🔍 收藏历史API调试信息:');
    console.log('  - ctx.state.user:', user);
    console.log('  - 解析的user_id:', user_id);
    console.log('  - 查询参数:', { page, limit, offset });

    if (!user_id) {
      console.log('❌ 用户未登录，user_id为空');
      return handleResponse(ctx, 401, { error: "用户未登录" });
    }

    // 获取用户收藏历史
    console.log('📝 执行收藏历史查询，参数:', [user_id, parseInt(limit), offset]);
    const history = await articleFavoritesService.getUserFavoriteHistory(user_id, limit, offset);

    console.log('📊 查询结果:', {
      historyCount: history.length,
      firstRecord: history[0] || null
    });

    // 获取总数
    const total = await articleFavoritesService.getUserFavoriteHistoryCount(user_id);

    console.log('📈 总数统计:', total);

    return handleResponse(ctx, 200, {
      data: {
        history,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取用户收藏历史失败:', error);
    return handleResponse(ctx, 500, { error: "获取用户收藏历史失败" });
  }
});

// 获取热门收藏文章
articleFavorites.get("/popular", async (ctx) => {
  try {
    const { limit = 10 } = ctx.query;

    const popularArticles = await articleFavoritesService.getPopularFavoriteArticles(limit);

    return handleResponse(ctx, 200, {
      data: popularArticles
    });

  } catch (error) {
    console.error('获取热门收藏文章失败:', error);
    return handleResponse(ctx, 500, { error: "获取热门收藏文章失败" });
  }
});

// 辅助函数：获取文章收藏统计
async function getArticleFavoriteStats(articleId) {
  const favorites_count = await articleFavoritesService.getArticleFavoriteStats(articleId);
  return {
    favorites_count
  };
}

module.exports = articleFavorites;
