<template>
  <div class="article-ai-tools">
    <el-card class="ai-tools-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>🤖 AI写作助手</span>
          <el-tag :type="aiStatus === 'online' ? 'success' : 'danger'" size="small">
            {{ aiStatus === 'online' ? '在线' : '离线' }}
          </el-tag>
        </div>
      </template>

      <div class="ai-tools-content">
        <!-- 快速操作按钮 -->
        <div class="quick-actions">
          <el-button 
            type="primary" 
            size="small"
            @click="generateTagsForArticle"
            :loading="tagLoading"
            :disabled="!articleContent"
          >
            🏷️ 生成标签
          </el-button>
          
          <el-button 
            type="success" 
            size="small"
            @click="generateSummaryForArticle"
            :loading="summaryLoading"
            :disabled="!articleContent"
          >
            📝 生成摘要
          </el-button>
          
          <el-button 
            type="warning" 
            size="small"
            @click="generateTitlesForArticle"
            :loading="titleLoading"
            :disabled="!articleContent"
          >
            📰 生成标题
          </el-button>
          
          <el-button 
            type="info" 
            size="small"
            @click="moderateArticle"
            :loading="moderateLoading"
            :disabled="!articleContent"
          >
            🔍 内容审核
          </el-button>
        </div>

        <!-- AI生成结果展示 -->
        <div v-if="showResults" class="ai-results">
          <!-- 标签结果 -->
          <div v-if="generatedTags.length > 0" class="result-section">
            <div class="result-header">
              <span>🏷️ 推荐标签：</span>
              <el-button size="small" @click="applyTags">应用标签</el-button>
            </div>
            <div class="tags-container">
              <el-tag 
                v-for="tag in generatedTags" 
                :key="tag"
                :class="{ 'selected': selectedTags.includes(tag) }"
                @click="toggleTag(tag)"
                style="margin: 4px; cursor: pointer;"
              >
                {{ tag }}
              </el-tag>
            </div>
          </div>

          <!-- 摘要结果 -->
          <div v-if="generatedSummary" class="result-section">
            <div class="result-header">
              <span>📝 生成的摘要：</span>
              <el-button size="small" @click="applySummary">应用摘要</el-button>
            </div>
            <el-input
              v-model="generatedSummary"
              type="textarea"
              :rows="3"
              readonly
              class="summary-display"
            />
          </div>

          <!-- 标题结果 -->
          <div v-if="generatedTitles.length > 0" class="result-section">
            <div class="result-header">
              <span>📰 推荐标题：</span>
            </div>
            <div class="titles-container">
              <div 
                v-for="(title, index) in generatedTitles" 
                :key="index"
                :class="{ 'selected': selectedTitle === title }"
                class="title-option"
                @click="selectTitle(title)"
              >
                <span class="title-number">{{ index + 1 }}.</span>
                <span class="title-text">{{ title }}</span>
                <el-button 
                  size="small" 
                  type="text"
                  @click.stop="applyTitle(title)"
                >
                  应用
                </el-button>
              </div>
            </div>
          </div>

          <!-- 审核结果 -->
          <div v-if="moderationResult" class="result-section">
            <div class="result-header">
              <span>🔍 内容审核结果：</span>
            </div>
            <el-alert
              :title="moderationResult.safe ? '✅ 内容安全' : '⚠️ 发现问题'"
              :type="moderationResult.safe ? 'success' : 'warning'"
              :description="moderationResult.suggestion"
              show-icon
            />
            <div class="score-info">
              <el-progress 
                :percentage="moderationResult.score" 
                :color="getScoreColor(moderationResult.score)"
                style="margin-top: 8px;"
              />
              <span style="margin-top: 4px; display: block;">
                安全评分: {{ moderationResult.score }}/100
              </span>
            </div>
          </div>
        </div>

        <!-- AI写作建议 -->
        <div v-if="writingSuggestions.length > 0" class="writing-suggestions">
          <div class="suggestions-header">💡 写作建议：</div>
          <ul class="suggestions-list">
            <li v-for="(suggestion, index) in writingSuggestions" :key="index">
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { aiApi } from '@/utils/aiApi'

// Props
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  content: {
    type: String,
    default: ''
  },
  summary: {
    type: String,
    default: ''
  },
  tags: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:title', 'update:content', 'update:summary', 'update:tags'])

// 响应式数据
const aiStatus = ref('offline')
const showResults = ref(false)

// 加载状态
const tagLoading = ref(false)
const summaryLoading = ref(false)
const titleLoading = ref(false)
const moderateLoading = ref(false)

// AI生成结果
const generatedTags = ref([])
const selectedTags = ref([])
const generatedSummary = ref('')
const generatedTitles = ref([])
const selectedTitle = ref('')
const moderationResult = ref(null)

// 写作建议
const writingSuggestions = ref([])

// 计算属性
const articleContent = computed(() => props.content)

// 生命周期
onMounted(() => {
  checkAIStatus()
})

// 监听内容变化，提供实时建议
watch(articleContent, (newContent) => {
  if (newContent && newContent.length > 100) {
    generateWritingSuggestions(newContent)
  }
}, { debounce: 1000 })

// 方法
const checkAIStatus = async () => {
  try {
    const response = await aiApi.getStatus()
    aiStatus.value = response.data.status
  } catch (error) {
    console.error('检查AI状态失败:', error)
    aiStatus.value = 'offline'
  }
}

// 为文章生成标签
const generateTagsForArticle = async () => {
  if (!articleContent.value.trim()) {
    ElMessage.warning('请先输入文章内容')
    return
  }

  tagLoading.value = true
  try {
    const response = await aiApi.generateTags({
      title: props.title,
      content: articleContent.value
    })
    
    generatedTags.value = response.data.tags
    selectedTags.value = []
    showResults.value = true
    ElMessage.success('标签生成成功')
  } catch (error) {
    console.error('标签生成失败:', error)
    ElMessage.error('标签生成失败')
  } finally {
    tagLoading.value = false
  }
}

// 为文章生成摘要
const generateSummaryForArticle = async () => {
  if (!articleContent.value.trim()) {
    ElMessage.warning('请先输入文章内容')
    return
  }

  summaryLoading.value = true
  try {
    const response = await aiApi.generateSummary({
      content: articleContent.value,
      maxLength: 200
    })
    
    generatedSummary.value = response.data.summary
    showResults.value = true
    ElMessage.success('摘要生成成功')
  } catch (error) {
    console.error('摘要生成失败:', error)
    ElMessage.error('摘要生成失败')
  } finally {
    summaryLoading.value = false
  }
}

// 为文章生成标题
const generateTitlesForArticle = async () => {
  if (!articleContent.value.trim()) {
    ElMessage.warning('请先输入文章内容')
    return
  }

  titleLoading.value = true
  try {
    const response = await aiApi.generateTitles({
      content: articleContent.value,
      count: 5
    })
    
    generatedTitles.value = response.data.titles
    selectedTitle.value = ''
    showResults.value = true
    ElMessage.success('标题生成成功')
  } catch (error) {
    console.error('标题生成失败:', error)
    ElMessage.error('标题生成失败')
  } finally {
    titleLoading.value = false
  }
}

// 审核文章内容
const moderateArticle = async () => {
  if (!articleContent.value.trim()) {
    ElMessage.warning('请先输入文章内容')
    return
  }

  moderateLoading.value = true
  try {
    const response = await aiApi.moderateContent({
      content: articleContent.value,
      type: 'article'
    })
    
    moderationResult.value = response.data.moderation
    showResults.value = true
    ElMessage.success('内容审核完成')
  } catch (error) {
    console.error('内容审核失败:', error)
    ElMessage.error('内容审核失败')
  } finally {
    moderateLoading.value = false
  }
}

// 生成写作建议
const generateWritingSuggestions = async (content) => {
  try {
    // 这里可以调用AI API生成写作建议
    // 暂时使用静态建议
    const suggestions = [
      '建议增加更多具体的例子来支持你的观点',
      '可以考虑添加小标题来改善文章结构',
      '建议在结尾部分添加总结段落'
    ]
    writingSuggestions.value = suggestions
  } catch (error) {
    console.error('生成写作建议失败:', error)
  }
}

// 标签操作
const toggleTag = (tag) => {
  const index = selectedTags.value.indexOf(tag)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tag)
  }
}

const applyTags = () => {
  if (selectedTags.value.length === 0) {
    selectedTags.value = [...generatedTags.value]
  }
  emit('update:tags', selectedTags.value)
  ElMessage.success(`已应用 ${selectedTags.value.length} 个标签`)
}

// 摘要操作
const applySummary = () => {
  emit('update:summary', generatedSummary.value)
  ElMessage.success('摘要已应用')
}

// 标题操作
const selectTitle = (title) => {
  selectedTitle.value = title
}

const applyTitle = (title) => {
  emit('update:title', title)
  ElMessage.success('标题已应用')
}

// 工具函数
const getScoreColor = (score) => {
  if (score >= 80) return '#67c23a'
  if (score >= 60) return '#e6a23c'
  return '#f56c6c'
}
</script>

<style scoped>
.article-ai-tools {
  margin-bottom: 20px;
}

.ai-tools-card {
  border: 1px solid #e4e7ed;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-tools-content {
  padding: 0;
}

.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.ai-results {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.result-section {
  margin-bottom: 20px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: bold;
  color: #333;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tags-container .el-tag {
  transition: all 0.2s;
}

.tags-container .el-tag.selected {
  background-color: #409eff;
  color: white;
}

.tags-container .el-tag:hover {
  transform: scale(1.05);
}

.summary-display {
  margin-top: 8px;
}

.titles-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.title-option {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.title-option:hover {
  background-color: #f5f7fa;
  border-color: #409eff;
}

.title-option.selected {
  background-color: #ecf5ff;
  border-color: #409eff;
}

.title-number {
  font-weight: bold;
  color: #409eff;
  margin-right: 8px;
  min-width: 20px;
}

.title-text {
  flex: 1;
  line-height: 1.4;
}

.score-info {
  margin-top: 8px;
}

.writing-suggestions {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  margin-top: 16px;
}

.suggestions-header {
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.suggestions-list {
  margin: 0;
  padding-left: 20px;
}

.suggestions-list li {
  margin-bottom: 4px;
  color: #666;
  line-height: 1.4;
}
</style>
