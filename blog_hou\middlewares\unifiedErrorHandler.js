// 统一错误处理中间件
// 合并了 errorHandler.js 和 enhancedErrorHandler.js 的功能
const logger = require('../plugin/logger');
const { handleResponse } = require('./responseHandler');

// 错误类型映射
const ERROR_TYPES = {
  ValidationError: { status: 400, message: "参数验证失败" },
  UnauthorizedError: { status: 401, message: "未授权访问" },
  ForbiddenError: { status: 403, message: "禁止访问" },
  NotFoundError: { status: 404, message: "资源不存在" },
  ConflictError: { status: 409, message: "资源冲突" },
  TooManyRequestsError: { status: 429, message: "请求过于频繁" },
  InternalServerError: { status: 500, message: "服务器内部错误" }
};

// 生成错误ID
function generateErrorId() {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 判断是否为服务器错误
function isServerError(status) {
  return status >= 500;
}

// 判断是否为客户端错误
function isClientError(status) {
  return status >= 400 && status < 500;
}

// 获取错误日志级别
function getLogLevel(status) {
  if (status >= 500) return 'error';
  if (status >= 400) return 'warn';
  return 'info';
}

// 过滤敏感信息
function sanitizeRequestBody(body) {
  if (!body || typeof body !== 'object') return body;
  
  const bodyStr = JSON.stringify(body);
  return bodyStr
    .replace(/"password":"[^"]*"/g, '"password":"[REDACTED]"')
    .replace(/"token":"[^"]*"/g, '"token":"[REDACTED]"')
    .replace(/"secret":"[^"]*"/g, '"secret":"[REDACTED]"')
    .replace(/"key":"[^"]*"/g, '"key":"[REDACTED]"')
    .substring(0, 500);
}

// 创建标准化错误响应
function createErrorResponse(status, message, details = null, errorId = null) {
  return {
    success: false,
    code: status,
    message,
    details,
    meta: {
      timestamp: new Date().toISOString(),
      errorId
    }
  };
}

/**
 * 统一错误处理中间件
 * @param {Object} options 配置选项
 * @param {boolean} options.exposeStack 是否暴露错误堆栈（开发环境）
 * @param {boolean} options.logClientErrors 是否记录客户端错误
 * @param {Array} options.sensitiveHeaders 敏感头部列表
 */
module.exports = (options = {}) => {
  const {
    exposeStack = process.env.NODE_ENV === 'development',
    logClientErrors = true,
    sensitiveHeaders = ['authorization', 'cookie', 'x-api-key']
  } = options;

  return async (ctx, next) => {
    const startTime = Date.now();
    
    try {
      await next();
      
      // 检查响应状态
      if (ctx.status >= 400) {
        const duration = Date.now() - startTime;
        const logLevel = getLogLevel(ctx.status);
        
        if (logClientErrors || ctx.status >= 500) {
          logger[logLevel]("HTTP错误响应", {
            requestId: ctx.state.requestId,
            method: ctx.method,
            url: ctx.url,
            status: ctx.status,
            duration,
            userAgent: ctx.get("User-Agent"),
            ip: ctx.ip,
            userId: ctx.state.user?.id
          });
        }
      }
      
    } catch (err) {
      const duration = Date.now() - startTime;
      const errorId = generateErrorId();
      
      // 确定错误状态码和消息
      let status = err.status || err.statusCode || 500;
      let message = err.message || "服务器内部错误";
      let details = null;
      
      // 根据错误类型调整状态码和消息
      const errorType = ERROR_TYPES[err.constructor.name];
      if (errorType) {
        status = errorType.status;
        if (!err.message || err.message === err.constructor.name) {
          message = errorType.message;
        }
      }
      
      // 处理验证错误的详细信息
      if (err.name === "ValidationError" && err.details) {
        details = err.details;
      }
      
      // 创建错误响应
      const errorResponse = createErrorResponse(status, message, details, errorId);
      
      // 在开发环境暴露错误堆栈
      if (exposeStack && isServerError(status)) {
        errorResponse.stack = err.stack;
      }
      
      // 构建日志数据
      const logLevel = getLogLevel(status);
      const logData = {
        errorId,
        requestId: ctx.state.requestId,
        method: ctx.method,
        url: ctx.url,
        path: ctx.path,
        status,
        duration,
        error: err.message,
        errorType: err.constructor.name,
        ip: ctx.ip,
        userAgent: ctx.get("User-Agent"),
        userId: ctx.state.user?.id
      };
      
      // 添加查询参数（过滤敏感信息）
      if (ctx.query && Object.keys(ctx.query).length > 0) {
        const sanitizedQuery = { ...ctx.query };
        ['password', 'token', 'secret', 'key'].forEach(key => {
          if (sanitizedQuery[key]) {
            sanitizedQuery[key] = '[REDACTED]';
          }
        });
        logData.query = sanitizedQuery;
      }
      
      // 添加请求头（过滤敏感信息）
      const headers = {};
      Object.keys(ctx.headers).forEach(key => {
        if (!sensitiveHeaders.includes(key.toLowerCase())) {
          headers[key] = ctx.headers[key];
        } else {
          headers[key] = '[REDACTED]';
        }
      });
      logData.headers = headers;
      
      // 过滤敏感的请求体信息
      if (ctx.request.body) {
        logData.requestBody = sanitizeRequestBody(ctx.request.body);
      }
      
      // 服务器错误记录完整堆栈
      if (isServerError(status)) {
        logData.stack = err.stack;
      }
      
      logger[logLevel]("请求处理错误", logData);
      
      // 设置响应
      ctx.status = status;
      ctx.body = errorResponse;
      
      // 设置错误响应头
      ctx.set('X-Error-ID', errorId);
      ctx.set('X-Response-Time', `${duration}ms`);
      
      // 如果是严重错误，发送告警
      if (isServerError(status)) {
        logger.error("严重错误告警", {
          errorId,
          requestId: ctx.state.requestId,
          url: ctx.url,
          error: err.message,
          userId: ctx.state.user?.id,
          stack: err.stack
        });
      }
      
      // 触发应用级错误事件
      ctx.app.emit('error', err, ctx);
    }
  };
};

// 导出工具函数
module.exports.createErrorResponse = createErrorResponse;
module.exports.ERROR_TYPES = ERROR_TYPES;
