<template>
  <div class="user-intro">
    <el-form :model="form" label-width="80px" class="user-form">
      <el-form-item label="用户名">
        <el-input v-model="form.username" disabled />
      </el-form-item>
      <el-form-item label="邮箱">
        <el-input v-model="form.email" />
      </el-form-item>
      <el-form-item label="头像">
        <el-upload class="avatar-uploader" action="" :show-file-list="false" :before-upload="handleAvatarChange">
          <img v-if="avatarUrl" :src="avatarUrl" class="avatar" />
          <el-icon v-else>
            <User />
          </el-icon>
        </el-upload>
      </el-form-item>
      <el-form-item label="地址">
        <el-input v-model="form.address" />
      </el-form-item>
      <el-form-item label="职位">
        <el-input v-model="form.position" />
      </el-form-item>
      <el-form-item label="简介">
        <el-input v-model="form.intro" type="textarea" />
      </el-form-item>
      <el-form-item label="技术标签">
        <el-select v-model="form.tech_tags" multiple placeholder="请选择">
          <el-option v-for="tag in tagOptions" :key="tag" :label="tag" :value="tag" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { GetProfileApi, UpdateProfileApi, GetTagsApi } from "../../utils/api";
import { User } from "@element-plus/icons-vue";

import { useRouter } from "vue-router";
const router = useRouter();
const userId = localStorage.getItem("id") || "";
const form = ref({
  id: userId,
  username: "",
  email: "",
  avatar: "",
  address: "",
  position: "",
  intro: "",
  tech_tags: [],
});
const avatarUrl = ref("");
const tagOptions = ref<string[]>([]);
const avatarFile = ref<File | null>(null);

onMounted(async () => {
  // 获取用户信息
  const res = await GetProfileApi({ id: userId });
  if (res?.user) {
    Object.assign(form.value, res.user);
    avatarUrl.value = form.value.avatar
      ? `${import.meta.env.VITE_BASE_URL || ""}/avatars/${form.value.avatar}`
      : "";
    if (typeof form.value.tech_tags === "string") {
      try {
        form.value.tech_tags = JSON.parse(form.value.tech_tags);
      } catch {
        form.value.tech_tags = [];
      }
    }
  }
  // 获取技术标签
  const tagRes = await GetTagsApi();
  if (Array.isArray(tagRes)) {
    tagOptions.value = tagRes.map((t: any) => t.name || t);
  } else if (Array.isArray(tagRes?.tags)) {
    tagOptions.value = tagRes.tags.map((t: any) => t.name || t);
  }
});

// 头像选择处理（不自动上传，只保存文件对象）
const handleAvatarChange = (file: File) => {
  avatarFile.value = file;
  const reader = new FileReader();
  reader.onload = e => {
    avatarUrl.value = e.target?.result as string;
  };
  reader.readAsDataURL(file);
  // 阻止 el-upload 默认上传
  return false;
};

const submit = async () => {
  const fd = new FormData();
  Object.entries(form.value).forEach(([key, value]) => {
    if (key === "tech_tags") {
      fd.append(key, JSON.stringify(value));
    } else {
      fd.append(key, value ?? "");
    }
  });
  if (avatarFile.value) {
    fd.append("file", avatarFile.value);
  }
  const res = await UpdateProfileApi(fd);
  if (res?.message) {
    ElMessage.success("保存成功");
    // 两秒后返回首页
    setTimeout(() => {
      router.push("/");
    }, 2000);
    // 如果后端返回新头像文件名，更新前端显示
    if (res.avatar) {
      form.value.avatar = res.avatar;
      avatarUrl.value = `${import.meta.env.VITE_BASE_URL || ""}/avatars/${res.avatar}`;
    }
  } else {
    ElMessage.error(res?.error || "保存失败");
  }
};
</script>

<style scoped lang="less">
.user-intro {
  --primary-color: #409EFF;
  --bg-color: #fff;
  --border-radius: 6px;
  --gap: 16px;
  --padding: 24px;

  max-width: 500px;
  margin: 0 auto;
  margin-top: 20px;
  padding: var(--padding);
  background: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

  .user-form {
    .el-form-item {
      margin-bottom: var(--gap) !important;
    }
  }

  .avatar-uploader {
    display: flex;
    align-items: center;
    cursor: pointer;

    .avatar,
    .el-icon {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: #f5f7fa;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 40px;
      color: #bbb;
      transition: background 0.3s, color 0.3s;
    }

    .avatar {
      object-fit: cover;
      margin-right: 10px;
    }

    &:hover .avatar,
    &:hover .el-icon {
      background: #f0f7ff;
      color: var(--primary-color);
    }
  }

  .el-button {
    width: 100%;
    padding: 12px 0;
    border-radius: var(--border-radius);
  }
}

@media (max-width: 576px) {
  .user-intro {
    padding: 16px;

    .avatar-uploader {
      flex-direction: column;

      .avatar {
        margin: 0 0 8px;
      }
    }

    .el-button {
      padding: 10px 0;
    }
  }
}
</style>