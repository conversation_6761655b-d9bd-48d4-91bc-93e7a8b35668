// 添加文章浏览量字段的数据库迁移脚本
const db = require("../utils/db");
const logger = require("../plugin/logger");

async function addViewsField() {
  try {
    console.log("🚀 开始添加文章浏览量字段...");

    // 检查views字段是否已存在
    const checkFieldSql = `
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'articles' 
      AND COLUMN_NAME = 'views'
    `;
    
    const existingField = await db.query(checkFieldSql);
    
    if (existingField.length > 0) {
      console.log("✅ views字段已存在，无需添加");
      return;
    }

    // 添加views字段
    const addFieldSql = `
      ALTER TABLE articles 
      ADD COLUMN views INT DEFAULT 0 COMMENT '浏览量' 
      AFTER content
    `;
    
    await db.query(addFieldSql);
    console.log("✅ 成功添加views字段");

    // 为现有文章初始化浏览量（随机值，模拟真实数据）
    const initViewsSql = `
      UPDATE articles 
      SET views = FLOOR(RAND() * 100) + 1 
      WHERE views IS NULL OR views = 0
    `;
    
    const result = await db.query(initViewsSql);
    console.log(`✅ 成功初始化 ${result.affectedRows} 篇文章的浏览量`);

    // 添加索引以优化查询性能
    const addIndexSql = `
      ALTER TABLE articles 
      ADD INDEX idx_views (views)
    `;
    
    await db.query(addIndexSql);
    console.log("✅ 成功添加views字段索引");

    console.log("🎉 文章浏览量字段添加完成！");

  } catch (error) {
    console.error("❌ 添加浏览量字段失败:", error);
    logger.error("添加浏览量字段失败", { error: error.message });
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addViewsField()
    .then(() => {
      console.log("✅ 迁移完成");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ 迁移失败:", error);
      process.exit(1);
    });
}

module.exports = { addViewsField };
