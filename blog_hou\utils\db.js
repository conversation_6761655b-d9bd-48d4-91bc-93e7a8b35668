const mysql = require("mysql");
const logger = require("../plugin/logger");
const { getDatabaseConfig } = require("../config/database");

// 获取数据库配置
const config = getDatabaseConfig();

// 创建连接池，增加更多配置
const pool = mysql.createPool({
  ...config,
  // 连接池优化配置
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  connectionLimit: config.connectionLimit || 20,
  queueLimit: 0,

  // 连接保活
  keepAliveInitialDelay: 0,
  enableKeepAlive: true,

  // 字符集
  charset: 'utf8mb4',

  // 时区
  timezone: '+08:00'
});

// 简单内存缓存 (生产环境建议使用Redis)
const cache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

// 启动时测试连接并监控连接池状态
pool.getConnection((err, connection) => {
  if (err) {
    logger.error("数据库连接失败:", err.message);
    console.error("数据库连接失败:", err.message);
  } else {
    logger.info("数据库连接成功！");
    console.log("数据库连接成功！");
    connection.release();
  }
});

// 监控连接池状态
setInterval(() => {
  const poolStatus = {
    totalConnections: pool._allConnections.length,
    freeConnections: pool._freeConnections.length,
    acquiringConnections: pool._acquiringConnections.length,
    queuedCallbacks: pool._connectionQueue.length
  };

  if (poolStatus.freeConnections === 0) {
    logger.warn("数据库连接池耗尽", poolStatus);
  }

  // 每10分钟记录一次连接池状态
  if (Date.now() % (10 * 60 * 1000) < 30000) {
    logger.info("数据库连接池状态", poolStatus);
  }
}, 30000); // 每30秒检查一次

// 清理过期缓存
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of cache.entries()) {
    if (now - value.timestamp > CACHE_TTL) {
      cache.delete(key);
    }
  }
}, 60000); // 每分钟清理一次

module.exports = {
  // 基础查询方法
  query(sql, args = []) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();

      pool.getConnection(function (err, connection) {
        if (err) {
          logger.error("获取数据库连接失败:", err.message);
          reject(err);
        } else {
          connection.query(sql, args, function (err, rows) {
            const duration = Date.now() - startTime;

            if (err) {
              logger.error("SQL查询失败:", {
                sql: sql.substring(0, 100) + (sql.length > 100 ? '...' : ''),
                args,
                error: err.message,
                duration
              });
              reject(err);
            } else {
              // 记录慢查询 (超过1秒)
              if (duration > 1000) {
                logger.warn("慢查询检测:", {
                  sql: sql.substring(0, 100) + (sql.length > 100 ? '...' : ''),
                  args,
                  duration,
                  rowCount: Array.isArray(rows) ? rows.length : 1
                });
              }

              resolve(rows);
            }
            connection.release();
          });
        }
      });
    });
  },

  // 带缓存的查询方法
  queryWithCache(sql, args = [], cacheKey = null, ttl = CACHE_TTL) {
    // 生成缓存键
    const key = cacheKey || `${sql}_${JSON.stringify(args)}`;

    // 检查缓存
    const cached = cache.get(key);
    if (cached && (Date.now() - cached.timestamp) < ttl) {
      logger.debug("缓存命中:", key);
      return Promise.resolve(cached.data);
    }

    // 执行查询并缓存结果
    return this.query(sql, args).then(result => {
      cache.set(key, {
        data: result,
        timestamp: Date.now()
      });
      return result;
    });
  },

  // 事务处理
  transaction(callback) {
    return new Promise((resolve, reject) => {
      pool.getConnection((err, connection) => {
        if (err) {
          logger.error("获取事务连接失败:", err.message);
          reject(err);
          return;
        }

        connection.beginTransaction(async (err) => {
          if (err) {
            connection.release();
            logger.error("开始事务失败:", err.message);
            reject(err);
            return;
          }

          try {
            const result = await callback(connection);

            connection.commit((err) => {
              if (err) {
                connection.rollback(() => {
                  connection.release();
                  logger.error("事务提交失败:", err.message);
                  reject(err);
                });
              } else {
                connection.release();
                logger.info("事务提交成功");
                resolve(result);
              }
            });
          } catch (error) {
            connection.rollback(() => {
              connection.release();
              logger.error("事务执行失败:", error.message);
              reject(error);
            });
          }
        });
      });
    });
  },

  // 批量插入优化
  batchInsert(table, columns, values) {
    if (!values || values.length === 0) {
      return Promise.resolve({ affectedRows: 0 });
    }

    const placeholders = values.map(() => `(${columns.map(() => '?').join(', ')})`).join(', ');
    const sql = `INSERT INTO ${table} (${columns.join(', ')}) VALUES ${placeholders}`;
    const flatValues = values.flat();

    return this.query(sql, flatValues);
  },

  // 清理缓存
  clearCache(pattern = null) {
    if (pattern) {
      for (const key of cache.keys()) {
        if (key.includes(pattern)) {
          cache.delete(key);
        }
      }
    } else {
      cache.clear();
    }
    logger.info("缓存已清理", { pattern });
  },

  // 获取连接池状态
  getPoolStatus() {
    return {
      totalConnections: pool._allConnections.length,
      freeConnections: pool._freeConnections.length,
      acquiringConnections: pool._acquiringConnections.length,
      queuedCallbacks: pool._connectionQueue.length,
      cacheSize: cache.size
    };
  }
};