const fs = require("fs");
const path = require("path");
const logger = require("../plugin/logger");
const db = require("./db");
const exportConfig = require("./exportConfig");

/**
 * SQL导出工具类
 */
class SqlExporter {
  constructor() {
    this.ensureExportDirectory();
  }

  /**
   * 转义SQL值
   * @param {*} val - 要转义的值
   * @returns {string} 转义后的值
   */
  escapeValue(val) {
    if (val === null || val === undefined) return "NULL";
    if (typeof val === "number") return val.toString();
    if (typeof val === "boolean") return val ? "1" : "0";
    
    const stringVal = val.toString();
    return `'${stringVal.replace(/\\/g, "\\\\").replace(/'/g, "\\'")}'`;
  }

  /**
   * 确保导出目录存在
   */
  ensureExportDirectory() {
    if (!fs.existsSync(exportConfig.EXPORT.DIR)) {
      fs.mkdirSync(exportConfig.EXPORT.DIR, { recursive: true });
      logger.info(`创建导出目录: ${exportConfig.EXPORT.DIR}`);
    }
  }

  /**
   * 生成表结构SQL
   * @param {string} tableName - 表名
   * @returns {Promise<string>} 表结构SQL
   */
  async generateTableStructure(tableName) {
    try {
      const createResult = await db.query(`SHOW CREATE TABLE \`${tableName}\``);
      const createTableSql = createResult[0]["Create Table"];
      
      const structureParts = [];
      
      if (exportConfig.DATABASE.ADD_COMMENTS) {
        structureParts.push(
          `-- ----------------------------`,
          `-- Table structure for \`${tableName}\``,
          `-- ----------------------------`
        );
      }
      
      if (exportConfig.DATABASE.ADD_DROP_TABLE) {
        structureParts.push(`DROP TABLE IF EXISTS \`${tableName}\`;`);
      }
      
      structureParts.push(`${createTableSql};`, "");
      
      return structureParts.join("\n");
    } catch (error) {
      logger.error(`生成表结构失败: ${tableName}`, error);
      throw error;
    }
  }

  /**
   * 生成表数据SQL
   * @param {string} tableName - 表名
   * @returns {Promise<string>} 表数据SQL
   */
  async generateTableData(tableName) {
    try {
      const rows = await db.query(
        `SELECT * FROM \`${tableName}\` LIMIT ?`, 
        [exportConfig.DATABASE.MAX_RECORDS_PER_TABLE]
      );
      
      if (rows.length === 0) return "";
      
      const dataParts = [];
      
      if (exportConfig.DATABASE.ADD_COMMENTS) {
        dataParts.push(
          `-- ----------------------------`,
          `-- Records of \`${tableName}\``,
          `-- ----------------------------`
        );
      }
      
      rows.forEach((row) => {
        const keys = Object.keys(row).map(k => `\`${k}\``).join(", ");
        const values = Object.values(row).map(val => this.escapeValue(val)).join(", ");
        dataParts.push(`INSERT INTO \`${tableName}\` (${keys}) VALUES (${values});`);
      });
      
      return dataParts.join("\n") + "\n";
    } catch (error) {
      logger.error(`生成表数据失败: ${tableName}`, error);
      throw error;
    }
  }

  /**
   * 生成完整的SQL导出内容
   * @returns {Promise<string>} 完整的SQL内容
   */
  async generateFullSqlExport() {
    try {
      // 获取所有表名
      const tables = await db.query("SHOW TABLES");
      const tableNames = tables.map(row => Object.values(row)[0]);
      
      if (tableNames.length === 0) {
        throw new Error("数据库中没有找到任何表");
      }
      
      logger.info(`开始导出 ${tableNames.length} 个表`);
      
      // 生成SQL头部信息
      const sqlParts = [
        `-- 导出时间: ${new Date().toLocaleString()}`,
        `-- 数据库: myblog`,
        `-- 表数量: ${tableNames.length}`,
        `-- 每个表最大记录数: ${exportConfig.DATABASE.MAX_RECORDS_PER_TABLE}`,
        `-- ---------------------------------------------`,
        ""
      ];
      
      // 为每个表生成结构和数据
      for (const tableName of tableNames) {
        logger.info(`正在处理表: ${tableName}`);
        
        if (exportConfig.DATABASE.INCLUDE_STRUCTURE) {
          const structureSql = await this.generateTableStructure(tableName);
          sqlParts.push(structureSql);
        }
        
        if (exportConfig.DATABASE.INCLUDE_DATA) {
          const dataSql = await this.generateTableData(tableName);
          if (dataSql) {
            sqlParts.push(dataSql);
          }
        }
      }
      
      return sqlParts.join("\n");
    } catch (error) {
      logger.error("生成SQL导出内容失败", error);
      throw error;
    }
  }

  /**
   * 导出SQL文件
   * @returns {Promise<string>} 导出的文件路径
   */
  async exportSqlFile() {
    try {
      const sqlContent = await this.generateFullSqlExport();
      const sqlFilePath = path.join(exportConfig.EXPORT.DIR, exportConfig.EXPORT.SQL_FILENAME);
      
      fs.writeFileSync(sqlFilePath, sqlContent, exportConfig.EXPORT.ENCODING);
      
      logger.info(`SQL文件导出成功: ${sqlFilePath}`);
      return sqlFilePath;
    } catch (error) {
      logger.error("导出SQL文件失败", error);
      throw error;
    }
  }

  /**
   * 获取导出文件信息
   * @returns {Object} 文件信息
   */
  getExportFileInfo() {
    const sqlFilePath = path.join(exportConfig.EXPORT.DIR, exportConfig.EXPORT.SQL_FILENAME);
    const exists = fs.existsSync(sqlFilePath);
    
    let fileInfo = null;
    if (exists) {
      const stats = fs.statSync(sqlFilePath);
      fileInfo = {
        exists: true,
        size: stats.size,
        lastModified: stats.mtime,
        path: sqlFilePath
      };
    }
    
    return {
      exportDirectory: exportConfig.EXPORT.DIR,
      sqlFilename: exportConfig.EXPORT.SQL_FILENAME,
      fileExists: exists,
      fileInfo
    };
  }

  /**
   * 获取导出配置
   * @returns {Object} 导出配置
   */
  getExportConfig() {
    return exportConfig;
  }
}

module.exports = SqlExporter; 