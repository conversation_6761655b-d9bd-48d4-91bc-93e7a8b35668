前端文档说明
一、项目简介
本项目为博客系统的前端，基于 Vue 3 + TypeScript + Vite 技术栈开发，配合 Element Plus、Vant、TailwindCSS 等 UI 框架，支持文章管理、用户中心、文件上传、资源预览、聊天室、AI对话等功能。前后端分离，所有数据通过 RESTful API 或 WebSocket 与后端交互。

二、目录结构说明
目录/文件	说明
index.html	项目入口 HTML 文件
package.json	项目依赖与脚本配置
vite.config.ts	Vite 构建与开发配置
src/	源码目录
├─ main.ts	应用入口，挂载 Vue 实例
├─ App.vue	根组件，包含路由出口
├─ assets/	静态资源（图片等）
├─ components/	公共组件（如评论、加密、导出SQL等）
├─ composables/	组合式 API 封装（如图片预览）
├─ config/	配置文件（如表单校验规则）
├─ css/	样式文件（如 TailwindCSS）
├─ design/	设计相关页面或组件
├─ plug/	通用 JS 工具（如复制功能）
├─ router/	路由配置
├─ utils/	工具函数、API 封装
├─ views/	业务页面（如首页、文章、资源、上传、AI模型、聊天室等）

三、主要功能模块
用户中心：注册、登录、个人信息、头像上传、标签管理等
文章管理：文章列表、详情、编辑、发布、评论、标签、分类、回收站
文件与资源：文件上传、分片上传、资源列表、下载、预览
聊天室：WebSocket 实时聊天
AI大模型：与后端大模型接口对话
创意中心：创意/灵感记录与管理
Bug管理：Bug 列表、增删改查、批量操作
日志监控：查看后端日志
加密/解密：图片、视频加解密操作

四、技术栈
Vue 3 + TypeScript
Vite（开发/构建工具）
Element Plus、Vant（UI 组件库）
TailwindCSS（原子化 CSS 框架）
Axios（HTTP 请求）
Socket.io-client（WebSocket 通信）
highlight.js、markdown-it（代码高亮与 Markdown 渲染）

五、开发与运行
pnpm install
# 或 npm install

pnpm dev
# 或 npm run dev

访问地址
默认 http://localhost:5173

六、API 交互说明
所有 API 统一通过 /api 前缀代理到后端（见 vite.config.ts 的 proxy 配置）
登录后接口需携带 Authorization: Bearer <token>，token 存储于 localStorage
统一封装在 api.ts，便于维护和调用

七、常见问题
接口 401 未授权：请重新登录，token 失效会自动跳转登录页
静态资源无法访问：请检查后端静态目录和代理配置



# Question

- [#fitten_ref_0#] context file path: frontend\src\utils\api.ts

#fitten_ref_0# 整理一下接口

# Answer

根据您提供的 `#fitten_ref_0#` 文件内容，以下是按照功能模块整理的接口列表：

### 用户相关
- 登录：`loginApi(params: { username: string; password: string })`
- 注册：`registerApi(params: { username: string; email: string; password: string })`
- 登出：`logoutApi(username: string)`
- 获取用户信息：`GetProfileApi(params: any)`
- 更新用户信息：`UpdateProfileApi(formData: FormData)`

### 文章相关
- 显示文章列表：`ShowArticlesApi(params: any)`，`ShowArticlesByPageApi(params: any)`
- 添加文章：`AddArticleApi(params: any)`
- 更新文章：`UpdateArticleApi(params: any)`
- 删除文章：`DeleteArticleApi(params: any)`
- 伪删除文章：`FakeDeleteArticleApi(params: any)`
- 发布文章：`PublishArticleApi(formData: FormData)`
- 获取文章详情：`GetArticleByIdApi(params: any)`
- 文章评论：`CommentArticleApi(params: any)`
- 提交评论：`SubmitCommentApi(params: any)`
- 分享文章：`ShareArticleApi(id: number, is_share: number)`
- 获取所有共享文章：`GetSharedArticlesApi(params: any)`
- 恢复已删除文章：`RestoreArticleApi(params: any)`
- 获取已删除文章：`DeleteRestoredArticleApi(params: any)`

### 日志相关
- 显示日志：`ShowLogsApi(params: { page: number; limit: number })`

### 上传相关
- 上传头像：`UploadImageApi(formData: FormData)`
- 上传文件：`UploadApi(formData: FormData, config = {})`
- 上传文件分片：`UploadChunkApi(formData: FormData, config = {})`
- 合并文件分片：`MergeChunksApi(data: any)`

### 媒体相关
- 获取媒体文件：`GetMediaApi(params: { filename: string })`
- 获取媒体文件列表：`GetMediaListApi()`
- 获取视频封面及文件列表：`GetMediaCoverListApi()`

### 墙相关
- 显示墙图片：`ShowWallApi(params: any)`

### 标签相关
- 获取标签列表：`GetTagsApi()`

### 导出SQL相关
- 导出SQL文件：`ExportSqlApi()`

### 加密相关
- 加密图片：`EncryptImagesApi(params: any)`
- 加密视频：`EncryptVideosApi(params: any)`
- 单文件加密：`EncryptSingleVideoApi(params: any)`

### 大模型相关
- 聊天：`ChatApi(params: any)`

### 好友相关
- 搜索好友：`SearchFriendApi(q: string)`
- 获取好友列表：`GetFriendsApi(params: any)`
- 搜索用户：`SearchUserApi(username: string)`
- 添加好友：`AddFriendApi(user_id: number, friend_id: number)`
- 获取好友列表：`GetFriendListApi(user_id: number)`
- 保存聊天记录：`SaveChatRecordApi(user_id: number, friend_id: number, message: string)`
- 获取聊天记录：`GetChatRecordApi(user_id: number, friend_id: number)`
- 发送好友请求：`SendFriendRequestApi(from_user_id: number, to_user_id: number)`
- 获取待处理的好友请求：`GetPendingFriendRequestsApi(user_id: number)`
- 处理好友请求：`HandleFriendRequestApi(request_id: number, agree: boolean)`

### Bug相关
- 获取Bug列表：`GetBugListApi()`
- 添加Bug：`AddBugApi(params: any)`
- 更新Bug状态：`UpdateBugStatusApi(id: number, status: number, severity: string)`
- 删除Bug：`DeleteBugApi(id: number)`
- 批量删除Bug：`DeleteBugsApi(ids: number[])`
- 更新Bug：`UpdateBugApi(params: any)`

### 创意记录相关
- 获取创意列表：`GetCreativeListApi()`
- 新增创意：`AddCreativeApi(params: any)`
- 删除创意：`DeleteCreativeApi(id: number)`

### 音乐相关
- 获取音乐列表：`getMusicListApi()`
- 获取歌词：`getLyricsApi(filename: string)`
- 获取音乐文件封面：`getMusicCoverApi(filename: string)`

### 资源相关
- 获取资源列表：`GetResourceListApi()`
- 下载资源：`DownloadResourceApi(filename: string)`
- 预览资源：`PreviewResourceApi(filename: string)`
- 获取资源列表（按用户）：`GetResourceListByUserApi(user_id: number)`
- 取消分享或分享资源：`CancelShareOrShareApi(user_id: number, file_id: number, is_share: number)`
- 删除资源：`DeleteResourceApi(user_id: number, file_id: number)`
- 永久删除资源：`PermanentlyDeleteResourceApi(user_id: number, file_id: number, file_name: string)`

### 仪表盘相关
- 获取仪表盘数据：`dashboardApi()`
- 获取统计数据：`getCountApi()`
- 获取资源下载量统计：`getResourceDownloadCountApi(file_name: string, user_id: number, file_type: string)`