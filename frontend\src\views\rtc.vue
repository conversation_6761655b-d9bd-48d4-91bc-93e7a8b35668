<template>
  <div>
    <h1>视频通话 Demo</h1>
    <video ref="localVideo" autoplay muted playsinline style="width: 45%;"></video>
    <video ref="remoteVideo" autoplay playsinline style="width: 45%;"></video>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { io } from 'socket.io-client'

const localVideo = ref(null)
const remoteVideo = ref(null)
let localStream = null
let peerConnection = null
const roomId = 'room-123'
const config = {
  iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
}
const socket = io('http://**************:3000', {
  transports: ['websocket'] // 强制使用 websocket
})

// 只绑定一次 socket 事件
let socketInited = false

function createPeerConnection() {
  if (peerConnection) return
  peerConnection = new RTCPeerConnection(config)

  peerConnection.onicecandidate = (event) => {
    if (event.candidate) {
      socket.emit('signal', {
        roomId,
        data: { type: 'candidate', candidate: event.candidate }
      })
    }
  }

  peerConnection.ontrack = (event) => {
    remoteVideo.value.srcObject = event.streams[0]
    // 兼容部分浏览器
    remoteVideo.value.play?.()
  }

  localStream.getTracks().forEach(track => {
    peerConnection.addTrack(track, localStream)
  })
}

async function handleUserJoined() {
  createPeerConnection()
  const offer = await peerConnection.createOffer()
  await peerConnection.setLocalDescription(offer)
  socket.emit('signal', { roomId, data: { type: 'offer', sdp: offer } })
}

async function handleSignal({ from, data }) {
  createPeerConnection()
  if (data.type === 'offer') {
    await peerConnection.setRemoteDescription(new RTCSessionDescription(data.sdp))
    const answer = await peerConnection.createAnswer()
    await peerConnection.setLocalDescription(answer)
    socket.emit('signal', { roomId, data: { type: 'answer', sdp: answer } })
  } else if (data.type === 'answer') {
    await peerConnection.setRemoteDescription(new RTCSessionDescription(data.sdp))
  } else if (data.type === 'candidate') {
    if (data.candidate) {
      try {
        await peerConnection.addIceCandidate(new RTCIceCandidate(data.candidate))
      } catch (e) {
        // 忽略重复 candidate
      }
    }
  }
}

async function init() {
  localStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true })
  localVideo.value.srcObject = localStream
  localVideo.value.play?.()

  socket.emit('join', roomId)

  if (!socketInited) {
    socketInited = true
    socket.on('user-joined', handleUserJoined)
    socket.on('signal', handleSignal)
  }

  // 先加入者也要创建 PeerConnection
  createPeerConnection()
}

onMounted(() => {
  init()
})

onBeforeUnmount(() => {
  if (peerConnection) peerConnection.close()
  if (localStream) {
    localStream.getTracks().forEach(track => track.stop())
  }
  socket.disconnect()
})
</script>

<style scoped>
video {
  background: #000;
  margin: 8px;
  border-radius: 8px;
}
</style>