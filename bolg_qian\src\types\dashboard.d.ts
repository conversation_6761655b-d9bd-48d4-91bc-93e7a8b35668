// Dashboard相关类型定义

export interface StatItem {
  label: string
  value: number | string
}

export interface GrowthData {
  date: string
  count: number
}

export interface DownloadData {
  date: string
  file_name: string
  file_type: string
  count: number
}

export interface DashboardResponse {
  data: {
    userCount: number
    articleCount: number
    shareCount: number
    resourceCount: number
    downloadCount: number
  }
}

export interface TrendResponse {
  data: {
    userGrowth: GrowthData[]
    articlePublish: GrowthData[]
    downloadData: DownloadData[]
  }
} 