const Router = require("koa-router");
const { spawn } = require("child_process");
const db = require("../../utils/db");
const { handleResponse } = require("../../middlewares/responseHandler");

const bigModel = new Router();
let ollamaProcess = null;

bigModel.post("/chatai", async (ctx) => {
  const { ok } = ctx.request.body;
  console.log("收到大模型请求", ok);

  if (ok === 1) {
    if (ollamaProcess && !ollamaProcess.killed) {
      ctx.body = { message: "大模型服务已经启动", code: 1 };
      return;
    }

    try {
      ollamaProcess = spawn("ollama", ["run", "llama3.1"]);

      ollamaProcess.stdout.on("data", (data) => {
        console.log(`ollama stdout: ${data.toString()}`);
      });

      ollamaProcess.stderr.on("data", (data) => {
        console.error(`ollama stderr: ${data.toString()}`);
      });

      ollamaProcess.on("close", (code) => {
        console.log(`ollama 进程退出，退出码 ${code}`);
        ollamaProcess = null;
      });

      ctx.body = { message: "大模型服务启动中", code: 0 };
    } catch (err) {
      ctx.body = { message: "大模型启动失败", error: err.message, code: -1 };
    }
  } else {
    ctx.body = { message: "未触发启动" };
  }
});

// 保存AI聊天历史
bigModel.post("/save-history", async (ctx) => {
  try {
    const { user_message, bot_response, model_used = 'qwen2.5:0.5b' } = ctx.request.body;
    const user_id = ctx.state.user?.id || 1; // 从JWT中获取用户ID，如果没有则默认为1

    if (!user_message || !bot_response) {
      return handleResponse(ctx, 400, null, "用户消息和AI回复不能为空");
    }

    // 检查ai_chat_history表是否存在，如果不存在则创建
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS ai_chat_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        user_message TEXT NOT NULL,
        bot_response TEXT NOT NULL,
        model_used VARCHAR(50) DEFAULT 'qwen2.5:0.5b',
        response_time INT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;

    await db.query(createTableSQL);

    // 插入聊天历史
    const insertSQL = `
      INSERT INTO ai_chat_history (user_id, user_message, bot_response, model_used, created_at)
      VALUES (?, ?, ?, ?, NOW())
    `;

    const result = await db.query(insertSQL, [user_id, user_message, bot_response, model_used]);

    return handleResponse(ctx, 200, { id: result.insertId }, "聊天历史保存成功");
  } catch (error) {
    console.error("保存AI聊天历史失败:", error);
    return handleResponse(ctx, 500, null, "保存聊天历史失败");
  }
});

// 获取AI聊天历史
bigModel.get("/history", async (ctx) => {
  try {
    const { page = 1, limit = 20 } = ctx.query;
    const user_id = ctx.state.user?.id || 1;

    const offset = (page - 1) * limit;

    // 检查表是否存在
    const checkTableSQL = `
      SELECT COUNT(*) as count
      FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'ai_chat_history'
    `;

    const tableExists = await db.query(checkTableSQL);
    if (tableExists[0].count === 0) {
      return handleResponse(ctx, 200, { list: [], total: 0 }, "暂无聊天历史");
    }

    // 获取总数
    const countSQL = `SELECT COUNT(*) as total FROM ai_chat_history WHERE user_id = ?`;
    const countResult = await db.query(countSQL, [user_id]);
    const total = countResult[0].total;

    // 获取聊天历史列表
    const listSQL = `
      SELECT id, user_message, bot_response, model_used, created_at
      FROM ai_chat_history
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const list = await db.query(listSQL, [user_id, parseInt(limit), offset]);

    return handleResponse(ctx, 200, { list, total, page: parseInt(page), limit: parseInt(limit) }, "获取聊天历史成功");
  } catch (error) {
    console.error("获取AI聊天历史失败:", error);
    return handleResponse(ctx, 500, null, "获取聊天历史失败");
  }
});

module.exports = bigModel;