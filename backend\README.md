博客后端文档说明
一、项目简介
本项目为个人博客系统的后端，基于 Node.js（Koa 框架）开发，支持用户管理、文章管理、文件上传、即时通讯等功能。
后端采用 RESTful API 设计，部分功能支持 WebSocket 实时通信。

二、目录结构说明
目录/文件	说明
app.js	Koa 应用主入口，配置中间件、静态资源、路由等
index.js	启动服务，集成 WebSocket、WebRTC 信令等
middlewares/	中间件（如鉴权、日志、错误处理等）
plugin/	通用工具（如加密、日志、校验等）
communication/	实时通信相关（WebSocket 聊天、WebRTC 信令）
router/	业务路由（用户、文章、上传、日志、资源等接口）
utils/	工具类和数据库操作
uploads/上传文件存储目录
public/	静态资源目录
assets/	静态图片、测试图片等资源
logs/	日志文件目录
三、主要功能模块
1. 用户管理
用户注册、登录、信息修改、头像上传
JWT 鉴权
2. 文章管理
文章增删改查
评论、分享、回收站功能
3. 文件上传与管理
图片、视频、文档等文件上传
分片上传与合并
静态资源访问
4. 日志与错误处理
统一日志记录（info、error）
全局异常捕获
5. 实时通信
聊天室（WebSocket）
WebRTC 信令服务
四、接口规范
所有接口返回统一格式：
需要登录的接口需在请求头加 Authorization: Bearer <token>
五、启动与部署
安装依赖
配置环境变量（.env 文件）
启动服务
访问接口或 WebSocket 服务
六、常见问题
数据库连接失败：检查 .env 配置和数据库服务
上传失败：检查 uploads 目录权限
JWT 校验失败：确认 token 是否正确传递


根目录
.env
环境变量配置文件（如数据库、JWT 密钥等）。

app.js
Koa 应用主入口，配置中间件、静态资源、路由等。

index.js
启动 HTTP 服务，集成 WebSocket、WebRTC 信令等。

package.json
项目依赖和脚本配置。

assets/
存放静态图片、测试图片等资源。
logs/
error.log、info.log
日志文件，分别记录错误和普通日志。
middlewares/
errorHandler.js
全局错误处理，捕获异常并记录日志。

koaJwtMiddleware.js
JWT 鉴权中间件，配置白名单和 token 校验。

loggerMiddleware.js
记录每个请求的日志（方法、状态、耗时、IP）。

communication/
chat.js
WebSocket 聊天室服务端实现，管理在线人数、消息广播。

webrtc.js
WebRTC 信令服务器，转发 offer/answer/ice-candidate。

plugin/
crypto.js
密码加密与验证（bcrypt）。

logger.js
日志系统配置（log4js），支持 info/error 等多级日志。

validators.js
用户名、密码等格式校验工具。

public/
images/、media/
静态公开图片、视频文件目录。
router/
后端所有业务路由，按功能划分：

articles.js
文章相关接口（增删改查、评论、分享、回收站等）。

bigmodel.js
大模型（AI）相关接口。

bugsend.js
Bug 管理接口（增删改查、批量操作）。

create.js
创意/灵感管理接口。

daosql.js
数据库导出 SQL 文件接口。

friend.js
好友管理相关接口。

index.js
路由聚合入口，统一注册所有子路由。

jiamitupian.js
图片加密/解密接口。

logs.js
日志查询接口，支持分页、关键字、级别筛选。

media.js
视频文件管理、流式播放、封面生成等接口。

mediaguanli.js
视频文件移动、管理接口（待开发）。

resource.js
资源文件（如上传的文档、压缩包等）下载、预览接口。

responseHandler.js
通用响应封装函数。

shipingjiami.js
视频加密/解密接口，支持批量和单文件。

showwall.js
图片墙相关接口，分页获取图片、图片访问。

statusCodes.js
状态码及对应消息定义。

user.js
用户相关接口（登录、注册、信息、头像、标签等）。

upload.js
文件上传、分片上传、合并、头像上传等接口。

uploads/
上传文件的存储目录（如 avatars、articles、resource 等）。
utils/
工具类和数据库相关代码。

db.js
数据库连接池及 query 封装。

sql/
具体业务的数据库操作实现，如 articleService.js、authService.js、uploadServer.js、bugsendService.js 等，分别对应文章、用户、上传、bug 的数据库操作。

