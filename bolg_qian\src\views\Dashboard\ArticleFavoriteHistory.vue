<template>
  <div class="article-favorite-history">
    <div class="page-header">
      <h2>💖 我的收藏</h2>
      <p class="page-description">查看您收藏的精彩文章</p>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-number">{{ pagination.total }}</div>
              <div class="stats-label">总收藏</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-number">{{ getThisMonthCount() }}</div>
              <div class="stats-label">本月收藏</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-number">{{ getRecentCount() }}</div>
              <div class="stats-label">最近7天</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 收藏列表 -->
    <div class="favorites-list">
      <el-card 
        v-for="favorite in favoritesList" 
        :key="favorite.id"
        class="favorite-item"
        shadow="hover"
        @click="goToArticle(favorite.article_id)"
      >
        <div class="favorite-content">
          <div class="article-info">
            <div class="article-cover">
              <img 
                v-if="favorite.cover_image" 
                :src="getImageUrl(favorite.cover_image)" 
                :alt="favorite.title"
                @error="handleImageError"
              />
              <div v-else class="default-cover">
                <el-icon><Document /></el-icon>
              </div>
            </div>
            
            <div class="article-details">
              <h3 class="article-title">
                {{ favorite.title || '文章标题' }}
              </h3>
              <p class="article-summary">
                {{ favorite.summary || '暂无摘要' }}
              </p>
              <div class="article-meta">
                <span class="author">
                  <el-icon><User /></el-icon>
                  {{ favorite.author_name || '未知作者' }}
                </span>
                <span class="favorite-time">
                  <el-icon><span>💖</span></el-icon>
                  {{ formatTime(favorite.created_at) }}
                </span>
              </div>
            </div>
          </div>

          <div class="favorite-actions">
            <ArticleFavorite 
              :article-id="favorite.article_id"
              :show-text="false"
              :show-count="false"
              size="small"
              @click.stop
              @favorite-changed="handleFavoriteChanged"
            />
            <el-button 
              type="primary" 
              link 
              size="small"
              @click.stop="goToArticle(favorite.article_id)"
            >
              阅读
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 空状态 -->
      <div v-if="favoritesList.length === 0 && !loading" class="empty-state">
        <el-empty 
          description="还没有收藏任何文章"
          :image-size="120"
        >
          <el-button type="primary" @click="goToArticles">
            去发现好文章
          </el-button>
        </el-empty>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <el-skeleton :rows="3" animated />
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="pagination.total > 0" class="pagination-wrapper">
      <el-pagination
        :current-page="pagination.page"
        :page-size="pagination.limit"
        :total="pagination.total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="loadFavorites"
        @size-change="loadFavorites"
      />
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, User } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import request from '../../utils/index'
import ArticleFavorite from '../../components/ArticleFavorite.vue'

export default {
  name: 'ArticleFavoriteHistory',
  components: {
    Document,
    User,
    ArticleFavorite
  },
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const favoritesList = ref([])
    
    const pagination = reactive({
      page: 1,
      limit: 20,
      total: 0
    })

    // 加载收藏历史
    const loadFavorites = async () => {
      loading.value = true
      try {
        const params = {
          page: pagination.page,
          limit: pagination.limit
        }

        console.log('🚀 开始请求收藏历史，参数:', params)
        const response = await request.get('/article-favorites/history', { params })
        console.log('📦 收藏历史API响应:', response)

        if (response.code === 200) {
          favoritesList.value = response.data?.history || []
          pagination.total = response.data?.pagination?.total || 0
          console.log('✅ 数据设置成功:')
          console.log('  - favoritesList长度:', favoritesList.value.length)
          console.log('  - pagination.total:', pagination.total)
          console.log('  - favoritesList内容:', favoritesList.value)
        } else {
          console.log('❌ API返回错误:', response)
          ElMessage.error(response.message || '获取收藏历史失败')
        }
      } catch (error) {
        console.error('获取收藏历史失败:', error)
        if (error.response?.status === 401) {
          ElMessage.warning('请先登录')
          router.push('/login')
        } else {
          ElMessage.error('获取收藏历史失败，请稍后重试')
        }
      } finally {
        loading.value = false
      }
    }

    // 处理收藏状态变化
    const handleFavoriteChanged = (data) => {
      if (data.action === 'removed') {
        // 如果取消收藏，从列表中移除
        favoritesList.value = favoritesList.value.filter(
          item => item.article_id !== data.articleId
        )
        pagination.total = Math.max(0, pagination.total - 1)
      }
    }

    // 获取本月收藏数量
    const getThisMonthCount = () => {
      const now = new Date()
      const thisMonth = now.getMonth()
      const thisYear = now.getFullYear()
      
      return favoritesList.value.filter(item => {
        const date = new Date(item.created_at)
        return date.getMonth() === thisMonth && date.getFullYear() === thisYear
      }).length
    }

    // 获取最近7天收藏数量
    const getRecentCount = () => {
      const now = new Date()
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      
      return favoritesList.value.filter(item => {
        const date = new Date(item.created_at)
        return date >= sevenDaysAgo
      }).length
    }

    // 格式化时间
    const formatTime = (time) => {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 获取图片URL
    const getImageUrl = (imagePath) => {
      if (!imagePath) return ''
      if (imagePath.startsWith('http')) return imagePath
      return `/articles/${imagePath}`
    }

    // 图片加载错误处理
    const handleImageError = (event) => {
      event.target.style.display = 'none'
      event.target.parentNode.innerHTML = '<div class="default-cover"><i class="el-icon-document"></i></div>'
    }

    // 跳转到文章详情
    const goToArticle = (articleId) => {
      if (articleId) {
        router.push(`/index/details/${articleId}`)
      }
    }

    // 跳转到文章列表
    const goToArticles = () => {
      router.push('/index/allarticles')
    }

    onMounted(() => {
      loadFavorites()
    })

    return {
      loading,
      favoritesList,
      pagination,
      loadFavorites,
      handleFavoriteChanged,
      getThisMonthCount,
      getRecentCount,
      formatTime,
      getImageUrl,
      handleImageError,
      goToArticle,
      goToArticles
    }
  }
}
</script>

<style scoped>
.article-favorite-history {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-section {
  margin-bottom: 24px;
}

.stats-card {
  text-align: center;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-item {
  padding: 8px 0;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #f56c6c;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.favorites-list {
  margin-bottom: 24px;
}

.favorite-item {
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.favorite-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.favorite-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.article-info {
  display: flex;
  flex: 1;
  gap: 16px;
}

.article-cover {
  width: 80px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.article-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-cover {
  color: #c0c4cc;
  font-size: 24px;
}

.article-details {
  flex: 1;
  min-width: 0;
}

.article-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.article-summary {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.article-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.article-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.favorite-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.loading-state {
  padding: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .article-favorite-history {
    padding: 16px;
  }
  
  .favorite-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .article-info {
    width: 100%;
  }
  
  .article-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .favorite-actions {
    align-self: flex-end;
  }
}
</style>
