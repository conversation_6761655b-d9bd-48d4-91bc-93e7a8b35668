// 管理视频文件，是否移动到资源目录
const Router = require("koa-router");
const mediaguanli = new Router();
const bodyParser = require("koa-bodyparser");
const { handleResponse } = require("./responseHandler");
const logger = require("../utils/logger");
const path = require("path");
const fs = require("fs");

mediaguanli.use(bodyParser());

// 移动视频文件接口
mediaguanli.post("/move", async (ctx) => {
    // 待开发
});
 
  


mediaguanli.use(bodyParser());
