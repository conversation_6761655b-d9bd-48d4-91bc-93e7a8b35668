<template>
  <div class="effect-preview" ref="previewContainer">
    <canvas 
      v-if="effectType !== 'none'" 
      ref="canvas" 
      :width="width" 
      :height="height"
      class="preview-canvas"
    />
    <div v-else class="no-effect">
      <el-icon size="32"><Close /></el-icon>
      <span>无特效</span>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { Close } from '@element-plus/icons-vue'

export default {
  name: 'EffectPreview',
  components: {
    Close
  },
  props: {
    effectType: {
      type: String,
      default: 'none'
    },
    config: {
      type: Object,
      default: () => ({})
    },
    width: {
      type: Number,
      default: 200
    },
    height: {
      type: Number,
      default: 120
    }
  },
  setup(props) {
    const canvas = ref(null)
    const previewContainer = ref(null)
    let animationId = null
    let ctx = null

    // 初始化画布
    const initCanvas = () => {
      if (!canvas.value) return
      ctx = canvas.value.getContext('2d')
      if (!ctx) return
      
      // 清除之前的动画
      if (animationId) {
        cancelAnimationFrame(animationId)
        animationId = null
      }
      
      // 根据特效类型启动动画
      switch (props.effectType) {
        case 'meteor':
          startMeteorPreview()
          break
        case 'nightsky':
          startNightSkyPreview()
          break
        case 'particle':
          startParticlePreview()
          break
      }
    }

    // 流星雨预览
    const startMeteorPreview = () => {
      const meteors = []
      const meteorCount = Math.min(props.config.count || 3, 5) // 预览时减少数量
      
      // 创建流星
      for (let i = 0; i < meteorCount; i++) {
        meteors.push({
          x: Math.random() * props.width,
          y: Math.random() * props.height,
          length: Math.random() * 20 + 10,
          speed: Math.random() * 1 + 0.5,
          opacity: Math.random() * 0.5 + 0.5
        })
      }
      
      const animate = () => {
        if (!ctx || props.effectType !== 'meteor') return
        
        ctx.clearRect(0, 0, props.width, props.height)
        
        meteors.forEach(meteor => {
          // 绘制流星
          const gradient = ctx.createLinearGradient(
            meteor.x, meteor.y,
            meteor.x - meteor.length, meteor.y - meteor.length
          )
          
          const color = getThemeColor(props.config.theme || 'white')
          gradient.addColorStop(0, `rgba(${color}, ${meteor.opacity})`)
          gradient.addColorStop(1, `rgba(${color}, 0)`)
          
          ctx.strokeStyle = gradient
          ctx.lineWidth = (props.config.size || 2) * 0.5 // 预览时减小尺寸
          ctx.beginPath()
          ctx.moveTo(meteor.x, meteor.y)
          ctx.lineTo(meteor.x - meteor.length, meteor.y - meteor.length)
          ctx.stroke()
          
          // 更新位置
          meteor.x += meteor.speed
          meteor.y += meteor.speed
          
          // 重置位置
          if (meteor.x > props.width + meteor.length || meteor.y > props.height + meteor.length) {
            meteor.x = -meteor.length
            meteor.y = Math.random() * props.height
          }
        })
        
        animationId = requestAnimationFrame(animate)
      }
      
      animate()
    }

    // 夜空星辰预览
    const startNightSkyPreview = () => {
      const stars = []
      const starCount = Math.min(props.config.starCount || 50, 100) // 预览时减少数量
      
      // 创建星星
      for (let i = 0; i < starCount; i++) {
        stars.push({
          x: Math.random() * props.width,
          y: Math.random() * props.height,
          radius: Math.random() * 1.5 + 0.5,
          opacity: Math.random() * 0.8 + 0.2,
          twinkleSpeed: Math.random() * 0.02 + 0.01
        })
      }
      
      let time = 0
      
      const animate = () => {
        if (!ctx || props.effectType !== 'nightsky') return
        
        ctx.clearRect(0, 0, props.width, props.height)
        time += 0.02
        
        stars.forEach(star => {
          // 闪烁效果
          let opacity = star.opacity
          if (props.config.twinkle) {
            opacity = star.opacity + Math.sin(time * star.twinkleSpeed) * 0.3
          }
          
          ctx.fillStyle = `rgba(255, 255, 255, ${opacity})`
          ctx.beginPath()
          ctx.arc(star.x, star.y, star.radius, 0, Math.PI * 2)
          ctx.fill()
        })
        
        animationId = requestAnimationFrame(animate)
      }
      
      animate()
    }

    // 粒子连线预览
    const startParticlePreview = () => {
      const particles = []
      const particleCount = Math.min(props.config.particleCount || 20, 30) // 预览时减少数量
      
      // 创建粒子
      for (let i = 0; i < particleCount; i++) {
        particles.push({
          x: Math.random() * props.width,
          y: Math.random() * props.height,
          vx: (Math.random() - 0.5) * 1,
          vy: (Math.random() - 0.5) * 1,
          radius: Math.random() * 2 + 1
        })
      }
      
      const animate = () => {
        if (!ctx || props.effectType !== 'particle') return
        
        ctx.clearRect(0, 0, props.width, props.height)
        
        // 更新粒子
        particles.forEach(particle => {
          particle.x += particle.vx * 0.5
          particle.y += particle.vy * 0.5
          
          // 边界检测
          if (particle.x < 0 || particle.x > props.width) particle.vx *= -1
          if (particle.y < 0 || particle.y > props.height) particle.vy *= -1
          
          // 绘制粒子
          ctx.fillStyle = props.config.particleColor || '#409eff'
          ctx.beginPath()
          ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2)
          ctx.fill()
        })
        
        // 绘制连线
        const linkDistance = Math.min(props.config.linkDistance || 50, 60) // 预览时减小连线距离
        for (let i = 0; i < particles.length; i++) {
          for (let j = i + 1; j < particles.length; j++) {
            const dx = particles[i].x - particles[j].x
            const dy = particles[i].y - particles[j].y
            const distance = Math.sqrt(dx * dx + dy * dy)
            
            if (distance < linkDistance) {
              ctx.strokeStyle = `rgba(64, 158, 255, ${(1 - distance / linkDistance) * 0.5})`
              ctx.lineWidth = 0.5
              ctx.beginPath()
              ctx.moveTo(particles[i].x, particles[i].y)
              ctx.lineTo(particles[j].x, particles[j].y)
              ctx.stroke()
            }
          }
        }
        
        animationId = requestAnimationFrame(animate)
      }
      
      animate()
    }

    // 获取主题颜色
    const getThemeColor = (theme) => {
      const colors = {
        white: '255, 255, 255',
        rainbow: `${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}`,
        blue: '64, 158, 255',
        gold: '255, 215, 0'
      }
      return colors[theme] || colors.white
    }

    // 停止动画
    const stopAnimation = () => {
      if (animationId) {
        cancelAnimationFrame(animationId)
        animationId = null
      }
      if (ctx) {
        ctx.clearRect(0, 0, props.width, props.height)
      }
    }

    // 监听特效类型变化
    watch(() => props.effectType, () => {
      stopAnimation()
      if (props.effectType !== 'none') {
        setTimeout(initCanvas, 100) // 延迟初始化，确保DOM更新
      }
    })

    // 监听配置变化
    watch(() => props.config, () => {
      if (props.effectType !== 'none') {
        stopAnimation()
        setTimeout(initCanvas, 100)
      }
    }, { deep: true })

    onMounted(() => {
      if (props.effectType !== 'none') {
        setTimeout(initCanvas, 100)
      }
    })

    onUnmounted(() => {
      stopAnimation()
    })

    return {
      canvas,
      previewContainer
    }
  }
}
</script>

<style scoped>
.effect-preview {
  width: 100%;
  height: 100%;
  position: relative;
  background: #1a1a1a;
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.no-effect {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 12px;
  gap: 8px;
}
</style>
