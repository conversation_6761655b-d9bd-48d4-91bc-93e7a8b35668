const Router = require("koa-router");
const bodyParser = require("koa-bodyparser");
const koaBody = require("koa-body");
const path = require("path");
const fs = require("fs");

const friendService = require("../../utils/sql/friendService");
const friend = new Router();

friend.use(bodyParser());

// 聊天图片上传接口
friend.post("/chat_images", async (ctx) => {
  const file = ctx.request.files?.file;
  if (!file) {
    ctx.body = { code: 1, msg: "未上传文件" };
    return;
  }
  // 确保目标目录存在
  const uploadDir = path.join(__dirname, "../../uploads/chat_images");
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }
  // 生成唯一文件名
  const ext = path.extname(file.name);
  const filename = `chat_${Date.now()}${ext}`;
  const filePath = path.join(uploadDir, filename);

  // 移动文件到目标目录
  const reader = fs.createReadStream(file.path);
  const stream = fs.createWriteStream(filePath);
  reader.pipe(stream);
  await new Promise((resolve, reject) => {
    stream.on("finish", resolve);
    stream.on("error", reject);
  });

  const responseUrl = `chat_images/${filename}`;
  console.log('图片上传成功，返回URL:', responseUrl);

  ctx.body = {
    code: 0,
    url: responseUrl, // 去掉前面的斜杠
    filename: filename,
  };
});

// 显示聊天记录
friend.get("/history", async (ctx) => {
  const { to, userId } = ctx.query;
  console.log("Fetching chat history for:", { to, userId });
  let rows;
  if (to) {
    // 私聊历史
    rows = await friendService.getPrivateChatHistory(userId, to);
    console.log("私聊历史记录数量:", rows.length);
  } else {
    // 群聊历史
    console.log("查询群聊历史记录...");
    rows = await friendService.getGroupChatHistory();
    console.log("群聊历史记录数量:", rows.length);
    console.log("群聊历史记录:", rows);

    // 检查最新的chat_message记录，特别关注头像字段
    const latestMessages = await friendService.getLatestChatMessages();
    console.log("🔍 最新5条消息的头像字段:", latestMessages.map(msg => ({
      id: msg.id,
      from_id: msg.from_id,
      from_nickname: msg.from_nickname,
      from_avatar: msg.from_avatar,
      type: msg.type
    })));
  }
  ctx.body = { code: 0, data: rows };
});

// 搜索注册用户 返回参数 id username avatar
friend.post("/search", async (ctx) => {
  const { keyword } = ctx.request.body;
  if (!keyword) {
    ctx.body = { code: 1, msg: "关键词不能为空" };
    return;
  }
  const rows = await friendService.searchUsers(keyword);
  ctx.body = { code: 0, data: rows };
});

// 发送好友申请 如果是好友则直接返回code为200,如果已申请则返回code为1,
// 如果没有申请则插入一条记录 饭后code为0
friend.post("/apply", async (ctx) => {
  const { from_id, to_id } = ctx.request.body;
  if (!from_id || !to_id) {
    ctx.body = { code: 500, msg: "参数错误" };
    return;
  }
  // 检查是否已申请或已是好友
  const existingRequest = await friendService.checkFriendRequest(from_id, to_id);
  if (existingRequest.length) {
    ctx.body = { code: 1, msg: "已发送申请或已是好友" };
    return;
  }
  const existingFriend = await friendService.checkExistingFriend(from_id, to_id);
  if (existingFriend.length) {
    ctx.body = { code: 200, msg: "已是好友" };
    return;
  }
  // 插入好友申请记录
  await friendService.insertFriendRequest(from_id, to_id);
  ctx.body = { code: 0, msg: "申请已发送" };

});

// 获取好友申请列表
friend.get("/apply/list", async (ctx) => {
  const { user_id } = ctx.query;
  const rows = await friendService.getFriendRequestList(user_id);
  ctx.body = { code: 0, data: rows };
});

// 处理添加好友 同意 拒绝
friend.post("/apply/handle", async (ctx) => {
  const { request_id, accept } = ctx.request.body;
  const req = await friendService.getFriendRequestById(request_id);
  if (!req.length) {
    ctx.body = { code: 1, msg: "申请不存在" };
    return;
  }
  if (accept) {
    // 双向加好友，分两次插入并忽略重复
    const { from_id, to_id } = req[0];
    await friendService.addFriendRelation(from_id, to_id);
    await friendService.updateFriendRequestStatus(request_id, 'accepted');
    ctx.body = { code: 0, msg: "已同意" };
  } else {
    await friendService.updateFriendRequestStatus(request_id, 'rejected');
    ctx.body = { code: 0, msg: "已拒绝" };
  }
});

// 获取好友列表
friend.get("/list", async (ctx) => {
  const { user_id } = ctx.query;
  const rows = await friendService.getFriendList(user_id);
  ctx.body = { code: 0, data: rows };
});

// 标记离线消息为已读
friend.post("/mark-read", async (ctx) => {
  const { userId } = ctx.request.body;
  if (!userId) {
    ctx.body = { code: 1, msg: "用户ID不能为空" };
    return;
  }
  
  try {
    await friendService.markMessagesAsRead(userId);
    ctx.body = { code: 0, msg: "标记成功" };
  } catch (error) {
    console.error("标记离线消息为已读失败:", error);
    ctx.body = { code: 1, msg: "标记失败" };
  }
});

// 获取未读消息数量
friend.get("/unread-count", async (ctx) => {
  const { userId } = ctx.query;
  if (!userId) {
    ctx.body = { code: 1, msg: "用户ID不能为空" };
    return;
  }
  
  try {
    const result = await friendService.getUnreadMessageCount(userId);
    ctx.body = { code: 0, data: { count: result[0].count } };
  } catch (error) {
    console.error("获取未读消息数量失败:", error);
    ctx.body = { code: 1, msg: "获取失败" };
  }
});

module.exports = friend;


// 一共7个接口
// 1. 聊天图片上传接口
// 2. 显示聊天记录
// 3. 搜索注册用户
// 4. 发送好友申请
// 5. 获取好友申请列表
// 6. 处理添加好友（同意/拒绝）
// 7. 获取好友列表
