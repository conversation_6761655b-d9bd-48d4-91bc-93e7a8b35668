const WebSocket = require('ws')

function setupChat(server) {
  const wss = new WebSocket.Server({ server })

  // 记录在线人数
  let onlineCount = 0

  // 广播消息给所有客户端
  function broadcast(data) {
    const msg = typeof data === 'string' ? data : JSON.stringify(data)
    wss.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(msg)
      }
    })
  }

  // 广播系统消息
  function broadcastSystemMsg(message) {
    broadcast({
      id: 'system',
      nickname: '系统',
      avatar: '',
      message,
      time: Date.now()
    })
  }

  wss.on('connection', (ws) => {
    onlineCount++
    broadcastSystemMsg(`有人进入聊天室，当前在线人数：${onlineCount}`)

    ws.on('message', (msg) => {
      try {
        const data = JSON.parse(msg)
        data.time = Date.now()
        broadcast(data)
      } catch {
        ws.send(JSON.stringify({
          id: 'system',
          nickname: '系统',
          avatar: '',
          message: '消息格式错误',
          time: Date.now()
        }))
      }
    })

    ws.on('close', () => {
      onlineCount = Math.max(0, onlineCount - 1)
      broadcastSystemMsg(`有人离开聊天室，当前在线人数：${onlineCount}`)
    })
  })

  return wss
}

module.exports = setupChat