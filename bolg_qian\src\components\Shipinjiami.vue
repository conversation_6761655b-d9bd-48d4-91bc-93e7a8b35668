<!-- 替换后完整片段 -->
<template>
  <div class="video-encryptor">
    <div class="left-panel">
      <div class="header-section">
        <h2>🔐 视频加密/解密工具</h2>
        <el-tag :type="type === '1' ? 'success' : 'warning'" size="large">
          {{ type === '1' ? '🔒 加密模式' : '🔓 解密模式' }}
        </el-tag>
      </div>

      <!-- 文件输入区域 -->
      <div class="input-section">
        <el-input
          v-model="filename"
          :placeholder="getInputPlaceholder()"
          :prefix-icon="Document"
          clearable
          @keyup.enter="handleSingleFile"
          @blur="handleFilenameBlur"
        >
          <template #append>
            <el-button :icon="FolderOpened" @click="showFileSelector = true">选择</el-button>
          </template>
        </el-input>

        <!-- 文件格式提示 -->
        <div class="format-tips">
          <el-text size="small" type="info">
            {{ getFormatTips() }}
          </el-text>
          <el-text v-if="filename && getPreviewFilename() !== filename" size="small" type="success" style="display: block; margin-top: 4px;">
            💡 处理后文件名: {{ getPreviewFilename() }}
          </el-text>

          <!-- 快速选择建议 -->
          <div v-if="availableFiles.length > 0 && !filename" class="quick-suggestions">
            <el-text size="small" type="primary" style="display: block; margin-top: 8px;">
              📋 快速选择:
            </el-text>
            <div class="suggestion-buttons">
              <el-button
                v-for="file in availableFiles.slice(0, 3)"
                :key="file.name"
                size="small"
                type="primary"
                plain
                @click="filename = file.name"
                style="margin: 2px;"
              >
                {{ file.name }}
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作模式选择 -->
      <div class="mode-section">
        <el-radio-group v-model="type" size="large" @change="onModeChange">
          <el-radio-button label="1">
            <el-icon><Lock /></el-icon>
            加密
          </el-radio-button>
          <el-radio-button label="0">
            <el-icon><Unlock /></el-icon>
            解密
          </el-radio-button>
        </el-radio-group>
      </div>

      <!-- 单文件处理 -->
      <div class="action-section">
        <el-button
          type="primary"
          size="large"
          @click="handleSingleFile"
          :loading="loadingSingle"
          :disabled="!filename.trim()"
          class="action-btn"
        >
          <el-icon><VideoPlay /></el-icon>
          处理单个文件
        </el-button>

        <!-- 实时进度显示 -->
        <div v-if="loadingSingle || progressSingle > 0" class="progress-section">
          <el-progress
            :percentage="progressSingle"
            :status="getProgressStatus(progressSingle)"
            :stroke-width="8"
            striped
            striped-flow
          >
            <template #default="{ percentage }">
              <span class="progress-text">{{ percentage }}%</span>
            </template>
          </el-progress>
          <div class="progress-info">
            <el-text size="small">
              {{ currentFileInfo.name }} - {{ currentFileInfo.status }}
            </el-text>
            <el-text size="small" type="info">
              {{ formatFileSize(currentFileInfo.processed) }} / {{ formatFileSize(currentFileInfo.total) }}
            </el-text>
          </div>
        </div>
      </div>

      <el-divider>
        <el-icon><Operation /></el-icon>
      </el-divider>

      <!-- 批量处理 -->
      <div class="batch-section">
        <el-button
          type="success"
          size="large"
          @click="handleBatch"
          :loading="loadingBatch"
          class="action-btn"
        >
          <el-icon><Files /></el-icon>
          批量处理
        </el-button>

        <div v-if="loadingBatch || progressBatch > 0" class="progress-section">
          <el-progress
            :percentage="progressBatch"
            :status="getProgressStatus(progressBatch)"
            :stroke-width="8"
            striped
            striped-flow
          />
          <div class="batch-info">
            <el-text size="small">
              已处理: {{ batchInfo.completed }} / {{ batchInfo.total }} 个文件
            </el-text>
            <el-text size="small" type="info">
              预计剩余时间: {{ estimatedTime }}
            </el-text>
          </div>
        </div>
      </div>
    </div>

    <div class="right-panel">
      <!-- 性能统计 -->
      <el-card v-if="performanceStats.totalFiles > 0" class="stats-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>📊 处理统计</span>
            <el-button size="small" @click="clearStats">清除</el-button>
          </div>
        </template>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ performanceStats.totalFiles }}</div>
            <div class="stat-label">总文件数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ performanceStats.successCount }}</div>
            <div class="stat-label">成功</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ performanceStats.errorCount }}</div>
            <div class="stat-label">失败</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ formatDuration(performanceStats.totalTime) }}</div>
            <div class="stat-label">总耗时</div>
          </div>
        </div>
      </el-card>

      <!-- 日志显示 -->
      <div class="log-section">
        <div class="log-header">
          <h3>📝 操作日志</h3>
          <div class="log-controls">
            <el-button size="small" @click="clearLogs">清空日志</el-button>
            <el-button size="small" @click="exportLogs">导出日志</el-button>
          </div>
        </div>

        <div class="log-box" ref="logContainer">
          <div v-if="logs.length === 0" class="empty-logs">
            <el-empty description="暂无日志信息" :image-size="80" />
          </div>
          <div v-else>
            <div
              v-for="(log, index) in logs"
              :key="index"
              :class="['log-item', `log-${log.type}`]"
            >
              <div class="log-time">{{ log.time }}</div>
              <div class="log-content">
                <el-icon v-if="log.type === 'success'"><SuccessFilled /></el-icon>
                <el-icon v-else-if="log.type === 'error'"><CircleCloseFilled /></el-icon>
                <el-icon v-else-if="log.type === 'warning'"><WarningFilled /></el-icon>
                <el-icon v-else><InfoFilled /></el-icon>
                <span>{{ log.message }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 结果消息 -->
      <el-alert
        v-if="resultMessage"
        :title="resultMessage.title"
        :description="resultMessage.description"
        :type="resultMessage.type"
        :closable="false"
        show-icon
        class="result-alert"
      />
    </div>

    <!-- 文件选择对话框 -->
    <el-dialog v-model="showFileSelector" title="选择文件" width="600px">
      <div class="file-selector">
        <el-input
          v-model="fileSearchQuery"
          placeholder="搜索文件..."
          :prefix-icon="Search"
          clearable
        />
        <div class="file-list">
          <div
            v-for="file in filteredFiles"
            :key="file.name"
            :class="['file-item', { selected: filename === file.name }]"
            @click="selectFile(file.name)"
          >
            <el-icon><VideoPlay /></el-icon>
            <span>{{ file.name }}</span>
            <span class="file-size">{{ formatFileSize(file.size) }}</span>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="showFileSelector = false">取消</el-button>
        <el-button type="primary" @click="confirmFileSelection">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>



<script setup>
import { ref, computed, nextTick, onMounted } from "vue";
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Document, FolderOpened, Lock, Unlock, VideoPlay, Operation, Files,
  Search, SuccessFilled, CircleCloseFilled, WarningFilled, InfoFilled
} from '@element-plus/icons-vue';
import { EncryptVideosApi, EncryptSingleVideoApi, GetEncryptableFilesApi } from "@/utils/api";

// 基础状态
const type = ref("1"); // 默认加密
const filename = ref("");
const loadingSingle = ref(false);
const loadingBatch = ref(false);
const progressSingle = ref(0);
const progressBatch = ref(0);
const logs = ref([]);
const resultMessage = ref(null);

// 文件选择相关
const showFileSelector = ref(false);
const fileSearchQuery = ref("");
const availableFiles = ref([]);

// 进度信息
const currentFileInfo = ref({
  name: '',
  status: '',
  processed: 0,
  total: 0
});

const batchInfo = ref({
  completed: 0,
  total: 0,
  startTime: null
});

// 性能统计
const performanceStats = ref({
  totalFiles: 0,
  successCount: 0,
  errorCount: 0,
  totalTime: 0
});

// 计算属性
const filteredFiles = computed(() => {
  if (!fileSearchQuery.value) return availableFiles.value;
  return availableFiles.value.filter(file =>
    file.name.toLowerCase().includes(fileSearchQuery.value.toLowerCase())
  );
});

const estimatedTime = computed(() => {
  if (!batchInfo.value.startTime || batchInfo.value.completed === 0) return '--';

  const elapsed = Date.now() - batchInfo.value.startTime;
  const avgTimePerFile = elapsed / batchInfo.value.completed;
  const remaining = (batchInfo.value.total - batchInfo.value.completed) * avgTimePerFile;

  return formatDuration(remaining);
});

// 工具函数
function appendLog(message, type = 'info') {
  const log = {
    time: new Date().toLocaleTimeString(),
    message,
    type
  };
  logs.value.push(log);

  // 自动滚动到底部
  nextTick(() => {
    const container = document.querySelector('.log-box');
    if (container) {
      container.scrollTop = container.scrollHeight;
    }
  });
}

function formatFileSize(bytes) {
  if (!bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDuration(ms) {
  if (!ms) return '0秒';
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) return `${hours}小时${minutes % 60}分钟`;
  if (minutes > 0) return `${minutes}分钟${seconds % 60}秒`;
  return `${seconds}秒`;
}

function getProgressStatus(percentage) {
  if (percentage === 100) return 'success';
  if (percentage >= 80) return 'warning';
  return 'active';
}

function clearLogs() {
  logs.value = [];
  appendLog('日志已清空', 'info');
}

function clearStats() {
  performanceStats.value = {
    totalFiles: 0,
    successCount: 0,
    errorCount: 0,
    totalTime: 0
  };
}

function exportLogs() {
  const logText = logs.value.map(log =>
    `[${log.time}] [${log.type.toUpperCase()}] ${log.message}`
  ).join('\n');

  const blob = new Blob([logText], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `video-encrypt-logs-${new Date().toISOString().slice(0, 10)}.txt`;
  a.click();
  URL.revokeObjectURL(url);
}

function selectFile(fileName) {
  filename.value = fileName;
}

function confirmFileSelection() {
  showFileSelector.value = false;
  if (filename.value) {
    appendLog(`已选择文件: ${filename.value}`, 'info');
  }
}

// 文件名处理相关函数
function getInputPlaceholder() {
  if (type.value === "1") {
    return "请输入文件名（如: video 或 video.mp4）";
  } else {
    return "请输入加密文件名（如: video.mp4.enc）";
  }
}

function getFormatTips() {
  if (type.value === "1") {
    return "加密模式: 支持 .mp4, .avi, .mov, .mkv, .wmv 格式，可自动补充后缀";
  } else {
    return "解密模式: 支持 .enc 格式，可自动补充后缀";
  }
}

function getPreviewFilename() {
  if (!filename.value.trim()) return '';

  let processedName = filename.value.trim();

  if (type.value === "1") {
    // 加密模式
    if (!/\.(mp4|avi|mov|mkv|wmv)$/i.test(processedName)) {
      processedName += ".mp4";
    }
  } else {
    // 解密模式
    if (!/\.enc$/i.test(processedName)) {
      if (/\.(mp4|avi|mov|mkv|wmv)$/i.test(processedName)) {
        processedName += ".enc";
      } else {
        processedName += ".mp4.enc";
      }
    }
  }

  return processedName;
}

function handleFilenameBlur() {
  if (filename.value.trim()) {
    const preview = getPreviewFilename();
    if (preview !== filename.value) {
      // 可以选择是否自动更新文件名
      // filename.value = preview;
    }
  }
}

// 智能文件名处理函数
function processFilename(inputFilename) {
  let processedName = inputFilename.trim();

  if (type.value === "1") {
    // 加密模式：确保有视频格式后缀
    if (!/\.(mp4|avi|mov|mkv|wmv)$/i.test(processedName)) {
      // 检查是否已经是加密文件
      if (/\.enc$/i.test(processedName)) {
        ElMessage.warning("检测到加密文件，请切换到解密模式");
        return null;
      }
      processedName += ".mp4";
      appendLog(`🔧 自动补充视频后缀: ${processedName}`, 'info');
    }
  } else {
    // 解密模式：确保有 .enc 后缀
    if (!/\.enc$/i.test(processedName)) {
      if (/\.(mp4|avi|mov|mkv|wmv)$/i.test(processedName)) {
        processedName += ".enc";
      } else {
        processedName += ".mp4.enc";
      }
      appendLog(`🔧 自动补充加密后缀: ${processedName}`, 'info');
    }
  }

  return processedName;
}

// 处理单个文件
async function handleSingleFile() {
  if (!filename.value.trim()) {
    ElMessage.warning("请输入文件名！");
    return;
  }

  // 使用智能文件名处理
  const processedFilename = processFilename(filename.value);

  if (!processedFilename) {
    return; // 处理失败，错误信息已在 processFilename 中显示
  }

  // 更新显示的文件名
  filename.value = processedFilename;

  // 检查文件是否存在于可用文件列表中
  const fileExists = availableFiles.value.some(file => file.name === processedFilename);
  if (!fileExists && availableFiles.value.length > 0) {
    const shouldContinue = await ElMessageBox.confirm(
      `文件 "${processedFilename}" 在服务器上未找到。是否继续处理？`,
      '文件不存在警告',
      {
        confirmButtonText: '继续处理',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).catch(() => false);

    if (!shouldContinue) {
      return;
    }

    appendLog(`⚠️ 警告: 文件 ${processedFilename} 可能不存在`, 'warning');
  }

  const startTime = Date.now();
  loadingSingle.value = true;
  progressSingle.value = 0;

  // 重置当前文件信息
  currentFileInfo.value = {
    name: processedFilename,
    status: type.value === "1" ? "准备加密..." : "准备解密...",
    processed: 0,
    total: 0
  };

  try {
    const operation = type.value === "1" ? "加密" : "解密";
    appendLog(`🚀 开始${operation}文件: ${processedFilename}`, 'info');

    // 创建进度更新器
    const progressUpdater = createProgressUpdater('single');

    const res = await EncryptSingleVideoApi({
      type: type.value,
      filename: processedFilename
    });

    clearInterval(progressUpdater);
    progressSingle.value = 100;

    const duration = Date.now() - startTime;

    if (res.success) {
      appendLog(`✅ ${operation}成功！耗时: ${formatDuration(duration)}`, 'success');

      // 处理后端返回的日志
      if (res.logs && res.logs.length) {
        res.logs.forEach((logMsg) => appendLog(logMsg, 'info'));
      }

      // 更新统计信息
      performanceStats.value.totalFiles++;
      performanceStats.value.successCount++;
      performanceStats.value.totalTime += duration;

      resultMessage.value = {
        title: `${operation}完成`,
        description: `文件 ${processedFilename} ${operation}成功，耗时 ${formatDuration(duration)}`,
        type: 'success'
      };

      ElMessage.success(`${operation}完成！`);

    } else {
      const errorMsg = res.error || "未知错误";
      appendLog(`❌ ${operation}失败: ${errorMsg}`, 'error');

      performanceStats.value.totalFiles++;
      performanceStats.value.errorCount++;

      resultMessage.value = {
        title: `${operation}失败`,
        description: errorMsg,
        type: 'error'
      };

      ElMessage.error(`${operation}失败: ${errorMsg}`);
    }

  } catch (err) {
    clearInterval(progressUpdater);
    const errorMsg = err.message || "网络请求异常";
    appendLog(`💥 异常错误: ${errorMsg}`, 'error');

    performanceStats.value.totalFiles++;
    performanceStats.value.errorCount++;

    resultMessage.value = {
      title: "处理异常",
      description: errorMsg,
      type: 'error'
    };

    ElMessage.error(`处理异常: ${errorMsg}`);

  } finally {
    loadingSingle.value = false;
    currentFileInfo.value.status = "处理完成";
  }
}

// 进度更新器
function createProgressUpdater(mode) {
  let progress = 0;
  const maxProgress = 90; // 最大模拟进度，留10%给最终处理

  return setInterval(() => {
    if (progress < maxProgress) {
      progress += Math.random() * 15; // 随机增长
      progress = Math.min(progress, maxProgress);

      if (mode === 'single') {
        progressSingle.value = Math.floor(progress);
        currentFileInfo.value.status = type.value === "1" ? "加密中..." : "解密中...";
      } else {
        progressBatch.value = Math.floor(progress);
      }
    }
  }, 500);
}

// 批量处理函数
async function handleBatch() {
  try {
    await ElMessageBox.confirm(
      '确定要批量处理所有符合条件的文件吗？此操作不可撤销。',
      '批量处理确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
  } catch {
    return; // 用户取消
  }

  const startTime = Date.now();
  loadingBatch.value = true;
  progressBatch.value = 0;

  // 重置批量信息
  batchInfo.value = {
    completed: 0,
    total: 0,
    startTime: Date.now()
  };

  try {
    const operation = type.value === "1" ? "加密" : "解密";
    appendLog(`🚀 开始批量${operation}处理...`, 'info');

    // 创建进度更新器
    const progressUpdater = createProgressUpdater('batch');

    const res = await EncryptVideosApi({ type: type.value });

    clearInterval(progressUpdater);
    progressBatch.value = 100;

    const duration = Date.now() - startTime;

    if (res.success) {
      const processedCount = res.files ? res.files.length : 0;

      appendLog(`✅ 批量${operation}成功！处理了 ${processedCount} 个文件，耗时: ${formatDuration(duration)}`, 'success');

      // 更新统计信息
      performanceStats.value.totalFiles += processedCount;
      performanceStats.value.successCount += processedCount;
      performanceStats.value.totalTime += duration;

      batchInfo.value.completed = processedCount;
      batchInfo.value.total = processedCount;

      resultMessage.value = {
        title: `批量${operation}完成`,
        description: `成功处理 ${processedCount} 个文件，耗时 ${formatDuration(duration)}`,
        type: 'success'
      };

      ElMessage.success(`批量${operation}完成！`);

    } else {
      const errorMsg = res.error || "未知错误";
      appendLog(`❌ 批量${operation}失败: ${errorMsg}`, 'error');

      performanceStats.value.errorCount++;

      resultMessage.value = {
        title: `批量${operation}失败`,
        description: errorMsg,
        type: 'error'
      };

      ElMessage.error(`批量${operation}失败: ${errorMsg}`);
    }

  } catch (err) {
    const errorMsg = err.message || "网络请求异常";
    appendLog(`💥 批量处理异常: ${errorMsg}`, 'error');

    performanceStats.value.errorCount++;

    resultMessage.value = {
      title: "批量处理异常",
      description: errorMsg,
      type: 'error'
    };

    ElMessage.error(`批量处理异常: ${errorMsg}`);

  } finally {
    loadingBatch.value = false;
  }
}

// 组件挂载时的初始化
onMounted(() => {
  appendLog('🎬 视频加密工具已就绪', 'info');
  // 这里可以加载可用文件列表
  loadAvailableFiles();
});

// 加载可用文件列表
async function loadAvailableFiles() {
  try {
    const res = await GetEncryptableFilesApi({ type: type.value });
    if (res.success && res.files) {
      availableFiles.value = res.files;
      appendLog(`📁 已加载 ${res.files.length} 个可处理文件`, 'info');
    } else {
      availableFiles.value = [];
      appendLog('📁 未找到可处理的文件', 'warning');
    }
  } catch (error) {
    console.error('加载文件列表失败:', error);
    availableFiles.value = [];
    appendLog('❌ 加载文件列表失败', 'error');
  }
}

// 监听模式变化，重新加载文件列表
function onModeChange() {
  loadAvailableFiles();
  filename.value = ''; // 清空当前文件名
}
</script>

<style scoped>
.video-encryptor {
  display: flex;
  gap: 24px;
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  min-height: 600px;
}

.left-panel,
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.left-panel {
  max-width: 500px;
}

/* 头部区域 */
.header-section {
  text-align: center;
  margin-bottom: 24px;
}

.header-section h2 {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 16px 0;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 输入区域 */
.input-section {
  margin-bottom: 24px;
}

.format-tips {
  margin-top: 8px;
  text-align: center;
}

.quick-suggestions {
  margin-top: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.suggestion-buttons {
  margin-top: 4px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
}

.suggestion-buttons .el-button {
  font-size: 11px;
  padding: 4px 8px;
  height: auto;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 模式选择 */
.mode-section {
  margin-bottom: 24px;
  text-align: center;
}

.mode-section .el-radio-group {
  width: 100%;
}

.mode-section .el-radio-button {
  flex: 1;
}

/* 操作区域 */
.action-section {
  margin-bottom: 24px;
}

.action-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
}

/* 进度区域 */
.progress-section {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.progress-text {
  font-weight: 600;
  color: #409eff;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}

.batch-info {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}

/* 批量处理区域 */
.batch-section {
  margin-top: 16px;
}

/* 统计卡片 */
.stats-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 日志区域 */
.log-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.log-header h3 {
  margin: 0;
  font-size: 18px;
  color: #2c3e50;
}

.log-controls {
  display: flex;
  gap: 8px;
}

.log-box {
  flex: 1;
  background: #1e1e1e;
  border-radius: 12px;
  padding: 16px;
  overflow-y: auto;
  min-height: 300px;
  max-height: 400px;
  font-family: 'JetBrains Mono', 'Consolas', monospace;
  border: 1px solid #333;
}

.empty-logs {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
}

.log-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.4;
}

.log-time {
  color: #6c757d;
  font-size: 11px;
  min-width: 80px;
  font-family: monospace;
}

.log-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.log-info {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
  border-left: 3px solid #409eff;
}

.log-success {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
  border-left: 3px solid #67c23a;
}

.log-warning {
  background: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
  border-left: 3px solid #e6a23c;
}

.log-error {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
  border-left: 3px solid #f56c6c;
}

/* 结果提示 */
.result-alert {
  margin-top: 16px;
}

/* 文件选择对话框 */
.file-selector {
  max-height: 400px;
}

.file-list {
  margin-top: 16px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f5f7fa;
}

.file-item:hover {
  background-color: #f5f7fa;
}

.file-item.selected {
  background-color: #e6f7ff;
  border-color: #409eff;
}

.file-size {
  margin-left: auto;
  color: #909399;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .video-encryptor {
    flex-direction: column;
    gap: 16px;
  }

  .left-panel {
    max-width: none;
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .video-encryptor {
    padding: 16px;
    margin: 16px;
  }

  .left-panel,
  .right-panel {
    padding: 16px;
  }

  .header-section h2 {
    font-size: 20px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .log-box {
    min-height: 200px;
    max-height: 250px;
  }
}

/* 动画效果 */
.log-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 滚动条样式 */
.log-box::-webkit-scrollbar {
  width: 6px;
}

.log-box::-webkit-scrollbar-track {
  background: #2c2c2c;
  border-radius: 3px;
}

.log-box::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 3px;
}

.log-box::-webkit-scrollbar-thumb:hover {
  background: #777;
}
</style>

