// 本地大模型配置文件
export interface LLMConfig {
  name: string;
  apiBase: string;
  modelName: string;
  timeout: number;
  maxRetries: number;
  requestFormat: string;
  description: string;
}

export const LLM_PROVIDERS: Record<string, LLMConfig> = {
  ollama: {
    name: 'Ollama',
    apiBase: 'http://localhost:11434/api/chat',
    modelName: 'qwen2.5:1.5b',
    timeout: 30000,
    maxRetries: 3,
    requestFormat: 'ollama',
    description: 'Ollama 本地部署，支持多种开源模型'
  }
};

// 默认使用的提供商
export const DEFAULT_PROVIDER = 'ollama';

// 获取当前配置（固定使用 Ollama）
export function getCurrentConfig(): LLMConfig {
  return LLM_PROVIDERS.ollama;
}

// 翻译练习相关的提示词模板
export const PROMPT_TEMPLATES = {
  chineseGeneration: [
    "请生成一句适合英语初学者翻译的中文日常对话，长度12-18字，内容关于日常生活、工作或学习，不要包含英文字母或特殊符号。",
    "请创建一个简单的中文句子，适合英语学习者练习翻译，内容涉及家庭、朋友或兴趣爱好，长度10-16字，纯中文表达。",
    "请写一句中文日常用语，难度适中，适合翻译成英文练习，内容可以是描述天气、食物或活动，长度12-20字。",
    "请生成一句中文句子，适合英语学习，内容关于购物、旅行或娱乐，长度10-18字，使用简单词汇。",
    "请创建一个中文表达，适合翻译练习，内容涉及时间、地点或人物描述，长度12-16字，语言自然。"
  ],
  
  translationInstruction: function(chinese) {
    return `请将下面的中文句子翻译成自然、地道的英文，要求：
1. 使用简单易懂的词汇，适合英语学习者
2. 语法正确，表达自然
3. 只返回英文翻译结果，不要其他解释

中文句子：${chinese}`;
  },

  difficultyLevels: {
    beginner: "请使用最基础的英语词汇和语法结构",
    intermediate: "请使用中等难度的词汇，可以包含一些常用短语",
    advanced: "可以使用较复杂的词汇和语法结构"
  }
};

// 验证配置的有效性
export function validateConfig(config: LLMConfig): boolean {
  return !!(
    config.name &&
    config.apiBase &&
    config.modelName &&
    config.timeout > 0 &&
    config.maxRetries >= 0 &&
    config.requestFormat
  );
}

