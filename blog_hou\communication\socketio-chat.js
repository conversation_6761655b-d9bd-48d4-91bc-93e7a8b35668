// Socket.IO 聊天服务
const { Server } = require("socket.io");
const db = require("../utils/db");
const logger = require("../plugin/logger");

const SYSTEM_ID = "system";
const SYSTEM_NICKNAME = "系统";

// 在线用户管理
const onlineUsers = new Map(); // socketId -> userInfo
const userSockets = new Map(); // userId -> socketId

// 统计信息
const stats = {
  totalConnections: 0,
  activeConnections: 0,
  messagesSent: 0,
  messagesReceived: 0,
  errors: 0,
  startTime: Date.now()
};

// 配置
const MAX_CONNECTIONS = 1000;
const MESSAGE_RATE_LIMIT = 100; // 每分钟最大消息数

// 工具函数
const buildSystemMessage = (message) => {
  return {
    id: SYSTEM_ID,
    nickname: SYSTEM_NICKNAME,
    avatar: "",
    message,
    type: "text",
    time: Date.now(),
  };
};

// 获取在线用户列表
const getOnlineUsersList = () => {
  const users = [];
  onlineUsers.forEach((userInfo, socketId) => {
    users.push({
      ...userInfo,
      lastSeen: Date.now(),
      connectionTime: Date.now() - (userInfo.connectedAt || Date.now())
    });
  });
  return users;
};

// 获取统计信息
const getStats = () => {
  return {
    ...stats,
    activeConnections: onlineUsers.size,
    uptime: Date.now() - stats.startTime
  };
};

// 数据库操作函数
async function saveMessage(msg) {
  const {
    id: from_id,
    nickname: from_nickname,
    avatar: from_avatar,
    to,
    type = "text",
    message,
    url = null,
    filename = null,
    time,
  } = msg;

  console.log("保存群聊消息到数据库:", {
    from_id,
    from_nickname,
    from_avatar,
    to,
    type,
    message,
    url,
    filename,
    time
  });

  // 群聊
  await db.query(
    `INSERT INTO chat_message 
    (from_id, from_nickname, from_avatar, to_id, type, message, url, filename, time)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
    [
      from_id,
      from_nickname,
      from_avatar,
      to || null,
      type,
      message || "",
      url || null,
      filename,
      time,
    ]
  );
  
  console.log("群聊消息保存成功");
}

// 存储离线消息（未读）
async function saveOfflineMessage(msg, isRead = false) {
  console.log("保存私聊消息到数据库:", {
    from_id: msg.id,
    from_nickname: msg.nickname,
    from_avatar: msg.avatar,
    to_id: msg.to,
    type: msg.type || "text",
    message: msg.message || "",
    url: msg.url || null,
    filename: msg.filename || null,
    time: msg.time,
    is_read: isRead ? 1 : 0
  });

  await db.query(
    `INSERT INTO offline_messages (from_id, from_nickname, from_avatar, to_id, type, message, url, filename, time, is_read)
     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
    [
      msg.id,
      msg.nickname,
      msg.avatar,
      msg.to,
      msg.type || "text",
      msg.message || "",
      msg.url || null,
      msg.filename || null,
      msg.time,
      isRead ? 1 : 0
    ]
  );
  
  console.log("私聊消息保存成功");
}

// 是否为好友
const isFriend = async (userId, friendId) => {
  const rows = await db.query(
    "SELECT 1 FROM friends WHERE user_id=? AND friend_id=?",
    [userId, friendId]
  );
  return rows.length > 0;
};

// 主要的Socket.IO设置函数
function setupSocketIOChat(server) {
  const io = new Server(server, {
    cors: {
      origin: "*", // 生产环境中应该设置具体的域名
      methods: ["GET", "POST"],
      credentials: true
    },
    pingTimeout: 60000,
    pingInterval: 25000
  });

  io.on("connection", (socket) => {
    stats.totalConnections++;
    stats.activeConnections++;
    
    logger.info('新Socket.IO连接', {
      socketId: socket.id,
      totalConnections: stats.activeConnections
    });

    // 用户加入
    socket.on("join", async (userInfo) => {
      try {
        // 检查连接数限制
        if (stats.activeConnections >= MAX_CONNECTIONS) {
          logger.warn('连接数达到上限，拒绝新连接', {
            activeConnections: stats.activeConnections,
            maxConnections: MAX_CONNECTIONS,
            socketId: socket.id
          });
          socket.emit("error", { message: "服务器繁忙，请稍后再试" });
          socket.disconnect();
          return;
        }

        // 存储用户信息
        const userData = {
          ...userInfo,
          socketId: socket.id,
          connectedAt: Date.now()
        };
        
        onlineUsers.set(socket.id, userData);
        userSockets.set(userInfo.id, socket.id);
        
        socket.userId = userInfo.id;
        socket.userInfo = userData;

        logger.info('用户加入聊天', {
          socketId: socket.id,
          userId: userInfo.id,
          nickname: userInfo.nickname
        });

        // 广播在线用户列表
        const onlineUsersList = getOnlineUsersList();
        io.emit("onlineUsers", { users: onlineUsersList });

        // 发送系统消息
        const systemMsg = buildSystemMessage(`${userInfo.nickname} 加入了聊天室`);
        socket.broadcast.emit("message", systemMsg);
        
        // 保存系统消息到数据库
        await saveMessage(systemMsg);

      } catch (error) {
        logger.error('用户加入失败', { error: error.message, socketId: socket.id });
        socket.emit("error", { message: "加入聊天室失败" });
      }
    });

    // 处理聊天消息
    socket.on("message", async (data) => {
      try {
        stats.messagesReceived++;
        
        logger.info('收到消息', {
          socketId: socket.id,
          userId: socket.userId,
          type: data.type,
          to: data.to
        });

        // 验证消息
        if (!data.message && data.type !== 'image') {
          socket.emit("error", { message: "消息内容不能为空" });
          return;
        }

        const messageData = {
          ...data,
          time: Date.now(),
          socketId: socket.id
        };

        if (data.to) {
          // 私聊消息
          const targetSocketId = userSockets.get(data.to);
          if (targetSocketId) {
            // 目标用户在线，直接发送
            io.to(targetSocketId).emit("message", messageData);
            socket.emit("message", messageData); // 也发送给发送者
            
            // 保存为已读的离线消息
            await saveOfflineMessage(messageData, true);
          } else {
            // 目标用户离线，保存为未读消息
            await saveOfflineMessage(messageData, false);
            socket.emit("message", messageData); // 发送给发送者确认
          }
        } else {
          // 群聊消息
          io.emit("message", messageData);
          await saveMessage(messageData);
        }

        stats.messagesSent++;

      } catch (error) {
        stats.errors++;
        logger.error('处理消息失败', { 
          error: error.message, 
          socketId: socket.id,
          userId: socket.userId 
        });
        socket.emit("error", { message: "消息发送失败" });
      }
    });

    // 处理断开连接
    socket.on("disconnect", async (reason) => {
      try {
        stats.activeConnections--;
        
        const userInfo = onlineUsers.get(socket.id);
        if (userInfo) {
          onlineUsers.delete(socket.id);
          userSockets.delete(userInfo.id);

          logger.info('用户断开连接', {
            socketId: socket.id,
            userId: userInfo.id,
            nickname: userInfo.nickname,
            reason,
            activeConnections: stats.activeConnections
          });

          // 广播在线用户列表
          const onlineUsersList = getOnlineUsersList();
          socket.broadcast.emit("onlineUsers", { users: onlineUsersList });

          // 发送系统消息
          const systemMsg = buildSystemMessage(`${userInfo.nickname} 离开了聊天室`);
          socket.broadcast.emit("message", systemMsg);
          
          // 保存系统消息到数据库
          await saveMessage(systemMsg);
        }

      } catch (error) {
        logger.error('处理断开连接失败', { 
          error: error.message, 
          socketId: socket.id 
        });
      }
    });

    // 处理错误
    socket.on("error", (error) => {
      stats.errors++;
      logger.error('Socket错误', {
        socketId: socket.id,
        userId: socket.userId,
        error: error.message
      });
    });
  });

  // 添加管理方法
  io.getStats = () => getStats();
  io.getOnlineUsers = () => getOnlineUsersList();
  io.getConnectionCount = () => stats.activeConnections;

  logger.info('Socket.IO聊天服务器已启动', {
    maxConnections: MAX_CONNECTIONS,
    pingTimeout: 60000,
    pingInterval: 25000
  });

  return io;
}

module.exports = setupSocketIOChat;
