const Router = require("koa-router");
const db = require("../../utils/db");
const { handleResponse } = require("../../middlewares/responseHandler");
const logger = require("../../plugin/logger");
const fs = require("fs");
const path = require("path");

const videoManagement = new Router();

// 定义路径
const mediaDir = path.join(__dirname, "../../public/media");
const coversDir = path.join(mediaDir, "covers");

// 视频文件扩展名
const isVideoFile = (filename) => {
  const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'];
  return videoExtensions.some(ext => filename.toLowerCase().endsWith(ext));
};

// ==================== 视频列表管理 ====================

// 获取视频管理列表
videoManagement.get("/list", async (ctx) => {
  try {
    const { 
      page = 1, 
      pageSize = 12, 
      search = '', 
      category = '', 
      status = '', 
      sortBy = 'upload_time',
      sortOrder = 'desc'
    } = ctx.query;

    logger.info(`获取视频管理列表: 页码=${page}, 每页=${pageSize}, 搜索="${search}", 分类="${category}", 状态="${status}"`);

    let whereConditions = [];
    let queryParams = [];

    // 搜索条件
    if (search) {
      whereConditions.push("(vm.title LIKE ? OR vm.file_name LIKE ? OR vm.description LIKE ?)");
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    // 分类筛选
    if (category) {
      whereConditions.push("vm.category = ?");
      queryParams.push(category);
    }

    // 状态筛选
    if (status) {
      whereConditions.push("vm.status = ?");
      queryParams.push(status);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 排序字段映射
    const sortFields = {
      'upload_time': 'vm.upload_time',
      'file_size': 'vm.file_size',
      'title': 'vm.title',
      'category': 'vm.category',
      'status': 'vm.status',
      'play_count': 'ms.play_count',
      'like_count': 'ms.like_count'
    };

    const orderField = sortFields[sortBy] || 'vm.upload_time';
    const orderDirection = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    // 获取实际的视频文件列表
    let videoFiles = [];
    try {
      if (fs.existsSync(mediaDir)) {
        videoFiles = fs.readdirSync(mediaDir).filter(file => {
          const filePath = path.join(mediaDir, file);
          return fs.statSync(filePath).isFile() && isVideoFile(file);
        });
      }
    } catch (error) {
      logger.warn("读取媒体目录失败:", error.message);
    }

    // 构建文件信息数组
    let allVideos = [];
    for (const filename of videoFiles) {
      try {
        const filePath = path.join(mediaDir, filename);
        const stats = fs.statSync(filePath);

        // 从video_management表获取管理信息
        const vmResult = await db.query("SELECT * FROM video_management WHERE file_name = ?", [filename]);
        const vmData = vmResult.length > 0 ? vmResult[0] : null;

        // 从media_stats表获取统计信息
        const msResult = await db.query("SELECT * FROM media_stats WHERE file_name = ?", [filename]);
        const msData = msResult.length > 0 ? msResult[0] : null;

        // 从files表获取上传信息
        const filesResult = await db.query("SELECT * FROM files WHERE file_name = ?", [filename]);
        const fileData = filesResult.length > 0 ? filesResult[0] : null;

        // 从users表获取用户信息
        let userData = null;
        if (fileData && fileData.user_id) {
          const userResult = await db.query("SELECT username FROM users WHERE id = ?", [fileData.user_id]);
          userData = userResult.length > 0 ? userResult[0] : null;
        }

        const videoInfo = {
          file_name: filename,
          title: vmData?.title || filename.replace(/\.[^/.]+$/, ""),
          description: vmData?.description || null,
          category: vmData?.category || '其他',
          tags: vmData?.tags ? JSON.parse(vmData.tags) : [],
          status: vmData?.status || 'online',
          file_size: stats.size,
          upload_time: fileData?.upload_time || stats.mtime.getTime(),
          upload_user_id: fileData?.user_id || null,
          upload_username: userData?.username || null,
          play_count: msData?.play_count || 0,
          like_count: msData?.like_count || 0,
          collect_count: msData?.collect_count || 0,
          duration: msData?.duration || null,
          resolution: msData?.resolution || null,
          codec: msData?.codec || null
        };

        allVideos.push(videoInfo);
      } catch (error) {
        logger.warn(`处理视频文件 ${filename} 失败:`, error.message);
      }
    }

    // 应用搜索过滤
    if (search) {
      allVideos = allVideos.filter(video =>
        video.title.toLowerCase().includes(search.toLowerCase()) ||
        video.file_name.toLowerCase().includes(search.toLowerCase()) ||
        (video.description && video.description.toLowerCase().includes(search.toLowerCase()))
      );
    }

    // 应用分类过滤
    if (category) {
      allVideos = allVideos.filter(video => video.category === category);
    }

    // 应用状态过滤
    if (status) {
      allVideos = allVideos.filter(video => video.status === status);
    }

    // 排序
    allVideos.sort((a, b) => {
      let aValue, bValue;
      switch (sortBy) {
        case 'upload_time':
          aValue = a.upload_time || 0;
          bValue = b.upload_time || 0;
          break;
        case 'file_size':
          aValue = a.file_size || 0;
          bValue = b.file_size || 0;
          break;
        case 'title':
          aValue = a.title || '';
          bValue = b.title || '';
          break;
        case 'category':
          aValue = a.category || '';
          bValue = b.category || '';
          break;
        case 'play_count':
          aValue = a.play_count || 0;
          bValue = b.play_count || 0;
          break;
        case 'like_count':
          aValue = a.like_count || 0;
          bValue = b.like_count || 0;
          break;
        default:
          aValue = a.upload_time || 0;
          bValue = b.upload_time || 0;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    // 分页
    const total = allVideos.length;
    const offset = (page - 1) * pageSize;
    const videos = allVideos.slice(offset, offset + parseInt(pageSize));

    // 处理视频数据
    const processedVideos = videos.map(video => ({
      ...video,
      cover_url: `/media/covers/${path.basename(video.file_name, path.extname(video.file_name))}.jpg`,
      video_url: `/media/${video.file_name}`,
      file_size_mb: video.file_size ? (video.file_size / 1024 / 1024).toFixed(2) : null,
      duration_formatted: video.duration ? formatDuration(video.duration) : null,
      upload_time_formatted: video.upload_time ? new Date(video.upload_time).toLocaleString('zh-CN') : null
    }));

    return handleResponse(ctx, 200, {
      list: processedVideos,
      total,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });

  } catch (error) {
    logger.error("获取视频管理列表失败:", error);
    return handleResponse(ctx, 500, null, "获取视频列表失败");
  }
});

// 获取视频详情
videoManagement.get("/detail/:fileName", async (ctx) => {
  try {
    const { fileName } = ctx.params;
    const decodedFileName = decodeURIComponent(fileName);

    // 首先检查物理文件是否存在
    const filePath = path.join(mediaDir, decodedFileName);
    if (!fs.existsSync(filePath) || !isVideoFile(decodedFileName)) {
      return handleResponse(ctx, 404, null, "视频文件不存在");
    }

    // 获取文件信息
    const stats = fs.statSync(filePath);

    // 从video_management表获取管理信息
    const vmResult = await db.query("SELECT * FROM video_management WHERE file_name = ?", [decodedFileName]);
    const vmData = vmResult.length > 0 ? vmResult[0] : null;

    // 从media_stats表获取统计信息
    const msResult = await db.query("SELECT * FROM media_stats WHERE file_name = ?", [decodedFileName]);
    const msData = msResult.length > 0 ? msResult[0] : null;

    // 从files表获取上传信息
    const filesResult = await db.query("SELECT * FROM files WHERE file_name = ?", [decodedFileName]);
    const fileData = filesResult.length > 0 ? filesResult[0] : null;

    // 从users表获取用户信息
    let userData = null;
    if (fileData && fileData.user_id) {
      const userResult = await db.query("SELECT username FROM users WHERE id = ?", [fileData.user_id]);
      userData = userResult.length > 0 ? userResult[0] : null;
    }

    // 构建视频信息
    const video = {
      file_name: decodedFileName,
      title: vmData?.title || decodedFileName.replace(/\.[^/.]+$/, ""),
      description: vmData?.description || null,
      category: vmData?.category || msData?.category || '其他',
      tags: vmData?.tags ? JSON.parse(vmData.tags) : [],
      status: vmData?.status || 'online',
      file_size: stats.size,
      file_size_mb: (stats.size / 1024 / 1024).toFixed(2),
      upload_time: fileData?.created_at ? new Date(fileData.created_at).getTime() : stats.mtime.getTime(),
      upload_time_formatted: fileData?.created_at ? new Date(fileData.created_at).toLocaleString('zh-CN') : stats.mtime.toLocaleString('zh-CN'),
      upload_user_id: fileData?.user_id || null,
      upload_username: userData?.username || null,
      play_count: msData?.play_count || 0,
      like_count: msData?.like_count || 0,
      collect_count: msData?.collect_count || 0,
      duration: msData?.duration || null,
      duration_formatted: msData?.duration ? formatDuration(msData.duration) : null,
      resolution: msData?.resolution || null,
      codec: msData?.codec || null,
      cover_url: `/media/covers/${path.basename(decodedFileName, path.extname(decodedFileName))}.jpg`,
      video_url: `/media/${decodedFileName}`,
      file_type: fileData?.file_type || 'video'
    };

    return handleResponse(ctx, 200, video);

  } catch (error) {
    logger.error("获取视频详情失败:", error);
    return handleResponse(ctx, 500, null, "获取视频详情失败");
  }
});

// ==================== 视频信息编辑 ====================

// 更新视频信息
videoManagement.put("/update/:fileName", async (ctx) => {
  try {
    const { fileName } = ctx.params;
    const { title, description, category, tags, status } = ctx.request.body;
    const decodedFileName = decodeURIComponent(fileName);

    logger.info(`更新视频信息: ${fileName}`);

    // 首先检查物理文件是否存在
    const filePath = path.join(mediaDir, decodedFileName);
    if (!fs.existsSync(filePath) || !isVideoFile(decodedFileName)) {
      return handleResponse(ctx, 404, null, "视频文件不存在");
    }

    // 验证状态值
    if (status && !['online', 'offline'].includes(status)) {
      return handleResponse(ctx, 400, null, "状态值无效");
    }

    // 检查video_management表中是否有记录
    const checkSql = "SELECT * FROM video_management WHERE file_name = ?";
    const existing = await db.query(checkSql, [decodedFileName]);

    if (existing.length === 0) {
      // 如果没有记录，创建一个新记录
      const insertSql = `
        INSERT INTO video_management (
          file_name, title, description, category, tags, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
      `;
      await db.query(insertSql, [
        decodedFileName,
        title || decodedFileName.replace(/\.[^/.]+$/, ""),
        description || null,
        category || '其他',
        tags ? JSON.stringify(tags) : JSON.stringify([]),
        status || 'online'
      ]);
    } else {
      // 如果有记录，更新字段
      const updateFields = [];
      const updateValues = [];

      if (title !== undefined) {
        updateFields.push("title = ?");
        updateValues.push(title);
      }
      if (description !== undefined) {
        updateFields.push("description = ?");
        updateValues.push(description);
      }
      if (category !== undefined) {
        updateFields.push("category = ?");
        updateValues.push(category);
      }
      if (tags !== undefined) {
        updateFields.push("tags = ?");
        updateValues.push(JSON.stringify(tags));
      }
      if (status !== undefined) {
        updateFields.push("status = ?");
        updateValues.push(status);
      }

      if (updateFields.length > 0) {
        updateFields.push("updated_at = NOW()");
        updateValues.push(decodedFileName);
        const updateSql = `UPDATE video_management SET ${updateFields.join(', ')} WHERE file_name = ?`;
        await db.query(updateSql, updateValues);
      }
    }

    logger.info(`视频信息更新成功: ${fileName}`);
    return handleResponse(ctx, 200, { success: true }, "视频信息更新成功");

  } catch (error) {
    logger.error("更新视频信息失败:", error);
    return handleResponse(ctx, 500, null, "更新视频信息失败");
  }
});

// ==================== 上架下架管理 ====================

// 上架视频
videoManagement.post("/online/:fileName", async (ctx) => {
  try {
    const { fileName } = ctx.params;
    const decodedFileName = decodeURIComponent(fileName);

    // 首先检查物理文件是否存在
    const filePath = path.join(mediaDir, decodedFileName);
    if (!fs.existsSync(filePath) || !isVideoFile(decodedFileName)) {
      return handleResponse(ctx, 404, null, "视频文件不存在");
    }

    // 检查video_management表中是否有记录
    const checkSql = "SELECT * FROM video_management WHERE file_name = ?";
    const existing = await db.query(checkSql, [decodedFileName]);

    if (existing.length === 0) {
      // 如果没有记录，创建一个新记录
      const insertSql = `
        INSERT INTO video_management (file_name, title, status, created_at, updated_at)
        VALUES (?, ?, 'online', NOW(), NOW())
      `;
      await db.query(insertSql, [
        decodedFileName,
        decodedFileName.replace(/\.[^/.]+$/, "")
      ]);
    } else {
      // 如果有记录，更新状态
      const updateSql = "UPDATE video_management SET status = 'online', updated_at = NOW() WHERE file_name = ?";
      await db.query(updateSql, [decodedFileName]);
    }

    logger.info(`视频上架成功: ${fileName}`);
    return handleResponse(ctx, 200, { success: true }, "视频上架成功");

  } catch (error) {
    logger.error("视频上架失败:", error);
    return handleResponse(ctx, 500, null, "视频上架失败");
  }
});

// 下架视频
videoManagement.post("/offline/:fileName", async (ctx) => {
  try {
    const { fileName } = ctx.params;
    const decodedFileName = decodeURIComponent(fileName);

    // 首先检查物理文件是否存在
    const filePath = path.join(mediaDir, decodedFileName);
    if (!fs.existsSync(filePath) || !isVideoFile(decodedFileName)) {
      return handleResponse(ctx, 404, null, "视频文件不存在");
    }

    // 检查video_management表中是否有记录
    const checkSql = "SELECT * FROM video_management WHERE file_name = ?";
    const existing = await db.query(checkSql, [decodedFileName]);

    if (existing.length === 0) {
      // 如果没有记录，创建一个新记录
      const insertSql = `
        INSERT INTO video_management (file_name, title, status, created_at, updated_at)
        VALUES (?, ?, 'offline', NOW(), NOW())
      `;
      await db.query(insertSql, [
        decodedFileName,
        decodedFileName.replace(/\.[^/.]+$/, "")
      ]);
    } else {
      // 如果有记录，更新状态
      const updateSql = "UPDATE video_management SET status = 'offline', updated_at = NOW() WHERE file_name = ?";
      await db.query(updateSql, [decodedFileName]);
    }

    logger.info(`视频下架成功: ${fileName}`);
    return handleResponse(ctx, 200, { success: true }, "视频下架成功");

  } catch (error) {
    logger.error("视频下架失败:", error);
    return handleResponse(ctx, 500, null, "视频下架失败");
  }
});

// 批量上架下架
videoManagement.post("/batch-status", async (ctx) => {
  try {
    const { fileNames, status } = ctx.request.body;

    if (!fileNames || !Array.isArray(fileNames) || fileNames.length === 0) {
      return handleResponse(ctx, 400, null, "文件名列表不能为空");
    }

    if (!['online', 'offline'].includes(status)) {
      return handleResponse(ctx, 400, null, "状态值无效");
    }

    let affectedCount = 0;

    // 逐个处理每个文件
    for (const fileName of fileNames) {
      try {
        // 检查物理文件是否存在
        const filePath = path.join(mediaDir, fileName);
        if (!fs.existsSync(filePath) || !isVideoFile(fileName)) {
          logger.warn(`跳过不存在的视频文件: ${fileName}`);
          continue;
        }

        // 检查video_management表中是否有记录
        const checkSql = "SELECT * FROM video_management WHERE file_name = ?";
        const existing = await db.query(checkSql, [fileName]);

        if (existing.length === 0) {
          // 如果没有记录，创建一个新记录
          const insertSql = `
            INSERT INTO video_management (file_name, title, status, created_at, updated_at)
            VALUES (?, ?, ?, NOW(), NOW())
          `;
          await db.query(insertSql, [
            fileName,
            fileName.replace(/\.[^/.]+$/, ""),
            status
          ]);
        } else {
          // 如果有记录，更新状态
          const updateSql = "UPDATE video_management SET status = ?, updated_at = NOW() WHERE file_name = ?";
          await db.query(updateSql, [status, fileName]);
        }

        affectedCount++;
      } catch (error) {
        logger.error(`处理文件 ${fileName} 失败:`, error);
      }
    }

    logger.info(`批量${status === 'online' ? '上架' : '下架'}成功: ${affectedCount} 个视频`);
    return handleResponse(ctx, 200, {
      success: true,
      affected: affectedCount
    }, `批量${status === 'online' ? '上架' : '下架'}成功`);

  } catch (error) {
    logger.error("批量操作失败:", error);
    return handleResponse(ctx, 500, null, "批量操作失败");
  }
});

// ==================== 分类管理 ====================

// 获取分类列表
videoManagement.get("/categories", async (ctx) => {
  try {
    const sql = `
      SELECT
        vc.*,
        COUNT(vm.id) as video_count
      FROM video_categories vc
      LEFT JOIN video_management vm ON vc.name = vm.category
      WHERE vc.is_active = TRUE
      GROUP BY vc.id
      ORDER BY vc.sort_order ASC, vc.name ASC
    `;

    const categories = await db.query(sql);
    return handleResponse(ctx, 200, categories);

  } catch (error) {
    logger.error("获取分类列表失败:", error);
    return handleResponse(ctx, 500, null, "获取分类列表失败");
  }
});

// 添加分类
videoManagement.post("/categories", async (ctx) => {
  try {
    const { name, description, sort_order = 0 } = ctx.request.body;

    if (!name) {
      return handleResponse(ctx, 400, null, "分类名称不能为空");
    }

    const sql = "INSERT INTO video_categories (name, description, sort_order) VALUES (?, ?, ?)";
    await db.query(sql, [name, description, sort_order]);

    logger.info(`添加分类成功: ${name}`);
    return handleResponse(ctx, 200, { success: true }, "分类添加成功");

  } catch (error) {
    if (error.code === 'ER_DUP_ENTRY') {
      return handleResponse(ctx, 400, null, "分类名称已存在");
    }
    logger.error("添加分类失败:", error);
    return handleResponse(ctx, 500, null, "添加分类失败");
  }
});

// ==================== 标签管理 ====================

// 获取标签列表
videoManagement.get("/tags", async (ctx) => {
  try {
    const sql = "SELECT * FROM video_tags ORDER BY usage_count DESC, name ASC";
    const tags = await db.query(sql);
    return handleResponse(ctx, 200, tags);

  } catch (error) {
    logger.error("获取标签列表失败:", error);
    return handleResponse(ctx, 500, null, "获取标签列表失败");
  }
});

// 添加标签
videoManagement.post("/tags", async (ctx) => {
  try {
    const { name, color = '#1890ff' } = ctx.request.body;

    if (!name) {
      return handleResponse(ctx, 400, null, "标签名称不能为空");
    }

    const sql = "INSERT INTO video_tags (name, color) VALUES (?, ?)";
    await db.query(sql, [name, color]);

    logger.info(`添加标签成功: ${name}`);
    return handleResponse(ctx, 200, { success: true }, "标签添加成功");

  } catch (error) {
    if (error.code === 'ER_DUP_ENTRY') {
      return handleResponse(ctx, 400, null, "标签名称已存在");
    }
    logger.error("添加标签失败:", error);
    return handleResponse(ctx, 500, null, "添加标签失败");
  }
});

// ==================== 视频删除 ====================

// 删除视频
videoManagement.delete("/delete/:fileName", async (ctx) => {
  try {
    const { fileName } = ctx.params;
    const decodedFileName = decodeURIComponent(fileName);

    // 首先检查物理文件是否存在
    const videoPath = path.join(mediaDir, decodedFileName);
    if (!fs.existsSync(videoPath) || !isVideoFile(decodedFileName)) {
      return handleResponse(ctx, 404, null, "视频文件不存在");
    }

    // 删除物理文件
    const coverPath = path.join(coversDir, `${path.basename(decodedFileName, path.extname(decodedFileName))}.jpg`);

    try {
      if (fs.existsSync(videoPath)) {
        fs.unlinkSync(videoPath);
        logger.info(`删除视频文件: ${decodedFileName}`);
      }
      if (fs.existsSync(coverPath)) {
        fs.unlinkSync(coverPath);
        logger.info(`删除封面文件: ${path.basename(coverPath)}`);
      }
    } catch (fileError) {
      logger.warn(`删除文件失败: ${fileError.message}`);
    }

    // 删除数据库记录（如果存在）
    await db.query("DELETE FROM video_management WHERE file_name = ?", [decodedFileName]);
    await db.query("DELETE FROM media_stats WHERE file_name = ?", [decodedFileName]);
    await db.query("DELETE FROM media_likes WHERE file_name = ?", [decodedFileName]);
    await db.query("DELETE FROM media_collections WHERE file_name = ?", [decodedFileName]);
    await db.query("DELETE FROM media_play_history WHERE file_name = ?", [decodedFileName]);
    await db.query("DELETE FROM files WHERE file_name = ?", [decodedFileName]);

    logger.info(`视频删除成功: ${decodedFileName}`);
    return handleResponse(ctx, 200, { success: true }, "视频删除成功");

  } catch (error) {
    logger.error("删除视频失败:", error);
    return handleResponse(ctx, 500, null, "删除视频失败");
  }
});

// ==================== 辅助函数 ====================

// 格式化时长
function formatDuration(seconds) {
  if (!seconds) return null;

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
}

module.exports = videoManagement;
