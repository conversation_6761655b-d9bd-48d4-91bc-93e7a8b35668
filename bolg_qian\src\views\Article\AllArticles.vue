<template>
  <div class="all-articles-page">
    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <div class="search-container">
        <!-- 搜索栏和统计信息在同一行 -->
        <div class="search-row">
          <div class="search-bar">
            <SearchBox
              v-model="searchQuery"
              width="1000px"
              :loading="loading"
              placeholder="搜索文章/用户/标签"
              @search="filterArticles"
            />
          </div>
          <!-- 统计信息 -->
          <div class="stats-info">
            <div class="stat-item">
              <span class="stat-label">共</span>
              <span class="stat-number">{{ total }}</span>
              <span class="stat-label">篇</span>
            </div>
            <div class="stat-divider">|</div>
            <div class="stat-item" v-if="searchQuery">
              <span class="stat-label">找到</span>
              <span class="stat-number">{{ filteredArticles.length }}</span>
              <span class="stat-label">篇</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文章列表 -->
    <div class="articles-container">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="filteredArticles.length === 0" class="empty-container">
        <el-empty description="暂无文章" :image-size="120">
          <el-button type="primary" @click="goToCreate">
            <el-icon><i-ep-EditPen /></el-icon>
            创建第一篇文章
          </el-button>
        </el-empty>
      </div>

      <div v-else class="articles-grid">
        <article
          v-for="article in filteredArticles"
          :key="article.id"
          class="article-card"
          @click="goToArticle(article.id)"
        >
          <div class="article-header">
            <h2 class="article-title">{{ article.title }}</h2>
            <div class="article-meta">
              <span class="author" v-if="article.authorName">
                {{ article.authorName }}
              </span>
              <span class="date">
                {{ formatDate(article.updated_at) }}
              </span>
            </div>
          </div>

          <div class="article-content">
            <p class="article-summary">{{ article.summary || '暂无摘要' }}</p>
          </div>

          <div class="article-footer">
            <div class="tags-section">
              <el-tag
                :type="getCategoryType(article.category)"
                effect="light"
                size="small"
                class="category-tag"
              >
                {{ article.category || '未分类' }}
              </el-tag>
              <el-tag
                v-for="tag in getArticleTags(article.tags)"
                :key="tag"
                effect="plain"
                size="small"
                class="article-tag"
              >
                {{ tag }}
              </el-tag>
            </div>

            <div class="article-actions">
              <div class="action-buttons">
                <ArticleFavorite
                  :article-id="article.id"
                  :show-text="false"
                  :show-count="true"
                  size="small"
                  @click.stop
                />
                <el-button type="primary" link size="small" @click.stop="goToArticle(article.id)">
                  <el-icon><i-ep-View /></el-icon>
                  阅读全文
                </el-button>
              </div>
            </div>
          </div>
        </article>
      </div>

      <!-- 分页 -->
      <div class="pagination-section" v-if="total > pageSize">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { GetPublicArticlesApi } from "@/utils/api"
import ArticleFavorite from '@/components/ArticleFavorite.vue'
import SearchBox from '@/common/SearchBox.vue'

const router = useRouter()

const searchQuery = ref('') // 搜索内容
const articles = ref([])    // 文章列表
const filteredArticles = ref([]) // 过滤后的文章
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

// 获取公开文章列表
const fetchPublicArticles = async () => {
  try {
    loading.value = true
    const res = await GetPublicArticlesApi({
      page: currentPage.value,
      limit: pageSize.value
    });
    if (res?.data) {
      articles.value = res.data.articles || [];
      total.value = res.data.total || 0;
      filterArticles()
    }
  } catch (error) {
    console.error("获取文章列表失败:", error);
  } finally {
    loading.value = false
  }
};

// 过滤文章
const filterArticles = () => {
  if (!searchQuery.value) {
    filteredArticles.value = articles.value
  } else {
    filteredArticles.value = articles.value.filter(article =>
      (article.title || '').toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      (article.summary || '').toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      (article.category || '').toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }
}

// 监听搜索内容变化，自动过滤
watch(searchQuery, filterArticles)

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchPublicArticles()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchPublicArticles()
}

// 分类标签颜色
const getCategoryType = (category) => {
  switch (category) {
    case '技术': return 'success'
    case '框架': return 'info'
    case '性能优化': return 'warning'
    default: return 'primary'
  }
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return "";
  const d = new Date(dateStr);
  const pad = n => n.toString().padStart(2, "0");
  return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())}`;
};

// 获取文章标签
const getArticleTags = (tags) => {
  if (!tags) return [];
  return tags.split(' ').filter(tag => tag.trim() !== '').slice(0, 3); // 最多显示3个标签
};

// 跳转到文章详情
const goToArticle = (id) => {
  router.push(`/index/details/${id}`);
};

// 跳转到创建文章
const goToCreate = () => {
  router.push('/index/smart-edit');
};

onMounted(() => {
  fetchPublicArticles();
});
</script>

<style scoped>
/* 页面容器 */
.all-articles-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  font-family: var(--font-family-sans);
}

/* 搜索区域 */
.search-section {
  background: var(--bg-primary);
  padding: var(--spacing-xl) 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.search-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.search-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
  justify-content: space-between;
}

.search-bar {
  flex: 1;
  max-width: 600px;
}

.search-input {
  width: 100%;
}

.stats-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-shrink: 0;
  background: var(--bg-tertiary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-full);
  border: 1px solid var(--border-light);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--text-sm);
}

.stat-number {
  font-weight: var(--font-bold);
  color: var(--primary-color);
  font-size: var(--text-base);
}

.stat-label {
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.stat-divider {
  color: var(--border-medium);
  font-weight: var(--font-light);
}

/* 文章容器 */
.articles-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.loading-container {
  padding: var(--spacing-2xl);
}

.empty-container {
  padding: var(--spacing-2xl);
  text-align: center;
}

/* 文章网格 */
.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
  /* 确保网格与容器左侧对齐 */
  justify-content: start;
}

/* 文章卡片 */
.article-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  cursor: pointer;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.article-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

/* 文章头部 */
.article-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  text-align: left; /* 确保头部内容左对齐 */
}

.article-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  line-height: var(--leading-tight);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-align: left; /* 确保文字左对齐 */
}

.article-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.author,
.date {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 文章内容 */
.article-content {
  padding: var(--spacing-md) var(--spacing-lg);
  flex: 1;
}

.article-summary {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-align: left; /* 确保摘要左对齐 */
}

/* 文章底部 */
.article-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
}

.tags-section {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  flex: 1;
  max-width: 60%;
  overflow: hidden;
}

.category-tag {
  font-weight: var(--font-medium);
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.article-tag {
  font-size: var(--text-xs);
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.article-actions {
  flex-shrink: 0;
  min-width: 200px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

/* 分页区域 */
.pagination-section {
  display: flex;
  justify-content: center;
  padding: var(--spacing-2xl) 0;
  border-top: 1px solid var(--border-light);
  margin-top: var(--spacing-lg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-container {
    padding: 0 var(--spacing-md);
  }

  .search-row {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .search-bar {
    max-width: none;
  }

  .stats-info {
    justify-content: center;
    align-self: center;
  }

  .articles-container {
    padding: 0 var(--spacing-md);
  }

  .articles-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .article-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .tags-section {
    width: 100%;
    max-width: none;
    margin-bottom: var(--spacing-xs);
  }

  .article-actions {
    min-width: auto;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .search-container {
    padding: 0 var(--spacing-sm);
  }

  .search-row {
    gap: var(--spacing-sm);
  }

  .category-tag {
    max-width: 60px;
  }

  .article-tag {
    max-width: 50px;
  }

  .stats-info {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--text-xs);
  }

  .stat-number {
    font-size: var(--text-sm);
  }

  .articles-container {
    padding: 0 var(--spacing-sm);
  }

  .article-header {
    padding: var(--spacing-md);
  }

  .article-content {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .article-footer {
    padding: var(--spacing-sm) var(--spacing-md);
  }
}
</style>