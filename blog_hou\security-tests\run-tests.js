#!/usr/bin/env node

/**
 * 测试运行器
 * 统一运行所有安全和性能测试
 */

const SecurityTester = require('./security-tester');
const LoadTester = require('./load-tester');
const APISecurityTester = require('./api-security-tester');
const fs = require('fs');
const path = require('path');

class TestRunner {
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || 'http://localhost:3000';
    this.testTypes = options.testTypes || ['security', 'api', 'load'];
    this.loadTestOptions = options.loadTest || {};
    this.outputDir = options.outputDir || './test-reports';
    
    // 确保输出目录存在
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
  }

  async runSecurityTests() {
    console.log('🛡️  开始运行安全测试...\n');
    
    const tester = new SecurityTester(this.baseUrl);
    await tester.runAllTests();
    
    // 保存结果
    const reportPath = path.join(this.outputDir, `security-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(tester.results, null, 2));
    
    return tester.results;
  }

  async runAPISecurityTests() {
    console.log('\n🔐 开始运行API安全测试...\n');
    
    const tester = new APISecurityTester(this.baseUrl);
    await tester.runAllTests();
    
    // 保存结果
    const reportPath = path.join(this.outputDir, `api-security-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify({
      vulnerabilities: tester.vulnerabilities,
      testResults: tester.testResults
    }, null, 2));
    
    return { vulnerabilities: tester.vulnerabilities, testResults: tester.testResults };
  }

  async runLoadTests() {
    console.log('\n📊 开始运行负载测试...\n');
    
    const defaultOptions = {
      baseUrl: this.baseUrl,
      concurrency: 50,
      duration: 30,
      rampUp: 5,
      endpoints: ['/', '/articles', '/user/profile']
    };
    
    const options = { ...defaultOptions, ...this.loadTestOptions };
    const tester = new LoadTester(options);
    
    // 由于LoadTester使用cluster，我们需要特殊处理
    return new Promise((resolve) => {
      const originalLog = console.log;
      const logs = [];
      
      console.log = (...args) => {
        logs.push(args.join(' '));
        originalLog(...args);
      };
      
      tester.runLoadTest().then(() => {
        console.log = originalLog;
        
        // 保存结果
        const reportPath = path.join(this.outputDir, `load-test-report-${Date.now()}.json`);
        fs.writeFileSync(reportPath, JSON.stringify({
          stats: tester.stats,
          logs: logs
        }, null, 2));
        
        resolve(tester.stats);
      });
    });
  }

  async runAllTests() {
    console.log('🚀 开始运行完整的安全和性能测试套件');
    console.log(`🎯 测试目标: ${this.baseUrl}`);
    console.log(`📁 报告输出目录: ${this.outputDir}`);
    console.log('⚠️  警告: 请确保只在自己的系统上运行这些测试\n');
    
    const results = {
      timestamp: new Date().toISOString(),
      baseUrl: this.baseUrl,
      tests: {}
    };
    
    try {
      // 运行安全测试
      if (this.testTypes.includes('security')) {
        results.tests.security = await this.runSecurityTests();
      }
      
      // 运行API安全测试
      if (this.testTypes.includes('api')) {
        results.tests.apiSecurity = await this.runAPISecurityTests();
      }
      
      // 运行负载测试
      if (this.testTypes.includes('load')) {
        results.tests.loadTest = await this.runLoadTests();
      }
      
      // 生成综合报告
      this.generateSummaryReport(results);
      
    } catch (error) {
      console.error('测试运行过程中发生错误:', error);
    }
  }

  generateSummaryReport(results) {
    console.log('\n' + '='.repeat(80));
    console.log('📋 综合测试报告');
    console.log('='.repeat(80));
    console.log(`测试时间: ${results.timestamp}`);
    console.log(`测试目标: ${results.baseUrl}`);
    
    // 安全测试总结
    if (results.tests.security) {
      const securityVulns = results.tests.security.vulnerabilities || [];
      console.log(`\n🛡️  安全测试结果:`);
      console.log(`   发现漏洞: ${securityVulns.length}`);
      
      const critical = securityVulns.filter(v => v.severity === 'CRITICAL').length;
      const high = securityVulns.filter(v => v.severity === 'HIGH').length;
      const medium = securityVulns.filter(v => v.severity === 'MEDIUM').length;
      
      if (critical > 0) console.log(`   🔴 严重漏洞: ${critical}`);
      if (high > 0) console.log(`   🟠 高危漏洞: ${high}`);
      if (medium > 0) console.log(`   🟡 中危漏洞: ${medium}`);
    }
    
    // API安全测试总结
    if (results.tests.apiSecurity) {
      const apiVulns = results.tests.apiSecurity.vulnerabilities || [];
      console.log(`\n🔐 API安全测试结果:`);
      console.log(`   发现漏洞: ${apiVulns.length}`);
      console.log(`   发现端点: ${results.tests.apiSecurity.testResults?.length || 0}`);
    }
    
    // 负载测试总结
    if (results.tests.loadTest) {
      const loadStats = results.tests.loadTest;
      console.log(`\n📊 负载测试结果:`);
      console.log(`   总请求数: ${loadStats.totalRequests || 0}`);
      console.log(`   成功率: ${((loadStats.successfulRequests / loadStats.totalRequests) * 100).toFixed(2)}%`);
      
      if (loadStats.responseTimes && loadStats.responseTimes.length > 0) {
        const avgResponseTime = loadStats.responseTimes.reduce((a, b) => a + b) / loadStats.responseTimes.length;
        console.log(`   平均响应时间: ${avgResponseTime.toFixed(2)}ms`);
      }
    }
    
    // 安全评级
    console.log('\n🏆 整体安全评级:');
    const totalVulns = (results.tests.security?.vulnerabilities?.length || 0) + 
                      (results.tests.apiSecurity?.vulnerabilities?.length || 0);
    
    if (totalVulns === 0) {
      console.log('🟢 优秀 - 未发现明显安全漏洞');
    } else if (totalVulns <= 3) {
      console.log('🟡 良好 - 发现少量安全问题，建议修复');
    } else if (totalVulns <= 10) {
      console.log('🟠 一般 - 发现多个安全问题，需要及时修复');
    } else {
      console.log('🔴 较差 - 发现大量安全问题，存在严重风险');
    }
    
    // 保存综合报告
    const summaryPath = path.join(this.outputDir, `summary-report-${Date.now()}.json`);
    fs.writeFileSync(summaryPath, JSON.stringify(results, null, 2));
    
    console.log(`\n📄 详细报告已保存到: ${this.outputDir}`);
    console.log('='.repeat(80));
  }
}

// 命令行使用
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {
    baseUrl: 'http://localhost:3000',
    testTypes: ['security', 'api', 'load'],
    loadTest: {}
  };
  
  // 解析命令行参数
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace('--', '');
    const value = args[i + 1];
    
    switch (key) {
      case 'url':
        options.baseUrl = value;
        break;
      case 'tests':
        options.testTypes = value.split(',');
        break;
      case 'output':
        options.outputDir = value;
        break;
      case 'concurrency':
        options.loadTest.concurrency = parseInt(value);
        break;
      case 'duration':
        options.loadTest.duration = parseInt(value);
        break;
      case 'help':
        console.log(`
使用方法: node run-tests.js [选项]

选项:
  --url <url>           测试目标URL (默认: http://localhost:3000)
  --tests <types>       测试类型，逗号分隔 (默认: security,api,load)
  --output <dir>        报告输出目录 (默认: ./test-reports)
  --concurrency <num>   负载测试并发数 (默认: 50)
  --duration <sec>      负载测试持续时间 (默认: 30)
  --help               显示此帮助信息

示例:
  node run-tests.js --url http://localhost:3000 --tests security,api
  node run-tests.js --url http://example.com --concurrency 100 --duration 60
        `);
        process.exit(0);
    }
  }
  
  const runner = new TestRunner(options);
  runner.runAllTests().catch(console.error);
}

module.exports = TestRunner;
