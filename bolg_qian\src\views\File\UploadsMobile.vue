<template>
  <div class="vant-upload-container">
    <van-nav-bar title="移动端上传" fixed />
    <van-uploader v-model="imageList" :after-read="onAfterReadImage" :before-delete="onBeforeDeleteImage" multiple
      :max-count="5" :max-size="50 * 1024 * 1024" :preview-full-image="true" :deletable="true"
      :show-upload="imageList.length + otherFileList.length < 5" accept="image/*" upload-text="选择图片"
      class="vant-uploader" />
    <!-- 所有文件统一进度条 -->
    <div v-if="allUploadList.length" class="file-list">
      <div v-for="(item, idx) in allUploadList" :key="item.id" class="file-row">
        <span class="file-icon">{{ getFileIcon(item.file.name) }}</span>
        <span class="file-name">{{ item.file.name }}</span>
        <el-progress v-if="item.progress !== undefined" :percentage="item.progress"
          :status="item.progress === 100 ? 'success' : 'active'" style="width: 100px; margin-right: 8px;"
          :stroke-width="8" />
        <el-button v-if="!uploading" type="danger" size="small" text @click="removeFile(item)">删除</el-button>
      </div>
    </div>
    <el-button type="primary" :loading="uploading" :disabled="uploading" class="vant-upload-btn"
      style="width: 100%;margin-top: 16px;" @click="handleUpload">
      上传
    </el-button>
    <van-list v-if="urlList.length" class="vant-success-list">
      <van-cell v-for="(url, idx) in urlList" :key="url" :title="getFileTitle(url)" :value="url" is-link :url="url"
        target="_blank">
        <template #icon>
          <span class="file-icon">{{ getFileIcon(url) }}</span>
        </template>
      </van-cell>
    </van-list>
    <!-- 文件选择按钮（非图片） -->
    <input ref="fileInputRef" type="file" :multiple="true" style="display:none" @change="onOtherFileChange" />
    <el-button type="default" style="width:100%;margin-top:10px" @click="triggerOtherFile"
      :disabled="allUploadList.length >= 5">选择其它文件（视频/文档/压缩包等）</el-button>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, nextTick } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { UploadApi, UploadChunkApi, MergeChunksApi, QueryUploadedChunksApi } from "../../utils/api";

const imageList = ref<any[]>([]);
const otherFileList = ref<any[]>([]);
const urlList = ref<string[]>([]);
const uploading = ref(false);
const user_id = localStorage.getItem("id");
const username = localStorage.getItem("username");

const allUploadList = computed(() => [
  ...imageList.value,
  ...otherFileList.value
]);

const onAfterReadImage = (file: any) => {
  if (Array.isArray(file)) {
    file.forEach(f => f.progress = 0);
  } else if (file) {
    file.progress = 0;
  }
};

const removeFile = (item: any) => {
  ElMessageBox.confirm(`确定要删除 ${item.file.name} 吗？`, "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    const idxImg = imageList.value.indexOf(item);
    if (idxImg !== -1) return imageList.value.splice(idxImg, 1);
    const idxOther = otherFileList.value.indexOf(item);
    if (idxOther !== -1) otherFileList.value.splice(idxOther, 1);
  });
};

const fileInputRef = ref<HTMLInputElement>();
const triggerOtherFile = () => fileInputRef.value?.click();
const onOtherFileChange = (e: Event) => {
  const files = (e.target as HTMLInputElement).files;
  if (!files) return;
  for (let i = 0; i < files.length && allUploadList.value.length < 5; i++) {
    const file = files[i];
    if (!file.type.startsWith("image/")) {
      otherFileList.value.push({
        id: `${file.name}_${file.size}_${file.lastModified}_${Date.now()}`,
        file,
        progress: 0,
      });
    }
  }
  nextTick(() => {
    if (fileInputRef.value) fileInputRef.value.value = "";
  });
};

// 并发上传逻辑配置
const MAX_CONCURRENT = 4;
const MAX_RETRY = 2;

function withRetry(fn: () => Promise<void>, maxRetry = MAX_RETRY): Promise<void> {
  return new Promise((resolve, reject) => {
    let attempt = 0;
    const run = async () => {
      try {
        attempt++;
        await fn();
        resolve();
      } catch (err) {
        if (attempt <= maxRetry) {
          console.warn(`重试第 ${attempt} 次...`);
          run();
        } else {
          reject(err);
        }
      }
    };
    run();
  });
}

async function runConcurrentTasks(tasks: (() => Promise<void>)[], limit = MAX_CONCURRENT) {
  const queue = [...tasks];
  const results: Promise<void>[] = [];

  const runNext = async () => {
    if (queue.length === 0) return;
    const task = queue.shift();
    if (task) {
      await task();
      await runNext();
    }
  };

  for (let i = 0; i < limit; i++) {
    results.push(runNext());
  }

  return Promise.all(results);
}

const handleUpload = async () => {
  imageList.value.forEach(item => item.progress = 0);
  otherFileList.value.forEach(item => item.progress = 0);

  const files = allUploadList.value;
  if (!files.length) {
    ElMessage.warning("请先选择文件");
    return;
  }

  uploading.value = true;
  urlList.value = [];

  const tasks = files.map((item: any, idx: number) => {
    return () =>
      withRetry(async () => {
        const file = item.file;
        if (!file) return;

        if (file.size < 10 * 1024 * 1024) {
          await normalUpload(file, idx, (percent) => item.progress = percent);
        } else {
          await chunkUpload(file, idx, (percent) => item.progress = percent);
        }

        item.progress = 100;
      });
  });

  try {
    await runConcurrentTasks(tasks, MAX_CONCURRENT);
    ElMessage.success("全部上传完成！");
    imageList.value = [];
    otherFileList.value = [];
  } catch (err) {
    console.error("部分上传失败：", err);
    ElMessage.error("有文件上传失败，请检查重试");
  } finally {
    uploading.value = false;
  }
};

const normalUpload = async (file: File, idx: number, onProgress?: (percent: number) => void) => {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("user_id", user_id || "");
  formData.append("username", username || "");

  const res = await UploadApi(formData, {
    onUploadProgress: (e: any) => {
      if (e.total) {
        const percent = Math.round((e.loaded * 100) / e.total);
        onProgress && onProgress(percent);
      }
    },
  });

  onProgress && onProgress(100);
  urlList.value.push(res.files?.[0]?.filePath || "");
};

function createChunks(file: File, size = 2 * 1024 * 1024) {
  const result = [];
  let cur = 0;
  while (cur < file.size) {
    result.push(file.slice(cur, cur + size));
    cur += size;
  }
  return result;
}
function getFileHash(file: File) {
  return `${file.name}_${file.size}_${file.lastModified}`;
}

const chunkUpload = async (file: File, idx: number, onProgress?: (percent: number) => void) => {
  const chunkSize = 2 * 1024 * 1024;
  const chunks = createChunks(file, chunkSize);
  const fileHash = getFileHash(file);
  const totalChunks = chunks.length;

  let uploadedChunks: number[] = [];
  try {
    const res = await QueryUploadedChunksApi(fileHash);
    uploadedChunks = res.data.uploadedChunks || [];
  } catch {
    uploadedChunks = [];
  }

  let finished = uploadedChunks.length;
  onProgress && onProgress(Math.round((finished * 100) / totalChunks));

  // 构建所有未上传分片的任务
  const tasks: (() => Promise<void>)[] = [];
  for (let i = 0; i < totalChunks; i++) {
    if (uploadedChunks.includes(i)) continue;
    tasks.push(async () => {
      const form = new FormData();
      form.append("chunk", chunks[i]);
      form.append("chunkIndex", i.toString());
      form.append("fileHash", fileHash);
      form.append("fileName", file.name);
      form.append("user_id", user_id || "");
      form.append("totalChunks", totalChunks.toString());
      await UploadChunkApi(form);
      finished++;
      onProgress && onProgress(Math.round((finished * 100) / totalChunks));
    });
  }

  // 控制分片并发
  const MAX_CHUNK_CONCURRENT = 4;
  const pool = Array(MAX_CHUNK_CONCURRENT).fill(Promise.resolve());
  await Promise.all(
    pool.map(async () => {
      while (tasks.length) {
        const task = tasks.shift();
        if (task) await task();
      }
    })
  );

  const mergeRes = await MergeChunksApi({
    fileHash,
    fileName: file.name,
    user_id,
    totalChunks,
    fileType: file.type,
    fileSize: file.size,
  });

  onProgress && onProgress(100);
  urlList.value.push(mergeRes.filePath || "");
};

function getFileIcon(name: string) {
  const ext = name.split('.').pop()?.toLowerCase() || '';
  if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(ext)) return '🖼️';
  if (['mp4', 'mov', 'avi', 'mkv', 'webm'].includes(ext)) return '🎬';
  if (['mp3', 'wav', 'ogg', 'flac'].includes(ext)) return '🎵';
  if (['pdf'].includes(ext)) return '📄';
  if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'].includes(ext)) return '📑';
  if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) return '🗜️';
  return '📁';
}

function getFileTitle(url: string) {
  const name = url.split('/').pop() || url;
  return `上传成功：${name}`;
}
</script>

<style scoped>
.vant-upload-container {
  padding: 12px 8px 60px 8px;
  background: #f7f8fa;
  min-height: 100vh;
}

.vant-uploader {
  margin: 18px 0 12px 0;
}

.vant-upload-btn {
  margin: 16px 0 0 0;
  font-size: 17px;
  letter-spacing: 1px;
}

.vant-success-list {
  margin-top: 18px;
}

.file-list {
  margin: 10px 0 0 0;
  background: #fff;
  border-radius: 8px;
  padding: 8px 0;
}

.file-row {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.file-row:last-child {
  border-bottom: none;
}

.file-icon {
  margin-right: 8px;
  font-size: 18px;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>