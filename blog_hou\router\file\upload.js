const Router = require("koa-router");
const uploads = new Router();
const fs = require("fs");
const path = require("path");
const logger = require("../../plugin/logger");
const db = require("../../utils/sql/uploadServer");
const { handleResponse } = require("../../middlewares/responseHandler");
const { pipeline } = require("stream/promises");

// 文件路径这样才能访问到文件：
// http://localhost:3000/articles/1630.jpg
// http://localhost:3000/images/twitter_img_4.jpg
// http://localhost:3000/resource/twitter_img_4.jpg

// 统一资源保存目录
const resourceDir = path.join(__dirname, "../../uploads/resource");

// 智能文件分类函数
function getFileCategory(filename) {
  const ext = path.extname(filename).toLowerCase();

  // 图片文件
  const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico', '.heic'];
  if (imageExts.includes(ext)) return 'images';

  // 视频文件
  const videoExts = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm', '.m4v'];
  if (videoExts.includes(ext)) return 'videos';

  // 音频文件 (也归类为视频，因为媒体播放器可以处理)
  const audioExts = ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a'];
  if (audioExts.includes(ext)) return 'videos';

  // 文档文件
  const docExts = ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.xlsx', '.xls', '.ppt', '.pptx'];
  if (docExts.includes(ext)) return 'documents';

  // 压缩包文件
  const archiveExts = ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'];
  if (archiveExts.includes(ext)) return 'archives';

  // 默认归类为文档
  return 'documents';
}

// 判断是否为视频文件 (保持兼容性)
function isVideoFile(filename) {
  const category = getFileCategory(filename);
  return category === 'videos';
}

// 文件上传接口（单文件/多文件）
uploads.post("/uploads", async (ctx) => {
  let files = ctx.request.files.file;
  if (!files) {
    return handleResponse(ctx, 400, { error: "未上传文件" });
  }
  if (!Array.isArray(files)) {
    files = [files];
  }

  const { user_id, channel } = ctx.request.body;

  // 定义所有目录
  const directories = {
    images: path.join(__dirname, "../../uploads/images"),
    documents: path.join(__dirname, "../../uploads/documents"),
    videos: path.join(__dirname, "../../uploads/videos"),
    archives: path.join(__dirname, "../../uploads/archives"),
    media: path.join(__dirname, "../../public/media"),
    resource: resourceDir // 兼容旧版本
  };

  // 确保所有目录存在
  Object.values(directories).forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });

  const uploadedFiles = [];
  for (const file of files) {
    const { size: file_size, name: file_name, type: file_type } = file;

    // 智能确定文件分类和保存位置
    let saveDir, file_path, category;

    if (channel === 'media') {
      // 媒体通道：保存到 media 目录（公开访问）
      saveDir = directories.media;
      file_path = `http://**************:3000/media/${file_name}`;
      category = 'media';
    } else if (channel === 'document') {
      // 文档通道：智能分类保存
      const autoCategory = getFileCategory(file_name);
      saveDir = directories[autoCategory];
      file_path = `http://**************:3000/${autoCategory}/${file_name}`;
      category = autoCategory;
    } else {
      // 自动智能分类（推荐方式）
      const autoCategory = getFileCategory(file_name);

      // 视频和音频文件保存到 media 目录（公开访问）
      if (autoCategory === 'videos') {
        saveDir = directories.media;
        file_path = `http://**************:3000/media/${file_name}`;
        category = 'media';
      } else {
        // 其他文件按分类保存
        saveDir = directories[autoCategory];
        file_path = `http://**************:3000/${autoCategory}/${file_name}`;
        category = autoCategory;
      }
    }

    // 判断服务器是否已有该文件
    const targetPath = path.join(saveDir, file_name);
    if (fs.existsSync(targetPath)) {
      uploadedFiles.push({
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        filePath: file_path,
        category: category,
        skipped: true,
        message: "文件已存在，未重复上传"
      });
      logger.info(`文件已存在，未重复上传 - 文件名: ${file.name} - 通道: ${channel || 'auto'} - IP: ${ctx.ip}`);
      continue;
    }

    const result = await db.uploadFile(
      user_id,
      file_name,
      file_path,
      file_size,
      file_type
    );
    if (result.success) {
      try {
        await pipeline(
          fs.createReadStream(file.path),
          fs.createWriteStream(targetPath)
        );
        uploadedFiles.push({
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          filePath: file_path,
          category: category,
        });
        logger.info(`文件上传成功 - 文件名: ${file.name} - 通道: ${channel || 'auto'} - 分类: ${category} - IP: ${ctx.ip}`);
      } catch (err) {
        logger.error(`文件写入失败 - 文件名: ${file.name} - IP: ${ctx.ip} - 错误: ${err.message}`);
        return handleResponse(ctx, 500, { error: `文件写入失败: ${file.name}` });
      }
    } else {
      logger.error(`文件上传失败 - 文件名: ${file.name} - IP: ${ctx.ip}`);
      return handleResponse(ctx, 500, { error: `文件上传失败: ${file.name}` });
    }
  }

  return handleResponse(ctx, 200, {
    message: "文件上传处理完成",
    files: uploadedFiles,
  });
});

// 分片上传接口
uploads.post("/uploads/chunk", async (ctx) => {
  const { fileHash, chunkIndex, totalChunks, fileName, user_id } =
    ctx.request.body;
  const chunk = ctx.request.files?.chunk;
  if (!fileHash || chunkIndex === undefined || !chunk || !fileName) {
    return handleResponse(ctx, 400, { error: "缺少必要参数" });
  }

  const chunkDir = path.join(resourceDir, "chunks", fileHash);
  if (!fs.existsSync(chunkDir)) fs.mkdirSync(chunkDir, { recursive: true });

  const chunkPath = path.join(chunkDir, `${chunkIndex}`);
  try {
    await pipeline(
      fs.createReadStream(chunk.path),
      fs.createWriteStream(chunkPath)
    );
  } catch (err) {
    logger.error(`分片写入失败 - 文件: ${fileName} - 分片: ${chunkIndex} - IP: ${ctx.ip} - 错误: ${err.message}`);
    return handleResponse(ctx, 500, { error: "分片写入失败" });
  }

  logger.info(
    `分片上传成功 - 文件: ${fileName} - 分片: ${chunkIndex} - IP: ${ctx.ip}`
  );
  return handleResponse(ctx, 200, { message: "分片上传成功" });
});

// 合并分片接口
uploads.post("/uploads/merge", async (ctx) => {
  const { fileHash, fileName, totalChunks, user_id, fileType, fileSize, channel } = ctx.request.body;
  if (!fileHash || !fileName || !totalChunks) {
    return handleResponse(ctx, 400, { error: "缺少必要参数" });
  }

  // 智能确定保存位置（与普通上传逻辑一致）
  let saveDir, file_path, category;

  if (channel === 'media') {
    // 媒体通道：保存到 media 目录（公开访问）
    saveDir = path.join(__dirname, "../../public/media");
    file_path = `http://**************:3000/media/${fileName}`;
    category = 'media';
  } else if (channel === 'document') {
    // 文档通道：智能分类保存
    const autoCategory = getFileCategory(fileName);
    saveDir = path.join(__dirname, `../../uploads/${autoCategory}`);
    file_path = `http://**************:3000/${autoCategory}/${fileName}`;
    category = autoCategory;
  } else {
    // 自动智能分类（推荐方式）
    const autoCategory = getFileCategory(fileName);

    // 视频和音频文件保存到 media 目录（公开访问）
    if (autoCategory === 'videos') {
      saveDir = path.join(__dirname, "../../public/media");
      file_path = `http://**************:3000/media/${fileName}`;
      category = 'media';
    } else {
      // 其他文件按分类保存
      saveDir = path.join(__dirname, `../../uploads/${autoCategory}`);
      file_path = `http://**************:3000/${autoCategory}/${fileName}`;
      category = autoCategory;
    }
  }

  // 确保目标目录存在
  if (!fs.existsSync(saveDir)) {
    fs.mkdirSync(saveDir, { recursive: true });
  }

  if (!fs.existsSync(saveDir)) fs.mkdirSync(saveDir, { recursive: true });

  const finalPath = path.join(saveDir, fileName);
  const chunkDir = path.join(resourceDir, "chunks", fileHash);

  // 判断服务器是否已有该文件
  if (fs.existsSync(finalPath)) {
    // 清理分片目录
    if (fs.existsSync(chunkDir)) {
      fs.rmSync(chunkDir, { recursive: true, force: true });
    }
    logger.info(`分片合并时文件已存在，未重复合并 - 文件名: ${fileName} - IP: ${ctx.ip}`);
    return handleResponse(ctx, 200, {
      message: "文件已存在，未重复合并",
      filePath: file_path,
    });
  }

  const writeStream = fs.createWriteStream(finalPath);

  try {
    for (let i = 0; i < totalChunks; i++) {
      const chunkPath = path.join(chunkDir, `${i}`);
      if (!fs.existsSync(chunkPath)) {
        return handleResponse(ctx, 400, { error: `缺少分片${i}` });
      }
      await pipeline(
        fs.createReadStream(chunkPath),
        writeStream,
        { end: false }
      );
      fs.unlinkSync(chunkPath);
    }
    writeStream.end();
    fs.rmdirSync(chunkDir);
  } catch (err) {
    logger.error(`分片合并失败 - 文件名: ${fileName} - IP: ${ctx.ip} - 错误: ${err.message}`);
    return handleResponse(ctx, 500, { error: "分片合并失败" });
  }

  await db.uploadFile(user_id, fileName, file_path, fileSize, fileType);

  logger.info(`文件合并成功 - 用户ID: ${user_id} - 文件名: ${fileName} - 路径: ${file_path} - IP: ${ctx.ip}`);
  // 或者
  // console.log({ user_id, fileName, file_path, fileSize, fileType });

  return handleResponse(ctx, 200, {
    message: "文件合并成功",
    filePath: file_path,
  });
});

// 查询已上传分片
uploads.get("/uploads/chunks", async (ctx) => {
  const { fileHash } = ctx.query;
  if (!fileHash) {
    return handleResponse(ctx, 400, { error: "缺少 fileHash" });
  }
  const chunkDir = path.join(resourceDir, "chunks", fileHash);
  let uploaded = [];
  if (fs.existsSync(chunkDir)) {
    uploaded = fs.readdirSync(chunkDir)
      .map(name => Number(name))
      .filter(n => !isNaN(n));
  }
  return handleResponse(ctx, 200, { uploadedChunks: uploaded });
});



module.exports = uploads;
