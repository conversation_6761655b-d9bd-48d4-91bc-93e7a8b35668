// 错误处理中间件
const logger = require("../plugin/logger");

module.exports = (app) => { 
  app.on("error", (err, ctx) => {
    const method = ctx.method; // 请求方法
    const url = ctx.url; // 请求 URL
    const status = err.status || 500; // 错误状态码

    // 判断是否为参数错误
    if (err.name === "ValidationError") {
      // 捕获到参数验证错误
      ctx.status = 400; // 返回 400 状态码
      ctx.body = {
        success: false,
        message: "请求参数错误",
        details: err.details, // 假设错误详情在 `details` 字段
        method: method,
        url: url,
      };
    } else if (err.name === "UnauthorizedError") {
      // 捕获到无效或过期的 Token
      ctx.status = 401;
      ctx.body = {
        success: false,
        message: "无效或过期的 Token",
        method: method,
        url: url,
      };
    } else {
      // 捕获其他服务器错误
      logger.error(`请求错误: ${method} ${url}`, {
        ip: ctx.ip,
        status: status,
        message: err.message,
        stack: err.stack,
      });

      ctx.status = status;
      ctx.body = {
        success: false,
        message: "服务器内部错误",
        method: method,
        url: url,
      };
    }
  });
};
