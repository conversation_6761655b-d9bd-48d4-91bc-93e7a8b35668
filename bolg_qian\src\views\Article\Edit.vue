<template>
  <div class="smart-editor">
    <!-- 智能写作工作台 -->
    <div class="writing-workspace">
      <!-- AI状态栏 -->
      <div class="ai-status-bar" :class="{ 'ai-ready': aiStatus.available }">
        <div class="status-indicator">
          <el-icon class="status-icon"><Cpu /></el-icon>
          <span class="status-text">
            {{ aiStatus.available ? `AI助手 ${aiStatus.currentModel} 已就绪` : 'AI服务离线' }}
          </span>
          <div class="ai-stats" v-if="aiStatus.available">
            <el-tag size="small" type="info">今日使用: {{ dailyUsage }}</el-tag>
            <el-tag size="small" type="success">平均响应: {{ avgResponseTime }}ms</el-tag>
          </div>
        </div>

        <!-- 智能建议提示 -->
        <div class="smart-suggestions" v-if="smartSuggestions.length > 0">
          <el-icon><Lightbulb /></el-icon>
          <span>AI建议: </span>
          <el-button
            v-for="suggestion in smartSuggestions"
            :key="suggestion.type"
            size="small"
            text
            type="primary"
            @click="applySuggestion(suggestion)"
          >
            {{ suggestion.text }}
          </el-button>
        </div>
      </div>

      <!-- 主编辑区域 -->
      <el-card class="editor-card" shadow="never">
        <!-- 标题输入区 - 集成AI -->
        <div class="title-section">
          <div class="title-input-wrapper">
            <el-input
              v-model="article.title"
              placeholder="输入文章标题..."
              size="large"
              class="title-input"
              @input="onTitleChange"
              @blur="onTitleBlur"
            >
              <template #suffix>
                <div class="title-ai-actions">
                  <el-tooltip content="AI生成标题" placement="top">
                    <el-button
                      :icon="Star"
                      circle
                      size="small"
                      :loading="aiLoading.title"
                      @click="generateTitles"
                      :disabled="!canGenerateTitle"
                    />
                  </el-tooltip>
                  <el-tooltip content="优化标题" placement="top">
                    <el-button
                      :icon="Edit"
                      circle
                      size="small"
                      :loading="aiLoading.titleOptimize"
                      @click="optimizeTitle"
                      :disabled="!article.title"
                    />
                  </el-tooltip>
                </div>
              </template>
            </el-input>
          </div>

          <!-- 标题建议快速选择 -->
          <div class="title-suggestions-quick" v-if="titleSuggestions.length > 0">
            <div class="suggestions-label">AI建议标题:</div>
            <div class="suggestions-list">
              <el-tag
                v-for="(title, index) in titleSuggestions.slice(0, 3)"
                :key="index"
                class="suggestion-tag"
                @click="applyTitle(title)"
                effect="plain"
              >
                {{ title }}
              </el-tag>
              <el-button
                v-if="titleSuggestions.length > 3"
                size="small"
                text
                @click="showAllTitles"
              >
                查看更多 ({{ titleSuggestions.length - 3 }})
              </el-button>
            </div>
          </div>
        </div>

        <!-- 元信息区域 -->
        <div class="meta-section">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-select
                v-model="article.category"
                placeholder="选择分类"
                @change="onCategoryChange"
              >
                <el-option label="技术" value="技术" />
                <el-option label="艺术" value="艺术" />
                <el-option label="生活" value="生活" />
                <el-option label="教程" value="教程" />
                <el-option label="经验分享" value="经验分享" />
              </el-select>
            </el-col>
            <el-col :span="16">
              <div class="tags-input-wrapper">
                <el-select
                  v-model="article.tags"
                  multiple
                  filterable
                  allow-create
                  placeholder="添加标签..."
                  class="tags-input"
                >
                  <el-option
                    v-for="tag in allTags"
                    :key="tag"
                    :label="tag"
                    :value="tag"
                  />
                </el-select>
                <el-tooltip content="AI推荐标签" placement="top">
                  <el-button
                    :icon="PriceTag"
                    circle
                    size="small"
                    :loading="aiLoading.tags"
                    @click="suggestTags"
                    :disabled="!canSuggestTags"
                  />
                </el-tooltip>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 智能编辑器 -->
        <div class="smart-editor-container">
          <!-- 编辑器工具栏增强 -->
          <div class="editor-toolbar">
            <div class="toolbar-left">
              <el-button-group size="small">
                <el-tooltip content="AI续写" placement="top">
                  <el-button
                    :icon="EditPen"
                    :loading="aiLoading.continue"
                    @click="continueWriting"
                    :disabled="!article.content"
                  >
                    续写
                  </el-button>
                </el-tooltip>
                <el-tooltip content="内容优化" placement="top">
                  <el-button
                    :icon="Tools"
                    :loading="aiLoading.optimize"
                    @click="optimizeSelectedContent"
                    :disabled="!hasSelection"
                  >
                    优化
                  </el-button>
                </el-tooltip>
                <el-tooltip content="生成大纲" placement="top">
                  <el-button
                    :icon="List"
                    :loading="aiLoading.outline"
                    @click="generateOutline"
                  >
                    大纲
                  </el-button>
                </el-tooltip>
                <el-tooltip content="语法检查" placement="top">
                  <el-button
                    :icon="Check"
                    :loading="aiLoading.grammar"
                    @click="checkGrammar"
                    :disabled="!article.content"
                  >
                    检查
                  </el-button>
                </el-tooltip>
              </el-button-group>
            </div>

            <div class="toolbar-right">
              <div class="writing-stats">
                <span class="stat-item">字数: {{ contentStats.wordCount }}</span>
                <span class="stat-item">段落: {{ contentStats.paragraphs }}</span>
                <span class="stat-item">预计阅读: {{ contentStats.readTime }}分钟</span>
              </div>
            </div>
          </div>

          <!-- 主编辑器 -->
          <div class="editor-wrapper" @mouseup="onTextSelection">
            <v-md-editor
              v-model="article.content"
              :height="600"
              preview
              @input="onContentChange"
              @focus="onEditorFocus"
              @blur="onEditorBlur"
            />
          </div>

          <!-- 实时AI建议面板 -->
          <div class="ai-suggestions-panel" v-if="showAISuggestions && aiSuggestions.length > 0">
            <div class="panel-header">
              <el-icon><Lightbulb /></el-icon>
              <span>AI写作建议</span>
              <el-button size="small" text @click="showAISuggestions = false">
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
            <div class="suggestions-content">
              <div
                v-for="suggestion in aiSuggestions"
                :key="suggestion.id"
                class="suggestion-item"
                @click="applySuggestion(suggestion)"
              >
                <div class="suggestion-type">{{ suggestion.type }}</div>
                <div class="suggestion-text">{{ suggestion.text }}</div>
                <div class="suggestion-confidence">置信度: {{ suggestion.confidence }}%</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 封面上传区域 -->
        <div class="cover-upload-section">
          <div class="section-title">
            <el-icon><Picture /></el-icon>
            <span>文章封面</span>
          </div>
          <el-upload
            class="cover-uploader"
            :show-file-list="false"
            :before-upload="beforeUpload"
            accept="image/*"
          >
            <div v-if="previewUrl" class="cover-preview">
              <img :src="previewUrl" alt="封面预览" />
              <div class="cover-overlay">
                <el-button size="small" @click.stop="removeFile">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
            <div v-else class="cover-placeholder">
              <el-icon class="upload-icon"><Plus /></el-icon>
              <div class="upload-text">点击上传封面</div>
              <div class="upload-hint">支持 JPG、PNG 格式，建议尺寸 16:9</div>
            </div>
          </el-upload>
        </div>

        <!-- 发布设置 -->
        <div class="publish-settings">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-checkbox v-model="article.isPublic">公开发布</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="article.allowComments">允许评论</el-checkbox>
            </el-col>
          </el-row>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button size="large" @click="saveDraft" :loading="saving">
            <el-icon><DocumentCopy /></el-icon>
            保存草稿
          </el-button>
          <el-button type="primary" size="large" @click="handlePublish" :loading="publishing">
            <el-icon><Promotion /></el-icon>
            发布文章
          </el-button>
          <el-button @click="$router.push('/index')">
            <el-icon><Back /></el-icon>
            返回
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- AI智能对话框 -->
    <SmartAIDialog
      v-model="showAIDialog"
      :type="aiDialogType"
      :context="aiDialogContext"
      @apply="onAIResultApply"
    />

    <!-- 快速AI面板 -->
    <div class="quick-ai-panel" v-if="showQuickAI">
      <div class="panel-content">
        <div class="panel-title">快速AI助手</div>
        <div class="quick-actions">
          <el-button size="small" @click="quickAction('continue')">
            <el-icon><EditPen /></el-icon>
            续写
          </el-button>
          <el-button size="small" @click="quickAction('optimize')">
            <el-icon><Tools /></el-icon>
            优化
          </el-button>
          <el-button size="small" @click="quickAction('summarize')">
            <el-icon><Document /></el-icon>
            总结
          </el-button>
          <el-button size="small" @click="quickAction('translate')">
            <el-icon><Switch /></el-icon>
            翻译
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { PublishArticleApi } from "../../utils/api";
import { aiWritingApi } from "../../utils/aiApi";
import VMdEditor from "@kangc/v-md-editor";
import "@kangc/v-md-editor/lib/style/base-editor.css";
import githubTheme from "@kangc/v-md-editor/lib/theme/github.js";
import "@kangc/v-md-editor/lib/theme/style/github.css";
import hljs from "highlight.js";
import {
  Cpu,
  Star,
  PriceTag,
  Tools,
  List,
  Document,
  Edit,
  EditPen,
  Check,
  Lightbulb,
  Close,
  Picture,
  Plus,
  Delete,
  DocumentCopy,
  Promotion,
  Back,
  Switch
} from '@element-plus/icons-vue';

VMdEditor.use(githubTheme, { Hljs: hljs });

const router = useRouter();

// 核心数据
const article = ref({
  title: "",
  category: "",
  tags: [],
  content: "",
  isPublic: true,
  allowComments: true
});

// AI状态管理
const aiStatus = reactive({
  available: false,
  models: [],
  currentModel: '',
  lastCheck: null
});

// AI加载状态
const aiLoading = reactive({
  title: false,
  titleOptimize: false,
  tags: false,
  optimize: false,
  outline: false,
  continue: false,
  grammar: false
});

// 智能建议
const smartSuggestions = ref([]);
const titleSuggestions = ref([]);
const aiSuggestions = ref([]);
const showAISuggestions = ref(false);

// AI对话框
const showAIDialog = ref(false);
const aiDialogType = ref('');
const aiDialogContext = ref({});

// 编辑器状态
const hasSelection = ref(false);
const selectedText = ref('');
const showQuickAI = ref(false);

// 统计信息
const dailyUsage = ref(0);
const avgResponseTime = ref(0);

// 内容统计
const contentStats = computed(() => {
  const content = article.value.content;
  const wordCount = content.length;
  const paragraphs = content.split('\n\n').filter(p => p.trim()).length;
  const readTime = Math.ceil(wordCount / 200); // 假设每分钟200字

  return {
    wordCount,
    paragraphs,
    readTime
  };
});

// 标签库
const allTags = ref(['Vue', 'JavaScript', 'CSS', 'HTML', 'Node.js', 'React', 'TypeScript', 'Python']);

// 上传相关
const file = ref(null);
const previewUrl = ref("");
const saving = ref(false);
const publishing = ref(false);

const onFileChange = (fileItem) => {
  file.value = fileItem.raw;
  file.value.originalName = fileItem.name;
  previewUrl.value = URL.createObjectURL(file.value);
};
const onRemove = () => {
  file.value = null;
  previewUrl.value = "";
};
const handleExceed = () => {
  ElMessage.warning("只能上传一张封面图片");
};

const removeFile = () => {
  file.value = null;
  previewUrl.value = "";
};

// 计算属性
const canGenerateTitle = computed(() => {
  return article.value.content && article.value.content.length > 50;
});

const canSuggestTags = computed(() => {
  return article.value.title || (article.value.content && article.value.content.length > 20);
});

const canOptimizeContent = computed(() => {
  return article.value.content && article.value.content.length > 10;
});

// 智能编辑器核心方法
const checkAIStatus = async () => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      aiStatus.available = false;
      return;
    }

    const response = await aiWritingApi.getStatus();
    if (response.code === 200) {
      aiStatus.available = response.data.available;
      aiStatus.models = response.data.models;
      aiStatus.currentModel = response.data.currentModel;
      aiStatus.lastCheck = new Date();

      // 获取使用统计
      await loadUsageStats();
    } else {
      aiStatus.available = false;
    }
  } catch (error) {
    console.error('AI状态检查失败:', error);
    aiStatus.available = false;
  }
};

// 加载使用统计
const loadUsageStats = async () => {
  try {
    const response = await aiWritingApi.getUsageStats({ days: 1 });
    if (response.code === 200) {
      dailyUsage.value = response.data.summary.totalUsage || 0;
      avgResponseTime.value = Math.round(response.data.summary.avgResponseTime || 0);
    }
  } catch (error) {
    console.error('加载使用统计失败:', error);
  }
};

// 内容变化监听
const onContentChange = (content) => {
  article.value.content = content;

  // 实时分析内容，提供智能建议
  debounceAnalyzeContent();
};

// 防抖分析内容
let analyzeTimer = null;
const debounceAnalyzeContent = () => {
  if (analyzeTimer) clearTimeout(analyzeTimer);
  analyzeTimer = setTimeout(analyzeContent, 2000);
};

// 分析内容并提供建议
const analyzeContent = async () => {
  if (!aiStatus.available || !article.value.content) return;

  try {
    // 分析内容质量和结构
    const suggestions = [];

    // 检查段落结构
    const paragraphs = article.value.content.split('\n\n').filter(p => p.trim());
    if (paragraphs.length === 1 && article.value.content.length > 500) {
      suggestions.push({
        id: 'paragraph-structure',
        type: '结构建议',
        text: '建议将长段落分解为多个段落，提高可读性',
        confidence: 85,
        action: 'optimize-structure'
      });
    }

    // 检查标题
    if (!article.value.title && article.value.content.length > 100) {
      suggestions.push({
        id: 'missing-title',
        type: '标题建议',
        text: '建议为文章添加标题',
        confidence: 90,
        action: 'generate-title'
      });
    }

    // 检查标签
    if (article.value.tags.length === 0 && article.value.content.length > 200) {
      suggestions.push({
        id: 'missing-tags',
        type: '标签建议',
        text: '建议添加相关标签，提高文章可发现性',
        confidence: 80,
        action: 'suggest-tags'
      });
    }

    smartSuggestions.value = suggestions.slice(0, 3);
  } catch (error) {
    console.error('内容分析失败:', error);
  }
};

// 标题相关方法
const onTitleChange = (value) => {
  article.value.title = value;
  // 实时检查标题质量
  debounceCheckTitle();
};

const onTitleBlur = () => {
  if (article.value.title && article.value.content) {
    // 标题输入完成后，自动推荐标签
    suggestTags();
  }
};

let titleTimer = null;
const debounceCheckTitle = () => {
  if (titleTimer) clearTimeout(titleTimer);
  titleTimer = setTimeout(checkTitleQuality, 1000);
};

const checkTitleQuality = () => {
  const title = article.value.title;
  if (!title) return;

  const suggestions = [];

  // 检查标题长度
  if (title.length > 50) {
    suggestions.push({
      id: 'title-too-long',
      type: '标题优化',
      text: '标题过长，建议控制在50字以内',
      confidence: 85,
      action: 'optimize-title'
    });
  }

  // 检查标题是否包含关键词
  if (article.value.category && !title.includes(article.value.category)) {
    suggestions.push({
      id: 'title-missing-category',
      type: '标题优化',
      text: `建议在标题中体现"${article.value.category}"相关内容`,
      confidence: 70,
      action: 'optimize-title'
    });
  }

  // 更新建议
  smartSuggestions.value = [...smartSuggestions.value.filter(s => !s.id.startsWith('title-')), ...suggestions];
};

const generateTitles = async () => {
  if (!canGenerateTitle.value) {
    ElMessage.warning('请先输入足够的文章内容');
    return;
  }

  aiLoading.title = true;
  try {
    const response = await aiWritingApi.generateTitle({
      content: article.value.content,
      category: article.value.category,
      count: 8
    });

    if (response.code === 200) {
      titleSuggestions.value = response.data.titles;
      ElMessage.success(`生成了 ${response.data.titles.length} 个标题建议`);
    } else {
      ElMessage.error(response.message || '标题生成失败');
    }
  } catch (error) {
    console.error('生成标题失败:', error);
    ElMessage.error('生成标题失败');
  } finally {
    aiLoading.title = false;
  }
};

const optimizeTitle = async () => {
  if (!article.value.title) {
    ElMessage.warning('请先输入标题');
    return;
  }

  aiLoading.titleOptimize = true;
  try {
    const response = await aiWritingApi.optimizeContent({
      content: article.value.title,
      style: 'professional',
      target: 'general'
    });

    if (response.code === 200) {
      const optimizedTitle = response.data.optimizedContent;

      // 询问是否应用优化结果
      const result = await ElMessageBox.confirm(
        `优化后的标题：\n"${optimizedTitle}"\n\n是否应用此标题？`,
        '标题优化结果',
        {
          confirmButtonText: '应用',
          cancelButtonText: '取消',
          type: 'info'
        }
      );

      if (result === 'confirm') {
        article.value.title = optimizedTitle;
        ElMessage.success('标题已优化');
      }
    } else {
      ElMessage.error(response.message || '标题优化失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('优化标题失败:', error);
      ElMessage.error('优化标题失败');
    }
  } finally {
    aiLoading.titleOptimize = false;
  }
};

const applyTitle = (title) => {
  article.value.title = title;
  titleSuggestions.value = [];
  ElMessage.success('标题已应用');
};

const showAllTitles = () => {
  aiDialogType.value = 'titles';
  aiDialogContext.value = { titles: titleSuggestions.value };
  showAIDialog.value = true;
};

const suggestTags = async () => {
  if (!canSuggestTags.value) {
    ElMessage.warning('请先输入标题或内容');
    return;
  }

  aiLoading.tags = true;
  try {
    const response = await aiWritingApi.suggestTags({
      title: article.value.title,
      content: article.value.content,
      category: article.value.category,
      maxTags: 8
    });

    if (response.code === 200) {
      aiResults.tags = response.data.tags;
      aiResults.responseTime = response.data.responseTime;
      aiResults.model = response.data.model;

      // 自动应用标签建议
      const newTags = [...article.value.tags, ...aiResults.tags].filter((tag, index, arr) => arr.indexOf(tag) === index);
      article.value.tags = newTags;

      ElMessage.success(`推荐了 ${aiResults.tags.length} 个标签`);
    } else {
      ElMessage.error(response.message || '标签推荐失败');
    }
  } catch (error) {
    console.error('推荐标签失败:', error);
    ElMessage.error('推荐标签失败');
  } finally {
    aiLoading.tags = false;
  }
};

const showOptimizeDialog = () => {
  optimizeForm.content = article.value.content;
  aiDialogs.optimize = true;
};

const optimizeContent = async () => {
  if (!optimizeForm.content) {
    ElMessage.warning('请输入需要优化的内容');
    return;
  }

  aiLoading.optimize = true;
  try {
    const response = await aiWritingApi.optimizeContent({
      content: optimizeForm.content,
      style: optimizeForm.style,
      target: optimizeForm.target
    });

    if (response.code === 200) {
      aiResults.optimizedContent = response.data.optimizedContent;
      aiResults.originalLength = response.data.originalLength;
      aiResults.optimizedLength = response.data.optimizedLength;
      aiResults.responseTime = response.data.responseTime;
      aiResults.model = response.data.model;

      ElMessage.success('内容优化完成');
    } else {
      ElMessage.error(response.message || '内容优化失败');
    }
  } catch (error) {
    console.error('优化内容失败:', error);
    ElMessage.error('优化内容失败');
  } finally {
    aiLoading.optimize = false;
  }
};

const showOutlineDialog = () => {
  outlineForm.topic = article.value.title;
  outlineForm.category = article.value.category;
  aiDialogs.outline = true;
};

const generateOutline = async () => {
  if (!outlineForm.topic) {
    ElMessage.warning('请输入文章主题');
    return;
  }

  aiLoading.outline = true;
  try {
    const response = await aiWritingApi.generateOutline({
      topic: outlineForm.topic,
      category: outlineForm.category,
      depth: outlineForm.depth
    });

    if (response.code === 200) {
      aiResults.outline = response.data.outline;
      aiResults.responseTime = response.data.responseTime;
      aiResults.model = response.data.model;

      ElMessage.success('大纲生成完成');
    } else {
      ElMessage.error(response.message || '大纲生成失败');
    }
  } catch (error) {
    console.error('生成大纲失败:', error);
    ElMessage.error('生成大纲失败');
  } finally {
    aiLoading.outline = false;
  }
};


const applyOptimizedContent = () => {
  article.value.content = aiResults.optimizedContent;
  aiDialogs.optimize = false;
  ElMessage.success('优化内容已应用');
};

const applyOutline = () => {
  article.value.content = aiResults.outline;
  aiDialogs.outline = false;
  ElMessage.success('大纲已应用到编辑器');
};

const formatOutline = (outline) => {
  return outline
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^\* (.*$)/gim, '<li>$1</li>')
    .replace(/^\- (.*$)/gim, '<li>$1</li>')
    .replace(/\n/g, '<br>');
};

const user_id = localStorage.getItem("id");
const handleUpload = async () => {
  if (!file.value) return ElMessage.error("请上传封面图片");
  if (!article.value.title || !article.value.content)
    return ElMessage.warning("标题和内容不能为空");

  const fd = new FormData();
  fd.append("file", file.value);
  fd.append("user_id", user_id || "");
  fd.append("title", article.value.title);
  fd.append("category", article.value.category);
  fd.append("tags", article.value.tags);
  fd.append("content", article.value.content);
  if (file.value.originalName) {
    fd.append("cover_image", file.value.originalName);
  }

  const res = await PublishArticleApi(fd);
  if (res.code === 200) {
    ElMessage.success("文章发布成功");
    setTimeout(() => router.push("/index/home"), 1500);
  } else {
    ElMessage.error(res.message || "发布失败");
  }
};

// 生命周期
onMounted(() => {
  checkAIStatus();
});
</script>

<style scoped lang="less">
.upload-form {
  max-width: 1200px;
  margin: 25px auto;
  padding: 24px;
  background: #f9f9f9;
  border-radius: 12px;

  .el-card {
    padding: 32px;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
    background-color: #ffffff;
  }

  .section {
    margin-bottom: 32px;

    .el-form-item {
      margin-bottom: 24px;

      .el-input,
      .el-select {
        width: 100%;
      }
    }
  }

  .upload-cover {
    width: 160px;

    .el-upload--picture-card {
      width: 160px;
      height: 160px;
      line-height: 160px;
    }

    .el-upload-list__item {
      width: 160px;
      height: 160px;
    }
  }

  .cover-preview {
    position: relative;
    width: 160px;
    height: 160px;
  }

  .cover-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
  }

  .cover-preview .el-button {
    position: absolute;
    top: 4px;
    right: 4px;
    color: red;
  }

  .editor-container {
    margin-bottom: 32px;

    .v-md-editor {
      border: 1px solid #e5e5e5;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    }
  }

  .btn-wrapper {
    text-align: center;
    margin-top: 24px;

    .el-button {
      padding: 10px 28px;
      font-size: 16px;
      border-radius: 6px;
    }
  }

  // AI助手样式
  .ai-assistant {
    margin-bottom: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;

    .ai-status {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-size: 14px;

      &.available {
        color: #67c23a;
      }

      &.unavailable {
        color: #f56c6c;
      }

      .el-tag {
        margin-left: auto;
      }
    }

    .ai-functions {
      .el-button-group {
        .el-button {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.2);
          color: white;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }
  }
}

.dialog-tip {
  margin-bottom: 16px;
  color: #606266;
  font-size: 14px;
}

.title-suggestions {
  .title-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background: #e3f2fd;
      transform: translateY(-1px);
    }

    span {
      flex: 1;
      font-size: 14px;
    }
  }
}

.optimization-result,
.outline-result {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;

  h4 {
    margin-bottom: 12px;
    color: #303133;
  }

  .optimized-content,
  .outline-content {
    padding: 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
    line-height: 1.6;
    white-space: pre-wrap;
  }

  .optimization-stats,
  .response-info {
    margin-top: 8px;
    text-align: right;
  }
}

.response-info {
  margin-top: 12px;
  text-align: right;
}
</style>
