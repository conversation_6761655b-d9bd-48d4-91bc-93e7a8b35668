<template>
  <div class="chat-container">
    <div>
      <el-button type="primary" @click="$router.back()">返回</el-button>
      <el-button @click="clearHistory" type="danger">清除记录</el-button>
      <h2 class="title">与 Ollama 模型对话</h2>
    </div>

    <!-- 输入区域 -->
    <div class="input-group">
      <el-input v-model="prompt" class="input" placeholder="请输入提示..." type="textarea"
        :autosize="{ minRows: 2, maxRows: 4 }" @keydown.enter="handleEnter"
        @keydown.ctrl.enter.prevent="insertLineBreak" />
      <el-button @click="sendPrompt" :disabled="isLoading || !prompt.trim() || modelStatus !== 'ready'"
        class="send-button">
        {{ isLoading ? "发送中..." : "发送" }}
      </el-button>
      <el-button type="warning" @click="stopGenerating" :disabled="!isLoading" class="send-button"
        style="margin-left: 8px;">
        停止生成
      </el-button>
    </div>

    <!-- loading 效果 -->
    <el-loading v-if="isLoading" text="加载中..." />

    <!-- 模型回复 -->
    <div class="response-box markdown-body" ref="responseBox" v-if="response" v-html="renderedResponse"></div>
    <el-button class="copy-btn" type="primary" size="small" @click="copyText(item.answer)">
      复制
    </el-button>

    <div class="error-msg" v-if="errorMsg">{{ errorMsg }}</div>

    <!-- 历史对话 -->
    <el-divider content-position="left">历史对话</el-divider>

    <el-timeline>
      <el-timeline-item v-for="(item, index) in history" :key="index" :timestamp="item.time" placement="top">
        <p><strong>你：</strong>{{ item.question }}</p>
        <p><strong>模型：</strong></p>
        <div class="markdown-body" v-html="md.render(item.answer)"></div>
        <el-button class="copy-btn" type="primary" size="small" @click="copyText(item.answer)">
          复制
        </el-button>
        <el-button class="delete-btn" type="danger" size="small" @click="deleteHistory(index)"
          style="margin-bottom: 8px; margin-left: 8px;">
          删除
        </el-button>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { ChatApi } from "../utils/api";
import MarkdownIt from "markdown-it";
import hljs from "highlight.js";
import "highlight.js/styles/github.css";
import { copyText } from "@/plug/copy";

const md = new MarkdownIt({
  highlight(str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return `<pre class="hljs"><code>${hljs.highlight(str, { language: lang }).value}</code></pre>`;
      } catch { }
    }
    return `<pre class="hljs"><code>${md.utils.escapeHtml(str)}</code></pre>`;
  },
});

const prompt = ref("");
const response = ref("");
const isLoading = ref(false);
const errorMsg = ref("");
const history = ref([]);
const modelStatus = ref("loading"); // loading | ready | error
const statusText = ref("正在启动大模型...");
const responseBox = ref(null);

const renderedResponse = computed(() => md.render(response.value || ""));

const abortController = ref(null);

watch(response, () => {
  nextTick(() => {
    responseBox.value?.scrollTo({
      top: responseBox.value.scrollHeight,
      behavior: "smooth",
    });
  });
});

onMounted(async () => {
  try {
    const res = await ChatApi({ ok: 1 });
    if (res.code === 1) {
      statusText.value = "大模型已启动（无需重复启动）";
    } else {
      statusText.value = res.message || "大模型启动成功";
    }
    modelStatus.value = "ready";
  } catch (err) {
    statusText.value = "模型启动失败，请检查服务状态。";
    modelStatus.value = "error";
  }
  loadHistory();
});

function insertLineBreak(e) {
  const el = e.target;
  const start = el.selectionStart;
  const end = el.selectionEnd;
  prompt.value =
    prompt.value.substring(0, start) + "\n" + prompt.value.substring(end);
  nextTick(() => {
    el.selectionStart = el.selectionEnd = start + 1;
  });
}

function handleEnter(e) {
  if (!e.ctrlKey) {
    e.preventDefault();
    sendPrompt();
  }
}

// const apiBase = `http://localhost:11434/api/chat`;
const apiBase = "/api/chat";
async function sendPrompt() {
  if (!prompt.value.trim()) return;
  isLoading.value = true;
  response.value = "";
  errorMsg.value = "";

  const currentPrompt = "用中文回答：" + prompt.value;

  abortController.value = new AbortController();

  try {
    const res = await fetch(apiBase, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        model: "llama3.1",
        messages: [{ role: "user", content: currentPrompt }],
        stream: true,
      }),
      signal: abortController.value.signal,
    });

    const reader = res.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let reply = "";

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunkText = decoder.decode(value);
      const chunks = chunkText.trim().split("\n");

      for (const chunk of chunks) {
        try {
          const json = JSON.parse(chunk);
          if (json.message?.content) {
            reply += json.message.content;
            response.value += json.message.content;
          }
        } catch {
          // 忽略解析失败
        }
      }
    }

    const currentTime = new Date().toLocaleString();
    history.value.unshift({
      question: prompt.value,
      answer: reply,
      time: currentTime,
    });

    saveHistory();
    prompt.value = "";
  } catch (err) {
    if (err.name === "AbortError") {
      errorMsg.value = "已停止生成。";
    } else {
      errorMsg.value = "发送失败，请稍后重试。";
      ElMessage.error("请求失败");
    }
  } finally {
    isLoading.value = false;
    abortController.value = null;
  }
}

function stopGenerating() {
  if (abortController.value) {
    abortController.value.abort();
  }
}

function loadHistory() {
  const stored = localStorage.getItem("chatHistory");
  if (stored) {
    history.value = JSON.parse(stored).filter((item) =>
      isWithinOneWeek(item.time)
    );
  }
}

function saveHistory() {
  localStorage.setItem("chatHistory", JSON.stringify(history.value));
}

function clearHistory() {
  if (!confirm("确定要清除历史记录吗？")) return;
  localStorage.removeItem("chatHistory");
  history.value = [];
}

function isWithinOneWeek(dateString) {
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
  return new Date(dateString) > oneWeekAgo;
}

const statusClass = computed(() => ({
  "loading-status": modelStatus.value === "loading",
  "error-status": modelStatus.value === "error",
  "ready-status": modelStatus.value === "ready",
}));

function copyResponse() {
  if (!response.value) return;
  navigator.clipboard.writeText(response.value).then(
    () => {
      ElMessage.success("已复制到剪贴板");
    },
    () => {
      ElMessage.error("复制失败，请手动复制");
    }
  );
}


function deleteHistory(index) {
  history.value.splice(index, 1);
  saveHistory();
  ElMessage.success("已删除该条对话");
}
</script>

<style scoped>
.chat-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.title {
  text-align: center;
  color: #333;
  font-size: 24px;
  margin-bottom: 20px;
}

.input-group {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.input {
  flex: 1;
}

.send-button {
  width: 100px;
  align-self: flex-start;
}

.response-box {
  margin-bottom: 20px;
  padding: 10px;
  border: 1px solid #007bff;
  border-radius: 4px;
  background-color: #e6f7ff;
  overflow-wrap: break-word;
  max-height: 300px;
  overflow-y: auto;
}

.error-msg {
  color: red;
  margin-bottom: 20px;
}

.loading-status {
  color: #409eff;
  font-weight: bold;
  margin: 10px 0;
}

.error-status {
  color: red;
  font-weight: bold;
  margin: 10px 0;
}

.ready-status {
  color: #67c23a;
  font-weight: bold;
  margin: 10px 0;
}

.copy-btn {
  margin-top: -10px;
  margin-bottom: 16px;
  float: right;
}

.delete-btn {
  margin-top: -10px;
  margin-bottom: 16px;
  margin-right: 10px;
  float: right;
}

.el-timeline {
  margin-bottom: 80px;
}
</style>