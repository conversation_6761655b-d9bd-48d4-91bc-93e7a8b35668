# 安全和性能测试工具套件

这是一个专为博客系统设计的安全和性能测试工具套件，用于发现潜在的安全漏洞和性能问题。

## ⚠️ 重要声明

**这些工具仅用于测试您自己的系统！**
- 请勿在未经授权的系统上使用这些工具
- 仅用于合法的安全测试和性能评估
- 使用前请确保您有权限测试目标系统

## 🛠️ 工具列表

### 1. 综合安全测试器 (security-tester.js)
测试常见的Web安全漏洞：
- SQL注入
- XSS (跨站脚本)
- 认证绕过
- 文件上传漏洞
- DoS攻击
- 基础性能测试

### 2. API安全测试器 (api-security-tester.js)
专门测试REST API的安全性：
- API端点枚举
- HTTP方法安全性
- 输入验证
- 认证和授权
- 信息泄露
- CORS配置
- 速率限制

### 3. 负载测试器 (load-tester.js)
测试系统在高负载下的性能：
- 并发请求测试
- 响应时间统计
- 吞吐量测试
- 错误率分析
- 性能瓶颈识别

### 4. 测试运行器 (run-tests.js)
统一运行所有测试并生成综合报告

## 🚀 快速开始

### 安装依赖
```bash
# 确保Node.js已安装
node --version

# 进入测试目录
cd security-tests
```

### 运行完整测试套件
```bash
# 测试本地服务器
node run-tests.js

# 测试指定URL
node run-tests.js --url http://localhost:3000

# 只运行安全测试
node run-tests.js --tests security,api

# 自定义负载测试参数
node run-tests.js --concurrency 100 --duration 60
```

### 单独运行测试

#### 安全测试
```bash
node security-tester.js http://localhost:3000
```

#### API安全测试
```bash
node api-security-tester.js http://localhost:3000
```

#### 负载测试
```bash
# 基础负载测试
node load-tester.js --url http://localhost:3000

# 高并发测试
node load-tester.js --url http://localhost:3000 --concurrency 200 --duration 120

# 多端点测试
node load-tester.js --url http://localhost:3000 --endpoints "/,/articles,/user/profile"
```

## 📊 测试报告

测试完成后，会在 `test-reports` 目录下生成详细报告：

- `security-report-*.json` - 安全测试报告
- `api-security-report-*.json` - API安全测试报告
- `load-test-report-*.json` - 负载测试报告
- `summary-report-*.json` - 综合测试报告

## 🔍 测试项目详解

### 安全测试项目

1. **SQL注入测试**
   - 测试常见的SQL注入载荷
   - 检查错误信息泄露
   - 验证输入过滤机制

2. **XSS测试**
   - 反射型XSS
   - 存储型XSS
   - DOM型XSS

3. **认证绕过**
   - 无认证访问受保护资源
   - 弱Token验证
   - 会话管理漏洞

4. **文件上传漏洞**
   - 恶意文件上传
   - 路径遍历攻击
   - 文件类型验证绕过

### API安全测试项目

1. **端点发现**
   - 常见API端点枚举
   - 隐藏接口发现
   - 敏感文件检测

2. **HTTP方法测试**
   - 不安全的HTTP方法
   - 方法覆盖攻击
   - OPTIONS方法信息泄露

3. **输入验证**
   - 参数污染
   - 类型混淆
   - 边界值测试

### 性能测试项目

1. **并发测试**
   - 模拟多用户并发访问
   - 测试系统稳定性
   - 识别性能瓶颈

2. **响应时间分析**
   - 平均响应时间
   - 百分位数统计
   - 最大/最小响应时间

3. **吞吐量测试**
   - 每秒请求数(RPS)
   - 系统处理能力
   - 资源利用率

## 📈 结果解读

### 安全评级
- 🟢 **优秀**: 未发现明显安全漏洞
- 🟡 **良好**: 发现少量安全问题，建议修复
- 🟠 **一般**: 发现多个安全问题，需要及时修复
- 🔴 **较差**: 发现大量安全问题，存在严重风险

### 漏洞严重程度
- **CRITICAL**: 严重漏洞，可能导致系统完全妥协
- **HIGH**: 高危漏洞，可能导致数据泄露或系统损害
- **MEDIUM**: 中危漏洞，存在安全风险但影响有限
- **LOW**: 低危漏洞，安全影响较小

### 性能评级
- **RPS > 1000**: 🟢 优秀
- **RPS > 500**: 🟡 良好
- **RPS > 100**: 🟠 一般
- **RPS < 100**: 🔴 较差

## 🛡️ 安全建议

基于测试结果，以下是常见的安全加固建议：

1. **输入验证**
   - 实施严格的输入验证
   - 使用参数化查询防止SQL注入
   - 对用户输入进行HTML编码

2. **认证和授权**
   - 实施强认证机制
   - 使用安全的会话管理
   - 实施适当的访问控制

3. **安全配置**
   - 配置安全的HTTP头
   - 实施CORS策略
   - 启用HTTPS

4. **监控和日志**
   - 实施安全监控
   - 记录安全事件
   - 定期安全审计

## 🔧 自定义测试

您可以根据需要修改测试脚本：

1. **添加新的测试载荷**
2. **自定义测试端点**
3. **调整测试参数**
4. **扩展报告格式**

## 📞 支持

如果您在使用过程中遇到问题：

1. 检查Node.js版本是否兼容
2. 确保目标服务器正在运行
3. 检查网络连接
4. 查看错误日志

## 📝 许可证

此工具套件仅用于教育和合法的安全测试目的。使用者需要确保遵守相关法律法规。
