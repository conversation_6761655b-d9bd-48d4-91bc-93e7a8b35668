const db = require("../db");

// 获取所有 bug
async function getBugList() {
  const sql = "SELECT * FROM bug_record ORDER BY create_time DESC";
  return await db.query(sql);
}

// 新增 bug
async function addBug(title, description, status, severity) {
  const sql = "INSERT INTO bug_record(title, description, status, severity) VALUES(?, ?, ?, ?)";
  const result = await db.query(sql, [title, description, status, severity]);
  return result.affectedRows > 0;
}

// 修改 bug 状态和程度
async function updateBugStatusAndSeverity(id, status, severity) {
  const sql = "UPDATE bug_record SET status = ?, severity = ? WHERE id = ?";
  const result = await db.query(sql, [status, severity, id]);
  return result.affectedRows > 0;
}

// 删除 bug
async function deleteBug(id) {
  const sql = "DELETE FROM bug_record WHERE id = ?";
  const result = await db.query(sql, [id]);
  return result.affectedRows > 0;
}
// // 批量删除
async function deleteBugs(ids) {
  const sql = "DELETE FROM bug_record WHERE id IN (?)";
  const result = await db.query(sql, [ids]);
  return result.affectedRows > 0;
}

// bugsendService.js
async function updateBug(id, title, description, status, severity) {
  const sql = "UPDATE bug_record SET title = ?, description = ?, status = ?, severity = ? WHERE id = ?";
  const result = await db.query(sql, [title, description, status, severity, id]);
  return result.affectedRows > 0;
}

module.exports = {
  getBugList,
  addBug,
  updateBugStatusAndSeverity,
  deleteBug,
  deleteBugs,
  updateBug,
};
