// 本地大模型服务类
export interface LLMConfig {
  name: string;
  apiBase: string;
  modelName: string;
  timeout: number;
  maxRetries: number;
  requestFormat: string;
  description: string;
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface LLMResponse {
  content: string;
  success: boolean;
  error?: string;
}

export class LLMService {
  private config: LLMConfig;

  constructor(config: LLMConfig) {
    this.config = config;
  }

  // 更新配置
  updateConfig(config: LLMConfig): void {
    this.config = config;
  }

  // 检查连接状态
  async checkConnection(): Promise<{ success: boolean; error?: string; modelInfo?: string }> {
    try {
      const response = await this.callAPI([{ role: 'user', content: '你好' }], 5000);
      if (response.success) {
        return {
          success: true,
          modelInfo: `${this.config.name} - ${this.config.modelName}`
        };
      } else {
        return {
          success: false,
          error: response.error || '连接测试失败'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  // 带重试的 API 调用
  async callWithRetry(messages: ChatMessage[], retryCount = 0): Promise<LLMResponse> {
    try {
      return await this.callAPI(messages);
    } catch (error) {
      if (retryCount < this.config.maxRetries) {
        console.warn(`API调用失败，正在重试 (${retryCount + 1}/${this.config.maxRetries}):`, 
          error instanceof Error ? error.message : error);
        
        // 递增延迟重试
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
        return this.callWithRetry(messages, retryCount + 1);
      }
      
      return {
        content: '',
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  // 核心 API 调用方法
  private async callAPI(messages: ChatMessage[], customTimeout?: number): Promise<LLMResponse> {
    const controller = new AbortController();
    const timeout = customTimeout || this.config.timeout;
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const requestBody = this.buildRequestBody(messages);
      
      const response = await fetch(this.config.apiBase, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const content = this.extractContent(data);

      if (!content) {
        throw new Error('响应中未找到有效内容');
      }

      return {
        content: content.trim(),
        success: true
      };

    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(`请求超时 (${timeout}ms)`);
        }
        throw error;
      }
      
      throw new Error('未知错误');
    }
  }

  // 构建 Ollama 请求体
  private buildRequestBody(messages: ChatMessage[]): any {
    return {
      model: this.config.modelName,
      messages,
      stream: false,
      temperature: 0.7,
      max_tokens: 200
    };
  }

  // 从 Ollama 响应中提取内容
  private extractContent(data: any): string {
    // Ollama 格式
    if (data.message?.content) {
      return data.message.content;
    }

    return '';
  }

  // 获取配置信息
  getConfig(): LLMConfig {
    return { ...this.config };
  }
}

// 创建默认服务实例的工厂函数
export function createLLMService(config: LLMConfig): LLMService {
  return new LLMService(config);
}
