// API响应标准化中间件
const logger = require('../plugin/logger');

/**
 * 响应标准化类
 */
class ResponseStandardizer {
  static success(data = null, message = '操作成功', meta = {}) {
    return {
      success: true,
      code: 200,
      message,
      data,
      meta: {
        timestamp: new Date().toISOString(),
        ...meta
      }
    };
  }

  static error(code = 500, message = '服务器错误', details = null) {
    return {
      success: false,
      code,
      message,
      details,
      meta: {
        timestamp: new Date().toISOString()
      }
    };
  }

  static paginated(data, pagination) {
    return this.success(data, '获取成功', {
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total: pagination.total,
        totalPages: Math.ceil(pagination.total / pagination.limit)
      }
    });
  }

  static created(data = null, message = '创建成功') {
    return {
      success: true,
      code: 201,
      message,
      data,
      meta: {
        timestamp: new Date().toISOString()
      }
    };
  }

  static updated(data = null, message = '更新成功') {
    return {
      success: true,
      code: 200,
      message,
      data,
      meta: {
        timestamp: new Date().toISOString()
      }
    };
  }

  static deleted(message = '删除成功') {
    return {
      success: true,
      code: 200,
      message,
      data: null,
      meta: {
        timestamp: new Date().toISOString()
      }
    };
  }
}

/**
 * 响应标准化中间件
 * @param {Object} options 配置选项
 * @param {boolean} options.enableResponseTime 是否启用响应时间记录
 * @param {number} options.slowResponseThreshold 慢响应阈值(ms)
 */
function responseMiddleware(options = {}) {
  const {
    enableResponseTime = true,
    slowResponseThreshold = 1000
  } = options;

  return async (ctx, next) => {
    const startTime = Date.now();
    
    // 添加响应方法到ctx
    ctx.success = (data, message, meta) => {
      const responseTime = Date.now() - startTime;
      ctx.body = ResponseStandardizer.success(data, message, {
        ...meta,
        ...(enableResponseTime && { responseTime: `${responseTime}ms` }),
        requestId: ctx.state.requestId
      });
    };

    ctx.error = (code, message, details) => {
      const responseTime = Date.now() - startTime;
      ctx.status = code;
      ctx.body = ResponseStandardizer.error(code, message, details);
      if (enableResponseTime) {
        ctx.body.meta.responseTime = `${responseTime}ms`;
      }
      ctx.body.meta.requestId = ctx.state.requestId;
    };

    ctx.paginated = (data, pagination) => {
      const responseTime = Date.now() - startTime;
      ctx.body = ResponseStandardizer.paginated(data, pagination);
      if (enableResponseTime) {
        ctx.body.meta.responseTime = `${responseTime}ms`;
      }
      ctx.body.meta.requestId = ctx.state.requestId;
    };

    ctx.created = (data, message) => {
      const responseTime = Date.now() - startTime;
      ctx.status = 201;
      ctx.body = ResponseStandardizer.created(data, message);
      if (enableResponseTime) {
        ctx.body.meta.responseTime = `${responseTime}ms`;
      }
      ctx.body.meta.requestId = ctx.state.requestId;
    };

    ctx.updated = (data, message) => {
      const responseTime = Date.now() - startTime;
      ctx.body = ResponseStandardizer.updated(data, message);
      if (enableResponseTime) {
        ctx.body.meta.responseTime = `${responseTime}ms`;
      }
      ctx.body.meta.requestId = ctx.state.requestId;
    };

    ctx.deleted = (message) => {
      const responseTime = Date.now() - startTime;
      ctx.body = ResponseStandardizer.deleted(message);
      if (enableResponseTime) {
        ctx.body.meta.responseTime = `${responseTime}ms`;
      }
      ctx.body.meta.requestId = ctx.state.requestId;
    };

    await next();

    // 记录响应统计
    if (enableResponseTime) {
      const responseTime = Date.now() - startTime;
      
      if (responseTime > slowResponseThreshold) {
        logger.warn('慢响应检测', {
          requestId: ctx.state.requestId,
          method: ctx.method,
          url: ctx.url,
          responseTime,
          status: ctx.status,
          threshold: slowResponseThreshold
        });
      }
    }
  };
}

module.exports = { 
  ResponseStandardizer, 
  responseMiddleware 
};
