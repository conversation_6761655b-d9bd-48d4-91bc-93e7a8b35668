<template>
  <div class="app-layout">
    <!-- 顶部导航栏 -->
    <header class="app-header" v-if="!isMobile">
      <div class="header-container">
        <!-- 左侧品牌区域 -->
        <div class="brand-section">
          <div class="logo">
            <el-icon class="logo-icon">
              <House />
            </el-icon>
            <span class="brand-name">个人博客系统</span>
          </div>
        </div>

        <!-- 中间导航菜单 -->
        <nav class="nav-section">
          <el-menu mode="horizontal" :default-active="activeIndex" class="main-nav" :ellipsis="false">
            <!-- 首页 -->
            <el-menu-item index="home" @click="goTo('/index/home')">
              <el-icon>
                <House />
              </el-icon>
              <span>首页</span>
            </el-menu-item>

            <!-- 内容管理 -->
            <el-sub-menu index="content">
              <template #title>
                <el-icon>
                  <Document />
                </el-icon>
                <span>内容</span>
              </template>
              <el-menu-item index="articles" @click="goTo('/index/allarticles')">
                <el-icon>
                  <Reading />
                </el-icon>
                <span>浏览文章</span>
              </el-menu-item>
              <el-menu-item index="smart-edit" @click="goTo('/index/smart-edit')">
                <el-icon>
                  <Edit />
                </el-icon>
                <span>AI智能写作</span>
              </el-menu-item>
              <el-menu-item index="english-translator" @click="goTo('/index/english-translator')">
                <el-icon>
                  <Document />
                </el-icon>
                <span>英语翻译</span>
              </el-menu-item>
            </el-sub-menu>

            <!-- 媒体娱乐 -->
            <el-sub-menu index="media">
              <template #title>
                <el-icon>
                  <VideoPlay />
                </el-icon>
                <span>媒体</span>
              </template>
              <el-menu-item index="media-lib" @click="handleCurrentChange">
                <el-icon>
                  <Picture />
                </el-icon>
                <span>媒体库</span>
              </el-menu-item>
              <el-menu-item index="photo-wall" @click="handlePhotoWallClick">
                <el-icon>
                  <PictureFilled />
                </el-icon>
                <span>照片墙</span>
              </el-menu-item>
              <el-menu-item index="music" @click="openMusic">
                <el-icon>
                  <Headset />
                </el-icon>
                <span>音乐播放器</span>
              </el-menu-item>
            </el-sub-menu>

            <!-- 工具功能 -->
            <el-sub-menu index="tools">
              <template #title>
                <el-icon>
                  <Tools />
                </el-icon>
                <span>工具</span>
              </template>
              <el-menu-item index="chat" @click="goTo('/index/chat')">
                <el-icon>
                  <ChatDotRound />
                </el-icon>
                <span>聊天室</span>
              </el-menu-item>
              <el-menu-item index="video-call" @click="goTo('/index/videovcall')">
                <el-icon>
                  <VideoPlay />
                </el-icon>
                <span>视频通话</span>
              </el-menu-item>
              <el-menu-item index="friends" @click="goTo('/index/dashboard/friends')">
                <el-icon>
                  <User />
                </el-icon>
                <span>好友管理</span>
              </el-menu-item>
              <el-menu-item index="ai-model" @click="goTo('/index/model')">
                <el-icon>
                  <Cpu />
                </el-icon>
                <span>AI大模型</span>
              </el-menu-item>
              <el-menu-item index="resources" @click="goTo('/index/resource')">
                <el-icon>
                  <Folder />
                </el-icon>
                <span>文件资源</span>
              </el-menu-item>
              <el-menu-item index="upload" @click="goTo('/index/uploadsmobile')">
                <el-icon>
                  <Upload />
                </el-icon>
                <span>文件上传</span>
              </el-menu-item>
            </el-sub-menu>

            <!-- 管理后台 -->
            <el-sub-menu index="admin" v-if="isLoggedIn">
              <template #title>
                <el-icon>
                  <Setting />
                </el-icon>
                <span>管理</span>
              </template>
              <el-menu-item index="dashboard" @click="goTo('/index/dashboard')">
                <el-icon>
                  <DataBoard />
                </el-icon>
                <span>仪表盘</span>
              </el-menu-item>
              <el-menu-item index="dashboard" @click="goTo('/index/demo')">
                <el-icon>
                  <DataBoard />
                </el-icon>
                <span>测试</span>
              </el-menu-item>

            </el-sub-menu>
          </el-menu>
        </nav>

        <!-- 右侧用户区域 -->
        <div class="user-section">
          <div class="user-info" v-if="isLoggedIn">
            <el-avatar :size="32" class="user-avatar">
              {{ username?.charAt(0)?.toUpperCase() }}
            </el-avatar>
            <span class="username">{{ username }}</span>
          </div>

          <div class="action-buttons">
            <el-button v-if="!isLoggedIn" type="primary" @click="goTo('/login')" class="login-btn">
              登录
            </el-button>
            <el-button v-else type="danger" @click="logout" class="logout-btn" :icon="SwitchButton">
              退出
            </el-button>
          </div>
        </div>
      </div>
    </header>

    <!-- 移动端导航 -->
    <div class="mobile-header" v-if="isMobile">
      <div class="mobile-header-content">
        <div class="mobile-brand">
          <el-icon class="mobile-logo">
            <House />
          </el-icon>
          <span class="mobile-brand-name">博客系统</span>
        </div>
        <el-button class="mobile-menu-btn" :icon="Menu" @click="drawerVisible = true" circle type="primary" />
      </div>
    </div>

    <!-- 移动端侧边栏 -->
    <el-drawer v-model="drawerVisible" direction="ltr" size="280px" class="mobile-drawer">
      <template #header>
        <div class="drawer-header">
          <div class="drawer-user-info" v-if="isLoggedIn">
            <el-avatar :size="40">{{ username?.charAt(0)?.toUpperCase() }}</el-avatar>
            <div class="drawer-user-text">
              <div class="drawer-username">{{ username }}</div>
              <div class="drawer-user-role">管理员</div>
            </div>
          </div>
          <div class="drawer-guest-info" v-else>
            <el-icon class="guest-icon">
              <User />
            </el-icon>
            <span>访客模式</span>
          </div>
        </div>
      </template>

      <el-menu mode="vertical" :default-active="activeIndex" @select="drawerVisible = false" class="mobile-menu">
        <!-- 首页 -->
        <el-menu-item index="home" @click="goTo('/index/home')">
          <el-icon>
            <House />
          </el-icon>
          <span>首页</span>
        </el-menu-item>

        <!-- 内容管理 -->
        <el-sub-menu index="content">
          <template #title>
            <el-icon>
              <Document />
            </el-icon>
            <span>内容管理</span>
          </template>
          <el-menu-item index="articles" @click="goTo('/index/allarticles')">
            <el-icon>
              <Reading />
            </el-icon>
            <span>浏览文章</span>
          </el-menu-item>
          <el-menu-item index="viewhistory" @click="goTo('/index/dashboard/viewhistory')" v-if="isLoggedIn">
            <el-icon>
              <Clock />
            </el-icon>
            <span>浏览记录</span>
          </el-menu-item>
          <el-menu-item index="smart-edit" @click="goTo('/index/smart-edit')">
            <el-icon>
              <Edit />
            </el-icon>
            <span>AI智能写作</span>
          </el-menu-item>
          <el-menu-item index="english-translator" @click="goTo('/index/english-translator')">
            <el-icon>
              <Document />
            </el-icon>
            <span>英语翻译</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 媒体娱乐 -->
        <el-sub-menu index="media">
          <template #title>
            <el-icon>
              <VideoPlay />
            </el-icon>
            <span>媒体娱乐</span>
          </template>
          <el-menu-item index="media-lib" @click="handleCurrentChange">
            <el-icon>
              <Picture />
            </el-icon>
            <span>媒体库</span>
          </el-menu-item>
          <el-menu-item index="photo-wall" @click="handlePhotoWallClick">
            <el-icon>
              <PictureFilled />
            </el-icon>
            <span>照片墙</span>
          </el-menu-item>
          <el-menu-item index="music" @click="openMusic">
            <el-icon>
              <Headset />
            </el-icon>
            <span>音乐播放器</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 工具功能 -->
        <el-sub-menu index="tools">
          <template #title>
            <el-icon>
              <Tools />
            </el-icon>
            <span>工具功能</span>
          </template>
          <el-menu-item index="chat" @click="goTo('/index/chat')">
            <el-icon>
              <ChatDotRound />
            </el-icon>
            <span>聊天室</span>
          </el-menu-item>
          <el-menu-item index="video-call" @click="goTo('/index/videovcall')">
            <el-icon>
              <VideoPlay />
            </el-icon>
            <span>视频通话</span>
          </el-menu-item>
          <el-menu-item index="friends" @click="goTo('/index/dashboard/friends')">
            <el-icon>
              <User />
            </el-icon>
            <span>好友管理</span>
          </el-menu-item>
          <el-menu-item index="ai-model" @click="goTo('/index/model')">
            <el-icon>
              <Cpu />
            </el-icon>
            <span>AI大模型</span>
          </el-menu-item>
          <el-menu-item index="resources" @click="goTo('/index/resource')">
            <el-icon>
              <Folder />
            </el-icon>
            <span>文件资源</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 管理后台 -->
        <el-sub-menu index="admin" v-if="isLoggedIn">
          <template #title>
            <el-icon>
              <Setting />
            </el-icon>
            <span>管理后台</span>
          </template>
          <el-menu-item index="dashboard" @click="goTo('/index/dashboard')">
            <el-icon>
              <DataBoard />
            </el-icon>
            <span>仪表盘</span>
          </el-menu-item>
          <el-menu-item index="upload" @click="goTo('/index/uploadsmobile')">
            <el-icon>
              <Upload />
            </el-icon>
            <span>移动上传</span>
          </el-menu-item>
        </el-sub-menu>

        <el-divider />

        <!-- 登录/退出 -->
        <el-menu-item v-if="!isLoggedIn" index="login" @click="goTo('/login')">
          <el-icon>
            <User />
          </el-icon>
          <span>登录</span>
        </el-menu-item>
        <el-menu-item v-else index="logout" @click="logout">
          <el-icon>
            <SwitchButton />
          </el-icon>
          <span>退出登录</span>
        </el-menu-item>
      </el-menu>
    </el-drawer>

    <!-- 主内容区域 -->
    <main class="main-content">
      <!-- 面包屑导航 -->
      <div class="breadcrumb-section" v-if="showBreadcrumb">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/index/home' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item v-for="item in breadcrumbItems" :key="item.path" :to="{ path: item.path }">
            {{ item.name }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 公开访问提示区域 -->
      <div class="public-access-banner" v-if="!isLoggedIn && showPublicBanner">
        <el-card class="banner-card" shadow="hover">
          <div class="banner-content">
            <div class="banner-text">
              <h3 class="banner-title">
                <el-icon class="banner-icon">
                  <Star />
                </el-icon>
                欢迎访问个人博客系统
              </h3>
              <p class="banner-subtitle">以下功能无需登录即可使用，快来体验吧！</p>
            </div>
            <div class="quick-actions">
              <el-button type="primary" @click="goTo('/index/allarticles')" class="action-btn">
                <el-icon>
                  <Reading />
                </el-icon>
                浏览文章
              </el-button>
              <el-button type="success" @click="goTo('/index/music')" class="action-btn">
                <el-icon>
                  <Headset />
                </el-icon>
                音乐播放器
              </el-button>
              <el-button type="warning" @click="handlePhotoWallClick" class="action-btn">
                <el-icon>
                  <PictureFilled />
                </el-icon>
                照片墙
              </el-button>
              <el-button type="info" @click="goTo('/login')" class="action-btn">
                <el-icon>
                  <User />
                </el-icon>
                登录系统
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 页面内容 -->
      <div class="page-content">
        <keep-alive>
          <router-view />
        </keep-alive>
      </div>
    </main>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed } from "vue";
import { useRouter } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import { logoutApi, VerifyPasswordApi } from "@/utils/api";
import {
  House,
  Document,
  Reading,
  Edit,
  VideoPlay,
  Picture,
  PictureFilled,
  Headset,
  Tools,
  ChatDotRound,
  Cpu,
  Folder,
  Setting,
  DataBoard,
  Upload,
  SwitchButton,
  User,
  Menu,
  Star,
  Clock
} from '@element-plus/icons-vue'

const router = useRouter();
const active = ref(0);
const isMobile = ref(window.innerWidth <= 768);
const drawerVisible = ref(false);
const activeIndex = ref("home");
const username = localStorage.getItem("username");
const isLoggedIn = computed(() => !!localStorage.getItem("token"));

// 面包屑导航
const showBreadcrumb = ref(false);
const breadcrumbItems = ref([]);

// 公开访问横幅
const showPublicBanner = computed(() => {
  const currentPath = router.currentRoute.value.path;
  return currentPath === '/index/home' || currentPath === '/index';
});

const handleResize = () => {
  isMobile.value = window.innerWidth <= 768;
};
onMounted(() => {
  window.addEventListener("resize", handleResize);
  handleResize();
});
onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
});

// 跳转
const goTo = (path: string) => {
  router.push(path);
};

// 退出登录
const logout = async () => {
  try {
    await ElMessageBox.confirm("确定退出登录吗？");
    const res = await logoutApi(username);
    if (res.message === "退出登录成功") {
      localStorage.clear();
      router.push("/login");
    } else {
      ElMessage.error(res.message);
    }
  } catch (e) { }
};

// 照片墙密码验证
const passwordPrompt = async (title: string) => {
  try {
    const { value } = await ElMessageBox.prompt(
      `请输入${title}的密码`,
      "身份验证",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputType: "password",
      }
    );
    return value;
  } catch (error) {
    return null;
  }
};

const handlePhotoWallClick = async () => {
  // 不再读取 localStorage
  // const verified = localStorage.getItem("photo_wall_verified");
  // if (verified === "1") {
  //   router.push("/index/images");
  //   return;
  // }

  const value = await passwordPrompt("照片墙");
  if (value === null) {
    return; // 用户取消
  }

  try {
    const res = await VerifyPasswordApi({
      passwordType: "photo_wall",
      password: value
    });

    if (res?.data?.isValid) {
      // 不再设置 localStorage
      router.push("/index/images");
    } else {
      ElMessage.error("密码错误，无法访问照片墙");
    }
  } catch (error) {
    console.error("密码验证失败:", error);
    ElMessage.error("密码验证失败，请稍后重试");
  }
};

const handleCurrentChange = async () => {
  // 不再读取 localStorage
  // const verified = localStorage.getItem("media_verified");
  // if (verified === "1") {
  //   router.push("/index/media");
  //   return;
  // }

  const value = await passwordPrompt("媒体访问");
  if (value === null) {
    return; // 用户取消
  }

  try {
    const res = await VerifyPasswordApi({
      passwordType: "media",
      password: value
    });

    if (res?.data?.isValid) {
      // 不再设置 localStorage
      router.push("/index/media");
    } else {
      ElMessage.error("密码错误，无法访问媒体页面");
    }
  } catch (error) {
    console.error("密码验证失败:", error);
    ElMessage.error("密码验证失败，请稍后重试");
  }
};

const openMusic = () => {
  window.open('/index/music', '_blank');
};



const openDashboard = () => {
  window.open('/index/dashboard/board', '_blank');
};

// tabbar逻辑
onMounted(() => {
  isMobile.value =
    /Android|webOS|iPhone|iPod|BlackBerry|iPad|Windows Phone/i.test(
      navigator.userAgent
    ) || window.innerWidth <= 768;
});

const onChange = (index: number) => {
  let realIndex = index;
  if (isMobile.value && index > 0) realIndex++;
  if (realIndex === 2) {
    active.value = active.value;
    return;
  }
  switch (realIndex) {
    case 0:
      router.push("/index/home");
      break;
    case 1:
      router.push("/index/files");
      break;
    case 3:
      router.push("/index/settings");
      break;
  }
};
</script>

<style scoped lang="less">
.app-layout {
  min-height: 100vh;
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  position: relative;
}

/* 桌面端头部 */
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  .header-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    height: 70px;
    position: relative;
  }

  .brand-section {
    .logo {
      display: flex;
      align-items: center;
      gap: 12px;
      color: #2c3e50;
      font-weight: 700;
      font-size: 18px;

      .logo-icon {
        font-size: 24px;
        color: #667eea;
      }

      .brand-name {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }
  }

  .nav-section {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    /* 确保导航区域垂直居中 */
    height: 70px;
    /* 与整个头部同高 */

    .main-nav {
      background: transparent;
      border: none;
      height: 70px;
      line-height: 70px;

      :deep(.el-menu-item),
      :deep(.el-sub-menu) {
        margin: 0 2px;
        border-radius: 8px;
        transition: all 0.3s ease;
        height: 70px;
        /* 改为与容器同高 */
        line-height: 70px;
        /* 改为与容器同高 */
        display: flex;
        /* 改为flex */
        align-items: center;
        vertical-align: middle;

        &:hover {
          background: rgba(102, 126, 234, 0.1);
        }
      }

      :deep(.el-menu-item) {
        color: #2c3e50;
        font-weight: 500;
        border: none;
        padding: 0 16px;
        height: 40px;
        line-height: 40px;

        &:hover {
          background: rgba(102, 126, 234, 0.1);
          color: #667eea;
        }

        &.is-active {
          background: rgba(102, 126, 234, 0.15);
          color: #667eea !important;
          border-radius: 8px;
          border: 1px solid rgba(102, 126, 234, 0.3);
          font-weight: 600;

          &:hover {
            background: rgba(102, 126, 234, 0.2);
            color: #667eea !important;
            border-color: rgba(102, 126, 234, 0.4);
          }
        }

        .el-icon {
          margin-right: 6px;
          font-size: 16px;
        }

        span {
          font-size: 14px;
        }
      }

      :deep(.el-sub-menu) {
        .el-sub-menu__title {
          color: #2c3e50;
          font-weight: 500;
          border: none;
          padding: 0 16px;
          height: 40px;
          line-height: 40px;

          &:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
          }

          .el-icon {
            margin-right: 6px;
            font-size: 16px;
          }

          span {
            font-size: 14px;
          }
        }

        &.is-active .el-sub-menu__title {
          background: rgba(102, 126, 234, 0.15);
          color: #667eea !important;
          border-radius: 8px;
          border: 1px solid rgba(102, 126, 234, 0.3);
          font-weight: 600;

          &:hover {
            background: rgba(102, 126, 234, 0.2);
            color: #667eea !important;
            border-color: rgba(102, 126, 234, 0.4);
          }
        }

        .el-sub-menu__icon-arrow {
          margin-left: 6px;
        }
      }

      /* 确保所有菜单项在同一水平线上 */
      :deep(.el-menu--horizontal) {
        display: flex !important;
        align-items: center !important;
        height: 70px !important;
        border-bottom: none !important;
        line-height: 70px !important;

        >.el-menu-item,
        >.el-sub-menu {
          float: none !important;
          display: flex !important;
          align-items: center !important;
          height: 70px !important;
          line-height: 70px !important;
          vertical-align: middle !important;
          position: relative !important;
          top: 0 !important;
          margin: 0 !important;
          padding-top: 0 !important;
          padding-bottom: 0 !important;
          border-bottom: none !important;
        }

        >.el-sub-menu>.el-sub-menu__title {
          border-bottom: none !important;
          height: 70px !important;
          line-height: 70px !important;
          display: flex !important;
          align-items: center !important;
          margin: 0 !important;
          padding-top: 0 !important;
          padding-bottom: 0 !important;
        }
      }

      /* 强制重置所有可能影响对齐的样式 */
      :deep(.el-menu-item),
      :deep(.el-sub-menu__title) {
        border-bottom: none !important;
        margin: 0 !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        box-sizing: border-box !important;
        display: flex !important;
        align-items: center !important;
        height: 70px !important;
        line-height: 70px !important;
      }

      /* 确保图标和文字对齐 */
      :deep(.el-menu-item .el-icon),
      :deep(.el-sub-menu__title .el-icon) {
        margin-right: 6px !important;
        vertical-align: middle !important;
      }

      /* 确保下拉箭头对齐 */
      :deep(.el-sub-menu__icon-arrow) {
        margin-left: 6px !important;
        vertical-align: middle !important;
      }
    }
  }

  .user-section {
    display: flex;
    align-items: center;
    gap: 16px;
    height: 70px;
    /* 与导航菜单同高 */

    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background: rgba(102, 126, 234, 0.1);
      border-radius: 20px;
      height: auto;
      /* 让内容决定高度 */

      .user-avatar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }

      .username {
        color: #2c3e50;
        font-weight: 500;
        font-size: 14px;
        line-height: 1;
      }
    }

    .action-buttons {

      .login-btn,
      .logout-btn {
        border-radius: 20px;
        padding: 8px 20px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
      }

      .login-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
      }
    }
  }
}

/* 移动端头部 */
.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  .mobile-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    height: 60px;

    .mobile-brand {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #2c3e50;
      font-weight: 700;

      .mobile-logo {
        font-size: 20px;
        color: #667eea;
      }

      .mobile-brand-name {
        font-size: 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .mobile-menu-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
  }
}

/* 移动端抽屉 */
.mobile-drawer {
  :deep(.el-drawer__header) {
    padding: 0;
    margin-bottom: 0;
  }

  .drawer-header {
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    .drawer-user-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .drawer-user-text {
        .drawer-username {
          font-weight: 600;
          font-size: 16px;
        }

        .drawer-user-role {
          font-size: 12px;
          opacity: 0.8;
        }
      }
    }

    .drawer-guest-info {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;

      .guest-icon {
        font-size: 20px;
      }
    }
  }

  .mobile-menu {
    border: none;
    padding: 16px 0;

    :deep(.el-menu-item),
    :deep(.el-sub-menu__title) {
      height: 48px;
      line-height: 48px;
      padding: 0 20px;
      margin: 4px 16px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
      }
    }

    :deep(.is-active) {
      background: rgba(102, 126, 234, 0.15);
      color: #667eea !important;
      border: 1px solid rgba(102, 126, 234, 0.3);
      font-weight: 600;

      &:hover {
        background: rgba(102, 126, 234, 0.2);
        color: #667eea !important;
        border-color: rgba(102, 126, 234, 0.4);
      }
    }
  }
}

/* 主内容区域 */
.main-content {
  padding-top: 70px;
  min-height: calc(100vh - 70px);
  position: relative;

  .breadcrumb-section {
    padding: 16px 24px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);

    :deep(.el-breadcrumb) {
      font-weight: 500;
    }
  }

  .public-access-banner {
    padding: 24px;

    .banner-card {
      border-radius: 16px;
      border: none;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

      .banner-content {
        display: flex;
        flex-direction: column;
        gap: 20px;
        text-align: center;

        .banner-text {
          .banner-title {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;

            .banner-icon {
              color: #ffd700;
              font-size: 28px;
            }
          }

          .banner-subtitle {
            margin: 0;
            color: #6c757d;
            font-size: 16px;
          }
        }

        .quick-actions {
          display: flex;
          gap: 12px;
          justify-content: center;
          flex-wrap: wrap;

          .action-btn {
            border-radius: 20px;
            padding: 12px 24px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }
          }
        }
      }
    }
  }

  .page-content {
    position: relative;
    z-index: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header {
    display: none;
  }

  .main-content {
    padding-top: 60px;
    min-height: calc(100vh - 60px);

    .breadcrumb-section {
      padding: 12px 16px;
    }

    .public-access-banner {
      padding: 16px;

      .banner-content {
        .banner-text .banner-title {
          font-size: 20px;
        }

        .quick-actions {
          .action-btn {
            padding: 10px 20px;
            font-size: 14px;
          }
        }
      }
    }
  }
}

@media (min-width: 769px) {
  .mobile-header {
    display: none;
  }
}

/* 动画效果 */
.banner-card {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 确保页面内容层级正确 */
:deep(.el-container),
:deep(.el-main),
:deep(.el-aside) {
  position: relative;
  z-index: 1;
}

/* 全局重置 */
:deep(body),
:deep(html),
:deep(#app) {
  margin: 0;
  padding: 0;
}

:deep(.page-content) {
  position: relative;
  z-index: 1;
}
</style>
