<template>
  <div class="music-player" :style="{ backgroundImage: `url(${coverUrl})` }">
    <div class="music-overlay"></div>
    <el-card class="music-card content-layer">
      <div class="music-container">
        <section class="music-left">
          <div class="cover-section">
            <el-image v-if="coverUrl" :src="coverUrl" fit="cover" class="cover-img" :class="{ rotating: isPlaying }"
              @click="playMusicByCover">
              <template #error>
                <div class="image-slot">
                  <el-icon>
                    <PictureFilled />
                  </el-icon>
                  <span>暂无封面</span>
                </div>
              </template>
            </el-image>
            <div class="play-controls">
              <el-button
                circle
                size="small"
                @click="playMusicByCover"
                class="play-btn"
              >
                <el-icon>
                  <VideoPause v-if="isPlaying" />
                  <VideoPlay v-else />
                </el-icon>
              </el-button>
            </div>
          </div>

          <div class="music-info">
            <h3 class="song-title">{{ currentMusic || "请选择歌曲" }}</h3>
            <div class="lyric-container">
              <el-scrollbar v-if="lyricLines.length" class="lyric-scroll">
                <div class="lyrics">
                  <div v-for="(line, idx) in lyricLines" :key="idx" :id="`lyric-${idx}`"
                    :class="['lyric-line', { active: idx === currentLine }]">
                    {{ line.text }}
                  </div>
                </div>
              </el-scrollbar>
              <div v-else class="no-lyric">
                <el-icon>
                  <Document />
                </el-icon>
                <span>暂无歌词信息</span>
              </div>
            </div>
          </div>
        </section>

        <section class="music-right">
          <div class="playlist-header">
            <h4 class="playlist-title">
              <el-icon>
                <Headset />
              </el-icon>
              歌曲列表
            </h4>
            <el-input
              v-model="searchKeyword"
              placeholder="搜索歌曲名"
              clearable
              size="small"
              class="search-input"
            >
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>

          <div class="song-list-wrapper">
            <el-table 
              :data="filteredMusicList" 
              style="width: 100%; cursor: pointer;" 
              highlight-current-row 
              @row-click="playMusic"
              :row-class-name="rowClassName" 
              size="small"
              class="music-table"
            >
              <el-table-column prop="name" label="歌曲名" min-width="200">
                <template #default="{ row }">
                  <div class="song-name">
                    <el-icon class="song-icon">
                      <Headset />
                    </el-icon>
                    <span>{{ row.name }}</span>
                  </div>
                </template>
              </el-table-column>
              <template #empty>
                <div class="empty-state">
                  <el-icon>
                    <Search />
                  </el-icon>
                  <span>暂无匹配的歌曲~</span>
                </div>
              </template>
            </el-table>
          </div>
        </section>
      </div>

      <div class="audio-controls">
        <audio
          v-if="musicUrl"
          ref="audioRef"
          :src="musicUrl"
          controls
          autoplay
          preload="auto"
          class="audio-player"
          @timeupdate="onTimeUpdate"
          @play="isPlaying = true"
          @pause="isPlaying = false"
          @ended="playNext"   
        ></audio>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import { getMusicListApi, getLyricsApi } from "@/utils/api";
import { Document, Headset, Search, PictureFilled, VideoPlay, VideoPause } from "@element-plus/icons-vue";

interface LyricLine {
  time: number;
  text: string;
}

const musicList = ref<{ name: string }[]>([]);
const musicUrl = ref<string | null>(null);
const audioRef = ref<HTMLAudioElement | null>(null);
const currentMusic = ref<string>("");
const lyrics = ref<string>("");
const coverUrl = ref<string>("");
const isPlaying = ref(false);
const lyricLines = ref<LyricLine[]>([]);
const currentLine = ref(0);

import { computed } from "vue";

const searchKeyword = ref(""); // 搜索关键词

// 计算属性：根据关键词过滤歌曲
const filteredMusicList = computed(() =>
  musicList.value.filter((item) =>
    item.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
);


function parseLrc(lrc: string): LyricLine[] {
  const lines = lrc.split("\n");
  const timeExp = /\[(\d{2}):(\d{2})(?:\.(\d{2,3}))?\]/;
  return lines
    .map((line) => {
      const match = timeExp.exec(line);
      if (match) {
        const min = parseInt(match[1]);
        const sec = parseInt(match[2]);
        const ms = match[3] ? parseInt(match[3].padEnd(3, "0")) : 0;
        const time = min * 60 + sec + ms / 1000;
        const text = line.replace(timeExp, "").trim();
        return { time, text };
      }
      return null;
    })
    .filter((line): line is LyricLine => line !== null);
}

async function loadMusicData(name: string) {
  currentMusic.value = name;
  coverUrl.value = `/api/music/cover/${name}`;
  musicUrl.value = `/api/music/stream/${name}`;
  currentLine.value = 0;

  try {
    const lyricRes = await getLyricsApi(name);
    const lrc = typeof lyricRes === "string" ? lyricRes : lyricRes.data;
    lyrics.value = lrc;
    lyricLines.value = parseLrc(lrc);
  } catch {
    lyrics.value = "";
    lyricLines.value = [];
  }

  nextTick(() => {
    if (audioRef.value) {
      audioRef.value.currentTime = 0;
      audioRef.value
        .play()
        .catch((err) => console.warn("自动播放失败：", err));
    }
  });
}

onMounted(async () => {
  const res = await getMusicListApi();
  if (res.code === 0) {
    musicList.value = res.data.map((name: string) => ({ name }));
    if (musicList.value.length) {
      currentMusic.value = musicList.value[0].name;
      coverUrl.value = `/api/music/cover/${currentMusic.value}`;
      try {
        const lyricRes = await getLyricsApi(currentMusic.value);
        const lrc = typeof lyricRes === "string" ? lyricRes : lyricRes.data;
        lyrics.value = lrc;
        lyricLines.value = parseLrc(lrc);
      } catch {
        lyrics.value = "";
        lyricLines.value = [];
      }
    }
  }
});

const playMusic = (row: { name: string }) => loadMusicData(row.name);

const playMusicByCover = () => {
  if (!currentMusic.value || !audioRef.value) return;
  if (audioRef.value.paused) {
    audioRef.value.play().catch(console.warn);
  } else {
    audioRef.value.pause();
  }
};

const playNext = () => {
  if (!musicList.value.length) return;
  const idx = musicList.value.findIndex(item => item.name === currentMusic.value);
  let nextIdx = idx + 1;
  if (nextIdx >= musicList.value.length) nextIdx = 0; // 循环播放
  loadMusicData(musicList.value[nextIdx].name);
};

const rowClassName = ({ row }: { row: { name: string } }) =>
  row.name === currentMusic.value ? "current-row" : "";

const onTimeUpdate = () => {
  if (!audioRef.value || !lyricLines.value.length) return;
  const currentTime = audioRef.value.currentTime;
  for (let i = lyricLines.value.length - 1; i >= 0; i--) {
    if (currentTime >= lyricLines.value[i].time) {
      if (currentLine.value !== i) {
        currentLine.value = i;
        scrollToCurrentLine();
      }
      break;
    }
  }
};

const scrollToCurrentLine = () => {
  nextTick(() => {
    const el = document.getElementById(`lyric-${currentLine.value}`);
    const container = document.querySelector(".lyric-scroll");
    if (el && container) {
      const offsetTop =
        el.offsetTop - container.clientHeight / 2 + el.clientHeight / 2;
      container.scrollTo({ top: offsetTop, behavior: "smooth" });
    }
  });
};
</script>

<style lang="less" scoped>
.music-player {
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 40px auto;
  padding: 20px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  box-sizing: border-box;
  overflow: hidden;
  min-width: 720px;
  min-height: 480px;
  border-radius: 16px;
}

.music-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
  border-radius: 16px;
  z-index: 0;
}

.content-layer {
  position: relative;
  z-index: 1;
  background: transparent;
}

.music-card {
  padding: 32px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  opacity: 0.8;
}

.music-container {
  display: flex;
  justify-content: space-between;
  gap: 3rem;
  flex-wrap: wrap;
}

.music-left,
.music-right {
  flex: 1 1 45%;
  min-width: 300px;
}

.cover-section {
  position: relative;
  text-align: center;
  margin-bottom: 24px;
}

.cover-img {
  width: 140px;
  height: 140px;
  margin: 0 auto;
  border-radius: 50%;
  object-fit: cover;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  }
}

.rotating {
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

.play-controls {
  position: absolute;
  bottom: 10px;
  right: 10px;
}

.play-btn {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  &:hover {
    background: #409eff;
    color: white;
    transform: scale(1.1);
  }
}

.music-info {
  text-align: center;
}

.song-title {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.lyric-container {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.lyric-scroll {
  max-height: 200px;
  background: transparent;
  border-radius: 8px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(226, 232, 240, 0.3);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
  }
}

.lyrics {
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
}

.no-lyric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #94a3b8;
  font-style: italic;

  .el-icon {
    font-size: 24px;
    opacity: 0.6;
  }
}

.lyric-line {
  padding: 4px 0;
  transition: all 0.3s ease;
  border-radius: 4px;
  margin: 2px 0;
}

.lyric-line.active {
  color: #409eff;
  font-weight: 600;
  font-size: 16px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(64, 158, 255, 0.05));
  padding: 6px 12px;
  margin: 4px 0;
  border-left: 3px solid #409eff;
}

.image-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #94a3b8;
  gap: 8px;

  .el-icon {
    font-size: 32px;
    opacity: 0.6;
  }
}

.playlist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(226, 232, 240, 0.8);
}

.playlist-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;

  .el-icon {
    color: #409eff;
  }
}

.search-input {
  width: 220px;
  
  :deep(.el-input__inner) {
    border-radius: 20px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    background: rgba(248, 250, 252, 0.8);
    
    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

.song-list-wrapper {
  flex: 1;
  max-height: 320px;
  overflow-y: auto;
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 12px;
  background: rgba(248, 250, 252, 0.8);
}

.song-list-wrapper::-webkit-scrollbar {
  width: 6px;
}

.song-list-wrapper::-webkit-scrollbar-track {
  background: rgba(226, 232, 240, 0.3);
  border-radius: 3px;
}

.song-list-wrapper::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3px;
}

.song-list-wrapper::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
}

.music-table {
  :deep(.el-table__header) {
    background: rgba(248, 250, 252, 0.9);
    
    th {
      background: transparent;
      color: #64748b;
      font-weight: 600;
      border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    }
  }
  
  :deep(.el-table__body) {
    tr {
      transition: all 0.2s ease;
      
      &:hover {
        background: rgba(64, 158, 255, 0.05) !important;
      }
    }
  }
}

.song-name {
  display: flex;
  align-items: center;
  gap: 8px;

  .song-icon {
    color: #409eff;
    font-size: 14px;
  }
}

.current-row {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(64, 158, 255, 0.05)) !important;
  border-left: 3px solid #409eff;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #94a3b8;
  padding: 40px 20px;

  .el-icon {
    font-size: 32px;
    opacity: 0.6;
  }
}

.audio-controls {
  margin-top: 24px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(226, 232, 240, 0.8);
}

.audio-player {
  width: 100%;
  border-radius: 8px;
  
  &::-webkit-media-controls-panel {
    background: rgba(248, 250, 252, 0.9);
  }
  
  &::-webkit-media-controls-play-button {
    background: #409eff;
    border-radius: 50%;
  }
}

@media (max-width: 768px) {
  .music-player {
    padding: 16px;
    margin: 20px auto;
    min-width: auto;
    border-radius: 12px;
  }

  .music-card {
    padding: 20px;
    border-radius: 12px;
  }

  .music-container {
    flex-direction: column;
    gap: 2rem;
  }

  .music-left,
  .music-right {
    flex: 1 1 100%;
    min-width: auto;
  }

  .cover-img {
    width: 120px;
    height: 120px;
  }

  .song-title {
    font-size: 18px;
  }

  .lyric-scroll {
    max-height: 150px;
  }

  .lyric-line.active {
    font-size: 14px;
  }

  .playlist-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .search-input {
    width: 100%;
  }

  .song-list-wrapper {
    max-height: 250px;
  }
}

@media (max-width: 480px) {
  .music-player {
    padding: 12px;
    margin: 10px auto;
  }

  .music-card {
    padding: 16px;
  }

  .cover-img {
    width: 100px;
    height: 100px;
  }

  .song-title {
    font-size: 16px;
  }

  .lyric-scroll {
    max-height: 120px;
  }

  .playlist-title {
    font-size: 16px;
  }

  .song-list-wrapper {
    max-height: 200px;
  }
}
</style>
