<template>
  <div class="chat-container">
    <div class="header">
      <h2 class="title">🤖 AI 智能助手</h2>
      <div class="header-controls">
        <el-select
          v-model="selectedModel"
          placeholder="选择模型"
          size="small"
          class="model-select"
          @change="onModelChange"
        >
          <el-option
            v-for="model in availableModels"
            :key="model.value"
            :label="model.label"
            :value="model.value"
          >
            <span>{{ model.label }}</span>
            <span class="model-desc">{{ model.desc }}</span>
          </el-option>
        </el-select>
        <el-button @click="refreshModels" size="small" class="refresh-btn">
          <el-icon><Refresh /></el-icon>
          刷新模型
        </el-button>
        <el-button @click="clearHistory" type="danger" size="small" class="clear-btn">
          <el-icon><Delete /></el-icon>
          清除记录
        </el-button>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-section">
      <div class="input-group">
        <el-input 
          v-model="prompt" 
          class="input" 
          placeholder="请输入您的问题..." 
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 6 }" 
          @keydown.enter="handleEnter"
          @keydown.ctrl.enter.prevent="insertLineBreak" 
        />
        <div class="button-group">
          <el-button 
            @click="sendPrompt" 
            :disabled="isLoading || !prompt.trim() || modelStatus !== 'ready'"
            type="primary"
            class="send-button"
            :loading="isLoading"
          >
            <el-icon><Promotion /></el-icon>
            {{ isLoading ? "生成中..." : "发送" }}
          </el-button>
          <el-button 
            type="warning" 
            @click="stopGenerating" 
            :disabled="!isLoading" 
            class="stop-button"
          >
            <el-icon><VideoPause /></el-icon>
            停止
          </el-button>
        </div>
      </div>
    </div>

    <!-- 模型状态 -->
    <div class="status-section" v-if="modelStatus !== 'ready'">
      <el-alert 
        :title="statusText" 
        :type="modelStatus === 'error' ? 'error' : 'info'"
        show-icon
        :closable="false"
      />
    </div>

    <!-- 模型回复 -->
    <div class="response-section" v-if="response">
      <div class="response-header">
        <span class="response-title">🤖 AI 回复</span>
        <el-button 
          type="primary" 
          size="small" 
          @click="copyResponse"
          class="copy-response-btn"
        >
          <el-icon><CopyDocument /></el-icon>
          复制
        </el-button>
      </div>
      <div class="response-box markdown-body" ref="responseBox" v-html="renderedResponse"></div>
    </div>
    
    <div class="error-msg" v-if="errorMsg">
      <el-alert :title="errorMsg" type="error" show-icon />
    </div>

    <!-- 历史对话 -->
    <div class="history-section" v-if="history.length > 0">
      <el-divider content-position="left">
        <span class="history-title">📚 对话历史</span>
      </el-divider>

      <div class="history-list">
        <div 
          v-for="(item, index) in history" 
          :key="index" 
          class="history-item"
        >
          <div class="history-header">
            <span class="history-time">{{ item.time }}</span>
            <div class="history-actions">
              <el-button 
                type="primary" 
                size="small" 
                @click="copyText(item.answer)"
                class="action-btn"
              >
                <el-icon><CopyDocument /></el-icon>
                复制
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                @click="deleteHistory(index)"
                class="action-btn"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>
          
          <div class="conversation">
            <div class="user-message">
              <div class="message-avatar">👤</div>
              <div class="message-content">
                <div class="message-label">您的问题：</div>
                <div class="message-text">{{ item.question }}</div>
              </div>
            </div>
            
            <div class="ai-message">
              <div class="message-avatar">🤖</div>
              <div class="message-content">
                <div class="message-label">AI 回复：</div>
                <div class="message-text markdown-body" v-html="md.render(item.answer)"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="history.length === 0 && !response">
      <div class="empty-icon">💬</div>
      <div class="empty-text">开始与 AI 助手对话吧！</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { ChatApi } from "@/utils/api";
import MarkdownIt from "markdown-it";
import hljs from "highlight.js";
import "highlight.js/styles/github.css";
import { copyText } from "@/plugin/copy";
import {
  Delete,
  Promotion,
  VideoPause,
  CopyDocument,
  Refresh
} from '@element-plus/icons-vue';

const md = new MarkdownIt({
  highlight(str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return `<pre class="hljs"><code>${hljs.highlight(str, { language: lang }).value}</code></pre>`;
      } catch { }
    }
    return `<pre class="hljs"><code>${md.utils.escapeHtml(str)}</code></pre>`;
  },
});

const prompt = ref("");
const response = ref("");
const isLoading = ref(false);
const errorMsg = ref("");
const history = ref([]);
const modelStatus = ref("loading"); // loading | ready | error
const statusText = ref("正在检查模型状态...");
const responseBox = ref(null);

// 模型相关
const selectedModel = ref("qwen2.5:0.5b"); // 默认使用最轻量的模型
const availableModels = ref([
  { value: "qwen2.5:0.5b", label: "Qwen2.5 0.5B", desc: "最轻量，速度最快" },
  { value: "qwen2.5:1.5b", label: "Qwen2.5 1.5B", desc: "轻量，平衡性能" },
  { value: "qwen2.5:3b", label: "Qwen2.5 3B", desc: "中等，较好性能" },
  { value: "llama3.1", label: "Llama3.1", desc: "较重，高性能" },
  { value: "llama3.2", label: "Llama3.2", desc: "最新版本" }
]);

const renderedResponse = computed(() => md.render(response.value || ""));

const abortController = ref(null);

watch(response, () => {
  nextTick(() => {
    responseBox.value?.scrollTo({
      top: responseBox.value.scrollHeight,
      behavior: "smooth",
    });
  });
});

onMounted(async () => {
  // 加载保存的模型选择
  const savedModel = localStorage.getItem('selectedModel');
  if (savedModel && availableModels.value.some(m => m.value === savedModel)) {
    selectedModel.value = savedModel;
  }

  // 检查Ollama服务状态
  await checkModelStatus();

  // 加载历史记录
  loadHistory();
});

function insertLineBreak(e) {
  const el = e.target;
  const start = el.selectionStart;
  const end = el.selectionEnd;
  prompt.value =
    prompt.value.substring(0, start) + "\n" + prompt.value.substring(end);
  nextTick(() => {
    el.selectionStart = el.selectionEnd = start + 1;
  });
}

function handleEnter(e) {
  if (!e.ctrlKey) {
    e.preventDefault();
    sendPrompt();
  }
}

// const apiBase = `http://**************:11434/api/chat`;
const apiBase = "http://**************:11434/api/chat";
async function sendPrompt() {
  if (!prompt.value.trim()) return;
  isLoading.value = true;
  response.value = "";
  errorMsg.value = "";

  const currentPrompt = "用中文回答：" + prompt.value;

  abortController.value = new AbortController();

  try {
    console.log(`🚀 使用模型: ${selectedModel.value}`);
    const res = await fetch(apiBase, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        model: selectedModel.value,
        messages: [{ role: "user", content: currentPrompt }],
        stream: true,
      }),
      signal: abortController.value.signal,
    });

    const reader = res.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let reply = "";

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunkText = decoder.decode(value);
      const chunks = chunkText.trim().split("\n");

      for (const chunk of chunks) {
        try {
          const json = JSON.parse(chunk);
          if (json.message?.content) {
            reply += json.message.content;
            response.value += json.message.content;
          }
        } catch {
          // 忽略解析失败
        }
      }
    }

    const currentTime = new Date().toLocaleString();
    history.value.unshift({
      question: prompt.value,
      answer: reply,
      time: currentTime,
    });

    saveHistory();
    prompt.value = "";
  } catch (err) {
    if (err.name === "AbortError") {
      errorMsg.value = "已停止生成。";
    } else {
      errorMsg.value = "发送失败，请稍后重试。";
      ElMessage.error("请求失败");
    }
  } finally {
    isLoading.value = false;
    abortController.value = null;
  }
}

function stopGenerating() {
  if (abortController.value) {
    abortController.value.abort();
  }
}

function loadHistory() {
  const stored = localStorage.getItem("chatHistory");
  if (stored) {
    history.value = JSON.parse(stored).filter((item) =>
      isWithinOneWeek(item.time)
    );
  }
}

function saveHistory() {
  localStorage.setItem("chatHistory", JSON.stringify(history.value));
}

function clearHistory() {
  if (!confirm("确定要清除历史记录吗？")) return;
  localStorage.removeItem("chatHistory");
  history.value = [];
}

function isWithinOneWeek(dateString) {
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
  return new Date(dateString) > oneWeekAgo;
}

const statusClass = computed(() => ({
  "loading-status": modelStatus.value === "loading",
  "error-status": modelStatus.value === "error",
  "ready-status": modelStatus.value === "ready",
}));

function copyResponse() {
  if (!response.value) return;
  navigator.clipboard.writeText(response.value).then(
    () => {
      ElMessage.success("已复制到剪贴板");
    },
    () => {
      ElMessage.error("复制失败，请手动复制");
    }
  );
}


function deleteHistory(index) {
  history.value.splice(index, 1);
  saveHistory();
  ElMessage.success("已删除该条对话");
}

// 模型相关方法
function onModelChange(model) {
  console.log(`切换到模型: ${model}`);
  ElMessage.success(`已切换到 ${availableModels.value.find(m => m.value === model)?.label}`);
  localStorage.setItem('selectedModel', model);
}

async function refreshModels() {
  try {
    ElMessage.info("正在刷新模型列表...");
    const response = await fetch("http://**************:11434/api/tags");
    const data = await response.json();

    if (data.models && Array.isArray(data.models)) {
      const models = data.models.map(model => ({
        value: model.name,
        label: model.name,
        desc: `大小: ${(model.size / 1024 / 1024 / 1024).toFixed(1)}GB`
      }));

      availableModels.value = [
        ...availableModels.value.filter(m => models.some(nm => nm.value === m.value)),
        ...models.filter(m => !availableModels.value.some(am => am.value === m.value))
      ];

      ElMessage.success(`已刷新模型列表，找到 ${models.length} 个模型`);
    }
  } catch (error) {
    console.error("刷新模型列表失败:", error);
    ElMessage.error("刷新模型列表失败，请检查Ollama服务状态");
  }
}

// 检查模型状态
async function checkModelStatus() {
  try {
    const response = await fetch("http://**************:11434/api/tags");
    if (response.ok) {
      statusText.value = "Ollama服务正常，可以开始对话";
      modelStatus.value = "ready";

      // 自动刷新模型列表
      await refreshModels();
    } else {
      throw new Error("Ollama服务不可用");
    }
  } catch (error) {
    statusText.value = "Ollama服务不可用，请检查服务状态";
    modelStatus.value = "error";
    console.error("检查模型状态失败:", error);
  }
}
</script>

<style scoped>
.chat-container {
  max-width: 1000px;
  margin: 30px auto;
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  min-height: 80vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.model-select {
  min-width: 180px;
}

.model-select :deep(.el-select__wrapper) {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.model-desc {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.title {
  color: white;
  font-size: 28px;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.clear-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.clear-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.input-section {
  margin-bottom: 30px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.input {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  border: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.input:hover {
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.button-group {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.send-button {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.send-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.stop-button {
  background: linear-gradient(45deg, #ff9800, #f57c00);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.stop-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
}

.status-section {
  margin-bottom: 25px;
}

.response-section {
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
}

.response-title {
  font-weight: 700;
  font-size: 18px;
  color: #333;
}

.copy-response-btn {
  background: linear-gradient(45deg, #2196F3, #1976D2);
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.copy-response-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.response-box {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  border: 1px solid #e9ecef;
  overflow-wrap: break-word;
  max-height: 400px;
  overflow-y: auto;
  line-height: 1.6;
}

.response-box::-webkit-scrollbar {
  width: 6px;
}

.response-box::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.response-box::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.response-box::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.error-msg {
  margin-bottom: 25px;
}

.history-section {
  margin-bottom: 40px;
}

.history-title {
  font-weight: 700;
  font-size: 20px;
  color: white;
}

.history-list {
  margin-top: 20px;
}

.history-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.history-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
}

.history-time {
  font-weight: 600;
  color: #666;
  font-size: 14px;
}

.history-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
}

.conversation {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.user-message,
.ai-message {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.user-message .message-avatar {
  background: linear-gradient(45deg, #2196F3, #1976D2);
  color: white;
}

.ai-message .message-avatar {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
}

.message-content {
  flex: 1;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.message-label {
  font-weight: 700;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.message-text {
  color: #555;
  line-height: 1.6;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.empty-text {
  font-size: 20px;
  color: #666;
  font-weight: 500;
}

/* Markdown 样式优化 */
:deep(.markdown-body) {
  color: #333;
  line-height: 1.6;
}

:deep(.markdown-body h1),
:deep(.markdown-body h2),
:deep(.markdown-body h3),
:deep(.markdown-body h4),
:deep(.markdown-body h5),
:deep(.markdown-body h6) {
  color: #2c3e50;
  margin-top: 20px;
  margin-bottom: 10px;
}

:deep(.markdown-body code) {
  background: #f1f2f6;
  color: #e74c3c;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.9em;
}

:deep(.markdown-body pre) {
  background: #2d3748;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 16px 0;
}

:deep(.markdown-body pre code) {
  background: none;
  color: inherit;
  padding: 0;
}

:deep(.markdown-body blockquote) {
  border-left: 4px solid #4299e1;
  padding-left: 16px;
  margin: 16px 0;
  color: #4a5568;
  background: #f7fafc;
  padding: 12px 16px;
  border-radius: 0 8px 8px 0;
}

:deep(.markdown-body ul),
:deep(.markdown-body ol) {
  padding-left: 20px;
}

:deep(.markdown-body li) {
  margin: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-container {
    margin: 15px;
    padding: 20px;
  }
  
  .header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .title {
    font-size: 24px;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .history-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .history-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>