<template>
  <div class="favorite-test">
    <h2>收藏功能测试</h2>
    
    <div class="test-section">
      <h3>测试文章收藏</h3>
      <p>文章ID: {{ testArticleId }}</p>
      
      <ArticleFavorite 
        :article-id="testArticleId"
        :show-text="true"
        :show-count="true"
        :show-users-list="true"
        @favorite-changed="handleFavoriteChanged"
      />
      
      <div class="test-results">
        <h4>测试结果:</h4>
        <pre>{{ JSON.stringify(testResults, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import ArticleFavorite from '../../components/ArticleFavorite.vue'

export default {
  name: 'FavoriteTest',
  components: {
    ArticleFavorite
  },
  setup() {
    const testArticleId = ref(39) // 使用存在的文章ID
    const testResults = ref({})
    
    const handleFavoriteChanged = (data) => {
      console.log('收藏状态变化:', data)
      testResults.value = {
        ...testResults.value,
        lastChange: data,
        timestamp: new Date().toISOString()
      }
    }
    
    return {
      testArticleId,
      testResults,
      handleFavoriteChanged
    }
  }
}
</script>

<style scoped>
.favorite-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-results {
  margin-top: 20px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
