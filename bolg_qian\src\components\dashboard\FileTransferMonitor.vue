<template>
  <div class="file-transfer-monitor">
    <el-card class="monitor-card">
      <div slot="header" class="card-header">
        <span class="card-title">
          <i class="el-icon-monitor"></i>
          文件传输实时监控
        </span>
        <div class="header-actions">
          <el-switch
            v-model="autoRefresh"
            active-text="自动刷新"
            inactive-text="手动刷新"
            @change="toggleAutoRefresh"
          ></el-switch>
          <el-button 
            type="primary" 
            size="small" 
            @click="refreshData"
            :loading="loading"
          >
            <i class="el-icon-refresh"></i>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 统计概览 -->
      <div class="stats-overview">
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="stat-item download-stat">
              <div class="stat-icon">
                <i class="el-icon-download"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ transferStatus.downloads?.active || 0 }}</div>
                <div class="stat-label">活跃下载</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item upload-stat">
              <div class="stat-icon">
                <i class="el-icon-upload2"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ transferStatus.uploads?.active || 0 }}</div>
                <div class="stat-label">活跃上传</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item media-stat">
              <div class="stat-icon">
                <i class="el-icon-video-play"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ transferStatus.media?.activeSessions || 0 }}</div>
                <div class="stat-label">媒体播放</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item total-stat">
              <div class="stat-icon">
                <i class="el-icon-connection"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">
                  {{ (transferStatus.downloads?.active || 0) + (transferStatus.uploads?.active || 0) + (transferStatus.media?.activeSessions || 0) }}
                </div>
                <div class="stat-label">总连接数</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 传输详情 -->
      <div class="transfer-details" v-loading="loading">
        <el-tabs v-model="activeTab" type="card">
          <!-- 活跃下载 -->
          <el-tab-pane label="活跃下载" name="downloads">
            <div class="transfer-list">
              <div 
                v-for="download in transferStatus.downloads?.details || []" 
                :key="download.downloadId"
                class="transfer-item download-item"
              >
                <div class="item-header">
                  <div class="item-info">
                    <i class="el-icon-download item-icon"></i>
                    <div class="item-details">
                      <div class="item-title">{{ download.fileName }}</div>
                      <div class="item-meta">
                        用户: {{ download.userId }} | 等级: 
                        <el-tag 
                          size="mini" 
                          :type="getLevelTagType(download.userLevel)"
                        >
                          {{ getUserLevelLabel(download.userLevel) }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                  <div class="item-status">
                    <div class="status-value">{{ download.progress }}</div>
                    <div class="status-label">已下载</div>
                  </div>
                </div>
                <div class="item-progress">
                  <div class="progress-info">
                    <span>持续时间: {{ download.duration }}</span>
                    <span>下载ID: {{ download.downloadId.substring(0, 8) }}...</span>
                  </div>
                </div>
              </div>
              
              <div v-if="(!transferStatus.downloads?.details || transferStatus.downloads.details.length === 0)" class="empty-list">
                <i class="el-icon-info"></i>
                <p>暂无活跃下载</p>
              </div>
            </div>
          </el-tab-pane>

          <!-- 活跃上传 -->
          <el-tab-pane label="活跃上传" name="uploads">
            <div class="transfer-list">
              <div 
                v-for="upload in transferStatus.uploads?.details || []" 
                :key="upload.sessionId"
                class="transfer-item upload-item"
              >
                <div class="item-header">
                  <div class="item-info">
                    <i class="el-icon-upload2 item-icon"></i>
                    <div class="item-details">
                      <div class="item-title">{{ upload.fileName }}</div>
                      <div class="item-meta">
                        进度: {{ upload.progress }} | 速度: {{ upload.speed }}
                      </div>
                    </div>
                  </div>
                  <div class="item-status">
                    <div class="status-value">{{ upload.uploadedBytes }}</div>
                    <div class="status-label">已上传</div>
                  </div>
                </div>
                <div class="item-progress">
                  <el-progress 
                    :percentage="parseFloat(upload.progress)" 
                    :color="getProgressColor(parseFloat(upload.progress))"
                  ></el-progress>
                  <div class="progress-info">
                    <span>总大小: {{ upload.totalSize }}</span>
                    <span>会话ID: {{ upload.sessionId.substring(0, 8) }}...</span>
                  </div>
                </div>
              </div>
              
              <div v-if="(!transferStatus.uploads?.details || transferStatus.uploads.details.length === 0)" class="empty-list">
                <i class="el-icon-info"></i>
                <p>暂无活跃上传</p>
              </div>
            </div>
          </el-tab-pane>

          <!-- 媒体播放 -->
          <el-tab-pane label="媒体播放" name="media">
            <div class="transfer-list">
              <div 
                v-for="session in transferStatus.media?.details || []" 
                :key="session.sessionId"
                class="transfer-item media-item"
              >
                <div class="item-header">
                  <div class="item-info">
                    <i class="el-icon-video-play item-icon"></i>
                    <div class="item-details">
                      <div class="item-title">{{ session.mediaFile }}</div>
                      <div class="item-meta">
                        用户: {{ session.userId }} | 质量: 
                        <el-tag size="mini" :type="getQualityTagType(session.quality)">
                          {{ session.quality.toUpperCase() }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                  <div class="item-status">
                    <div class="status-value">{{ session.bytesStreamed }}</div>
                    <div class="status-label">已传输</div>
                  </div>
                </div>
                <div class="item-progress">
                  <div class="progress-info">
                    <span>播放时长: {{ session.duration }}</span>
                    <span>会话ID: {{ session.sessionId.substring(0, 8) }}...</span>
                  </div>
                </div>
              </div>
              
              <div v-if="(!transferStatus.media?.details || transferStatus.media.details.length === 0)" class="empty-list">
                <i class="el-icon-info"></i>
                <p>暂无活跃播放</p>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script>
import { dashboardFileApi, fileUtils } from '@/utils/fileManagementApi'

export default {
  name: 'FileTransferMonitor',
  data() {
    return {
      loading: false,
      autoRefresh: true,
      refreshInterval: null,
      activeTab: 'downloads',
      transferStatus: {
        downloads: { active: 0, details: [] },
        uploads: { active: 0, details: [] },
        media: { activeSessions: 0, details: [] }
      }
    }
  },
  mounted() {
    this.loadTransferStatus()
    this.startAutoRefresh()
  },
  beforeDestroy() {
    this.stopAutoRefresh()
  },
  methods: {
    // 加载传输状态
    async loadTransferStatus() {
      this.loading = true
      try {
        const response = await dashboardFileApi.getTransferStatus()
        if (response.code === 200) {
          this.transferStatus = response.data
        } else {
          console.error('获取传输状态失败:', response.message)
        }
      } catch (error) {
        console.error('获取传输状态失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 刷新数据
    refreshData() {
      this.loadTransferStatus()
    },

    // 切换自动刷新
    toggleAutoRefresh(enabled) {
      if (enabled) {
        this.startAutoRefresh()
      } else {
        this.stopAutoRefresh()
      }
    },

    // 开始自动刷新
    startAutoRefresh() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval)
      }
      
      if (this.autoRefresh) {
        this.refreshInterval = setInterval(() => {
          this.loadTransferStatus()
        }, 5000) // 每5秒刷新一次
      }
    },

    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval)
        this.refreshInterval = null
      }
    },

    // 获取等级标签类型
    getLevelTagType(level) {
      const typeMap = {
        free: 'info',
        basic: 'success',
        premium: 'warning',
        vip: 'danger',
        unlimited: ''
      }
      return typeMap[level] || 'info'
    },

    // 获取用户等级标签
    getUserLevelLabel(level) {
      return fileUtils.getUserLevelLabel(level)
    },

    // 获取质量标签类型
    getQualityTagType(quality) {
      const typeMap = {
        low: 'info',
        medium: 'success',
        high: 'warning',
        lossless: 'danger'
      }
      return typeMap[quality] || 'info'
    },

    // 获取进度条颜色
    getProgressColor(percentage) {
      if (percentage < 30) return '#67C23A'
      if (percentage < 70) return '#E6A23C'
      return '#F56C6C'
    }
  }
}
</script>

<style scoped>
.file-transfer-monitor {
  height: 100%;
}

.monitor-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-title i {
  margin-right: 8px;
  color: #409EFF;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-overview {
  margin-bottom: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background-color: #FFFFFF;
  border: 1px solid #EBEEF5;
  transition: all 0.3s ease;
}

.stat-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.download-stat {
  border-left: 4px solid #67C23A;
}

.upload-stat {
  border-left: 4px solid #E6A23C;
}

.media-stat {
  border-left: 4px solid #409EFF;
}

.total-stat {
  border-left: 4px solid #F56C6C;
}

.stat-icon {
  font-size: 32px;
  margin-right: 16px;
  color: #409EFF;
}

.download-stat .stat-icon {
  color: #67C23A;
}

.upload-stat .stat-icon {
  color: #E6A23C;
}

.media-stat .stat-icon {
  color: #409EFF;
}

.total-stat .stat-icon {
  color: #F56C6C;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.transfer-details {
  height: 400px;
  overflow: hidden;
}

.transfer-list {
  height: 350px;
  overflow-y: auto;
  padding-right: 8px;
}

.transfer-item {
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.transfer-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
}

.download-item {
  border-left: 4px solid #67C23A;
}

.upload-item {
  border-left: 4px solid #E6A23C;
}

.media-item {
  border-left: 4px solid #409EFF;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-icon {
  font-size: 20px;
  margin-right: 12px;
  color: #409EFF;
}

.download-item .item-icon {
  color: #67C23A;
}

.upload-item .item-icon {
  color: #E6A23C;
}

.media-item .item-icon {
  color: #409EFF;
}

.item-details {
  flex: 1;
}

.item-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
  word-break: break-all;
}

.item-meta {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-status {
  text-align: right;
}

.status-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.status-label {
  font-size: 12px;
  color: #909399;
}

.item-progress {
  margin-top: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.empty-list {
  text-align: center;
  padding: 40px;
  color: #909399;
}

.empty-list i {
  font-size: 32px;
  margin-bottom: 12px;
}

/* 滚动条样式 */
.transfer-list::-webkit-scrollbar {
  width: 6px;
}

.transfer-list::-webkit-scrollbar-track {
  background: #F5F7FA;
  border-radius: 3px;
}

.transfer-list::-webkit-scrollbar-thumb {
  background: #C0C4CC;
  border-radius: 3px;
}

.transfer-list::-webkit-scrollbar-thumb:hover {
  background: #A4A9B0;
}
</style>
