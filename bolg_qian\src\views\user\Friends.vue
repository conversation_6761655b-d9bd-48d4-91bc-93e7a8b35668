<template>
  <div class="friends-container">
    <el-card class="friends-card">
      <template #header>
        <div class="card-header">
          <span>好友管理</span>
          <el-button type="primary" @click="showAddFriendDialog = true">
            <el-icon><Plus /></el-icon>
            添加好友
          </el-button>
        </div>
      </template>

      <!-- 好友申请通知 -->
      <el-alert
        v-if="friendRequests.length > 0"
        :title="`您有 ${friendRequests.length} 个好友申请待处理`"
        type="warning"
        show-icon
        :closable="false"
        style="margin-bottom: 20px"
      >
        <template #default>
          <el-button type="text" @click="showRequestsDialog = true">
            查看申请
          </el-button>
        </template>
      </el-alert>

      <!-- 好友列表 -->
      <div class="friends-list">
        <el-empty v-if="friends.length === 0" description="暂无好友">
          <el-button type="primary" @click="showAddFriendDialog = true">
            添加第一个好友
          </el-button>
        </el-empty>

        <div v-else class="friend-grid">
          <el-card
            v-for="friend in friends"
            :key="friend.id"
            class="friend-item"
            shadow="hover"
          >
            <div class="friend-info">
              <el-avatar
                :size="60"
                :src="getAvatarUrl(friend.avatar)"
                class="friend-avatar"
              />
              <div class="friend-details">
                <h3 class="friend-name">{{ friend.username }}</h3>
                <p class="friend-status">
                  <el-tag :type="isOnline(friend.id) ? 'success' : 'info'" size="small">
                    {{ isOnline(friend.id) ? '在线' : '离线' }}
                  </el-tag>
                </p>
              </div>
            </div>
            <div class="friend-actions">
              <el-button
                type="primary"
                size="small"
                @click="startPrivateChat(friend)"
              >
                私聊
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="removeFriend(friend)"
              >
                删除
              </el-button>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>

    <!-- 添加好友对话框 -->
    <el-dialog
      v-model="showAddFriendDialog"
      title="添加好友"
      width="500px"
    >
      <div class="add-friend-content">
        <el-input
          v-model="searchKeyword"
          placeholder="输入用户名搜索"
          @keyup.enter="searchUsers"
          clearable
        >
          <template #append>
            <el-button @click="searchUsers" :loading="searching">
              搜索
            </el-button>
          </template>
        </el-input>

        <div v-if="searchResults.length > 0" class="search-results">
          <h4>搜索结果</h4>
          <div
            v-for="user in searchResults"
            :key="user.id"
            class="search-result-item"
          >
            <el-avatar :size="40" :src="getAvatarUrl(user.avatar)" />
            <span class="result-username">{{ user.username }}</span>
            <el-button
              v-if="!isFriend(user.id) && user.id !== currentUserId"
              type="primary"
              size="small"
              @click="sendFriendRequest(user.id)"
              :loading="sendingRequest"
            >
              添加好友
            </el-button>
            <el-tag v-else-if="isFriend(user.id)" type="success" size="small">
              已是好友
            </el-tag>
            <el-tag v-else type="info" size="small">
              自己
            </el-tag>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 好友申请对话框 -->
    <el-dialog
      v-model="showRequestsDialog"
      title="好友申请"
      width="500px"
    >
      <div class="friend-requests">
        <div
          v-for="request in friendRequests"
          :key="request.id"
          class="request-item"
        >
          <el-avatar :size="40" :src="getAvatarUrl(request.avatar)" />
          <span class="request-username">{{ request.username }}</span>
          <div class="request-actions">
            <el-button
              type="success"
              size="small"
              @click="handleFriendRequest(request.id, true)"
              :loading="handlingRequest"
            >
              同意
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleFriendRequest(request.id, false)"
              :loading="handlingRequest"
            >
              拒绝
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import {
  GetFriendListApi,
  SearchUserApi,
  ApplyFriendApi,
  GetFriendApplyListApi,
  HandleAddFriendApi
} from '@/utils/api'

const router = useRouter()

// 响应式数据
const friends = ref([])
const friendRequests = ref([])
const searchResults = ref([])
const onlineUsers = ref([])

// 对话框状态
const showAddFriendDialog = ref(false)
const showRequestsDialog = ref(false)

// 表单数据
const searchKeyword = ref('')

// 加载状态
const searching = ref(false)
const sendingRequest = ref(false)
const handlingRequest = ref(false)

// 用户信息
const currentUserId = parseInt(localStorage.getItem('id') || '1')
const BASE_URL = '/api'

// 获取头像URL
const getAvatarUrl = (avatar) => {
  console.log(avatar)
  if (!avatar) return `${BASE_URL}avatars/moren.png`
  if (avatar.startsWith('http')) return avatar
  return `${BASE_URL}avatars/${avatar}`
}

// 检查是否为好友
const isFriend = (userId) => {
  return friends.value.some(friend => friend.id === userId)
}

// 检查是否在线
const isOnline = (userId) => {
  return onlineUsers.value.some(user => user.id === userId)
}

// 获取好友列表
const loadFriends = async () => {
  try {
    const response = await GetFriendListApi(currentUserId)
    if (response.code === 0) {
      friends.value = response.data || []
    }
  } catch (error) {
    console.error('获取好友列表失败:', error)
    ElMessage.error('获取好友列表失败')
  }
}

// 获取好友申请列表
const loadFriendRequests = async () => {
  try {
    const response = await GetFriendApplyListApi(currentUserId)
    if (response.code === 0) {
      friendRequests.value = response.data || []
    }
  } catch (error) {
    console.error('获取好友申请失败:', error)
  }
}

// 搜索用户
const searchUsers = async () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }

  searching.value = true
  try {
    const response = await SearchUserApi(searchKeyword.value.trim())
    if (response.code === 0) {
      searchResults.value = response.data || []
      if (searchResults.value.length === 0) {
        ElMessage.info('未找到相关用户')
      }
    } else {
      ElMessage.error('搜索失败')
    }
  } catch (error) {
    console.error('搜索用户失败:', error)
    ElMessage.error('搜索失败')
  } finally {
    searching.value = false
  }
}

// 发送好友申请
const sendFriendRequest = async (toUserId) => {
  sendingRequest.value = true
  try {
    const response = await ApplyFriendApi(currentUserId, toUserId)
    if (response.code === 0) {
      ElMessage.success('好友申请已发送')
      searchResults.value = searchResults.value.filter(user => user.id !== toUserId)
    } else if (response.code === 1) {
      ElMessage.warning(response.msg || '已发送申请或已是好友')
    } else if (response.code === 200) {
      ElMessage.info('已是好友')
    } else {
      ElMessage.error(response.msg || '发送申请失败')
    }
  } catch (error) {
    console.error('发送好友申请失败:', error)
    ElMessage.error('发送申请失败')
  } finally {
    sendingRequest.value = false
  }
}

// 处理好友申请
const handleFriendRequest = async (requestId, accept) => {
  handlingRequest.value = true
  try {
    const response = await HandleAddFriendApi(requestId, accept)
    if (response.code === 0) {
      ElMessage.success(accept ? '已同意好友申请' : '已拒绝好友申请')
      // 刷新列表
      await loadFriendRequests()
      if (accept) {
        await loadFriends()
      }
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('处理好友申请失败:', error)
    ElMessage.error('操作失败')
  } finally {
    handlingRequest.value = false
  }
}

// 删除好友
const removeFriend = async (friend) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除好友 ${friend.username} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // TODO: 实现删除好友API
    ElMessage.info('删除好友功能待实现')
  } catch {
    // 用户取消
  }
}

// 开始私聊
const startPrivateChat = (friend) => {
  // 跳转到聊天页面并设置私聊对象
  router.push({
    path: '/index/chat',
    query: {
      privateUserId: friend.id,
      privateUserName: friend.username,
      privateUserAvatar: friend.avatar || 'moren.png'
    }
  })
}

// 初始化
onMounted(async () => {
  await loadFriends()
  await loadFriendRequests()
})
</script>

<style scoped>
.friends-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.friends-card {
  min-height: 600px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.friend-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.friend-item {
  transition: transform 0.2s;
}

.friend-item:hover {
  transform: translateY(-2px);
}

.friend-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.friend-avatar {
  margin-right: 15px;
}

.friend-details {
  flex: 1;
}

.friend-name {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 600;
}

.friend-status {
  margin: 0;
  color: #666;
}

.friend-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.add-friend-content {
  padding: 10px 0;
}

.search-results {
  margin-top: 20px;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 10px;
  gap: 15px;
}

.result-username {
  flex: 1;
  font-weight: 500;
}

.friend-requests {
  max-height: 400px;
  overflow-y: auto;
}

.request-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 10px;
  gap: 15px;
}

.request-username {
  flex: 1;
  font-weight: 500;
}

.request-actions {
  display: flex;
  gap: 10px;
}
</style>
