<template>
  <div class="view-history-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Clock /></el-icon>
          浏览记录
        </h1>
        <p class="page-subtitle">查看您的文章浏览历史</p>
      </div>
    </div>

    <div class="page-container">
      <!-- 统计卡片 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-item">
                <div class="stat-icon total-views">
                  <el-icon><View /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ userStats.total_views || 0 }}</div>
                  <div class="stat-label">总浏览量</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-item">
                <div class="stat-icon unique-articles">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ userStats.unique_articles || 0 }}</div>
                  <div class="stat-label">浏览文章数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-item">
                <div class="stat-icon avg-duration">
                  <el-icon><Timer /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ formatDuration(userStats.avg_view_duration || 0) }}</div>
                  <div class="stat-label">平均时长</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-item">
                <div class="stat-icon last-view">
                  <el-icon><Calendar /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ formatLastView(userStats.last_view_at) }}</div>
                  <div class="stat-label">最后浏览</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 工具栏 -->
      <div class="toolbar-section">
        <div class="toolbar-left">
          <el-button @click="refreshHistory" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
        <div class="toolbar-right">
          <span class="total-count">
            共 {{ pagination.total }} 条记录
          </span>
        </div>
      </div>

      <!-- 浏览记录列表 -->
      <div class="history-section">
        <el-card class="history-card">
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="5" animated />
          </div>
          
          <div v-else-if="historyList.length === 0" class="empty-state">
            <el-empty description="暂无浏览记录">
              <el-button type="primary" @click="$router.push('/index/allarticles')">
                <el-icon><Document /></el-icon>
                去看看文章
              </el-button>
            </el-empty>
          </div>
          
          <div v-else class="history-list">
            <div 
              v-for="item in historyList" 
              :key="item.id"
              class="history-item"
              @click="goToArticle(item.article_id)"
            >
              <div class="article-cover">
                <img 
                  v-if="item.cover_image" 
                  :src="`/api/articles/${item.cover_image}`"
                  :alt="item.title"
                  @error="handleImageError"
                />
                <div v-else class="default-cover">
                  <el-icon><Document /></el-icon>
                </div>
              </div>
              
              <div class="article-info">
                <h3 class="article-title">{{ item.title || '无标题' }}</h3>
                <p class="article-summary">{{ item.summary || '暂无摘要' }}</p>
                
                <div class="article-meta">
                  <span class="author">
                    <el-icon><User /></el-icon>
                    {{ item.author_name || '未知作者' }}
                  </span>
                  <span class="view-time">
                    <el-icon><Clock /></el-icon>
                    {{ formatTime(item.created_at) }}
                  </span>
                </div>
              </div>
              
              <div class="view-stats">
                <div class="stat-item">
                  <span class="stat-label">浏览时长</span>
                  <span class="stat-value">{{ formatDuration(item.view_duration) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">滚动深度</span>
                  <span class="stat-value">{{ item.scroll_depth }}%</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">设备</span>
                  <span class="stat-value">{{ getDeviceLabel(item.device_type) }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 分页 -->
          <div v-if="historyList.length > 0" class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.limit"
              :total="pagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import {
  Clock, View, Document, Timer, Calendar, Refresh, User
} from '@element-plus/icons-vue';
import { getUserViewHistoryApi, getUserViewStatsApi } from '@/utils/api';

const router = useRouter();

// 响应式数据
const loading = ref(false);
const historyList = ref<any[]>([]);
const userStats = ref<any>({});
const pagination = ref({
  page: 1,
  limit: 20,
  total: 0,
  pages: 0
});

// 获取用户统计数据
const fetchUserStats = async () => {
  try {
    const userId = localStorage.getItem('id');
    if (!userId) return;

    console.log('正在获取用户统计数据，用户ID:', userId);
    const response = await getUserViewStatsApi(parseInt(userId));
    console.log('获取用户统计API响应:', response);

    if (response && response.stats) {
      userStats.value = response.stats;
      console.log('用户统计数据:', userStats.value);
    }
  } catch (error) {
    console.error('获取用户统计失败:', error);
    // 不显示错误消息，使用默认值
    userStats.value = {
      total_views: 0,
      unique_articles: 0,
      avg_view_duration: 0,
      last_view_at: null
    };
  }
};

// 获取浏览记录
const fetchViewHistory = async () => {
  try {
    loading.value = true;
    const userId = localStorage.getItem('id');
    if (!userId) {
      ElMessage.error('请先登录');
      router.push('/login');
      return;
    }

    console.log('正在获取用户浏览记录，用户ID:', userId);
    const response = await getUserViewHistoryApi(parseInt(userId), {
      page: pagination.value.page,
      limit: pagination.value.limit
    });
    console.log('获取浏览记录API响应:', response);

    if (response && response.data) {
      historyList.value = response.data;
      pagination.value = {
        ...pagination.value,
        ...response.pagination
      };
      console.log('浏览记录数据:', historyList.value.length, '条');
    }
  } catch (error) {
    console.error('获取浏览记录失败:', error);
    ElMessage.error('获取浏览记录失败: ' + (error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 刷新记录
const refreshHistory = () => {
  fetchUserStats();
  fetchViewHistory();
};

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.value.limit = size;
  pagination.value.page = 1;
  fetchViewHistory();
};

const handleCurrentChange = (page: number) => {
  pagination.value.page = page;
  fetchViewHistory();
};

// 跳转到文章
const goToArticle = (articleId: number) => {
  router.push(`/index/details/${articleId}`);
};

// 格式化时间
const formatTime = (dateStr: string) => {
  const date = new Date(dateStr);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 60) {
    return `${minutes}分钟前`;
  } else if (hours < 24) {
    return `${hours}小时前`;
  } else if (days < 7) {
    return `${days}天前`;
  } else {
    return date.toLocaleDateString();
  }
};

// 格式化时长
const formatDuration = (seconds: number) => {
  if (seconds < 60) {
    return `${seconds}秒`;
  } else if (seconds < 3600) {
    return `${Math.floor(seconds / 60)}分钟`;
  } else {
    return `${Math.floor(seconds / 3600)}小时`;
  }
};

// 格式化最后浏览时间
const formatLastView = (dateStr: string) => {
  if (!dateStr) return '从未';
  return formatTime(dateStr);
};

// 获取设备标签
const getDeviceLabel = (deviceType: string) => {
  const labels: Record<string, string> = {
    desktop: '桌面端',
    mobile: '移动端',
    tablet: '平板'
  };
  return labels[deviceType] || '未知';
};

// 处理图片错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.style.display = 'none';
};

// 生命周期
onMounted(() => {
  fetchUserStats();
  fetchViewHistory();
});
</script>

<style scoped>
/* 页面容器 */
.view-history-page {
  min-height: 100vh;
  background: var(--bg-secondary);
}

/* 页面头部 */
.page-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-xl) 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.page-subtitle {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin: 0;
}

/* 页面容器 */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

/* 统计区域 */
.stats-section {
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-icon.total-views {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.unique-articles {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.avg-duration {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.last-view {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* 工具栏 */
.toolbar-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.total-count {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* 历史记录区域 */
.history-section {
  margin-bottom: var(--spacing-lg);
}

.history-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.loading-container {
  padding: var(--spacing-lg);
}

.empty-state {
  padding: var(--spacing-2xl);
  text-align: center;
}

.history-list {
  padding: var(--spacing-md);
}

.history-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  margin-bottom: var(--spacing-md);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.history-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.article-cover {
  width: 120px;
  height: 80px;
  border-radius: var(--radius-md);
  overflow: hidden;
  flex-shrink: 0;
}

.article-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-cover {
  width: 100%;
  height: 100%;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: var(--text-tertiary);
}

.article-info {
  flex: 1;
  min-width: 0;
}

.article-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.article-summary {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-md) 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-meta {
  display: flex;
  gap: var(--spacing-lg);
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.article-meta span {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.view-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  min-width: 120px;
  text-align: right;
}

.view-stats .stat-item {
  display: flex;
  justify-content: space-between;
  padding: 0;
  gap: var(--spacing-sm);
}

.view-stats .stat-label {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.view-stats .stat-value {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.pagination-container {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: var(--spacing-md);
  }
  
  .stats-section .el-col {
    margin-bottom: var(--spacing-md);
  }
  
  .history-item {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .article-cover {
    width: 100%;
    height: 200px;
  }
  
  .view-stats {
    flex-direction: row;
    justify-content: space-around;
    text-align: center;
  }
}
</style>
