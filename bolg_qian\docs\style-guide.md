# 🎨 项目样式规范指南

## 📋 目录
- [概述](#概述)
- [颜色系统](#颜色系统)
- [字体规范](#字体规范)
- [组件样式](#组件样式)
- [布局规范](#布局规范)
- [使用指南](#使用指南)

## 🎯 概述

本项目采用统一的样式规范，确保整个应用的视觉一致性和用户体验。所有样式都基于CSS变量定义，便于维护和主题切换。

### 核心原则
- **一致性**: 所有页面使用相同的设计语言
- **可维护性**: 基于CSS变量的主题系统
- **响应式**: 适配不同设备和屏幕尺寸
- **可访问性**: 符合无障碍设计标准

## 🎨 颜色系统

### 主色调
```css
--primary-color: #667eea        /* 主品牌色 */
--primary-light: #818cf8        /* 浅色变体 */
--primary-dark: #4f46e5         /* 深色变体 */
--primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%)
```

### 功能色
```css
--success-color: #10b981        /* 成功 */
--warning-color: #f59e0b        /* 警告 */
--error-color: #ef4444          /* 错误 */
--info-color: #3b82f6           /* 信息 */
```

### 中性色
```css
--gray-50: #f9fafb             /* 最浅灰 */
--gray-100: #f3f4f6            /* 浅灰 */
--gray-200: #e5e7eb            /* 边框色 */
--gray-500: #6b7280            /* 中性灰 */
--gray-700: #374151            /* 深灰 */
--gray-900: #111827            /* 最深灰 */
```

### 文字颜色
```css
--text-primary: #1f2937         /* 主要文字 */
--text-secondary: #6b7280       /* 次要文字 */
--text-tertiary: #9ca3af        /* 辅助文字 */
--text-inverse: #ffffff         /* 反色文字 */
```

## 📝 字体规范

### 字体族
```css
--font-family-sans: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
--font-family-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
```

### 字体大小
```css
--text-xs: 12px                 /* 极小文字 */
--text-sm: 14px                 /* 小文字 */
--text-base: 16px               /* 基础文字 */
--text-lg: 18px                 /* 大文字 */
--text-xl: 20px                 /* 特大文字 */
--text-2xl: 24px                /* 标题 */
--text-3xl: 30px                /* 大标题 */
--text-4xl: 36px                /* 主标题 */
```

### 字重
```css
--font-light: 300               /* 细体 */
--font-normal: 400              /* 常规 */
--font-medium: 500              /* 中等 */
--font-semibold: 600            /* 半粗 */
--font-bold: 700                /* 粗体 */
```

## 🧩 组件样式

### 按钮
```html
<!-- 主要按钮 -->
<el-button type="primary">主要操作</el-button>

<!-- 次要按钮 -->
<el-button>次要操作</el-button>

<!-- 危险按钮 -->
<el-button type="danger">危险操作</el-button>
```

### 卡片
```html
<el-card class="card" shadow="hover">
  <template #header>
    <div class="card-header">
      <span>卡片标题</span>
    </div>
  </template>
  <div class="card-body">
    卡片内容
  </div>
</el-card>
```

### 表单
```html
<el-form :model="form" label-width="100px">
  <el-form-item label="用户名" prop="username">
    <el-input v-model="form.username" placeholder="请输入用户名" />
  </el-form-item>
</el-form>
```

## 📐 布局规范

### 间距系统
```css
--spacing-xs: 4px               /* 极小间距 */
--spacing-sm: 8px               /* 小间距 */
--spacing-md: 16px              /* 中等间距 */
--spacing-lg: 24px              /* 大间距 */
--spacing-xl: 32px              /* 特大间距 */
--spacing-2xl: 48px             /* 超大间距 */
```

### 圆角
```css
--radius-sm: 4px                /* 小圆角 */
--radius-md: 8px                /* 中等圆角 */
--radius-lg: 12px               /* 大圆角 */
--radius-xl: 16px               /* 特大圆角 */
--radius-full: 9999px           /* 完全圆角 */
```

### 阴影
```css
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05)
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1)
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1)
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1)
```

## 📱 响应式断点

```css
/* 移动端 */
@media (max-width: 640px) { }

/* 平板端 */
@media (min-width: 641px) and (max-width: 1024px) { }

/* 桌面端 */
@media (min-width: 1025px) { }
```

## 🛠️ 使用指南

### 1. 使用CSS变量
```css
.my-component {
  color: var(--text-primary);
  background: var(--bg-primary);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}
```

### 2. 使用工具类
```html
<div class="flex items-center justify-between p-4 rounded-lg shadow">
  <span class="text-lg font-semibold text-primary">标题</span>
  <el-button type="primary">操作</el-button>
</div>
```

### 3. 组件样式示例
```vue
<template>
  <div class="page-container">
    <el-card class="content-card">
      <template #header>
        <h2 class="page-title">页面标题</h2>
      </template>
      <div class="content-body">
        <!-- 页面内容 -->
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.page-container {
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  min-height: 100vh;
}

.content-card {
  max-width: 1200px;
  margin: 0 auto;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.page-title {
  color: var(--text-primary);
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  margin: 0;
}

.content-body {
  padding: var(--spacing-lg);
}
</style>
```

### 4. 主题定制
如需修改主题，只需更新 `src/styles/global.css` 中的CSS变量：

```css
:root {
  /* 修改主色调 */
  --primary-color: #your-color;
  
  /* 修改字体 */
  --font-family-sans: 'Your Font', sans-serif;
  
  /* 修改间距 */
  --spacing-md: 20px;
}
```

## ✅ 最佳实践

### DO ✅
- 使用CSS变量定义的颜色和尺寸
- 保持组件样式的一致性
- 使用语义化的类名
- 遵循响应式设计原则
- 使用Element Plus的主题定制

### DON'T ❌
- 硬编码颜色值和尺寸
- 覆盖Element Plus的核心样式
- 使用内联样式
- 忽略移动端适配
- 使用过于复杂的选择器

## 🔧 开发工具

### VS Code 插件推荐
- CSS Peek
- Color Highlight
- Tailwind CSS IntelliSense
- Vue Language Features (Volar)

### 浏览器开发工具
使用浏览器开发者工具检查CSS变量：
```javascript
// 获取CSS变量值
getComputedStyle(document.documentElement).getPropertyValue('--primary-color')

// 设置CSS变量值
document.documentElement.style.setProperty('--primary-color', '#new-color')
```

## 📚 参考资源

- [Element Plus 官方文档](https://element-plus.org/)
- [CSS 变量 MDN 文档](https://developer.mozilla.org/zh-CN/docs/Web/CSS/Using_CSS_custom_properties)
- [响应式设计指南](https://web.dev/responsive-web-design-basics/)
- [无障碍设计指南](https://www.w3.org/WAI/WCAG21/quickref/)

---

**注意**: 本样式规范会持续更新，请定期查看最新版本。如有疑问或建议，请联系开发团队。
