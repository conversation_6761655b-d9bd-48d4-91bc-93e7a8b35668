<template>
  <div class="bug-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon class="title-icon"><Warning /></el-icon>
            Bug 管理中心
          </h1>
          <p class="page-subtitle">高效管理和跟踪项目中的问题与缺陷</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" size="large" @click="dialogVisible = true" :icon="Plus">
            新增 Bug
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stats-card stats-total" shadow="hover">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ bugStats.total }}</div>
                <div class="stats-label">总计</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stats-card stats-new" shadow="hover">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><CirclePlus /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ bugStats.new }}</div>
                <div class="stats-label">新增</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stats-card stats-pending" shadow="hover">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ bugStats.pending }}</div>
                <div class="stats-label">处理中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stats-card stats-completed" shadow="hover">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ bugStats.completed }}</div>
                <div class="stats-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-card class="main-content" shadow="never">
      <!-- 搜索和筛选区域 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item>
            <el-input
              v-model="searchForm.title"
              placeholder="搜索标题..."
              :prefix-icon="Search"
              clearable
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="searchForm.description"
              placeholder="搜索描述..."
              :prefix-icon="Search"
              clearable
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item>
            <el-select v-model="searchForm.severity" placeholder="严重程度" clearable style="width: 120px;">
              <el-option label="一般" value="一般">
                <span class="option-item">
                  <el-tag type="info" size="small">一般</el-tag>
                </span>
              </el-option>
              <el-option label="严重" value="严重">
                <span class="option-item">
                  <el-tag type="warning" size="small">严重</el-tag>
                </span>
              </el-option>
              <el-option label="致命" value="致命">
                <span class="option-item">
                  <el-tag type="danger" size="small">致命</el-tag>
                </span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="searchForm.status" placeholder="状态" clearable style="width: 120px;">
              <el-option label="新增" value="2">
                <span class="option-item">
                  <el-tag type="info" size="small">新增</el-tag>
                </span>
              </el-option>
              <el-option label="处理中" value="0">
                <span class="option-item">
                  <el-tag type="warning" size="small">处理中</el-tag>
                </span>
              </el-option>
              <el-option label="已完成" value="1">
                <span class="option-item">
                  <el-tag type="success" size="small">已完成</el-tag>
                </span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="searchForm.createTime"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              clearable
              style="width: 240px;"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="doSearch" :icon="Search">搜索</el-button>
            <el-button @click="resetSearch" :icon="RefreshLeft">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button
            type="danger"
            :disabled="!multipleSelection.length"
            @click="deleteSelectedBugs"
            :icon="Delete"
          >
            批量删除 ({{ multipleSelection.length }})
          </el-button>
          <el-button
            type="info"
            @click="showCompleted = !showCompleted"
            :icon="showCompleted ? Hide : View"
          >
            {{ showCompleted ? "隐藏已完成" : "显示已完成" }}
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-text type="info">共 {{ filteredBugList.length }} 条记录</el-text>
        </div>
      </div>

      <!-- Bug 列表表格 -->
      <div class="table-section">
        <el-table
          :data="filteredBugList"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          ref="bugTableRef"
          class="bug-table"
          :row-class-name="getRowClassName"
          stripe
        >
          <el-table-column type="selection" width="50" />
          <el-table-column prop="id" label="ID" width="80" align="center">
            <template #default="{ row }">
              <el-tag type="info" size="small">#{{ row.id }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="title" label="标题" min-width="200">
            <template #default="{ row }">
              <div class="title-cell">
                <el-text class="title-text" :title="row.title">{{ row.title }}</el-text>
                <div class="title-meta">
                  <el-icon class="meta-icon"><Calendar /></el-icon>
                  <span class="meta-text">{{ formatTimeShort(row.create_time) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="300">
            <template #default="{ row }">
              <div class="description-cell">
                <el-text class="description-text" :title="row.description">
                  {{ showDesc(row.description) }}
                </el-text>
                <el-button
                  v-if="row.description && row.description.length > 50"
                  type="primary"
                  link
                  size="small"
                  @click="showDescDialog(row.description)"
                  class="view-more-btn"
                >
                  查看详情
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="severity" label="严重程度" width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="severityTagType(row.severity)" :icon="getSeverityIcon(row.severity)">
                {{ row.severity }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="statusTagType(row.status)" :icon="getStatusIcon(row.status)">
                {{ statusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" width="180" align="center">
            <template #default="{ row }">
              <div class="time-cell">
                <el-text type="info" size="small">{{ formatTime(row.create_time) }}</el-text>
                <el-text type="info" size="small">{{ getTimeAgo(row.create_time) }}</el-text>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" align="center" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="small"
                  @click="openEditDialog(row)"
                  :icon="Edit"
                  circle
                  title="编辑"
                />
                <el-button
                  v-if="row.status !== 1"
                  type="success"
                  size="small"
                  @click="updateStatus(row.id, 1)"
                  :icon="CircleCheck"
                  circle
                  title="标记完成"
                />
                <el-button
                  v-if="row.status !== 0"
                  type="warning"
                  size="small"
                  @click="updateStatus(row.id, 0)"
                  :icon="Clock"
                  circle
                  title="标记处理中"
                />
                <el-button
                  @click="deleteBug(row.id)"
                  type="danger"
                  size="small"
                  :icon="Delete"
                  circle
                  title="删除"
                />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 新增 Bug 弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="新增 Bug 报告"
      width="600px"
      :close-on-click-modal="false"
      class="bug-dialog"
    >
      <el-form
        :model="form"
        :rules="formRules"
        ref="formRef"
        label-width="100px"
        class="bug-form"
      >
        <el-form-item label="Bug 标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入简洁明确的Bug标题"
            maxlength="100"
            show-word-limit
            :prefix-icon="Document"
          />
        </el-form-item>

        <el-form-item label="详细描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请详细描述Bug的现象、重现步骤、预期结果等..."
            maxlength="500"
            show-word-limit
            :rows="6"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="严重程度" prop="severity">
              <el-select v-model="form.severity" placeholder="请选择严重程度" style="width: 100%;">
                <el-option label="一般" value="一般">
                  <div class="severity-option">
                    <el-tag type="info" size="small">一般</el-tag>
                    <span class="option-desc">轻微影响，不影响主要功能</span>
                  </div>
                </el-option>
                <el-option label="严重" value="严重">
                  <div class="severity-option">
                    <el-tag type="warning" size="small">严重</el-tag>
                    <span class="option-desc">影响主要功能，需要优先处理</span>
                  </div>
                </el-option>
                <el-option label="致命" value="致命">
                  <div class="severity-option">
                    <el-tag type="danger" size="small">致命</el-tag>
                    <span class="option-desc">系统崩溃或数据丢失，紧急处理</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="初始状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%;">
                <el-option label="新增" value="2">
                  <div class="status-option">
                    <el-tag type="info" size="small">新增</el-tag>
                    <span class="option-desc">刚发现的Bug</span>
                  </div>
                </el-option>
                <el-option label="处理中" value="0">
                  <div class="status-option">
                    <el-tag type="warning" size="small">处理中</el-tag>
                    <span class="option-desc">正在修复中</span>
                  </div>
                </el-option>
                <el-option label="已完成" value="1">
                  <div class="status-option">
                    <el-tag type="success" size="small">已完成</el-tag>
                    <span class="option-desc">已修复完成</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" size="large">取消</el-button>
          <el-button type="primary" @click="addBug" size="large" :loading="submitting">
            <el-icon><Plus /></el-icon>
            提交 Bug
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑 Bug 弹窗 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑 Bug 信息"
      width="600px"
      :close-on-click-modal="false"
      class="bug-dialog"
    >
      <el-form
        :model="editForm"
        :rules="formRules"
        ref="editFormRef"
        label-width="100px"
        class="bug-form"
      >
        <el-form-item label="Bug 标题" prop="title">
          <el-input
            v-model="editForm.title"
            placeholder="请输入简洁明确的Bug标题"
            maxlength="100"
            show-word-limit
            :prefix-icon="Document"
          />
        </el-form-item>

        <el-form-item label="详细描述" prop="description">
          <el-input
            v-model="editForm.description"
            type="textarea"
            placeholder="请详细描述Bug的现象、重现步骤、预期结果等..."
            maxlength="500"
            show-word-limit
            :rows="6"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="严重程度" prop="severity">
              <el-select v-model="editForm.severity" placeholder="请选择严重程度" style="width: 100%;">
                <el-option label="一般" value="一般">
                  <div class="severity-option">
                    <el-tag type="info" size="small">一般</el-tag>
                    <span class="option-desc">轻微影响</span>
                  </div>
                </el-option>
                <el-option label="严重" value="严重">
                  <div class="severity-option">
                    <el-tag type="warning" size="small">严重</el-tag>
                    <span class="option-desc">影响主要功能</span>
                  </div>
                </el-option>
                <el-option label="致命" value="致命">
                  <div class="severity-option">
                    <el-tag type="danger" size="small">致命</el-tag>
                    <span class="option-desc">系统崩溃</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理状态" prop="status">
              <el-select v-model="editForm.status" placeholder="请选择状态" style="width: 100%;">
                <el-option label="新增" value="2">
                  <div class="status-option">
                    <el-tag type="info" size="small">新增</el-tag>
                  </div>
                </el-option>
                <el-option label="处理中" value="0">
                  <div class="status-option">
                    <el-tag type="warning" size="small">处理中</el-tag>
                  </div>
                </el-option>
                <el-option label="已完成" value="1">
                  <div class="status-option">
                    <el-tag type="success" size="small">已完成</el-tag>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false" size="large">取消</el-button>
          <el-button type="primary" @click="submitEdit" size="large" :loading="submitting">
            <el-icon><Check /></el-icon>
            保存修改
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 描述详情弹窗 -->
    <el-dialog
      v-model="descDialogVisible"
      title="Bug 详细描述"
      width="700px"
      class="desc-dialog"
    >
      <div class="description-content">
        <el-scrollbar height="400px">
          <div class="description-text">{{ descDialogContent }}</div>
        </el-scrollbar>
      </div>
      <template #footer>
        <el-button type="primary" @click="descDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import {
  GetBugListApi,
  AddBugApi,
  UpdateBugStatusApi,
  DeleteBugApi,
  DeleteBugsApi,
  UpdateBugApi
} from "../../utils/api";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Plus, Document, CirclePlus, Clock, CircleCheck,
  Search, RefreshLeft, Delete, Hide, View, Calendar,
  Edit, Check, Warning, Close
} from '@element-plus/icons-vue';

// 启用相对时间插件
dayjs.extend(relativeTime);

// 基础状态
const bugList = ref<any[]>([]);
const loading = ref(false);
const submitting = ref(false);
const dialogVisible = ref(false);
const descDialogVisible = ref(false);
const editDialogVisible = ref(false);

// 表单引用
const formRef = ref();
const editFormRef = ref();

// 表单数据
const form = ref({
  title: "",
  description: "",
  status: "2", // 默认为新增
  severity: "一般", // 默认为一般
});

const editForm = ref({
  id: null,
  title: "",
  description: "",
  status: "2",
  severity: "一般",
});

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入Bug标题', trigger: 'blur' },
    { min: 5, max: 100, message: '标题长度应在5-100个字符之间', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入Bug描述', trigger: 'blur' },
    { min: 10, max: 500, message: '描述长度应在10-500个字符之间', trigger: 'blur' }
  ],
  severity: [
    { required: true, message: '请选择严重程度', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 其他状态
const descDialogContent = ref("");
const bugTableRef = ref();
const multipleSelection = ref<any[]>([]);
const showCompleted = ref(false);

// 搜索表单
const searchForm = ref({
  title: "",
  description: "",
  severity: "",
  status: "",
  createTime: [] as string[],
});

// 统计数据
const bugStats = computed(() => {
  const total = bugList.value.length;
  const newBugs = bugList.value.filter(bug => bug.status === 2).length;
  const pending = bugList.value.filter(bug => bug.status === 0).length;
  const completed = bugList.value.filter(bug => bug.status === 1).length;

  return {
    total,
    new: newBugs,
    pending,
    completed
  };
});

// 状态文本和样式
const statusText = (status: number) => {
  if (status === 1) return "已完成";
  if (status === 0) return "处理中";
  return "新增";
};

const statusTagType = (status: number) => {
  if (status === 1) return "success";
  if (status === 2) return "info";
  if (status === 0) return "warning";
  return "info";
};

// 程度样式
const severityTagType = (severity: string) => {
  if (severity === "一般") return "info";
  if (severity === "致命") return "danger";
  if (severity === "严重") return "warning";
  return "info";
};

// 获取状态图标
const getStatusIcon = (status: number) => {
  if (status === 1) return CircleCheck;
  if (status === 0) return Clock;
  return CirclePlus;
};

// 获取严重程度图标
const getSeverityIcon = (severity: string) => {
  if (severity === "致命") return Close;
  if (severity === "严重") return Warning;
  return Document;
};

// 表格行样式
const getRowClassName = ({ row }: { row: any }) => {
  if (row.status === 1) return 'completed-row';
  if (row.severity === '致命') return 'critical-row';
  if (row.severity === '严重') return 'severe-row';
  return '';
};

// 获取 bug 列表
const fetchBugList = async () => {
  loading.value = true;
  try {
    const res = await GetBugListApi();
    bugList.value = res.data || [];
  } catch (e) {
    ElMessage.error("获取 bug 列表失败");
  } finally {
    loading.value = false;
  }
};

// 新增 bug
const addBug = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;
  } catch {
    return;
  }

  submitting.value = true;
  try {
    await AddBugApi({
      title: form.value.title,
      description: form.value.description,
      status: form.value.status,
      severity: form.value.severity,
    });

    ElMessage.success("Bug 提交成功！");
    dialogVisible.value = false;

    // 重置表单
    form.value = {
      title: "",
      description: "",
      status: "2",
      severity: "一般",
    };
    formRef.value.resetFields();

    fetchBugList();
  } catch (e) {
    ElMessage.error("提交失败，请重试");
  } finally {
    submitting.value = false;
  }
};

// 修改 bug 状态
const updateStatus = async (id: number, status: number) => {
  // 找到当前 bug 的 severity
  const bug = bugList.value.find((item) => item.id === id);
  const severity = bug ? bug.severity : "一般";
  try {
    await UpdateBugStatusApi(id, status, severity);
    ElMessage.success("状态已更新");
    fetchBugList();
  } catch (e) {
    ElMessage.error("状态更新失败");
  }
};

// 删除 bug
const deleteBug = async (id: number) => {
  ElMessageBox.confirm("确定要删除该 bug 吗？", "提示", {
    type: "warning",
  })
    .then(async () => {
      await DeleteBugApi(id);
      ElMessage.success("删除成功");
      fetchBugList();
    })
    .catch(() => { });
};

// 批量删除选中 bug
const deleteSelectedBugs = () => {
  if (!multipleSelection.value.length) return;
  ElMessageBox.confirm(
    `确定要删除选中的 ${multipleSelection.value.length} 条 bug 吗？`,
    "提示",
    { type: "warning" }
  )
    .then(async () => {
      const ids = multipleSelection.value.map(item => item.id);
      await DeleteBugsApi(ids);
      ElMessage.success("批量删除成功");
      fetchBugList();
      // 清空选择
      bugTableRef.value && bugTableRef.value.clearSelection();
    })
    .catch(() => { });
};

// 显示描述
const showDesc = (desc: string) => {
  if (!desc) return "";
  return desc.length > 50 ? desc.slice(0, 50) + "..." : desc;
};

// 显示描述详情弹窗
const showDescDialog = (desc: string) => {
  descDialogContent.value = desc;
  descDialogVisible.value = true;
};

// 时间格式化方法
const formatTime = (time: string) => {
  return time ? dayjs(time).format("YYYY-MM-DD HH:mm:ss") : "";
};

// 短时间格式化
const formatTimeShort = (time: string) => {
  return time ? dayjs(time).format("MM-DD HH:mm") : "";
};

// 相对时间
const getTimeAgo = (time: string) => {
  return time ? dayjs(time).fromNow() : "";
};

// 过滤后的 bug 列表
const filteredBugList = computed(() => {
  return bugList.value.filter((bug) => {
    // 标题
    if (searchForm.value.title && !bug.title.includes(searchForm.value.title)) return false;
    // 描述
    if (searchForm.value.description && !bug.description.includes(searchForm.value.description)) return false;
    // 程度
    if (searchForm.value.severity && bug.severity !== searchForm.value.severity) return false;
    // 状态
    if (searchForm.value.status && String(bug.status) !== String(searchForm.value.status)) return false;
    // 折叠已完成
    if (!showCompleted.value && bug.status === 1) return false;
    // 创建时间
    if (
      searchForm.value.createTime &&
      searchForm.value.createTime.length === 2
    ) {
      const start = dayjs(searchForm.value.createTime[0] + " 00:00:00");
      const end = dayjs(searchForm.value.createTime[1] + " 23:59:59");
      const bugTime = dayjs(bug.create_time);
      if (bugTime.isBefore(start) || bugTime.isAfter(end)) return false;
    }
    return true;
  });
});

const doSearch = () => {
  // 这里只需触发 filteredBugList 重新计算即可
};
const resetSearch = () => {
  searchForm.value = {
    title: "",
    description: "",
    severity: "",
    status: "",
    createTime: [],
  };
};

const handleSelectionChange = (val: any[]) => {
  multipleSelection.value = val;
};

// 打开编辑弹窗
const openEditDialog = (row: any) => {
  editForm.value = { ...row }; // 浅拷贝
  editDialogVisible.value = true;
};

// 提交编辑
const submitEdit = async () => {
  if (!editFormRef.value) return;

  try {
    const valid = await editFormRef.value.validate();
    if (!valid) return;
  } catch {
    return;
  }

  submitting.value = true;
  try {
    await UpdateBugApi({
      id: editForm.value.id,
      title: editForm.value.title,
      description: editForm.value.description,
      status: editForm.value.status,
      severity: editForm.value.severity,
    });

    ElMessage.success("修改成功！");
    editDialogVisible.value = false;
    fetchBugList();
  } catch (e) {
    ElMessage.error("修改失败，请重试");
  } finally {
    submitting.value = false;
  }
};

onMounted(() => {
  fetchBugList();
});
</script>

<style scoped lang="less">
.bug-management {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    color: white;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  }

  .title-section {
    .page-title {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 700;
      display: flex;
      align-items: center;
      gap: 12px;

      .title-icon {
        font-size: 32px;
      }
    }

    .page-subtitle {
      margin: 0;
      font-size: 16px;
      opacity: 0.9;
    }
  }

  .header-actions {
    .el-button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
      }
    }
  }
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;

  .stats-card {
    border-radius: 12px;
    border: none;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
    }

    .stats-content {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 8px;
    }

    .stats-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
    }

    .stats-info {
      flex: 1;
    }

    .stats-number {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 4px;
    }

    .stats-label {
      font-size: 14px;
      color: #6c757d;
      font-weight: 500;
    }

    &.stats-total {
      .stats-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      .stats-number {
        color: #667eea;
      }
    }

    &.stats-new {
      .stats-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
      .stats-number {
        color: #4facfe;
      }
    }

    &.stats-pending {
      .stats-icon {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      }
      .stats-number {
        color: #fa709a;
      }
    }

    &.stats-completed {
      .stats-icon {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
      }
      .stats-number {
        color: #52c41a;
      }
    }
  }
}

/* 主要内容 */
.main-content {
  border-radius: 16px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 搜索区域 */
.search-section {
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 20px;

  .search-form {
    .el-form-item {
      margin-bottom: 16px;
    }
  }

  .option-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 0;

  .toolbar-left {
    display: flex;
    gap: 12px;
  }

  .toolbar-right {
    color: #6c757d;
  }
}

/* 表格样式 */
.table-section {
  .bug-table {
    border-radius: 8px;
    overflow: hidden;

    :deep(.el-table__header) {
      background: #fafafa;

      th {
        background: #fafafa !important;
        color: #2c3e50;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
      }
    }

    :deep(.completed-row) {
      background: #f6ffed;
      opacity: 0.8;
    }

    :deep(.critical-row) {
      background: #fff2f0;
    }

    :deep(.severe-row) {
      background: #fffbe6;
    }
  }

  .title-cell {
    .title-text {
      font-weight: 600;
      color: #2c3e50;
      display: block;
      margin-bottom: 4px;
    }

    .title-meta {
      display: flex;
      align-items: center;
      gap: 4px;
      color: #6c757d;
      font-size: 12px;

      .meta-icon {
        font-size: 12px;
      }
    }
  }

  .description-cell {
    .description-text {
      color: #495057;
      line-height: 1.5;
      display: block;
      margin-bottom: 4px;
    }

    .view-more-btn {
      font-size: 12px;
      padding: 0;
      height: auto;
    }
  }

  .time-cell {
    display: flex;
    flex-direction: column;
    gap: 2px;
    align-items: center;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;

    .el-button {
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
      }
    }
  }
}

/* 对话框样式 */
.bug-dialog {
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    margin: 0;
    border-radius: 8px 8px 0 0;
  }

  :deep(.el-dialog__title) {
    color: white;
    font-weight: 600;
    font-size: 18px;
  }

  :deep(.el-dialog__close) {
    color: white;

    &:hover {
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .bug-form {
    padding: 24px;

    .severity-option,
    .status-option {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .option-desc {
        font-size: 12px;
        color: #6c757d;
      }
    }
  }

  .dialog-footer {
    padding: 16px 24px;
    background: #fafafa;
    border-radius: 0 0 8px 8px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

/* 描述详情对话框 */
.desc-dialog {
  .description-content {
    padding: 20px;

    .description-text {
      line-height: 1.8;
      color: #2c3e50;
      white-space: pre-wrap;
      word-break: break-word;
      font-size: 14px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .bug-management {
    padding: 16px;
  }

  .page-header .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .search-section .search-form {
    .el-form-item {
      width: 100%;
    }
  }
}

@media (max-width: 768px) {
  .stats-section {
    .el-col {
      margin-bottom: 16px;
    }
  }

  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;

    .toolbar-left {
      justify-content: center;
    }

    .toolbar-right {
      text-align: center;
    }
  }

  .table-section {
    overflow-x: auto;

    .bug-table {
      min-width: 800px;
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;

    .el-button {
      width: 100%;
    }
  }
}

/* 动画效果 */
.stats-card {
  animation: fadeInUp 0.6s ease-out;
}

.main-content {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background: #f8f9fa !important;
  transform: scale(1.01);
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
