const db = require("../db");

// 搜索user表的username用户
async function searchUser(username) {
  const sql = "SELECT * FROM users WHERE username = ?";
  const result = await db.query(sql, [username]);
  return result.length > 0 ? result[0] : null;
}

// 加好友用户 当前登录的用户id，被添加的用户id
async function addFriend(user_id, friend_id) {
  const sql = "INSERT INTO friends(user_id, friend_id) VALUES(?, ?)";
  const result = await db.query(sql, [user_id, friend_id]);
  return result.affectedRows > 0;
}

// 显示所有好友列表
async function showFriendList(user_id) {
  // 根据user_id查询friends表，获取所有好友的friend_id,friend_id为users表的id再查询users表的username
  const sql =
    "SELECT * FROM users WHERE id IN (SELECT friend_id FROM friends WHERE user_id = ?)";
  const result = await db.query(sql, [user_id]);
  return result;
  // const sql = "SELECT * FROM friends WHERE user_id = ?";
  // const result = await db.query(sql, [user_id]);
  // return result;
}

// 存储聊天记录
async function saveChatRecord(user_id, friend_id, message) {
  const sql =
    "INSERT INTO chat_record(user_id, friend_id, message) VALUES(?, ?, ?)";
  const result = await db.query(sql, [user_id, friend_id, message]);
  return result.affectedRows > 0;
}

// 显示所有好友的聊天记录
async function showChatRecord(user_id, friend_id) {
  const sql =
    "SELECT * FROM chat_record WHERE (user_id = ? AND friend_id = ?) OR (user_id = ? AND friend_id = ?) ORDER BY create_time DESC";
  const result = await db.query(sql, [user_id, friend_id, friend_id, user_id]);
  return result;
}

// 发送好友请求
async function sendFriendRequest(from_id, to_id) {
  const sql =
    "INSERT INTO friend_request(from_id, to_id, status) VALUES(?, ?, 'pending')";
  const result = await db.query(sql, [from_id, to_id]);
  return result.affectedRows > 0;
}

// 查询待处理的好友请求
async function getPendingRequests(user_id) {
  //   const sql =
  //     "SELECT * FROM friend_request WHERE to_id = ? AND status = 'pending'";
  //   return await db.query(sql, [user_id]);
  // 通过user_id查询friend_request表，获取所有待处理的请求，并返回请求列表 to_id为users表的id再查询users表的username
  // 返回用户id 和username
  const sql =
    "SELECT fr.*, u.username AS from_username FROM friend_request fr JOIN users u ON fr.from_id = u.id WHERE fr.to_id = ? AND fr.status = 'pending'";
  const result = await db.query(sql, [user_id]);
  return result.map((req) => ({
    id: req.id,
    from_id: req.from_id,
    from_username: req.from_username,
    to_id: req.to_id,
    status: req.status,
  }));
}

// 处理好友请求（同意/拒绝）
async function handleFriendRequest(request_id, agree) {
  // 查询请求
  const req = await db.query("SELECT * FROM friend_request WHERE id=?", [
    request_id,
  ]);
  if (!req.length) return false;
  if (agree) {
    await db.query("UPDATE friend_request SET status='accepted' WHERE id=?", [
      request_id,
    ]);
    // 双向加好友
    await db.query("INSERT IGNORE INTO friends(user_id, friend_id) VALUES(?, ?)", [
      req[0].from_id,
      req[0].to_id,
    ]);
    await db.query("INSERT IGNORE INTO friends(user_id, friend_id) VALUES(?, ?)", [
      req[0].to_id,
      req[0].from_id,
    ]);
    return true;
  } else {
    await db.query("UPDATE friend_request SET status='rejected' WHERE id=?", [
      request_id,
    ]);
    return true;
  }
}

// 检查是否已发送过好友请求
async function checkFriendRequest(from_id, to_id) {
  const sql = "SELECT * FROM friend_request WHERE from_id=? AND to_id=? AND status='pending'";
  const result = await db.query(sql, [from_id, to_id]);
  return result.length > 0;
}

module.exports = {
  searchUser,
  addFriend,
  showFriendList,
  saveChatRecord,
  showChatRecord,
  sendFriendRequest,
  getPendingRequests,
  handleFriendRequest,
  checkFriendRequest, // 新增导出
};
