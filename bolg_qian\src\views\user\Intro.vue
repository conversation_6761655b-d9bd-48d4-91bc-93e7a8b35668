<template>
  <div class="intro-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon class="title-icon"><User /></el-icon>
          个人资料
        </h1>
        <p class="page-description">完善您的个人信息，让其他用户更好地了解您</p>
      </div>
      <div class="header-actions">
        <el-button @click="goBack">返回</el-button>
        <el-button @click="resetForm">重置</el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-container">
      <!-- 头像预览卡片 -->
      <div class="avatar-preview-card">
        <div class="avatar-section">
          <div class="avatar-container">
            <el-avatar
              :size="120"
              :src="avatarUrl"
              class="user-avatar"
              :icon="UserFilled"
            />
            <div class="avatar-overlay" @click="triggerAvatarUpload">
              <el-icon class="camera-icon"><Camera /></el-icon>
              <span>更换头像</span>
            </div>
          </div>
          <div class="avatar-info">
            <h3>{{ form.username || '用户' }}</h3>
            <p class="user-role">{{ form.position || '暂未设置职位' }}</p>
            <el-tag v-if="form.tech_tags && form.tech_tags.length > 0" type="primary" size="small">
              {{ form.tech_tags.length }} 个技能标签
            </el-tag>
          </div>
        </div>

        <!-- 隐藏的文件上传 -->
        <el-upload
          ref="avatarUploadRef"
          class="hidden-upload"
          action=""
          :show-file-list="false"
          :before-upload="handleAvatarChange"
          accept="image/*"
        >
        </el-upload>
      </div>

      <!-- 表单卡片 -->
      <div class="form-card">
        <div class="card-header">
          <h3>基本信息</h3>
          <el-progress
            :percentage="formCompleteness"
            :color="progressColor"
            :stroke-width="6"
            class="progress-bar"
          />
        </div>

        <el-form
          :model="form"
          :rules="formRules"
          ref="formRef"
          label-position="top"
          class="user-form"
        >
          <div class="form-grid">
            <!-- 基础信息组 -->
            <div class="form-group">
              <h4 class="group-title">
                <el-icon><UserFilled /></el-icon>
                基础信息
              </h4>

              <el-form-item label="用户名" prop="username">
                <el-input
                  v-model="form.username"
                  disabled
                  prefix-icon="User"
                  class="disabled-input"
                />
                <div class="field-tip">用户名不可修改</div>
              </el-form-item>

              <el-form-item label="邮箱地址" prop="email">
                <el-input
                  v-model="form.email"
                  prefix-icon="Message"
                  placeholder="请输入您的邮箱地址"
                />
              </el-form-item>

              <el-form-item label="所在地址" prop="address">
                <el-input
                  v-model="form.address"
                  prefix-icon="Location"
                  placeholder="请输入您的所在地址"
                />
              </el-form-item>
            </div>

            <!-- 职业信息组 -->
            <div class="form-group">
              <h4 class="group-title">
                <el-icon><Briefcase /></el-icon>
                职业信息
              </h4>

              <el-form-item label="职位/职业" prop="position">
                <el-input
                  v-model="form.position"
                  prefix-icon="Briefcase"
                  placeholder="如：前端工程师、产品经理等"
                />
              </el-form-item>

              <el-form-item label="技术标签" prop="tech_tags">
                <el-select
                  v-model="form.tech_tags"
                  multiple
                  filterable
                  allow-create
                  placeholder="选择或输入您的技术标签"
                  class="tech-tags-select"
                >
                  <el-option
                    v-for="tag in tagOptions"
                    :key="tag"
                    :label="tag"
                    :value="tag"
                  >
                    <span class="tag-option">
                      <el-icon><PriceTag /></el-icon>
                      {{ tag }}
                    </span>
                  </el-option>
                </el-select>
                <div class="field-tip">最多选择10个标签，可自定义添加</div>
              </el-form-item>
            </div>
          </div>

          <!-- 个人简介 -->
          <div class="form-group full-width">
            <h4 class="group-title">
              <el-icon><Document /></el-icon>
              个人简介
            </h4>

            <el-form-item label="个人简介" prop="intro">
              <el-input
                v-model="form.intro"
                type="textarea"
                :rows="4"
                placeholder="介绍一下您自己，让其他人更好地了解您..."
                maxlength="500"
                show-word-limit
                class="intro-textarea"
              />
            </el-form-item>
          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button @click="resetForm" size="large">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
            <el-button
              type="primary"
              @click="submit"
              :loading="submitting"
              size="large"
            >
              <el-icon><Check /></el-icon>
              保存资料
            </el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { GetProfileApi, UpdateProfileApi, GetTagsApi } from "../../utils/api";
import {
  User, UserFilled, Camera, ArrowLeft, RefreshLeft, Check,
  Message, Location, Briefcase, PriceTag, Document
} from "@element-plus/icons-vue";
import { useRouter } from "vue-router";

// 路由和用户信息
const router = useRouter();
const userId = localStorage.getItem("id") || "";

// 表单数据
const form = ref({
  id: userId,
  username: "",
  email: "",
  avatar: "",
  address: "",
  position: "",
  intro: "",
  tech_tags: [] as string[],
});

// 响应式数据
const avatarUrl = ref("");
const tagOptions = ref<string[]>([]);
const avatarFile = ref<File | null>(null);
const submitting = ref(false);
const formRef = ref();
const avatarUploadRef = ref();

// 表单验证规则
const formRules = {
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  intro: [
    { max: 500, message: '个人简介不能超过500个字符', trigger: 'blur' }
  ],
  tech_tags: [
    {
      validator: (rule: any, value: string[], callback: Function) => {
        if (value && value.length > 10) {
          callback(new Error('最多只能选择10个技术标签'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ]
};

// 计算属性
const formCompleteness = computed(() => {
  const fields = ['username', 'email', 'address', 'position', 'intro'];
  const filledFields = fields.filter(field => {
    const value = form.value[field as keyof typeof form.value];
    return value && String(value).trim() !== '';
  });

  // 头像和技术标签额外加分
  let bonus = 0;
  if (avatarUrl.value) bonus += 10;
  if (form.value.tech_tags.length > 0) bonus += 10;

  return Math.min(100, Math.round((filledFields.length / fields.length) * 80) + bonus);
});

const progressColor = computed(() => {
  const percentage = formCompleteness.value;
  if (percentage < 30) return '#f56c6c';
  if (percentage < 70) return '#e6a23c';
  return '#67c23a';
});

// 生命周期
onMounted(async () => {
  await loadUserProfile();
  await loadTechTags();
});

// 加载用户资料
const loadUserProfile = async () => {
  try {
    const res = await GetProfileApi({ id: userId });
    const userData = res?.data?.user || res?.user || res?.data;

    if (userData) {
      Object.assign(form.value, userData);
      avatarUrl.value = form.value.avatar
        ? `/api/avatars/${form.value.avatar}`
        : "";

      // 处理技术标签数据
      if (typeof form.value.tech_tags === "string") {
        try {
          form.value.tech_tags = JSON.parse(form.value.tech_tags);
        } catch {
          form.value.tech_tags = [];
        }
      }

      if (!Array.isArray(form.value.tech_tags)) {
        form.value.tech_tags = [];
      }
    }
  } catch (error) {
    console.error('加载用户资料失败:', error);
    ElMessage.error('加载用户资料失败');
  }
};

// 加载技术标签
const loadTechTags = async () => {
  try {
    const tagRes = await GetTagsApi();
    const tagsData = tagRes?.data?.tags || tagRes?.tags || tagRes?.data || tagRes;

    if (Array.isArray(tagsData)) {
      tagOptions.value = tagsData.map((t: any) => t.name || t);
    } else {
      // 默认技术标签
      tagOptions.value = [
        'JavaScript', 'TypeScript', 'Vue.js', 'React', 'Angular',
        'Node.js', 'Python', 'Java', 'Go', 'Rust',
        'HTML', 'CSS', 'SCSS', 'Tailwind CSS', 'Bootstrap',
        'MySQL', 'PostgreSQL', 'MongoDB', 'Redis',
        'Docker', 'Kubernetes', 'AWS', 'Git'
      ];
    }
  } catch (error) {
    console.error('加载技术标签失败:', error);
    // 使用默认标签
    tagOptions.value = [
      'JavaScript', 'TypeScript', 'Vue.js', 'React', 'Node.js', 'Python'
    ];
  }
};

// 触发头像上传
const triggerAvatarUpload = () => {
  avatarUploadRef.value?.$el?.querySelector('input')?.click();
};

// 头像选择处理
const handleAvatarChange = (file: File) => {
  // 验证文件类型
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    ElMessage.error('只能上传图片文件！');
    return false;
  }

  // 验证文件大小 (5MB)
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB！');
    return false;
  }

  avatarFile.value = file;
  const reader = new FileReader();
  reader.onload = e => {
    avatarUrl.value = e.target?.result as string;
  };
  reader.readAsDataURL(file);

  ElMessage.success('头像已选择，保存后生效');
  return false; // 阻止自动上传
};

// 重置表单
const resetForm = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置表单吗？这将清除所有未保存的修改。',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    await loadUserProfile();
    avatarFile.value = null;
    ElMessage.success('表单已重置');
  } catch {
    // 用户取消
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 提交表单
const submit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate();

    submitting.value = true;

    const fd = new FormData();
    Object.entries(form.value).forEach(([key, value]) => {
      if (key === "tech_tags") {
        fd.append(key, JSON.stringify(value));
      } else {
        fd.append(key, String(value ?? ""));
      }
    });

    if (avatarFile.value) {
      fd.append("file", avatarFile.value);
    }

    const res = await UpdateProfileApi(fd);
    const responseData = res?.data || res;

    if (responseData?.message || responseData?.success) {
      ElMessage.success("个人资料保存成功！");

      // 更新头像显示
      if (responseData.avatar) {
        form.value.avatar = responseData.avatar;
        avatarUrl.value = `${import.meta.env.VITE_BASE_URL || ""}/avatars/${responseData.avatar}`;
      }

      // 延迟跳转
      setTimeout(() => {
        router.push("/");
      }, 1500);
    } else {
      ElMessage.error(responseData?.error || "保存失败，请重试");
    }
  } catch (error: any) {
    console.error('保存失败:', error);
    if (error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error("保存失败，请检查网络连接");
    }
  } finally {
    submitting.value = false;
  }
};
</script>

<style scoped>
/* 主容器 */
.intro-page {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, #e2e8f0 100%);
  font-family: var(--font-family-sans);
}

/* 页面标题区域 */
.page-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  padding: var(--spacing-2xl) var(--spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
}

.header-content {
  flex: 1;
  z-index: 2;
}

.page-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 2.5rem;
  font-weight: var(--font-bold);
  color: white;
  margin: 0 0 var(--spacing-sm) 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-icon {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-sm);
  border-radius: var(--radius-lg);
  font-size: 1.8rem;
  backdrop-filter: blur(10px);
}

.page-description {
  color: rgba(255, 255, 255, 0.9);
  font-size: var(--text-lg);
  margin: 0;
  font-weight: var(--font-medium);
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
  z-index: 2;
}

.header-actions .el-button {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  font-weight: var(--font-medium);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 内容容器 */
.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: var(--spacing-xl);
  margin-top: -60px;
  position: relative;
  z-index: 10;
}

/* 头像预览卡片 */
.avatar-preview-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: fit-content;
  position: sticky;
  top: var(--spacing-xl);
}

.avatar-section {
  text-align: center;
}

.avatar-container {
  position: relative;
  display: inline-block;
  margin-bottom: var(--spacing-lg);
}

.user-avatar {
  border: 4px solid var(--primary-color);
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  transition: all var(--transition-normal);
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all var(--transition-normal);
  cursor: pointer;
  color: white;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

.avatar-container:hover .user-avatar {
  transform: scale(1.05);
}

.camera-icon {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-xs);
}

.avatar-info h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.user-role {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  margin: 0 0 var(--spacing-md) 0;
}

.hidden-upload {
  display: none;
}

/* 表单卡片 */
.form-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.progress-bar {
  width: 200px;
}

/* 表单样式 */
.user-form {
  padding: var(--spacing-xl);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.group-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--primary-color);
}

.group-title .el-icon {
  color: var(--primary-color);
  font-size: 1.2rem;
}

/* 表单项样式 */
.user-form :deep(.el-form-item) {
  margin-bottom: var(--spacing-lg);
}

.user-form :deep(.el-form-item__label) {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  font-size: var(--text-sm);
  margin-bottom: var(--spacing-xs);
}

.user-form :deep(.el-input__wrapper) {
  border-radius: var(--radius-lg);
  border: 2px solid var(--border-light);
  transition: all var(--transition-normal);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.user-form :deep(.el-input__wrapper:hover) {
  border-color: var(--primary-light);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.user-form :deep(.el-input__wrapper.is-focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
}

.disabled-input :deep(.el-input__wrapper) {
  background: var(--bg-tertiary);
  border-color: var(--border-medium);
}

.field-tip {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  margin-top: var(--spacing-xs);
}

/* 技术标签选择器 */
.tech-tags-select {
  width: 100%;
}

.tech-tags-select :deep(.el-select__wrapper) {
  border-radius: var(--radius-lg);
  border: 2px solid var(--border-light);
  transition: all var(--transition-normal);
}

.tag-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 文本域 */
.intro-textarea :deep(.el-textarea__inner) {
  border-radius: var(--radius-lg);
  border: 2px solid var(--border-light);
  transition: all var(--transition-normal);
  font-family: var(--font-family-sans);
  line-height: var(--leading-relaxed);
}

.intro-textarea :deep(.el-textarea__inner:hover) {
  border-color: var(--primary-light);
}

.intro-textarea :deep(.el-textarea__inner:focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
}

/* 操作按钮 */
.form-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
}

.form-actions .el-button {
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-lg);
  font-weight: var(--font-medium);
  transition: all var(--transition-normal);
}

.form-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.form-actions .el-button--primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
  }

  .avatar-preview-card {
    position: static;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: var(--spacing-xl) var(--spacing-lg);
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-lg);
    text-align: center;
  }

  .page-title {
    font-size: 2rem;
    justify-content: center;
  }

  .header-actions {
    justify-content: center;
  }

  .content-container {
    margin-top: -30px;
    padding: var(--spacing-md);
  }

  .avatar-preview-card,
  .form-card {
    padding: var(--spacing-lg);
  }

  .card-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .progress-bar {
    width: 100%;
  }

  .form-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.75rem;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .content-container {
    padding: var(--spacing-sm);
  }

  .avatar-preview-card,
  .form-card {
    padding: var(--spacing-md);
  }

  .user-form {
    padding: var(--spacing-md);
  }
}

/* 动画效果 */
.intro-page {
  animation: fadeInUp 0.6s ease-out;
}

.avatar-preview-card {
  animation: slideInFromLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

.form-card {
  animation: slideInFromRight 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>