import request from "./index";

// 获取文章点赞历史
export const getArticleLikesHistoryApi = (params: any) =>
    request.get("/article-likes/history", { params });

// 文章收藏相关API
export const toggleArticleFavoriteApi = (article_id: number) =>
    request.post("/article-favorites/toggle", { article_id });

export const getArticleFavoriteStatsApi = (articleId: number) =>
    request.get(`/article-favorites/stats/${articleId}`);

export const getArticleFavoriteUsersApi = (articleId: number, params?: any) =>
    request.get(`/article-favorites/users/${articleId}`, { params });

export const getArticleFavoriteHistoryApi = (params: any) =>
    request.get("/article-favorites/history", { params });