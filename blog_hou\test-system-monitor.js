// 系统监控API测试脚本
const axios = require('axios');

const baseURL = 'http://localhost:3000';
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTksInVzZXJJZCI6MTksInVzZXJuYW1lIjoiYWRtaW4xMjMiLCJyb2xlIjoiYWRtaW4iLCJpYXQiOjE3NTE2OTY4MjIsImV4cCI6MTc1MjMwMTYyMiwiYWxnIjoiSFMyNTYiLCJpc3MiOiJteWJsb2ctYXBwIiwiYXVkIjoibXlibG9nLXVzZXJzIn0.Qs8Qs_Qs8Qs_Qs8Qs_Qs8Qs_Qs8Qs_Qs8Qs_Qs8Qs'; // 需要有效的JWT token

async function testSystemMonitorAPIs() {
  console.log('🧪 开始测试系统监控API...\n');

  const apis = [
    { name: '系统概览', path: '/system-monitor/overview' },
    { name: '数据库统计', path: '/system-monitor/database-stats' },
    { name: 'API统计', path: '/system-monitor/api-stats' },
    { name: '实时监控', path: '/system-monitor/realtime' },
    { name: '系统日志', path: '/system-monitor/logs?level=all&limit=5' }
  ];

  for (const api of apis) {
    try {
      console.log(`📡 测试 ${api.name}...`);
      const response = await axios.get(`${baseURL}${api.path}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.status === 200) {
        console.log(`✅ ${api.name} - 成功`);
        console.log(`   状态码: ${response.status}`);
        console.log(`   数据结构: ${response.data.data ? '✅ 有数据' : '❌ 无数据'}`);
        console.log(`   消息: ${response.data.message}`);
        
        // 显示部分数据结构
        if (response.data.data) {
          const keys = Object.keys(response.data.data);
          console.log(`   数据字段: ${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''}`);
        }
      } else {
        console.log(`❌ ${api.name} - 失败 (状态码: ${response.status})`);
      }
    } catch (error) {
      console.log(`❌ ${api.name} - 错误: ${error.message}`);
      if (error.response) {
        console.log(`   状态码: ${error.response.status}`);
        console.log(`   错误信息: ${error.response.data?.message || '未知错误'}`);
      }
    }
    console.log('');
  }

  console.log('🎉 系统监控API测试完成!');
}

testSystemMonitorAPIs().catch(console.error);
