const Router = require("koa-router");
const fs = require("fs");
const path = require("path");
const { handleResponse } = require("../../middlewares/responseHandler");
const { getMimeType } = require("../../utils/mimeTypeMap.js");
const bodyParser = require("koa-bodyparser");

const resource = new Router();
resource.use(bodyParser());
const resourceDir = path.join(__dirname, "../../uploads/resource");
const logger = require("../../plugin/logger");
const db = require("../../utils/db");

// 下载频率限制
const downloadMap = new Map(); // key: ip+filename, value: 时间戳

// 获取资源列表
resource.get("/list", async (ctx) => {
  try {
    // 从数据库获取所有共享文件信息
    const jieguo = await db.query("SELECT * FROM files WHERE is_share = 1 AND is_deleted = 0");

    const files = [];

    for (const dbFile of jieguo) {
      // 提取文件路径
      const filePathUrl = dbFile.file_path;
      const match = filePathUrl.match(/^https?:\/\/(?:localhost|192\.168\.31\.222):3000\/(.+)$/);
      const relativePath = match ? match[1] : null;

      if (relativePath) {
        // 尝试在各个目录中查找文件
        const searchPaths = [
          path.join(__dirname, "../../uploads", relativePath),
          path.join(__dirname, "../../public", relativePath)
        ];

        let realPath = null;
        for (const searchPath of searchPaths) {
          if (fs.existsSync(searchPath)) {
            realPath = searchPath;
            break;
          }
        }

        if (realPath) {
          const stat = fs.statSync(realPath);

          // 统一路径格式为当前IP地址
          let normalizedPath = dbFile.file_path;
          if (normalizedPath.includes('localhost')) {
            normalizedPath = normalizedPath.replace('localhost', '**************');
          }

          files.push({
            filename: dbFile.file_name,
            file_name: dbFile.file_name,
            file_path: normalizedPath,
            file_size: dbFile.file_size,
            file_type: dbFile.file_type,
            size: stat.size,
            mtime: stat.mtime,
            created_at: dbFile.created_at,
            updated_at: dbFile.updated_at
          });
        } else {
          logger.warn(`资源列表：文件不存在 - ${dbFile.file_name} - 路径: ${filePathUrl}`);
        }
      }
    }

    return handleResponse(ctx, 200, { data: files });

  } catch (error) {
    logger.error("获取资源列表失败:", error);
    return handleResponse(ctx, 500, { error: "获取资源列表失败" });
  }
});

// 下载资源
resource.post("/", async (ctx) => {
  const { filename } = ctx.request.body;
  const ip = ctx.ip;
  const key = `${ip}_${filename}`;
  const now = Date.now();

  const jieguo = await db.query("SELECT * FROM files WHERE file_name = ?", [
    filename,
  ]);
  if (!jieguo.length) {
    return handleResponse(ctx, 404, { error: "数据库无此文件" });
  }

  // 提取 file_path 的相对路径部分
  const filePathUrl = jieguo[0].file_path;
  // 匹配多种URL格式后面的路径
  const match = filePathUrl.match(/^https?:\/\/(?:localhost|192\.168\.31\.222):3000\/(.+)$/);
  const relativePath = match ? match[1] : null;
  if (!relativePath) {
    logger.warn(`文件路径格式无效 - ${filename} - 路径: ${filePathUrl} - IP: ${ip}`);
    return handleResponse(ctx, 404, { error: "文件路径无效" });
  }

  // 依次尝试 uploads 和 public 目录
  const localPathUploads = path.join(__dirname, "../../uploads", relativePath);
  const localPathPublic = path.join(__dirname, "../../public", relativePath);

  let realPath = null;
  if (fs.existsSync(localPathUploads)) {
    realPath = localPathUploads;
  } else if (fs.existsSync(localPathPublic)) {
    realPath = localPathPublic;
  } else {
    logger.warn(`下载失败：文件不存在 - ${filename} - IP: ${ip}`);
    return handleResponse(ctx, 404, { error: "资源不存在" });
  }

  // 60秒内同IP同文件只允许一次下载
  if (downloadMap.has(key) && now - downloadMap.get(key) < 60 * 1000) {
    logger.warn(`重复下载拦截：${filename} - IP: ${ip}`);
    return handleResponse(ctx, 429, { error: "请勿频繁重复下载同一文件" });
  }
  downloadMap.set(key, now);

  // 获取文件信息
  const stats = fs.statSync(realPath);
  const fileSize = stats.size;

  // 设置正确的响应头
  ctx.set("Content-Type", "application/octet-stream");
  ctx.set("Content-Length", fileSize.toString());
  ctx.set(
    "Content-Disposition",
    `attachment; filename="${encodeURIComponent(filename)}"`
  );

  logger.info(`文件下载：${filename} (${fileSize} bytes) - IP: ${ip}`);

  // 直接使用文件流，不使用Throttle管道
  ctx.body = fs.createReadStream(realPath);
});

// 预览
resource.get("/prew/:filename", async (ctx) => {
  const { filename } = ctx.params;
  const ip = ctx.ip;

  // 从数据库获取文件信息
  const jieguo = await db.query("SELECT * FROM files WHERE file_name = ? AND is_deleted = 0", [filename]);
  if (jieguo.length === 0) {
    logger.warn(`预览失败：文件不存在于数据库 - ${filename} - IP: ${ip}`);
    return handleResponse(ctx, 404, { error: "文件不存在" });
  }

  // 提取 file_path 的相对路径部分
  const filePathUrl = jieguo[0].file_path;
  const match = filePathUrl.match(/^https?:\/\/(?:localhost|192\.168\.31\.222):3000\/(.+)$/);
  const relativePath = match ? match[1] : null;
  if (!relativePath) {
    logger.warn(`文件路径格式无效 - ${filename} - 路径: ${filePathUrl} - IP: ${ip}`);
    return handleResponse(ctx, 404, { error: "文件路径无效" });
  }

  // 构建文件路径 - 支持新的文件夹结构
  const searchPaths = [
    // 新的分类目录
    path.join(__dirname, "../../uploads/images", path.basename(relativePath)),
    path.join(__dirname, "../../uploads/documents", path.basename(relativePath)),
    path.join(__dirname, "../../uploads/videos", path.basename(relativePath)),
    path.join(__dirname, "../../uploads/archives", path.basename(relativePath)),
    path.join(__dirname, "../../public/media", path.basename(relativePath)),

    // 功能性目录
    path.join(__dirname, "../../uploads/avatars", path.basename(relativePath)),
    path.join(__dirname, "../../uploads/articles", path.basename(relativePath)),
    path.join(__dirname, "../../uploads/chat_images", path.basename(relativePath)),

    // 兼容旧版本
    path.join(__dirname, "../../uploads/resource", path.basename(relativePath)),
    path.join(__dirname, "../../uploads/files", path.basename(relativePath))
  ];

  let realPath = null;
  for (const searchPath of searchPaths) {
    if (fs.existsSync(searchPath)) {
      realPath = searchPath;
      break;
    }
  }

  if (!realPath) {
    logger.warn(`预览失败：文件不存在 - ${filename} - 搜索路径: ${searchPaths.length}个 - IP: ${ip}`);
    return handleResponse(ctx, 404, { error: "文件不存在" });
  }

  const ext = path.extname(filename).toLowerCase().replace(".", "");

  // 扩展支持的文本文件类型
  const textExtensions = [
    // 基础文本文件
    'txt', 'text', 'log', 'md', 'markdown',
    // 配置文件
    'json', 'xml', 'yaml', 'yml', 'ini', 'conf', 'config',
    // 代码文件
    'js', 'ts', 'jsx', 'tsx', 'vue', 'html', 'htm', 'css', 'scss', 'sass', 'less',
    'php', 'py', 'java', 'c', 'cpp', 'h', 'hpp', 'cs', 'go', 'rs', 'rb', 'pl',
    // 脚本文件
    'sh', 'bat', 'cmd', 'ps1',
    // 数据文件
    'csv', 'tsv', 'sql',
    // 其他文本格式
    'rtf', 'tex', 'latex'
  ];

  if (textExtensions.includes(ext)) {
    try {
      // 设置正确的Content-Type
      const mimeType = getMimeType(ext) || 'text/plain';
      ctx.set("Content-Type", mimeType + "; charset=utf-8");

      // 读取文件内容
      const content = fs.readFileSync(realPath, "utf-8");
      ctx.body = content;

      logger.info(`文件预览成功：${filename} (${ext}) - 大小: ${content.length} 字符 - IP: ${ip}`);
    } catch (error) {
      logger.error(`预览失败：读取文件错误 - ${filename} - ${error.message} - IP: ${ip}`);
      return handleResponse(ctx, 500, { error: "文件读取失败" });
    }
  } else {
    logger.warn(`预览失败：不支持的文本类型 - ${filename} (${ext}) - IP: ${ip}`);
    return handleResponse(ctx, 415, { error: `不支持预览 .${ext} 文件类型` });
  }
});

// 资源下载量统计
// file_name VARCHAR(255) NOT NULL,
// file_type VARCHAR(50) NOT NULL,
// user_id INT,
// ip VARCHAR(64)
resource.post("/downloadCount", async (ctx) => {
  const { file_name, user_id, file_type } = ctx.request.body;
  const ip = ctx.ip;
  const reslut = await db.query(
    "INSERT INTO download_logs (file_name, user_id, ip, file_type) VALUES (?,?,?,?)",
    [file_name, user_id, ip, file_type]
  );
  return handleResponse(ctx, 200, { data: reslut });
});

module.exports = resource;
