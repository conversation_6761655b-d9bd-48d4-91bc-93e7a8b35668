const db = require("../db");

// 检查文章是否存在
async function checkArticleExists(article_id) {
  const sql = 'SELECT id FROM articles WHERE id = ? AND is_deleted = 0';
  const result = await db.query(sql, [article_id]);
  return result.length > 0;
}

// 检查用户是否已点赞文章（登录用户）
async function checkUserLike(user_id, article_id) {
  const sql = 'SELECT * FROM article_likes WHERE user_id = ? AND article_id = ?';
  const result = await db.query(sql, [user_id, article_id]);
  return result.length > 0 ? result[0] : null;
}

// 检查匿名用户是否已点赞文章
async function checkAnonymousLike(ip_address, session_id, article_id) {
  const sql = 'SELECT * FROM article_likes WHERE ip_address = ? AND session_id = ? AND article_id = ?';
  const result = await db.query(sql, [ip_address, session_id, article_id]);
  return result.length > 0 ? result[0] : null;
}

// 删除点赞记录
async function removeLike(likeId) {
  const sql = 'DELETE FROM article_likes WHERE id = ?';
  const result = await db.query(sql, [likeId]);
  return result;
}

// 更新点赞类型
async function updateLikeType(likeId, like_type) {
  const sql = 'UPDATE article_likes SET like_type = ?, updated_at = NOW() WHERE id = ?';
  const result = await db.query(sql, [like_type, likeId]);
  return result;
}

// 添加点赞记录
async function addLike(user_id, article_id, ip_address, user_agent, session_id, like_type) {
  const sql = `
    INSERT INTO article_likes
    (user_id, article_id, ip_address, user_agent, session_id, like_type)
    VALUES (?, ?, ?, ?, ?, ?)
  `;
  const result = await db.query(sql, [user_id, article_id, ip_address, user_agent, session_id, like_type]);
  return result;
}

// 获取用户点赞状态（登录用户）
async function getUserLikeStatus(user_id, articleId) {
  const sql = 'SELECT like_type FROM article_likes WHERE user_id = ? AND article_id = ?';
  const result = await db.query(sql, [user_id, articleId]);
  return result.length > 0 ? result[0].like_type : null;
}

// 获取匿名用户点赞状态
async function getAnonymousLikeStatus(ip_address, session_id, articleId) {
  const sql = 'SELECT like_type FROM article_likes WHERE ip_address = ? AND session_id = ? AND article_id = ?';
  const result = await db.query(sql, [ip_address, session_id, articleId]);
  return result.length > 0 ? result[0].like_type : null;
}

// 获取文章的点赞用户列表
async function getArticleLikeUsers(articleId, like_type, limit, offset) {
  const sql = `
    SELECT al.like_type, al.created_at, u.id, u.username, u.avatar
    FROM article_likes al
    LEFT JOIN users u ON al.user_id = u.id
    WHERE al.article_id = ? AND al.like_type = ? AND al.user_id IS NOT NULL
    ORDER BY al.created_at DESC
    LIMIT ? OFFSET ?
  `;
  const result = await db.query(sql, [articleId, like_type, parseInt(limit), offset]);
  return result;
}

// 获取文章点赞用户总数
async function getArticleLikeUsersCount(articleId, like_type) {
  const sql = 'SELECT COUNT(*) as total FROM article_likes WHERE article_id = ? AND like_type = ? AND user_id IS NOT NULL';
  const result = await db.query(sql, [articleId, like_type]);
  return result[0]?.total || 0;
}

// 获取用户点赞历史
async function getUserLikeHistory(user_id, like_type, limit, offset) {
  let whereClause = 'al.user_id = ? AND a.is_deleted = 0';
  let params = [user_id];

  if (like_type && (like_type === 'like' || like_type === 'dislike')) {
    whereClause += ' AND al.like_type = ?';
    params.push(like_type);
  }

  const sql = `
    SELECT al.*, a.title, a.summary, a.cover_image, a.user_id as author_id,
           u.username as author_name, u.avatar as author_avatar
    FROM article_likes al
    LEFT JOIN articles a ON al.article_id = a.id
    LEFT JOIN users u ON a.user_id = u.id
    WHERE ${whereClause}
    ORDER BY al.created_at DESC
    LIMIT ? OFFSET ?
  `;
  const result = await db.query(sql, [...params, parseInt(limit), offset]);
  return result;
}

// 获取用户点赞历史总数
async function getUserLikeHistoryCount(user_id, like_type) {
  let whereClause = 'al.user_id = ? AND a.is_deleted = 0';
  let params = [user_id];

  if (like_type && (like_type === 'like' || like_type === 'dislike')) {
    whereClause += ' AND al.like_type = ?';
    params.push(like_type);
  }

  const sql = `
    SELECT COUNT(*) as total
    FROM article_likes al
    LEFT JOIN articles a ON al.article_id = a.id
    WHERE ${whereClause}
  `;
  const result = await db.query(sql, params);
  return result[0]?.total || 0;
}

// 获取文章点赞统计
async function getArticleLikeStats(articleId) {
  const sql = `
    SELECT
      COUNT(CASE WHEN like_type = 'like' THEN 1 END) as likes_count,
      COUNT(CASE WHEN like_type = 'dislike' THEN 1 END) as dislikes_count,
      COUNT(*) as total_interactions
    FROM article_likes
    WHERE article_id = ?
  `;
  const result = await db.query(sql, [articleId]);
  return result[0] || { likes_count: 0, dislikes_count: 0, total_interactions: 0 };
}

// 更新文章点赞数量
async function updateArticleLikeCount(articleId) {
  const sql = `
    UPDATE articles
    SET likes_count = (
      SELECT COUNT(*) FROM article_likes WHERE article_id = ? AND like_type = 'like'
    ),
    dislikes_count = (
      SELECT COUNT(*) FROM article_likes WHERE article_id = ? AND like_type = 'dislike'
    )
    WHERE id = ?
  `;
  const result = await db.query(sql, [articleId, articleId, articleId]);
  return result;
}

module.exports = {
  checkArticleExists,
  checkUserLike,
  checkAnonymousLike,
  removeLike,
  updateLikeType,
  addLike,
  getUserLikeStatus,
  getAnonymousLikeStatus,
  getArticleLikeUsers,
  getArticleLikeUsersCount,
  getUserLikeHistory,
  getUserLikeHistoryCount,
  getArticleLikeStats,
  updateArticleLikeCount,
};
