#!/usr/bin/env node

/**
 * 负载和压力测试工具
 * 用于测试系统在高负载下的性能表现
 */

const http = require('http');
const cluster = require('cluster');
const os = require('os');

class LoadTester {
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || 'http://localhost:3000';
    this.concurrency = options.concurrency || 100;
    this.duration = options.duration || 60; // 秒
    this.rampUp = options.rampUp || 10; // 秒
    this.endpoints = options.endpoints || ['/'];
    
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      responseTimes: [],
      errors: {},
      startTime: null,
      endTime: null
    };
  }

  // 发送单个请求
  async sendRequest(endpoint) {
    return new Promise((resolve) => {
      const startTime = Date.now();
      const url = new URL(endpoint, this.baseUrl);
      
      const req = http.request({
        hostname: url.hostname,
        port: url.port || 80,
        path: url.pathname,
        method: 'GET',
        timeout: 30000
      }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          const responseTime = Date.now() - startTime;
          resolve({
            success: res.statusCode < 400,
            statusCode: res.statusCode,
            responseTime,
            size: Buffer.byteLength(data)
          });
        });
      });

      req.on('error', (error) => {
        resolve({
          success: false,
          error: error.message,
          responseTime: Date.now() - startTime
        });
      });

      req.on('timeout', () => {
        req.destroy();
        resolve({
          success: false,
          error: 'timeout',
          responseTime: Date.now() - startTime
        });
      });

      req.end();
    });
  }

  // 工作进程逻辑
  async workerProcess() {
    const requestsPerWorker = Math.ceil(this.concurrency / os.cpus().length);
    const testDuration = this.duration * 1000;
    const rampUpDuration = this.rampUp * 1000;
    
    let requestCount = 0;
    let activeRequests = 0;
    const maxConcurrent = requestsPerWorker;
    
    const startTime = Date.now();
    
    const sendRequestBatch = async () => {
      while (Date.now() - startTime < testDuration && activeRequests < maxConcurrent) {
        activeRequests++;
        
        const endpoint = this.endpoints[Math.floor(Math.random() * this.endpoints.length)];
        
        this.sendRequest(endpoint).then(result => {
          activeRequests--;
          requestCount++;
          
          // 发送结果到主进程
          process.send({
            type: 'result',
            data: result
          });
          
          // 继续发送请求
          if (Date.now() - startTime < testDuration) {
            setImmediate(sendRequestBatch);
          }
        });
        
        // 渐进式增加负载
        const elapsed = Date.now() - startTime;
        if (elapsed < rampUpDuration) {
          const delay = Math.max(1, rampUpDuration / requestsPerWorker - elapsed / requestsPerWorker);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    };
    
    // 开始发送请求
    for (let i = 0; i < Math.min(10, maxConcurrent); i++) {
      setImmediate(sendRequestBatch);
    }
    
    // 等待测试完成
    setTimeout(() => {
      process.send({ type: 'complete' });
    }, testDuration + 5000);
  }

  // 主进程逻辑
  async runLoadTest() {
    if (cluster.isMaster) {
      console.log('🚀 开始负载测试...');
      console.log(`目标: ${this.baseUrl}`);
      console.log(`并发数: ${this.concurrency}`);
      console.log(`持续时间: ${this.duration}秒`);
      console.log(`渐进时间: ${this.rampUp}秒`);
      console.log(`测试端点: ${this.endpoints.join(', ')}`);
      console.log('');

      this.stats.startTime = Date.now();
      
      // 启动工作进程
      const numWorkers = Math.min(os.cpus().length, 4);
      const workers = [];
      
      for (let i = 0; i < numWorkers; i++) {
        const worker = cluster.fork();
        workers.push(worker);
        
        worker.on('message', (msg) => {
          if (msg.type === 'result') {
            this.processResult(msg.data);
          } else if (msg.type === 'complete') {
            worker.kill();
          }
        });
      }
      
      // 实时统计显示
      const statsInterval = setInterval(() => {
        this.displayRealTimeStats();
      }, 2000);
      
      // 等待所有工作进程完成
      await new Promise((resolve) => {
        let completedWorkers = 0;
        cluster.on('exit', () => {
          completedWorkers++;
          if (completedWorkers === numWorkers) {
            clearInterval(statsInterval);
            resolve();
          }
        });
      });
      
      this.stats.endTime = Date.now();
      this.generateReport();
      
    } else {
      // 工作进程
      await this.workerProcess();
    }
  }

  // 处理测试结果
  processResult(result) {
    this.stats.totalRequests++;
    
    if (result.success) {
      this.stats.successfulRequests++;
      this.stats.responseTimes.push(result.responseTime);
    } else {
      this.stats.failedRequests++;
      const errorKey = result.error || `HTTP_${result.statusCode}`;
      this.stats.errors[errorKey] = (this.stats.errors[errorKey] || 0) + 1;
    }
  }

  // 实时统计显示
  displayRealTimeStats() {
    const elapsed = (Date.now() - this.stats.startTime) / 1000;
    const rps = (this.stats.totalRequests / elapsed).toFixed(2);
    const successRate = ((this.stats.successfulRequests / this.stats.totalRequests) * 100).toFixed(2);
    
    process.stdout.write(`\r📊 已运行 ${elapsed.toFixed(0)}s | 请求数: ${this.stats.totalRequests} | RPS: ${rps} | 成功率: ${successRate}%`);
  }

  // 生成详细报告
  generateReport() {
    console.log('\n\n' + '='.repeat(60));
    console.log('📊 负载测试报告');
    console.log('='.repeat(60));
    
    const duration = (this.stats.endTime - this.stats.startTime) / 1000;
    const rps = this.stats.totalRequests / duration;
    const successRate = (this.stats.successfulRequests / this.stats.totalRequests) * 100;
    
    console.log(`测试时间: ${new Date().toLocaleString()}`);
    console.log(`测试持续时间: ${duration.toFixed(2)}秒`);
    console.log(`总请求数: ${this.stats.totalRequests}`);
    console.log(`成功请求数: ${this.stats.successfulRequests}`);
    console.log(`失败请求数: ${this.stats.failedRequests}`);
    console.log(`成功率: ${successRate.toFixed(2)}%`);
    console.log(`平均RPS: ${rps.toFixed(2)}`);
    
    if (this.stats.responseTimes.length > 0) {
      const sortedTimes = this.stats.responseTimes.sort((a, b) => a - b);
      const avg = sortedTimes.reduce((a, b) => a + b) / sortedTimes.length;
      const p50 = sortedTimes[Math.floor(sortedTimes.length * 0.5)];
      const p90 = sortedTimes[Math.floor(sortedTimes.length * 0.9)];
      const p95 = sortedTimes[Math.floor(sortedTimes.length * 0.95)];
      const p99 = sortedTimes[Math.floor(sortedTimes.length * 0.99)];
      const min = sortedTimes[0];
      const max = sortedTimes[sortedTimes.length - 1];
      
      console.log('\n响应时间统计 (ms):');
      console.log(`  平均值: ${avg.toFixed(2)}`);
      console.log(`  最小值: ${min}`);
      console.log(`  最大值: ${max}`);
      console.log(`  P50: ${p50}`);
      console.log(`  P90: ${p90}`);
      console.log(`  P95: ${p95}`);
      console.log(`  P99: ${p99}`);
    }
    
    if (Object.keys(this.stats.errors).length > 0) {
      console.log('\n错误统计:');
      Object.entries(this.stats.errors).forEach(([error, count]) => {
        console.log(`  ${error}: ${count}`);
      });
    }
    
    // 性能评估
    console.log('\n性能评估:');
    if (rps > 1000) {
      console.log('🟢 优秀 - 系统能够处理高负载');
    } else if (rps > 500) {
      console.log('🟡 良好 - 系统性能可接受');
    } else if (rps > 100) {
      console.log('🟠 一般 - 系统性能需要优化');
    } else {
      console.log('🔴 较差 - 系统性能存在严重问题');
    }
    
    if (successRate < 95) {
      console.log('⚠️  警告: 成功率低于95%，系统可能存在稳定性问题');
    }
    
    console.log('='.repeat(60));
  }
}

// 命令行使用
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};
  
  // 解析命令行参数
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace('--', '');
    const value = args[i + 1];
    
    switch (key) {
      case 'url':
        options.baseUrl = value;
        break;
      case 'concurrency':
      case 'c':
        options.concurrency = parseInt(value);
        break;
      case 'duration':
      case 'd':
        options.duration = parseInt(value);
        break;
      case 'rampup':
        options.rampUp = parseInt(value);
        break;
      case 'endpoints':
        options.endpoints = value.split(',');
        break;
    }
  }
  
  const tester = new LoadTester(options);
  tester.runLoadTest().catch(console.error);
}

module.exports = LoadTester;
