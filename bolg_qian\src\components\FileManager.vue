<template>
  <div class="file-manager">
    <el-card class="manager-card">
      <!-- 头部工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <h3 class="title">
            <el-icon><Folder /></el-icon>
            文件管理器
          </h3>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>{{ currentCategory }}</el-breadcrumb-item>
            <el-breadcrumb-item 
              v-for="(item, index) in breadcrumbPath" 
              :key="index"
              @click="navigateToPath(index)"
              class="breadcrumb-link"
            >
              {{ item }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="toolbar-right">
          <el-select 
            v-model="currentCategory" 
            @change="changeCategory"
            style="width: 150px; margin-right: 12px;"
          >
            <el-option label="图片" value="images" />
            <el-option label="文档" value="documents" />
            <el-option label="视频" value="videos" />
            <el-option label="压缩包" value="archives" />
            <el-option label="媒体" value="media" />
            <el-option label="头像" value="avatars" />
            <el-option label="文章" value="articles" />
            <el-option label="聊天图片" value="chat_images" />
          </el-select>
          
          <el-button type="primary" @click="showCreateFolderDialog">
            <el-icon><FolderAdd /></el-icon>
            新建文件夹
          </el-button>
          
          <el-button @click="refreshStructure">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="content-area">
        <!-- 左侧文件夹树 -->
        <div class="folder-tree">
          <el-tree
            :data="treeData"
            :props="treeProps"
            node-key="path"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            :default-expanded-keys="expandedKeys"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <el-icon v-if="data.type === 'folder'">
                  <Folder />
                </el-icon>
                <el-icon v-else>
                  <Document />
                </el-icon>
                <span class="node-label">{{ data.name }}</span>
                <div class="node-actions" v-if="data.type === 'folder'">
                  <el-button 
                    size="small" 
                    text 
                    @click.stop="showCreateSubFolderDialog(data)"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-button>
                </div>
              </div>
            </template>
          </el-tree>
        </div>

        <!-- 右侧文件列表 -->
        <div class="file-list">
          <div class="list-header">
            <span>{{ currentFolderName || '根目录' }}</span>
            <div class="view-controls">
              <el-radio-group v-model="viewMode" size="small">
                <el-radio-button label="list">列表</el-radio-button>
                <el-radio-button label="grid">网格</el-radio-button>
              </el-radio-group>
            </div>
          </div>

          <!-- 列表视图 -->
          <div v-if="viewMode === 'list'" class="list-view">
            <el-table 
              :data="currentFiles" 
              style="width: 100%"
              @row-contextmenu="showContextMenu"
            >
              <el-table-column width="50">
                <template #default="{ row }">
                  <el-icon class="file-icon">
                    <Folder v-if="row.type === 'folder'" />
                    <Document v-else />
                  </el-icon>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="名称" min-width="200">
                <template #default="{ row }">
                  <span 
                    @dblclick="handleDoubleClick(row)"
                    class="file-name"
                  >
                    {{ row.name }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="size" label="大小" width="120">
                <template #default="{ row }">
                  {{ row.type === 'file' ? formatFileSize(row.size) : '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="mtime" label="修改时间" width="180">
                <template #default="{ row }">
                  {{ row.type === 'file' ? formatTime(row.mtime) : '-' }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-button size="small" @click="showMoveDialog(row)">
                    移动
                  </el-button>
                  <el-button size="small" @click="showRenameDialog(row)">
                    重命名
                  </el-button>
                  <el-button 
                    size="small" 
                    type="danger" 
                    @click="deleteItem(row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 网格视图 -->
          <div v-else class="grid-view">
            <div 
              v-for="item in currentFiles" 
              :key="item.path"
              class="grid-item"
              @dblclick="handleDoubleClick(item)"
              @contextmenu="showContextMenu($event, item)"
            >
              <div class="item-icon">
                <el-icon size="48">
                  <Folder v-if="item.type === 'folder'" />
                  <Document v-else />
                </el-icon>
              </div>
              <div class="item-name">{{ item.name }}</div>
              <div class="item-info" v-if="item.type === 'file'">
                {{ formatFileSize(item.size) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 创建文件夹对话框 -->
    <el-dialog 
      v-model="createFolderVisible" 
      title="创建文件夹" 
      width="400px"
    >
      <el-form :model="createFolderForm" label-width="80px">
        <el-form-item label="文件夹名">
          <el-input 
            v-model="createFolderForm.name" 
            placeholder="请输入文件夹名称"
            @keyup.enter="createFolder"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="createFolderVisible = false">取消</el-button>
        <el-button type="primary" @click="createFolder">确定</el-button>
      </template>
    </el-dialog>

    <!-- 移动文件对话框 -->
    <el-dialog 
      v-model="moveFileVisible" 
      title="移动文件" 
      width="500px"
    >
      <el-form :model="moveFileForm" label-width="100px">
        <el-form-item label="目标分类">
          <el-select v-model="moveFileForm.toCategory" style="width: 100%">
            <el-option label="图片" value="images" />
            <el-option label="文档" value="documents" />
            <el-option label="视频" value="videos" />
            <el-option label="压缩包" value="archives" />
            <el-option label="媒体" value="media" />
            <el-option label="头像" value="avatars" />
            <el-option label="文章" value="articles" />
            <el-option label="聊天图片" value="chat_images" />
          </el-select>
        </el-form-item>
        <el-form-item label="目标路径">
          <el-input 
            v-model="moveFileForm.toPath" 
            placeholder="留空表示根目录，如：subfolder/path"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="moveFileVisible = false">取消</el-button>
        <el-button type="primary" @click="moveFile">确定</el-button>
      </template>
    </el-dialog>

    <!-- 重命名对话框 -->
    <el-dialog 
      v-model="renameVisible" 
      title="重命名" 
      width="400px"
    >
      <el-form :model="renameForm" label-width="80px">
        <el-form-item label="新名称">
          <el-input 
            v-model="renameForm.newName" 
            placeholder="请输入新名称"
            @keyup.enter="rename"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="renameVisible = false">取消</el-button>
        <el-button type="primary" @click="rename">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Folder,
  FolderAdd,
  Document,
  Refresh,
  Plus
} from '@element-plus/icons-vue';
import { 
  getFileStructureApi,
  createFolderApi,
  moveFileApi,
  renameItemApi,
  deleteItemApi
} from '../utils/fileManagerApi';

// 定义事件
const emit = defineEmits(['stats-updated']);

// 响应式数据
const currentCategory = ref('images');
const currentPath = ref('');
const fileStructure = ref<any>({});
const viewMode = ref('list');
const expandedKeys = ref<string[]>([]);

// 对话框状态
const createFolderVisible = ref(false);
const moveFileVisible = ref(false);
const renameVisible = ref(false);

// 表单数据
const createFolderForm = ref({
  name: '',
  parentPath: ''
});

const moveFileForm = ref({
  fileName: '',
  fromCategory: '',
  fromPath: '',
  toCategory: '',
  toPath: ''
});

const renameForm = ref({
  oldPath: '',
  newName: '',
  type: 'file'
});

// 计算属性
const breadcrumbPath = computed(() => {
  return currentPath.value ? currentPath.value.split('/').filter(Boolean) : [];
});

const currentFolderName = computed(() => {
  const path = breadcrumbPath.value;
  return path.length > 0 ? path[path.length - 1] : '';
});

const treeData = computed(() => {
  const structure = fileStructure.value[currentCategory.value];
  console.log('treeData计算:', {
    currentCategory: currentCategory.value,
    fileStructure: fileStructure.value,
    structure: structure,
    hasStructure: !!structure
  });
  return structure ? [structure] : [];
});

const treeProps = {
  children: 'children',
  label: 'name'
};

const currentFiles = computed(() => {
  let current = fileStructure.value[currentCategory.value];
  if (!current) return [];

  // 根据当前路径导航到对应文件夹
  const pathParts = breadcrumbPath.value;
  for (const part of pathParts) {
    const found = current.children?.find((item: any) => item.name === part && item.type === 'folder');
    if (found) {
      current = found;
    } else {
      return [];
    }
  }

  return current.children || [];
});

// 方法
const refreshStructure = async () => {
  try {
    const response = await getFileStructureApi(currentCategory.value);
    console.log('API响应:', response); // 调试日志

    // 后端响应格式: { code: 200, message: "成功", data: {...} }
    if (response && response.data) {
      fileStructure.value = response.data;
    } else {
      fileStructure.value = {};
    }

    // 触发统计更新
    emit('stats-updated');
  } catch (error) {
    console.error('获取文件结构失败:', error);
    ElMessage.error('获取文件结构失败');
  }
};

const changeCategory = async (category: string) => {
  currentPath.value = '';
  await refreshStructure();
};

const handleNodeClick = (data: any) => {
  if (data.type === 'folder') {
    currentPath.value = data.path;
  }
};

const handleDoubleClick = (item: any) => {
  if (item.type === 'folder') {
    currentPath.value = item.path;
  }
};

const navigateToPath = (index: number) => {
  const pathParts = breadcrumbPath.value.slice(0, index + 1);
  currentPath.value = pathParts.join('/');
};

const showCreateFolderDialog = () => {
  createFolderForm.value = {
    name: '',
    parentPath: currentPath.value
  };
  createFolderVisible.value = true;
};

const showCreateSubFolderDialog = (folderData: any) => {
  createFolderForm.value = {
    name: '',
    parentPath: folderData.path
  };
  createFolderVisible.value = true;
};

const createFolder = async () => {
  if (!createFolderForm.value.name.trim()) {
    ElMessage.warning('请输入文件夹名称');
    return;
  }

  try {
    await createFolderApi({
      category: currentCategory.value,
      folderPath: createFolderForm.value.parentPath,
      folderName: createFolderForm.value.name.trim()
    });

    ElMessage.success('文件夹创建成功');
    createFolderVisible.value = false;
    await refreshStructure();
  } catch (error: any) {
    ElMessage.error(error.response?.data?.error || '创建文件夹失败');
  }
};

const showMoveDialog = (item: any) => {
  moveFileForm.value = {
    fileName: item.name,
    fromCategory: currentCategory.value,
    fromPath: currentPath.value,
    toCategory: currentCategory.value,
    toPath: ''
  };
  moveFileVisible.value = true;
};

const moveFile = async () => {
  try {
    await moveFileApi(moveFileForm.value);
    ElMessage.success('文件移动成功');
    moveFileVisible.value = false;
    await refreshStructure();
  } catch (error: any) {
    ElMessage.error(error.response?.data?.error || '文件移动失败');
  }
};

const showRenameDialog = (item: any) => {
  renameForm.value = {
    oldPath: item.path,
    newName: item.name,
    type: item.type
  };
  renameVisible.value = true;
};

const rename = async () => {
  if (!renameForm.value.newName.trim()) {
    ElMessage.warning('请输入新名称');
    return;
  }

  try {
    await renameItemApi({
      category: currentCategory.value,
      oldPath: renameForm.value.oldPath,
      newName: renameForm.value.newName.trim(),
      type: renameForm.value.type
    });

    ElMessage.success('重命名成功');
    renameVisible.value = false;
    await refreshStructure();
  } catch (error: any) {
    ElMessage.error(error.response?.data?.error || '重命名失败');
  }
};

const deleteItem = async (item: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 "${item.name}" 吗？此操作不可恢复！`,
      '确认删除',
      { type: 'warning' }
    );

    await deleteItemApi({
      category: currentCategory.value,
      targetPath: item.path,
      type: item.type
    });

    ElMessage.success('删除成功');
    await refreshStructure();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.response?.data?.error || '删除失败');
    }
  }
};

const showContextMenu = (event: MouseEvent, item?: any) => {
  event.preventDefault();
  // 这里可以实现右键菜单功能
};

// 工具函数
const formatFileSize = (size: number): string => {
  if (!size) return '0 B';
  const units = ['B', 'KB', 'MB', 'GB'];
  let index = 0;
  let fileSize = size;
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  return `${fileSize.toFixed(1)} ${units[index]}`;
};

const formatTime = (time: string | Date): string => {
  const date = new Date(time);
  return date.toLocaleString();
};

// 生命周期
onMounted(() => {
  refreshStructure();
});
</script>

<style scoped>
.file-manager {
  height: 100vh;
  padding: 20px;
}

.manager-card {
  height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.title {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.breadcrumb-link {
  cursor: pointer;
  color: #409eff;
}

.breadcrumb-link:hover {
  text-decoration: underline;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.content-area {
  display: flex;
  flex: 1;
  gap: 16px;
  overflow: hidden;
}

.folder-tree {
  width: 300px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 100%;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.node-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.file-list {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.list-view {
  flex: 1;
  overflow-y: auto;
}

.file-icon {
  color: #409eff;
}

.file-name {
  cursor: pointer;
  color: #303133;
}

.file-name:hover {
  color: #409eff;
}

.grid-view {
  flex: 1;
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  overflow-y: auto;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.grid-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.item-icon {
  margin-bottom: 8px;
  color: #409eff;
}

.item-name {
  font-size: 12px;
  text-align: center;
  word-break: break-all;
  margin-bottom: 4px;
}

.item-info {
  font-size: 11px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-manager {
    padding: 12px;
  }

  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-left {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .content-area {
    flex-direction: column;
  }

  .folder-tree {
    width: 100%;
    height: 200px;
  }

  .grid-view {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 12px;
  }
}
</style>
