<template>
  <div>
    <!-- 返回 -->
    <div style="display: flex;">
      <el-button @click="$router.push('/index')">返回首页</el-button>
      <el-button type="primary" style="margin-right: 10px;" @click="$router.push('/index/edit')">
        写文章
      </el-button>
    </div>
    <!-- 搜索框 -->
    <div class="search-bar">
      <div>
        <el-input style="max-width: 600px;" v-model="searchQuery" placeholder="搜索文章分类、标题、摘要" clearable
          @input="filterArticles" prefix-icon="el-icon-search" />
      </div>
    </div>

    <!-- 文章列表 -->
    <div class="article-list">
      <el-row :gutter="20" type="flex" direction="vertical">
        <el-col :span="24" v-for="article in filteredArticles" :key="article.id">
          <el-card class="box-card">
            <template #header>
              <div class="card-header">
                <span class="article-title">{{ article.title }}</span>

              </div>

            </template>
            <!-- 文章分类 -->

            <div class="text item">
              <p class="summary">
                {{ article.summary }}
              </p>
            </div>
            <div class="category">
              <el-tag :type="getCategoryType(article.category)" effect="plain">{{ article.category }}</el-tag>
              <el-tag v-for="tag in (article.tags || '').split(' ')" :key="tag" class="tag" effect="plain">
                {{ tag }}
              </el-tag>
              <span class="date" style="padding-left: 10px;">{{ formatDate(article.updated_at) }}</span>
            </div>
            <div>
              <el-link :href="'/index/details/' + article.id" type="primary">阅读全文</el-link>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { GetSharedArticlesApi } from "../utils/api"

const searchQuery = ref('') // 搜索内容
const articles = ref([])    // 共享文章列表
const filteredArticles = ref([]) // 过滤后的文章

// 获取共享文章列表
const fetchSharedArticles = async () => {
  const res = await GetSharedArticlesApi();
  if (res?.data) {
    // 按更新时间降序排列，最新在前
    const sorted = [...res.data].sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at));
    articles.value = sorted;
    filteredArticles.value = sorted;
  }
  console.log("获取共享文章列表", articles.value);
};

// 过滤文章
const filterArticles = () => {
  if (!searchQuery.value) {
    filteredArticles.value = articles.value
  } else {
    filteredArticles.value = articles.value.filter(article =>
      (article.title || '').toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      (article.summary || '').toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      (article.category || '').toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }
}

// 分类标签颜色
const getCategoryType = (category) => {
  switch (category) {
    case '技术': return 'success'
    case '框架': return 'info'
    case '性能优化': return 'warning'
    default: return 'primary'
  }
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return "";
  const d = new Date(dateStr);
  const pad = n => n.toString().padStart(2, "0");
  return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())}`;
};






onMounted(() => {
  fetchSharedArticles();
  filterArticles();
});
</script>

<style scoped>
.search-bar {
  margin: 24px 0 16px 0;
  display: flex;
  justify-content: center;
}

.article-list {
  padding: 0 10vw;
}

.el-row {
  display: flex;
  flex-direction: column;
}

.el-col {
  flex: 1;
  margin-bottom: 24px;
}

.el-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.2s;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.el-card:hover {
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.article-title {
  font-size: 1.15rem;
  font-weight: 600;
  color: #333;
}

.date {
  color: #999;
  font-size: 0.95rem;
}

.category {
  margin: 10px 0 8px 0;
}

.tag {
  margin-left: 8px;
}

.text {
  flex-grow: 1;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.summary {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
  font-size: 1rem;
  margin: 0;
  flex: 1;
}
</style>