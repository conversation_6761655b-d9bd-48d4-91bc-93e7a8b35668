import { createRouter, createWebHistory, type RouteRecordRaw } from "vue-router";

const routes: RouteRecordRaw[] = [
  // 登录页面
  {
    path: "/login",
    name: "Login",
    component: () => import("../views/user/Login.vue"),
  },

  // 注册页面
  {
    path: "/register",
    name: "Register",
    component: () => import("../views/user/Register.vue"),
  },
  // 根路径重定向到首页
  {
    path: "/",
    redirect: "/index/home",
  },
  // 404 页面
  {
    path: "/:catchAll(.*)",
    name: "NotFound",
    component: () => import("../views/NotFound.vue"),
  },
  // 首页及其子页面
  {
    path: "/index", // 父路径
    name: "Index",
    // 重定向到子页面
    redirect: "/index/home",
    component: () => import("../views/Index.vue"),
    children: [
      {
        path: "home",
        name: "Home",
        component: () => import("../views/Dashboard/Home.vue"),
      },

      {
        path: "media", // 子路径，不带斜杠
        name: "Media",
        component: () => import("../views/File/Media.vue"),
      },

      {
        path: "details/:id",
        name: "Details",
        component: () => import("../views/Article/Details.vue"),
      },
      {
        path: "edit",
        name: "Edit",
        component: () => import("../views/Article/Edit.vue"),
      },
      {
        path: "smart-edit",
        name: "SmartEdit",
        component: () => import("../views/Article/SmartEdit.vue"),
      },
      {
        path: "english-translator",
        name: "EnglishTranslator",
        component: () => import("../views/Article/EnglishTranslator.vue"),
      },

      {
        path: "model",
        name: "Model",
        component: () => import("../views/Ai/Model.vue"),
      },
      {
        // 聊天室 chat
        path: "chat",
        name: "Chat",
        component: () => import("../views/Chat/Chat.vue"),
      },
      {
        path: "resource",
        name: "Resource",
        component: () => import("../views/File/Resource.vue"),
      },
      {
        // image
        path: "images",
        name: "Images",
        component: () => import("../views/File/Images.vue"),
      },
      {
        //UploadsMobile
        path: "uploadsmobile",
        name: "UploadsMobile",
        component: () => import("../views/File/UploadsMobile.vue"),
      },

      {
        path: "allarticles",
        name: "AllArticles",
        component: () => import("../views/Article/AllArticles.vue"),
      },

      // rtc
      {
        path: "videovcall",
        name: "VideoCall",
        component: () => import("../views/Chat/VideoCall.vue"),
      },

      // MUSIC
      {
        path: "music",
        name: "Music",
        component: () => import("../views/Media/Music.vue"),
      },

      {
        path: "dashboard", // 子路径，不带斜杠
        name: "Dashboard",
        redirect: "dashboard/board", // 使用正确的相对路径
        component: () => import("../views/Dashboard/Dashboard.vue"),
        children: [
          {
            path: "board",
            name: "Board",
            component: () => import("../views/Dashboard/Board.vue"),
          },
          {
            // intro
            path: "intro",
            name: "Intro",
            component: () => import("../views/user/Intro.vue"),
          },
          // shall
          {
            path: "share",
            name: "Share",
            component: () => import("../components/Share.vue"),
          },
          {
            path: "files", // 子路径，不带斜杠
            name: "Files",
            component: () => import("../views/File/Files.vue"),
          },
          {
            path: "uploads",
            name: "Uploads",
            component: () => import("../views/File/Uploads.vue"),
          },
          {
            path: "fileManager",
            name: "FileManager",
            component: () => import("../views/Dashboard/FileManager.vue"),
          },

          {
            path: "articleManage",
            name: "ArticleManage",
            component: () => import("../views/Creative/ArticleManage.vue"),
          },
          {
            path: "bug",
            name: "Bug",
            component: () => import("../views/Creative/Bug.vue"),
          },
          {
            path: "logs", // 子路径，不带斜杠
            name: "Logs",
            component: () => import("../views/Logs/Logs.vue"),
          },
          // create
          {
            path: "creative",
            name: "Creative",
            component: () => import("../views/Creative/creative.vue"),
          },
          {
            path: "jiami",
            name: "Jiami",
            component: () => import("../components/Jiami.vue"),
          },
          {
            path: "shipinjiami",
            name: "Shipinjiami",
            component: () => import("../components/Shipinjiami.vue"),
          },
          {
            path: "daochusql",
            name: "Daochusql",
            component: () => import("../components/Daochusql.vue"),
          },
          {
            path: "passwordManagement",
            name: "PasswordManagement",
            component: () => import("../views/Dashboard/PasswordManagement.vue"),
          },
          {
            path: "userLevelManage",
            name: "UserLevelManage",
            component: () => import("../views/Dashboard/UserLevelManage.vue"),
          },
          {
            path: "systemMonitor",
            name: "SystemMonitor",
            component: () => import("../views/Dashboard/SystemMonitor.vue"),
          },
          {
            path: "friends",
            name: "Friends",
            component: () => import("../views/user/Friends.vue"),
          },
          {
            path: "viewhistory",
            name: "ViewHistory",
            component: () => import("../views/user/ViewHistory.vue"),
          },
          {
            path: "likehistory",
            name: "LikeHistory",
            component: () => import("../views/Dashboard/ArticleLikeHistory.vue"),
          },
          {
            path: "favoritehistory",
            name: "FavoriteHistory",
            component: () => import("../views/Dashboard/ArticleFavoriteHistory.vue"),
          },
          {
            path: "test-api",
            name: "TestAPI",
            component: () => import("../views/Dashboard/TestAPI.vue"),
          },
          {
            path: "effects",
            name: "EffectManagement",
            component: () => import("../views/Dashboard/EffectManagement.vue"),
          },
          {
            path: "videoManagement",
            name: "VideoManagement",
            component: () => import("../views/Admin/VideoManagement.vue"),
          },
          {
            path: "fileManagerDebug",
            name: "FileManagerDebug",
            component: () => import("../views/Dashboard/FileManagerDebug.vue"),
          },
          {
            path: "downloadTest",
            name: "DownloadTest",
            component: () => import("../views/Dashboard/DownloadTest.vue"),
          },
        ],
      },
    ],
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

// 添加路由守卫

router.beforeEach((to, from, next) => {
  console.log('路由守卫 - 从:', from.path, '到:', to.path);
  
  // 不需要登录的页面：登录、注册、照片墙、文章列表、文章详情、音乐播放器
  const whiteList = [
    "/login", 
    "/register", 
    "/index/images",
    "/index/allarticles",  // 文章列表
    "/index/details",      // 文章详情（动态路由）
    "/index/music"         // 音乐播放器
  ];
  const token = localStorage.getItem("token");

  // 检查是否是文章详情页（动态路由）
  if (to.path.startsWith("/index/details/")) {
    console.log('文章详情页，允许访问');
    next();
    return;
  }

  if (whiteList.includes(to.path)) {
    console.log('白名单页面，允许访问');
    next();
  } else {
    if (token) {
      console.log('已登录，允许访问');
      next();
    } else {
      console.log('未登录，重定向到登录页');
      next("/login");
    }
  }
});

export default router;
