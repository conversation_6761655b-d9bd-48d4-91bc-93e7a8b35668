// API性能监控中间件
const logger = require('../plugin/logger');

// 性能数据存储
const performanceData = {
  requests: new Map(),      // 请求统计
  endpoints: new Map(),     // 端点性能
  errors: new Map(),        // 错误统计
  slowQueries: [],          // 慢查询记录
  systemMetrics: {          // 系统指标
    startTime: Date.now(),
    totalRequests: 0,
    totalErrors: 0,
    averageResponseTime: 0
  }
};

// 性能配置
const PERFORMANCE_CONFIG = {
  // 慢请求阈值 (毫秒)
  slowRequestThreshold: 1000,
  
  // 保留的慢查询记录数（减少）
  maxSlowQueries: 50,
  
  // 统计窗口时间 (毫秒)
  statsWindow: 3 * 60 * 1000, // 3分钟（减少）
  
  // 内存使用监控间隔
  memoryCheckInterval: 30 * 1000, // 30秒
  
  // 警告阈值
  thresholds: {
    responseTime: 2000,     // 响应时间警告阈值 (毫秒)
    errorRate: 0.05,        // 错误率警告阈值 (5%)
    memoryUsage: 0.8        // 内存使用警告阈值 (80%)
  }
};

// 获取内存使用情况
function getMemoryUsage() {
  const usage = process.memoryUsage();
  return {
    rss: Math.round(usage.rss / 1024 / 1024), // MB
    heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
    heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
    external: Math.round(usage.external / 1024 / 1024),
    arrayBuffers: Math.round(usage.arrayBuffers / 1024 / 1024)
  };
}

// 获取CPU使用情况
function getCPUUsage() {
  const usage = process.cpuUsage();
  return {
    user: Math.round(usage.user / 1000), // 毫秒
    system: Math.round(usage.system / 1000)
  };
}

// 记录慢查询
function recordSlowQuery(query, duration, endpoint) {
  const slowQuery = {
    query: query.substring(0, 200) + (query.length > 200 ? '...' : ''),
    duration,
    endpoint,
    timestamp: new Date().toISOString()
  };
  
  performanceData.slowQueries.unshift(slowQuery);
  
  // 保持记录数量限制
  if (performanceData.slowQueries.length > PERFORMANCE_CONFIG.maxSlowQueries) {
    performanceData.slowQueries = performanceData.slowQueries.slice(0, PERFORMANCE_CONFIG.maxSlowQueries);
  }
  
  logger.warn('慢查询检测', slowQuery);
}

// 更新端点统计
function updateEndpointStats(endpoint, duration, status, error = null) {
  if (!performanceData.endpoints.has(endpoint)) {
    performanceData.endpoints.set(endpoint, {
      totalRequests: 0,
      totalDuration: 0,
      averageResponseTime: 0,
      minResponseTime: Infinity,
      maxResponseTime: 0,
      errorCount: 0,
      statusCodes: new Map(),
      lastAccessed: null
    });
  }
  
  const stats = performanceData.endpoints.get(endpoint);
  
  stats.totalRequests++;
  stats.totalDuration += duration;
  stats.averageResponseTime = Math.round(stats.totalDuration / stats.totalRequests);
  stats.minResponseTime = Math.min(stats.minResponseTime, duration);
  stats.maxResponseTime = Math.max(stats.maxResponseTime, duration);
  stats.lastAccessed = new Date().toISOString();
  
  // 统计状态码
  const statusKey = Math.floor(status / 100) * 100; // 200, 300, 400, 500
  stats.statusCodes.set(statusKey, (stats.statusCodes.get(statusKey) || 0) + 1);
  
  // 统计错误
  if (error || status >= 400) {
    stats.errorCount++;
  }
}

// 清理过期数据
function cleanupOldData() {
  const cutoff = Date.now() - PERFORMANCE_CONFIG.statsWindow;
  
  // 清理过期的请求记录
  for (const [key, data] of performanceData.requests.entries()) {
    if (data.timestamp < cutoff) {
      performanceData.requests.delete(key);
    }
  }
  
  // 清理过期的错误记录
  for (const [key, data] of performanceData.errors.entries()) {
    if (data.timestamp < cutoff) {
      performanceData.errors.delete(key);
    }
  }
}

// 定期清理数据
setInterval(cleanupOldData, 60 * 1000); // 每分钟清理一次

// 强制垃圾回收（如果可用）
setInterval(() => {
  if (global.gc) {
    const memBefore = process.memoryUsage().heapUsed;
    global.gc();
    const memAfter = process.memoryUsage().heapUsed;
    const freed = memBefore - memAfter;

    if (freed > 1024 * 1024) { // 释放超过1MB时记录
      logger.debug('手动垃圾回收', {
        freedMB: Math.round(freed / 1024 / 1024),
        heapUsedMB: Math.round(memAfter / 1024 / 1024)
      });
    }
  }
}, 2 * 60 * 1000); // 每2分钟执行一次

// 内存监控
setInterval(() => {
  const memory = getMemoryUsage();
  const heapUsageRatio = memory.heapUsed / memory.heapTotal;
  
  if (heapUsageRatio > PERFORMANCE_CONFIG.thresholds.memoryUsage) {
    logger.warn('内存使用过高', {
      heapUsed: memory.heapUsed,
      heapTotal: memory.heapTotal,
      usageRatio: Math.round(heapUsageRatio * 100) + '%'
    });
  }
}, PERFORMANCE_CONFIG.memoryCheckInterval);

// 性能监控中间件
function performanceMiddleware() {
  return async (ctx, next) => {
    const startTime = Date.now();
    const endpoint = `${ctx.method} ${ctx.path}`;
    const requestId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // 记录请求开始
    performanceData.requests.set(requestId, {
      endpoint,
      startTime,
      ip: ctx.ip,
      userAgent: ctx.get('User-Agent'),
      userId: ctx.state.user?.id
    });
    
    let error = null;
    
    try {
      await next();
    } catch (err) {
      error = err;
      
      // 记录错误
      const errorKey = `${endpoint}-${err.status || 500}`;
      if (!performanceData.errors.has(errorKey)) {
        performanceData.errors.set(errorKey, {
          endpoint,
          errorMessage: err.message,
          count: 0,
          lastOccurred: null
        });
      }
      
      const errorStats = performanceData.errors.get(errorKey);
      errorStats.count++;
      errorStats.lastOccurred = new Date().toISOString();
      
      throw err;
    } finally {
      const duration = Date.now() - startTime;
      const status = ctx.status || 500;
      
      // 更新系统指标
      performanceData.systemMetrics.totalRequests++;
      if (error || status >= 400) {
        performanceData.systemMetrics.totalErrors++;
      }
      
      // 计算平均响应时间
      const totalDuration = performanceData.systemMetrics.averageResponseTime * 
                           (performanceData.systemMetrics.totalRequests - 1) + duration;
      performanceData.systemMetrics.averageResponseTime = 
        Math.round(totalDuration / performanceData.systemMetrics.totalRequests);
      
      // 更新端点统计
      updateEndpointStats(endpoint, duration, status, error);
      
      // 检查慢请求
      if (duration > PERFORMANCE_CONFIG.slowRequestThreshold) {
        logger.warn('慢请求检测', {
          endpoint,
          duration,
          status,
          ip: ctx.ip,
          userId: ctx.state.user?.id
        });
      }
      
      // 设置性能响应头
      ctx.set({
        'X-Response-Time': `${duration}ms`,
        'X-Request-ID': requestId
      });
      
      // 清理请求记录
      performanceData.requests.delete(requestId);
      
      // 记录详细性能日志
      if (duration > PERFORMANCE_CONFIG.thresholds.responseTime) {
        logger.warn('响应时间超过阈值', {
          endpoint,
          duration,
          threshold: PERFORMANCE_CONFIG.thresholds.responseTime,
          status,
          error: error?.message
        });
      }
    }
  };
}

// 获取性能统计
function getPerformanceStats() {
  const now = Date.now();
  const uptime = now - performanceData.systemMetrics.startTime;
  
  // 计算错误率
  const errorRate = performanceData.systemMetrics.totalRequests > 0 
    ? performanceData.systemMetrics.totalErrors / performanceData.systemMetrics.totalRequests 
    : 0;
  
  // 获取最慢的端点
  const slowestEndpoints = Array.from(performanceData.endpoints.entries())
    .sort((a, b) => b[1].averageResponseTime - a[1].averageResponseTime)
    .slice(0, 10)
    .map(([endpoint, stats]) => ({
      endpoint,
      averageResponseTime: stats.averageResponseTime,
      maxResponseTime: stats.maxResponseTime,
      totalRequests: stats.totalRequests,
      errorCount: stats.errorCount
    }));
  
  // 获取最频繁的端点
  const mostFrequentEndpoints = Array.from(performanceData.endpoints.entries())
    .sort((a, b) => b[1].totalRequests - a[1].totalRequests)
    .slice(0, 10)
    .map(([endpoint, stats]) => ({
      endpoint,
      totalRequests: stats.totalRequests,
      averageResponseTime: stats.averageResponseTime,
      errorCount: stats.errorCount
    }));
  
  return {
    system: {
      uptime: Math.round(uptime / 1000), // 秒
      totalRequests: performanceData.systemMetrics.totalRequests,
      totalErrors: performanceData.systemMetrics.totalErrors,
      errorRate: Math.round(errorRate * 10000) / 100, // 百分比，保留2位小数
      averageResponseTime: performanceData.systemMetrics.averageResponseTime,
      memory: getMemoryUsage(),
      cpu: getCPUUsage()
    },
    endpoints: {
      total: performanceData.endpoints.size,
      slowest: slowestEndpoints,
      mostFrequent: mostFrequentEndpoints
    },
    errors: {
      total: performanceData.errors.size,
      recent: Array.from(performanceData.errors.values())
        .sort((a, b) => new Date(b.lastOccurred) - new Date(a.lastOccurred))
        .slice(0, 10)
    },
    slowQueries: performanceData.slowQueries.slice(0, 10),
    activeRequests: performanceData.requests.size
  };
}

// 性能健康检查
function performanceHealthCheck() {
  const stats = getPerformanceStats();
  const health = {
    status: 'healthy',
    issues: [],
    metrics: stats.system
  };
  
  // 检查错误率
  if (stats.system.errorRate > PERFORMANCE_CONFIG.thresholds.errorRate * 100) {
    health.issues.push(`错误率过高: ${stats.system.errorRate}%`);
    health.status = 'warning';
  }
  
  // 检查响应时间
  if (stats.system.averageResponseTime > PERFORMANCE_CONFIG.thresholds.responseTime) {
    health.issues.push(`平均响应时间过长: ${stats.system.averageResponseTime}ms`);
    health.status = 'warning';
  }
  
  // 检查内存使用
  const memoryUsage = stats.system.memory.heapUsed / stats.system.memory.heapTotal;
  if (memoryUsage > PERFORMANCE_CONFIG.thresholds.memoryUsage) {
    health.issues.push(`内存使用过高: ${Math.round(memoryUsage * 100)}%`);
    health.status = 'critical';
  }
  
  if (health.issues.length === 0) {
    health.message = '性能状态良好';
  }
  
  return health;
}

// 重置统计数据
function resetStats() {
  performanceData.endpoints.clear();
  performanceData.errors.clear();
  performanceData.slowQueries.length = 0;
  performanceData.systemMetrics = {
    startTime: Date.now(),
    totalRequests: 0,
    totalErrors: 0,
    averageResponseTime: 0
  };
  
  logger.info('性能统计数据已重置');
}

module.exports = {
  performanceMiddleware,
  getPerformanceStats,
  performanceHealthCheck,
  resetStats,
  recordSlowQuery,
  PERFORMANCE_CONFIG
};
