import { ref, onMounted, onUnmounted } from 'vue';
import { recordViewHistoryApi } from '@/utils/api';

export function useViewHistory(articleId: number) {
  const startTime = ref<number>(Date.now());
  const scrollDepth = ref<number>(0);
  const viewDuration = ref<number>(0);
  const isRecording = ref<boolean>(false);
  
  let scrollTimer: number | null = null;
  let recordTimer: number | null = null;

  // 计算滚动深度
  const calculateScrollDepth = () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
    const currentScrollDepth = documentHeight > 0 ? (scrollTop / documentHeight) * 100 : 0;
    
    // 只记录最大滚动深度
    if (currentScrollDepth > scrollDepth.value) {
      scrollDepth.value = Math.min(currentScrollDepth, 100);
    }
  };

  // 节流处理滚动事件
  const handleScroll = () => {
    if (scrollTimer) {
      clearTimeout(scrollTimer);
    }
    scrollTimer = setTimeout(calculateScrollDepth, 100);
  };

  // 记录浏览历史
  const recordViewHistory = async () => {
    if (!articleId || isRecording.value) return;
    
    try {
      isRecording.value = true;
      viewDuration.value = Math.floor((Date.now() - startTime.value) / 1000);
      
      await recordViewHistoryApi({
        article_id: articleId,
        view_duration: viewDuration.value,
        scroll_depth: Math.round(scrollDepth.value)
      });
      
      console.log('浏览记录已保存:', {
        article_id: articleId,
        view_duration: viewDuration.value,
        scroll_depth: scrollDepth.value
      });
    } catch (error) {
      console.error('记录浏览历史失败:', error);
    } finally {
      isRecording.value = false;
    }
  };

  // 定期记录浏览历史（每30秒）
  const startRecording = () => {
    // 立即记录一次（表示开始浏览）
    setTimeout(recordViewHistory, 1000);
    
    // 每30秒记录一次
    recordTimer = setInterval(recordViewHistory, 30000);
  };

  // 停止记录
  const stopRecording = () => {
    if (recordTimer) {
      clearInterval(recordTimer);
      recordTimer = null;
    }
    if (scrollTimer) {
      clearTimeout(scrollTimer);
      scrollTimer = null;
    }
    
    // 最后记录一次
    recordViewHistory();
  };

  // 页面可见性变化处理
  const handleVisibilityChange = () => {
    if (document.hidden) {
      // 页面隐藏时记录一次
      recordViewHistory();
    } else {
      // 页面重新可见时重置开始时间
      startTime.value = Date.now();
    }
  };

  // 页面卸载前记录
  const handleBeforeUnload = () => {
    recordViewHistory();
  };

  // 开始监听
  const startListening = () => {
    if (!articleId) return;
    
    startTime.value = Date.now();
    
    // 监听滚动事件
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // 监听页面卸载
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    // 开始定期记录
    startRecording();
  };

  // 停止监听
  const stopListening = () => {
    window.removeEventListener('scroll', handleScroll);
    document.removeEventListener('visibilitychange', handleVisibilityChange);
    window.removeEventListener('beforeunload', handleBeforeUnload);
    
    stopRecording();
  };

  // 手动触发记录（用于特殊事件，如点赞、评论等）
  const triggerRecord = () => {
    recordViewHistory();
  };

  // 获取当前浏览统计
  const getCurrentStats = () => {
    return {
      duration: Math.floor((Date.now() - startTime.value) / 1000),
      scrollDepth: Math.round(scrollDepth.value),
      isRecording: isRecording.value
    };
  };

  return {
    // 状态
    viewDuration,
    scrollDepth,
    isRecording,
    
    // 方法
    startListening,
    stopListening,
    triggerRecord,
    getCurrentStats,
    recordViewHistory
  };
}
