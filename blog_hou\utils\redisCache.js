const redis = require('redis');
const logger = require('../plugin/logger');

class RedisCache {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.config = {
      enabled: true, // 临时硬编码启用Redis
      host: 'localhost',
      port: 6379,
      password: undefined,
      db: 0,
      keyPrefix: 'myblog:'
    };

    logger.info('Redis配置加载:', this.config);

    // 如果Redis配置存在，则初始化连接
    if (this.config.enabled) {
      this.init();
    }
  }

  async init() {
    try {
      this.client = redis.createClient({
        socket: {
          host: this.config.host,
          port: this.config.port,
          connectTimeout: this.config.connectTimeout,
          reconnectStrategy: this.config.retryStrategy
        },
        password: this.config.password,
        database: this.config.db
      });

      this.client.on('connect', () => {
        logger.info('Redis连接成功');
        this.isConnected = true;
      });

      this.client.on('error', (err) => {
        logger.error('Redis连接错误:', err.message);
        this.isConnected = false;
      });

      this.client.on('end', () => {
        logger.warn('Redis连接断开');
        this.isConnected = false;
      });

      // 连接到Redis
      await this.client.connect();

    } catch (error) {
      logger.error('Redis初始化失败:', error);
      this.isConnected = false;
    }
  }

  async get(key) {
    if (!this.isConnected || !this.client) {
      return null;
    }

    try {
      const fullKey = this.config.keyPrefix + key;
      const result = await this.client.get(fullKey);
      return result ? JSON.parse(result) : null;
    } catch (error) {
      logger.error('Redis GET错误:', error.message);
      return null;
    }
  }

  async set(key, value, ttl = 300) {
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      const fullKey = this.config.keyPrefix + key;
      const serialized = JSON.stringify(value);
      if (ttl > 0) {
        await this.client.setEx(fullKey, ttl, serialized);
      } else {
        await this.client.set(fullKey, serialized);
      }
      return true;
    } catch (error) {
      logger.error('Redis SET错误:', error.message);
      return false;
    }
  }

  async del(key) {
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      const fullKey = this.config.keyPrefix + key;
      await this.client.del(fullKey);
      return true;
    } catch (error) {
      logger.error('Redis DEL错误:', error.message);
      return false;
    }
  }

  async exists(key) {
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      const fullKey = this.config.keyPrefix + key;
      const result = await this.client.exists(fullKey);
      return result === 1;
    } catch (error) {
      logger.error('Redis EXISTS错误:', error.message);
      return false;
    }
  }

  async flushAll() {
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      await this.client.flushAll();
      logger.info('Redis缓存已清空');
      return true;
    } catch (error) {
      logger.error('Redis FLUSHALL错误:', error.message);
      return false;
    }
  }

  isAvailable() {
    return this.isConnected && this.client;
  }

  async close() {
    if (this.client) {
      await this.client.quit();
      this.isConnected = false;
      logger.info('Redis连接已关闭');
    }
  }

  // 获取缓存统计信息
  async getStats() {
    if (!this.isConnected || !this.client) {
      return null;
    }

    try {
      const info = await this.client.info('memory');
      const keyCount = await this.client.dbSize();

      return {
        connected: this.isConnected,
        keyCount,
        memoryInfo: info
      };
    } catch (error) {
      logger.error('获取Redis统计信息错误:', error.message);
      return null;
    }
  }
}

// 创建单例实例
const redisCache = new RedisCache();

module.exports = redisCache;
