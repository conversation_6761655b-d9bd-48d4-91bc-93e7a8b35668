const redis = require('redis');
const logger = require('../plugin/logger');
const { getAppConfig } = require('../config');

class RedisCache {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.config = getAppConfig().redis || {};
    
    // 如果Redis配置存在，则初始化连接
    if (this.config.enabled) {
      this.init();
    }
  }

  async init() {
    try {
      this.client = redis.createClient({
        host: this.config.host || 'localhost',
        port: this.config.port || 6379,
        password: this.config.password,
        db: this.config.db || 0,
        retry_strategy: (options) => {
          if (options.error && options.error.code === 'ECONNREFUSED') {
            logger.error('Redis连接被拒绝');
            return new Error('Redis连接被拒绝');
          }
          if (options.total_retry_time > 1000 * 60 * 60) {
            logger.error('Redis重试时间超过1小时');
            return new Error('重试时间超过1小时');
          }
          if (options.attempt > 10) {
            logger.error('Redis重试次数超过10次');
            return undefined;
          }
          return Math.min(options.attempt * 100, 3000);
        }
      });

      this.client.on('connect', () => {
        logger.info('Redis连接成功');
        this.isConnected = true;
      });

      this.client.on('error', (err) => {
        logger.error('Redis连接错误:', err);
        this.isConnected = false;
      });

      this.client.on('end', () => {
        logger.warn('Redis连接断开');
        this.isConnected = false;
      });

    } catch (error) {
      logger.error('Redis初始化失败:', error);
      this.isConnected = false;
    }
  }

  async get(key) {
    if (!this.isConnected || !this.client) {
      return null;
    }

    try {
      const result = await this.client.get(key);
      return result ? JSON.parse(result) : null;
    } catch (error) {
      logger.error('Redis GET错误:', error);
      return null;
    }
  }

  async set(key, value, ttl = 300) {
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      const serialized = JSON.stringify(value);
      if (ttl > 0) {
        await this.client.setex(key, ttl, serialized);
      } else {
        await this.client.set(key, serialized);
      }
      return true;
    } catch (error) {
      logger.error('Redis SET错误:', error);
      return false;
    }
  }

  async del(key) {
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      logger.error('Redis DEL错误:', error);
      return false;
    }
  }

  async exists(key) {
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Redis EXISTS错误:', error);
      return false;
    }
  }

  async flushAll() {
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      await this.client.flushall();
      logger.info('Redis缓存已清空');
      return true;
    } catch (error) {
      logger.error('Redis FLUSHALL错误:', error);
      return false;
    }
  }

  isAvailable() {
    return this.isConnected && this.client;
  }

  async close() {
    if (this.client) {
      await this.client.quit();
      this.isConnected = false;
      logger.info('Redis连接已关闭');
    }
  }
}

// 创建单例实例
const redisCache = new RedisCache();

module.exports = redisCache;
