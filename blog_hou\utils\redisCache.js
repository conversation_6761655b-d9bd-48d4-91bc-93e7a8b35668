const redis = require('redis');
const logger = require('../plugin/logger');

class RedisCache {
  constructor() {
    this.client = null;
    this.isConnected = false;

    // 从配置文件读取Redis配置
    try {
      const { redis } = require('../config/app');
      this.config = redis;
    } catch (error) {
      logger.warn('无法加载Redis配置，使用环境变量', error.message);
      // 如果配置加载失败，使用环境变量
      this.config = {
        enabled: process.env.REDIS_ENABLED === 'true',
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT) || 6379,
        password: process.env.REDIS_PASSWORD || undefined,
        db: parseInt(process.env.REDIS_DB) || 0,
        keyPrefix: process.env.REDIS_KEY_PREFIX || 'myblog:'
      };
    }

    logger.info('Redis配置加载:', {
      enabled: this.config.enabled,
      host: this.config.host,
      port: this.config.port,
      password: this.config.password ? '***' : undefined,
      db: this.config.db,
      keyPrefix: this.config.keyPrefix
    });

    // 如果Redis配置启用，则初始化连接（延迟初始化）
    if (this.config.enabled) {
      // 延迟初始化，避免配置系统未完全加载
      setTimeout(() => {
        this.init();
      }, 1000);
    } else {
      logger.warn('Redis未启用，将使用内存缓存');
    }
  }

  async init() {
    try {
      logger.info('开始初始化Redis连接...', {
        host: this.config.host,
        port: this.config.port,
        db: this.config.db
      });

      this.client = redis.createClient({
        socket: {
          host: this.config.host,
          port: this.config.port,
          connectTimeout: 10000,
          reconnectStrategy: (retries) => {
            const delay = Math.min(retries * 50, 2000);
            logger.debug(`Redis重连尝试 ${retries}, 延迟 ${delay}ms`);
            return delay;
          }
        },
        password: this.config.password || undefined,
        database: this.config.db
      });

      this.client.on('connect', () => {
        logger.info('Redis连接成功');
        this.isConnected = true;
      });

      this.client.on('ready', () => {
        logger.info('Redis客户端就绪');
        this.isConnected = true;
      });

      this.client.on('error', (err) => {
        logger.error('Redis连接错误:', {
          message: err.message,
          code: err.code,
          errno: err.errno
        });
        this.isConnected = false;
      });

      this.client.on('end', () => {
        logger.warn('Redis连接断开');
        this.isConnected = false;
      });

      this.client.on('reconnecting', () => {
        logger.info('Redis正在重连...');
      });

      // 连接到Redis
      await this.client.connect();

      // 测试连接
      const pong = await this.client.ping();
      if (pong === 'PONG') {
        logger.info('Redis连接测试成功');
        this.isConnected = true;
      }

    } catch (error) {
      logger.error('Redis初始化失败:', {
        message: error.message,
        code: error.code,
        errno: error.errno,
        stack: error.stack
      });
      this.isConnected = false;
    }
  }

  async get(key) {
    if (!this.isConnected || !this.client) {
      return null;
    }

    try {
      const fullKey = this.config.keyPrefix + key;
      const result = await this.client.get(fullKey);
      return result ? JSON.parse(result) : null;
    } catch (error) {
      logger.error('Redis GET错误:', error.message);
      return null;
    }
  }

  async set(key, value, ttl = 300) {
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      const fullKey = this.config.keyPrefix + key;
      const serialized = JSON.stringify(value);
      if (ttl > 0) {
        await this.client.setEx(fullKey, ttl, serialized);
      } else {
        await this.client.set(fullKey, serialized);
      }
      return true;
    } catch (error) {
      logger.error('Redis SET错误:', error.message);
      return false;
    }
  }

  async del(key) {
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      const fullKey = this.config.keyPrefix + key;
      await this.client.del(fullKey);
      return true;
    } catch (error) {
      logger.error('Redis DEL错误:', error.message);
      return false;
    }
  }

  async exists(key) {
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      const fullKey = this.config.keyPrefix + key;
      const result = await this.client.exists(fullKey);
      return result === 1;
    } catch (error) {
      logger.error('Redis EXISTS错误:', error.message);
      return false;
    }
  }

  async flushAll() {
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      await this.client.flushAll();
      logger.info('Redis缓存已清空');
      return true;
    } catch (error) {
      logger.error('Redis FLUSHALL错误:', error.message);
      return false;
    }
  }

  isAvailable() {
    return this.isConnected && this.client;
  }

  // 手动初始化方法
  async manualInit() {
    if (!this.config.enabled) {
      logger.warn('Redis未启用，无法手动初始化');
      return false;
    }

    if (this.isConnected) {
      logger.info('Redis已连接，无需重复初始化');
      return true;
    }

    try {
      await this.init();
      return this.isConnected;
    } catch (error) {
      logger.error('Redis手动初始化失败:', error);
      return false;
    }
  }

  async close() {
    if (this.client) {
      await this.client.quit();
      this.isConnected = false;
      logger.info('Redis连接已关闭');
    }
  }

  // 获取缓存统计信息
  async getStats() {
    if (!this.isConnected || !this.client) {
      return null;
    }

    try {
      const info = await this.client.info('memory');
      const keyCount = await this.client.dbSize();

      return {
        connected: this.isConnected,
        keyCount,
        memoryInfo: info
      };
    } catch (error) {
      logger.error('获取Redis统计信息错误:', error.message);
      return null;
    }
  }
}

// 创建单例实例
const redisCache = new RedisCache();

module.exports = redisCache;
