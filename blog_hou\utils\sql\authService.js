const db = require("../db");
const { hashPassword, comparePassword } = require("../../plugin/crypto");

// 登录功能
async function login(username, password) {
  const sql = "SELECT * FROM users WHERE username = ?";
  const result = await db.query(sql, [username]);

  if (result.length === 0) {
    return { success: false, message: "用户名不存在" };
  }

  const user = result[0];
  const isPasswordValid = await comparePassword(password, user.password);

  if (!isPasswordValid) {
    return { success: false, message: "密码错误" };
  }

  return { success: true, id: user.id, username: user.username, role: user.role || 'user' };
}

// 注册功能
async function register(username, email, password) {
  const existingUser = await db.query(
    "SELECT * FROM users WHERE username = ?",
    [username]
  );

  if (existingUser.length > 0) {
    return { success: false, message: "用户名已存在" };
  }

  const hashedPassword = await hashPassword(password);
  const status = 1;
  const avatar = "moren.png";

  const sql = `INSERT INTO users(username, email, password, avatar, status) VALUES(?, ?, ?, ?, ?)`;
  const result = await db.query(sql, [
    username,
    email,
    hashedPassword,
    avatar,
    status,
  ]);

  if (result.affectedRows > 0) {
    return { success: true, userId: result.insertId };
  } else {
    return { success: false, message: "注册失败，请重试" };
  }
}

// 修改密码功能
async function changePassword(userId, oldPassword, newPassword) {
  const sql = "SELECT * FROM users WHERE id = ?";
  const result = await db.query(sql, [userId]);

  if (result.length === 0) {
    return { success: false, message: "用户不存在" };
  }

  const user = result[0];
  const isOldPasswordValid = await comparePassword(oldPassword, user.password);

  if (!isOldPasswordValid) {
    return { success: false, message: "当前密码错误" };
  }

  const hashedNewPassword = await hashPassword(newPassword);
  const updateSql = "UPDATE users SET password = ? WHERE id = ?";
  const updateResult = await db.query(updateSql, [hashedNewPassword, userId]);

  if (updateResult.affectedRows > 0) {
    const { password, ...userWithoutPassword } = user;
    return { success: true, user: userWithoutPassword };
  } else {
    return { success: false, message: "密码修改失败，请重试" };
  }
}

// 获取用户信息
async function getUserInfo(id) {
  const sql = "SELECT * FROM users WHERE id = ?";
  const result = await db.query(sql, [id]);

  if (result.length === 0) {
    return { success: false, message: "用户不存在" };
  }

  const user = result[0];
  const { password, ...userWithoutPassword } = user;
  return { success: true, user: userWithoutPassword };
}

// 更新用户信息
async function updateUserInfo(
  id,
  username,
  email,
  avatar,
  address,
  position,
  intro,
  tech_tags
) {
  const sql = `UPDATE users SET username = ?, email = ?, avatar = ?, address = ?, position = ?, intro = ?, tech_tags = ?, updated_at = NOW() WHERE id = ?`;
  const result = await db.query(sql, [
    username,
    email,
    avatar,
    address,
    position,
    intro,
    typeof tech_tags === "string" ? tech_tags : JSON.stringify(tech_tags),
    id,
  ]);

  if (result.affectedRows > 0) {
    const userRes = await db.query("SELECT * FROM users WHERE id = ?", [id]);
    if (userRes.length > 0) {
      const { password, ...userWithoutPassword } = userRes[0];
      return { success: true, user: userWithoutPassword };
    }
    return { success: true, message: "用户信息更新成功" };
  }
  return { success: false, message: "用户信息更新失败" };
}

// 获取技术标签列表
async function tags() {
  const sql = `SELECT * FROM tech_tags`;
  const result = await db.query(sql);
  if (result.length > 0) {
    return { success: true, data: result };
  }
  return { error: false, message: "技术标签列表获取失败" };
}

module.exports = {
  login,
  register,
  changePassword,
  getUserInfo,
  updateUserInfo,
  tags,
};
