const db = require('../utils/db');

async function createUserLevelLogsTable() {
  try {
    console.log('🔧 创建用户等级变更日志表...');
    
    // 创建用户等级变更日志表
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS user_level_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL COMMENT '用户ID',
        old_level ENUM('free','basic','premium','vip') NOT NULL COMMENT '原等级',
        new_level ENUM('free','basic','premium','vip') NOT NULL COMMENT '新等级',
        changed_by INT NOT NULL COMMENT '操作人ID',
        reason VARCHAR(255) DEFAULT NULL COMMENT '变更原因',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
        
        INDEX idx_user_id (user_id),
        INDEX idx_changed_by (changed_by),
        INDEX idx_created_at (created_at),
        INDEX idx_user_time (user_id, created_at),
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户等级变更日志表'
    `;
    
    await db.query(createTableQuery);
    console.log('✅ 用户等级变更日志表创建成功');
    
    // 检查表结构
    const columns = await db.query('SHOW COLUMNS FROM user_level_logs');
    console.log('\n📋 表结构:');
    columns.forEach(col => {
      console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '可空' : '非空'} ${col.Key ? `[${col.Key}]` : ''}`);
    });
    
    // 插入一些示例数据（可选）
    console.log('\n📊 插入示例数据...');
    
    // 获取管理员用户ID
    const adminResult = await db.query("SELECT id FROM users WHERE role = 'admin' LIMIT 1");
    if (adminResult.length > 0) {
      const adminId = adminResult[0].id;
      
      // 获取一些普通用户
      const usersResult = await db.query("SELECT id, level FROM users WHERE role = 'user' LIMIT 3");
      
      if (usersResult.length > 0) {
        for (const user of usersResult) {
          // 模拟一些等级变更记录
          await db.query(`
            INSERT INTO user_level_logs (user_id, old_level, new_level, changed_by, reason, created_at)
            VALUES (?, 'free', ?, ?, '系统初始化', DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY))
          `, [user.id, user.level, adminId]);
        }
        
        console.log(`✅ 插入了 ${usersResult.length} 条示例记录`);
      }
    }
    
    // 验证数据
    const count = await db.query('SELECT COUNT(*) as count FROM user_level_logs');
    console.log(`\n📈 当前日志记录数: ${count[0].count}`);
    
    console.log('\n✅ 用户等级变更日志表创建完成！');
    
  } catch (error) {
    console.error('❌ 创建失败:', error.message);
    
    // 如果是外键约束错误，提供解决方案
    if (error.message.includes('foreign key constraint')) {
      console.log('\n💡 解决方案:');
      console.log('1. 确保 users 表存在');
      console.log('2. 检查用户ID是否有效');
      console.log('3. 可以先创建表结构，后续再添加外键约束');
    }
  } finally {
    process.exit(0);
  }
}

createUserLevelLogsTable();
