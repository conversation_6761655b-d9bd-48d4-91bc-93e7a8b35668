<template>
  <el-radio-group
    :model-value="modelValue"
    @update:modelValue="onChange"
    size="small"
  >
    <el-radio-button label="line">折线图</el-radio-button>
    <el-radio-button label="bar">柱状图</el-radio-button>
  </el-radio-group>
</template>

<script setup lang="ts">
const props = defineProps<{ modelValue: string }>()
const emit = defineEmits(['update:modelValue', 'change'])

function onChange(val: string) {
  emit('update:modelValue', val)
  emit('change', val)
}
</script>