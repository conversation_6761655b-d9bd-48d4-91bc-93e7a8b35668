const Router = require("koa-router");
const router = new Router();

const users = require("./user");
const logs = require("./logs");
const articles = require("./articles");
const upload = require("./upload");
const videos = require("./media");
const bigModel = require("./bigmodel");
const show = require("./showwall");
const daosql = require("./daosql");
const jiami = require("./jiamitupian");
const jiamiVideos = require("./shipingjiami");
const resource = require("./resource");
const friend = require("./friend");
const bugsend = require("./bugsend");
const create = require("./create");

// 注册用户管理路由
router.use("/user", users.routes(), users.allowedMethods());
// 输出日志
router.use("/logs", logs.routes(), logs.allowedMethods());
// articles
router.use("/articles", articles.routes(), articles.allowedMethods());
// 上传文件
router.use("/upload", upload.routes(), upload.allowedMethods());
//
router.use("/media", videos.routes(), videos.allowedMethods());
// 调用大模型
router.use("/bigmodel", bigModel.routes(), bigModel.allowedMethods());
// 显示墙
router.use("/showwall", show.routes(), show.allowedMethods());
// 导出数据
router.use("/daosql", daosql.routes(), daosql.allowedMethods());
// 加密图片
router.use("/jiami", jiami.routes(), jiami.allowedMethods());
// 加密视频
router.use("/jiamivideos", jiamiVideos.routes(), jiamiVideos.allowedMethods());
// 资源管理
router.use("/resource", resource.routes(), resource.allowedMethods());
// 好友管理
router.use("/friend", friend.routes(), friend.allowedMethods());
// bug 管理
router.use("/bugsend", bugsend.routes(), bugsend.allowedMethods());
// 创意管理
router.use("/creative", create.routes(), create.allowedMethods());

// 重定向示例（如果需要）
// router.redirect("/", "/home");

module.exports = router;
