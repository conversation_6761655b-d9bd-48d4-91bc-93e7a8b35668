/* ========================================
   Element Plus 主题定制
   ======================================== */

/* Element Plus CSS 变量覆盖 */
:root {
  /* 主色调 */
  --el-color-primary: #667eea;
  --el-color-primary-light-3: #818cf8;
  --el-color-primary-light-5: #a5b4fc;
  --el-color-primary-light-7: #c7d2fe;
  --el-color-primary-light-8: #ddd6fe;
  --el-color-primary-light-9: #ede9fe;
  --el-color-primary-dark-2: #4f46e5;
  
  /* 成功色 */
  --el-color-success: #10b981;
  --el-color-success-light-3: #34d399;
  --el-color-success-light-5: #6ee7b7;
  --el-color-success-light-7: #a7f3d0;
  --el-color-success-light-8: #c6f6d5;
  --el-color-success-light-9: #d1fae5;
  
  /* 警告色 */
  --el-color-warning: #f59e0b;
  --el-color-warning-light-3: #fbbf24;
  --el-color-warning-light-5: #fcd34d;
  --el-color-warning-light-7: #fde68a;
  --el-color-warning-light-8: #fef3c7;
  --el-color-warning-light-9: #fffbeb;
  
  /* 危险色 */
  --el-color-danger: #ef4444;
  --el-color-danger-light-3: #f87171;
  --el-color-danger-light-5: #fca5a5;
  --el-color-danger-light-7: #fecaca;
  --el-color-danger-light-8: #fee2e2;
  --el-color-danger-light-9: #fef2f2;
  
  /* 信息色 */
  --el-color-info: #6b7280;
  --el-color-info-light-3: #9ca3af;
  --el-color-info-light-5: #d1d5db;
  --el-color-info-light-7: #e5e7eb;
  --el-color-info-light-8: #f3f4f6;
  --el-color-info-light-9: #f9fafb;
  
  /* 文字颜色 */
  --el-text-color-primary: #1f2937;
  --el-text-color-regular: #374151;
  --el-text-color-secondary: #6b7280;
  --el-text-color-placeholder: #9ca3af;
  --el-text-color-disabled: #d1d5db;
  
  /* 边框颜色 */
  --el-border-color: #e5e7eb;
  --el-border-color-light: #f3f4f6;
  --el-border-color-lighter: #f9fafb;
  --el-border-color-extra-light: #fafafa;
  --el-border-color-dark: #d1d5db;
  --el-border-color-darker: #9ca3af;
  
  /* 填充颜色 */
  --el-fill-color: #f3f4f6;
  --el-fill-color-light: #f9fafb;
  --el-fill-color-lighter: #fafafa;
  --el-fill-color-extra-light: #fefefe;
  --el-fill-color-dark: #e5e7eb;
  --el-fill-color-darker: #d1d5db;
  --el-fill-color-blank: #ffffff;
  
  /* 背景颜色 */
  --el-bg-color: transparent;
  --el-bg-color-page: transparent;
  --el-bg-color-overlay: #ffffff;
  
  /* 字体 */
  --el-font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --el-font-size-extra-large: 20px;
  --el-font-size-large: 18px;
  --el-font-size-medium: 16px;
  --el-font-size-base: 14px;
  --el-font-size-small: 13px;
  --el-font-size-extra-small: 12px;
  
  /* 圆角 */
  --el-border-radius-base: 8px;
  --el-border-radius-small: 4px;
  --el-border-radius-round: 20px;
  --el-border-radius-circle: 100%;
  
  /* 阴影 */
  --el-box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --el-box-shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --el-box-shadow-lighter: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --el-box-shadow-dark: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* ========================================
   组件样式定制
   ======================================== */

/* 按钮组件 */
.el-button {
  font-family: var(--el-font-family);
  font-weight: 500;
  border-radius: var(--el-border-radius-base);
  transition: all 0.3s ease;
}

.el-button:hover {
  transform: translateY(-1px);
}

.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* 卡片组件 */
.el-card {
  border-radius: var(--el-border-radius-base);
  box-shadow: var(--el-box-shadow-lighter);
  transition: box-shadow 0.3s ease;
}

.el-card:hover {
  box-shadow: var(--el-box-shadow-light);
}

.el-card__header {
  background: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color);
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 输入框组件 */
.el-input__wrapper {
  border-radius: var(--el-border-radius-base);
  transition: all 0.3s ease;
}

.el-input__wrapper:hover {
  box-shadow: 0 0 0 1px var(--el-color-primary-light-7);
}

.el-input__wrapper.is-focus {
  box-shadow: 0 0 0 2px var(--el-color-primary-light-5);
}

/* 表格组件 */
.el-table {
  font-family: var(--el-font-family);
  border-radius: var(--el-border-radius-base);
  overflow: hidden;
}

.el-table th.el-table__cell {
  background: var(--el-fill-color-light);
  color: var(--el-text-color-primary);
  font-weight: 600;
  border-bottom: 2px solid var(--el-border-color);
}

.el-table .el-table__row:hover > td {
  background: var(--el-fill-color-light);
}

/* 菜单组件 */
.el-menu {
  border: none;
}

.el-menu-item {
  font-family: var(--el-font-family);
  font-weight: 500;
  border-radius: var(--el-border-radius-base);
  margin: 2px 8px;
  transition: all 0.3s ease;
}

.el-menu-item:hover {
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.el-menu-item.is-active {
  background: var(--el-color-primary);
  color: white;
}

/* 下拉菜单组件 */
.el-dropdown-menu {
  border-radius: var(--el-border-radius-base);
  box-shadow: var(--el-box-shadow-dark);
  border: 1px solid var(--el-border-color);
  padding: 8px 0;
}

.el-dropdown-menu__item {
  font-family: var(--el-font-family);
  font-size: var(--el-font-size-base);
  color: var(--el-text-color-regular);
  padding: 10px 16px;
  transition: all 0.3s ease;
}

.el-dropdown-menu__item:hover {
  background: var(--el-fill-color-light);
  color: var(--el-color-primary);
}

.el-dropdown-menu__item.is-divided {
  border-top: 1px solid var(--el-border-color);
  margin-top: 4px;
  padding-top: 12px;
}

/* 标签组件 */
.el-tag {
  font-family: var(--el-font-family);
  font-weight: 500;
  border-radius: var(--el-border-radius-small);
}

/* 对话框组件 */
.el-dialog {
  border-radius: var(--el-border-radius-base);
  box-shadow: var(--el-box-shadow-dark);
}

.el-dialog__header {
  background: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base) var(--el-border-radius-base) 0 0;
}

.el-dialog__title {
  font-family: var(--el-font-family);
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 抽屉组件 */
.el-drawer {
  font-family: var(--el-font-family);
}

.el-drawer__header {
  background: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color);
  margin-bottom: 0;
  padding: 20px 24px;
}

.el-drawer__title {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 消息提示组件 */
.el-message {
  border-radius: var(--el-border-radius-base);
  box-shadow: var(--el-box-shadow-light);
  font-family: var(--el-font-family);
}

.el-notification {
  border-radius: var(--el-border-radius-base);
  box-shadow: var(--el-box-shadow-dark);
  font-family: var(--el-font-family);
}

/* 分页组件 */
.el-pagination {
  font-family: var(--el-font-family);
}

.el-pagination .el-pager li {
  border-radius: var(--el-border-radius-small);
  margin: 0 2px;
}

.el-pagination .el-pager li.is-active {
  background: var(--el-color-primary);
  color: white;
}

/* 表单组件 */
.el-form-item__label {
  font-family: var(--el-font-family);
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.el-form-item__error {
  font-family: var(--el-font-family);
  font-size: var(--el-font-size-small);
}

/* 选择器组件 */
.el-select .el-input__wrapper {
  border-radius: var(--el-border-radius-base);
}

.el-select-dropdown {
  border-radius: var(--el-border-radius-base);
  box-shadow: var(--el-box-shadow-dark);
}

.el-select-dropdown__item {
  font-family: var(--el-font-family);
  transition: all 0.3s ease;
}

.el-select-dropdown__item:hover {
  background: var(--el-fill-color-light);
}

.el-select-dropdown__item.is-selected {
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  font-weight: 500;
}

/* 开关组件 */
.el-switch {
  font-family: var(--el-font-family);
}

/* 步骤条组件 */
.el-steps {
  font-family: var(--el-font-family);
}

/* 时间线组件 */
.el-timeline {
  font-family: var(--el-font-family);
}

/* 面包屑组件 */
.el-breadcrumb {
  font-family: var(--el-font-family);
}

.el-breadcrumb__item {
  font-weight: 500;
}

.el-breadcrumb__inner {
  color: var(--el-text-color-secondary);
  transition: color 0.3s ease;
}

.el-breadcrumb__inner:hover {
  color: var(--el-color-primary);
}

.el-breadcrumb__item:last-child .el-breadcrumb__inner {
  color: var(--el-text-color-primary);
  font-weight: 600;
}

/* ========================================
   响应式适配
   ======================================== */
@media (max-width: 768px) {
  .el-button {
    padding: 8px 12px;
    font-size: var(--el-font-size-small);
  }
  
  .el-card {
    margin: 8px;
  }
  
  .el-table {
    font-size: var(--el-font-size-small);
  }
  
  .el-dialog {
    width: 90% !important;
    margin: 5vh auto !important;
  }
}
