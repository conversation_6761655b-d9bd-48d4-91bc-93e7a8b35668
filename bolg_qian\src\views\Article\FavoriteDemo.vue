<template>
  <div class="favorite-demo">
    <div class="demo-header">
      <h1>💖 文章收藏功能演示</h1>
      <p>这是一个演示如何在文章中使用收藏组件的示例页面</p>
    </div>

    <!-- 模拟文章内容 -->
    <div class="article-content">
      <el-card class="article-card" shadow="hover">
        <template #header>
          <div class="article-header">
            <h2>{{ article.title }}</h2>
            <div class="article-meta">
              <span class="author">
                <el-icon><User /></el-icon>
                {{ article.author }}
              </span>
              <span class="date">
                <el-icon><Calendar /></el-icon>
                {{ article.date }}
              </span>
              <span class="views">
                <el-icon><View /></el-icon>
                {{ article.views }} 次浏览
              </span>
            </div>
          </div>
        </template>

        <div class="article-body">
          <p>{{ article.content }}</p>
          
          <!-- 文章图片 -->
          <div class="article-image">
            <img :src="article.image" :alt="article.title" />
          </div>
          
          <p>{{ article.content2 }}</p>
        </div>

        <!-- 收藏和点赞组件 -->
        <div class="article-actions">
          <div class="interaction-section">
            <h3>喜欢这篇文章吗？</h3>
            <div class="interaction-buttons">
              <ArticleFavorite 
                :article-id="article.id"
                :show-text="true"
                :show-count="true"
                :show-users-list="true"
                @favorite-changed="handleFavoriteChanged"
              />
              <ArticleLike 
                :article-id="article.id"
                :show-users-list="true"
                @like-changed="handleLikeChanged"
              />
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 使用说明 -->
    <div class="usage-guide">
      <el-card class="guide-card">
        <template #header>
          <h3>使用说明</h3>
        </template>
        
        <div class="guide-content">
          <h4>收藏功能特点：</h4>
          <ul>
            <li>✅ 支持登录用户和匿名用户收藏</li>
            <li>✅ 一键收藏/取消收藏</li>
            <li>✅ 实时显示收藏统计数据</li>
            <li>✅ 防重复收藏机制</li>
            <li>✅ 响应式设计，适配移动端</li>
            <li>✅ 收藏历史记录管理</li>
          </ul>

          <h4>组件使用方法：</h4>
          <el-code-block>
            <pre><code>&lt;ArticleFavorite 
  :article-id="articleId"
  :show-text="true"
  :show-count="true"
  :show-users-list="true"
  @favorite-changed="handleFavoriteChanged"
/&gt;</code></pre>
          </el-code-block>

          <h4>参数说明：</h4>
          <el-table :data="apiDocs" style="width: 100%; margin-top: 16px;">
            <el-table-column prop="prop" label="参数" width="150" />
            <el-table-column prop="type" label="类型" width="120" />
            <el-table-column prop="required" label="必填" width="80" />
            <el-table-column prop="description" label="说明" />
          </el-table>

          <h4>事件说明：</h4>
          <el-table :data="eventDocs" style="width: 100%; margin-top: 16px;">
            <el-table-column prop="event" label="事件名" width="150" />
            <el-table-column prop="params" label="参数" width="200" />
            <el-table-column prop="description" label="说明" />
          </el-table>
        </div>
      </el-card>
    </div>

    <!-- 测试区域 -->
    <div class="test-area">
      <el-card class="test-card">
        <template #header>
          <h3>测试区域</h3>
        </template>
        
        <div class="test-content">
          <p>当前收藏状态：</p>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="收藏数">{{ favoriteStats.favorites_count }}</el-descriptions-item>
            <el-descriptions-item label="用户状态">
              <el-tag 
                :type="favoriteStats.isFavorited ? 'danger' : 'info'"
                size="small"
              >
                {{ favoriteStats.isFavorited ? '已收藏' : '未收藏' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="点赞数">{{ likeStats.likes_count }}</el-descriptions-item>
            <el-descriptions-item label="点赞状态">
              <el-tag 
                :type="getLikeStatusType(likeStats.userLikeStatus)"
                size="small"
              >
                {{ getLikeStatusText(likeStats.userLikeStatus) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Calendar, View } from '@element-plus/icons-vue'
import ArticleFavorite from '../../components/ArticleFavorite.vue'
import ArticleLike from '../../components/ArticleLike.vue'

export default {
  name: 'FavoriteDemo',
  components: {
    ArticleFavorite,
    ArticleLike,
    User,
    Calendar,
    View
  },
  setup() {
    // 模拟文章数据
    const article = reactive({
      id: 39, // 使用测试文章ID
      title: '文章收藏功能演示',
      author: 'admin123',
      date: '2025-06-25',
      views: 128,
      content: '这是一篇用于演示文章收藏功能的示例文章。收藏功能让用户可以保存喜欢的文章，方便日后查阅。我们的收藏系统支持登录用户和匿名用户，确保每个人都能使用这个便利的功能。',
      image: '/articles/demo-image.jpg',
      content2: '收藏功能的设计理念是简单易用，一键收藏，随时取消。用户可以在个人中心查看收藏历史，管理自己的收藏内容。这为用户提供了更好的阅读体验和内容管理方式。'
    })

    // 收藏统计数据
    const favoriteStats = reactive({
      favorites_count: 0,
      isFavorited: false
    })

    // 点赞统计数据
    const likeStats = reactive({
      likes_count: 0,
      dislikes_count: 0,
      userLikeStatus: null
    })

    // API文档数据
    const apiDocs = [
      {
        prop: 'article-id',
        type: 'Number/String',
        required: '是',
        description: '文章ID，用于标识要收藏的文章'
      },
      {
        prop: 'show-text',
        type: 'Boolean',
        required: '否',
        description: '是否显示收藏文字，默认为true'
      },
      {
        prop: 'show-count',
        type: 'Boolean',
        required: '否',
        description: '是否显示收藏数量，默认为true'
      },
      {
        prop: 'show-users-list',
        type: 'Boolean',
        required: '否',
        description: '是否显示收藏用户列表，默认为false'
      },
      {
        prop: 'size',
        type: 'String',
        required: '否',
        description: '按钮大小：large/default/small，默认为default'
      }
    ]

    // 事件文档数据
    const eventDocs = [
      {
        event: 'favorite-changed',
        params: '{ action, isFavorited, stats }',
        description: '收藏状态改变时触发，包含操作类型、收藏状态和最新统计数据'
      }
    ]

    // 处理收藏状态变化
    const handleFavoriteChanged = (data) => {
      console.log('收藏状态变化:', data)
      
      // 更新统计数据
      if (data.stats) {
        Object.assign(favoriteStats, data.stats)
        favoriteStats.isFavorited = data.isFavorited
      }
      
      // 显示操作结果
      const actionText = data.action === 'added' ? '收藏成功' : '取消收藏'
      ElMessage.success(actionText)
    }

    // 处理点赞状态变化
    const handleLikeChanged = (data) => {
      console.log('点赞状态变化:', data)
      
      // 更新统计数据
      if (data.stats) {
        Object.assign(likeStats, data.stats)
      }
      
      // 显示操作结果
      const actionText = {
        'added': data.type === 'like' ? '点赞成功' : '踩成功',
        'removed': data.type === 'like' ? '取消点赞' : '取消踩',
        'updated': data.type === 'like' ? '已切换为点赞' : '已切换为踩'
      }
      
      ElMessage.success(actionText[data.action] || '操作成功')
    }

    // 获取点赞状态类型
    const getLikeStatusType = (status) => {
      switch (status) {
        case 'like': return 'success'
        case 'dislike': return 'danger'
        default: return 'info'
      }
    }

    // 获取点赞状态文本
    const getLikeStatusText = (status) => {
      switch (status) {
        case 'like': return '已点赞'
        case 'dislike': return '已踩'
        default: return '未操作'
      }
    }

    return {
      article,
      favoriteStats,
      likeStats,
      apiDocs,
      eventDocs,
      handleFavoriteChanged,
      handleLikeChanged,
      getLikeStatusType,
      getLikeStatusText
    }
  }
}
</script>

<style scoped>
.favorite-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;
}

.demo-header h1 {
  color: #303133;
  margin-bottom: 8px;
}

.demo-header p {
  color: #606266;
  font-size: 16px;
}

.article-content {
  margin-bottom: 32px;
}

.article-card {
  margin-bottom: 24px;
}

.article-header h2 {
  margin: 0 0 16px 0;
  color: #303133;
}

.article-meta {
  display: flex;
  gap: 24px;
  color: #909399;
  font-size: 14px;
}

.article-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.article-body {
  line-height: 1.8;
  color: #606266;
  font-size: 16px;
}

.article-body p {
  margin-bottom: 16px;
}

.article-image {
  margin: 24px 0;
  text-align: center;
}

.article-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.article-actions {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

.interaction-section h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
}

.interaction-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.usage-guide,
.test-area {
  margin-bottom: 32px;
}

.guide-content h4 {
  color: #303133;
  margin: 16px 0 8px 0;
}

.guide-content ul {
  margin: 8px 0 16px 20px;
  color: #606266;
}

.guide-content li {
  margin-bottom: 4px;
}

.el-code-block {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  margin: 16px 0;
}

.el-code-block pre {
  margin: 0;
  color: #303133;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.test-content p {
  margin-bottom: 16px;
  color: #303133;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .favorite-demo {
    padding: 16px;
  }
  
  .article-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .interaction-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .demo-header h1 {
    font-size: 24px;
  }
}
</style>
