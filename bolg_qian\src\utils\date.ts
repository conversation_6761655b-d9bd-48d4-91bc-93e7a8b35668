/**
 * 格式化日期时间
 * @param timestamp 时间戳或日期对象
 * @param format 输出格式
 * @returns 格式化后的日期字符串
 * 
 * 支持的格式：
 * - YYYY: 四位年份
 * - MM: 两位月份
 * - DD: 两位日期
 * - HH: 两位小时（24小时制）
 * - mm: 两位分钟
 * - ss: 两位秒数
 * 
 * 示例:
 * formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss') => 2023-06-01 14:30:45
 * formatDate(Date.now(), 'HH:mm') => 14:30
 */
export function formatDate(timestamp: number | Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!timestamp) {
    return '';
  }
  
  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
  
  if (isNaN(date.getTime())) {
    console.error('Invalid date:', timestamp);
    return '';
  }
  
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();
  
  return format
    .replace('YYYY', year.toString())
    .replace('MM', month.toString().padStart(2, '0'))
    .replace('DD', day.toString().padStart(2, '0'))
    .replace('HH', hours.toString().padStart(2, '0'))
    .replace('mm', minutes.toString().padStart(2, '0'))
    .replace('ss', seconds.toString().padStart(2, '0'));
}

/**
 * 计算指定时间距离现在的相对时间
 * @param timestamp 时间戳或日期对象
 * @returns 相对时间文本，如"刚刚"、"5分钟前"、"2小时前"等
 */
export function timeAgo(timestamp: number | Date): string {
  if (!timestamp) {
    return '';
  }
  
  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
  const now = new Date();
  
  if (isNaN(date.getTime())) {
    console.error('Invalid date:', timestamp);
    return '';
  }
  
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  // 不到1分钟
  if (seconds < 60) {
    return '刚刚';
  }
  
  // 不到1小时
  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) {
    return `${minutes}分钟前`;
  }
  
  // 不到1天
  const hours = Math.floor(minutes / 60);
  if (hours < 24) {
    return `${hours}小时前`;
  }
  
  // 不到30天
  const days = Math.floor(hours / 24);
  if (days < 30) {
    return `${days}天前`;
  }
  
  // 不到1年
  const months = Math.floor(days / 30);
  if (months < 12) {
    return `${months}个月前`;
  }
  
  // 超过1年
  const years = Math.floor(days / 365);
  return `${years}年前`;
} 