<template>
  <div class="password-management">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span class="title">🔐 密码管理</span>
          <div>
            <el-button type="info" size="small" @click="fetchDebugData">
              <el-icon><Tools /></el-icon>
              调试数据
            </el-button>
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              新增密码配置
            </el-button>
          </div>
        </div>
      </template>

      <!-- 统计卡片 -->
      <div class="stats-cards">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number">{{ stats.total }}</div>
                <div class="stat-label">总配置数</div>
              </div>
              <el-icon class="stat-icon"><Key /></el-icon>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number">{{ stats.active }}</div>
                <div class="stat-label">启用中</div>
              </div>
              <el-icon class="stat-icon active"><Check /></el-icon>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number">{{ stats.totalUseCount }}</div>
                <div class="stat-label">总使用次数</div>
              </div>
              <el-icon class="stat-icon"><View /></el-icon>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number">{{ stats.recentlyUsed }}</div>
                <div class="stat-label">近期使用</div>
              </div>
              <el-icon class="stat-icon recent"><Clock /></el-icon>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 密码配置表格 -->
      <div class="table-container">
        <el-table 
          :data="passwordList" 
          v-loading="loading"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="password_type" label="类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getPasswordTypeTag(row.password_type)">
                {{ getPasswordTypeName(row.password_type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="description" label="描述" min-width="200" />

          <el-table-column prop="security_question" label="密保问题" min-width="180" show-overflow-tooltip />

          <el-table-column prop="is_active" label="状态" width="80">
            <template #default="{ row }">
              <el-switch
                v-model="row.is_active"
                :active-value="1"
                :inactive-value="0"
                @change="toggleStatus(row)"
              />
            </template>
          </el-table-column>

          <el-table-column prop="use_count" label="使用次数" width="100" sortable />

          <el-table-column prop="last_used_at" label="最后使用" width="160">
            <template #default="{ row }">
              <span v-if="row.last_used_at">{{ formatDate(row.last_used_at) }}</span>
              <span v-else class="text-gray">未使用</span>
            </template>
          </el-table-column>

          <el-table-column prop="created_by_name" label="创建者" width="100" />

          <el-table-column prop="updated_at" label="更新时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.updated_at) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="editPassword(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button size="small" type="info" @click="showResetDialog(row)">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
              <el-button size="small" type="danger" @click="deletePassword(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 批量操作 -->
        <div class="batch-actions" v-if="selectedRows.length > 0">
          <el-alert
            :title="`已选择 ${selectedRows.length} 项`"
            type="info"
            show-icon
            :closable="false"
          >
            <template #default>
              <el-button size="small" @click="batchToggleStatus(true)">批量启用</el-button>
              <el-button size="small" @click="batchToggleStatus(false)">批量禁用</el-button>
              <el-button size="small" type="danger" @click="batchDelete">批量删除</el-button>
            </template>
          </el-alert>
        </div>
      </div>
    </el-card>

    <!-- 创建/编辑密码配置对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="isEdit ? '编辑密码配置' : '新增密码配置'"
      width="600px"
      @close="updateDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="密码类型" prop="passwordType">
          <el-select
            v-model="form.passwordType"
            placeholder="请选择密码类型"
            :disabled="isEdit"
            style="width: 100%"
          >
            <el-option label="照片墙密码" value="photo_wall" />
            <el-option label="媒体访问密码" value="media" />
            <el-option label="管理员密码" value="admin" />
            <el-option label="系统密码" value="system" />
          </el-select>
        </el-form-item>

        <el-form-item label="密码描述" prop="description">
          <el-input
            v-model="form.description"
            placeholder="请输入密码用途描述"
            type="textarea"
            :rows="2"
          />
        </el-form-item>

        <el-form-item label="访问密码" prop="password">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入访问密码（至少6位）"
            show-password
          />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="form.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="密保问题" prop="securityQuestion">
          <el-input
            v-model="form.securityQuestion"
            placeholder="请输入密保问题，如：您最喜欢的颜色是什么？"
          />
        </el-form-item>

        <el-form-item label="密保答案" prop="securityAnswer">
          <el-input
            v-model="form.securityAnswer"
            type="password"
            placeholder="请输入密保答案"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 密保重置密码对话框 -->
    <el-dialog
      v-model="showResetPasswordDialog"
      title="通过密保重置密码"
      width="500px"
      @close="updateResetDialogClose"
    >
      <el-form
        ref="resetFormRef"
        :model="resetForm"
        :rules="resetFormRules"
        label-width="120px"
      >
        <el-form-item label="密码类型">
          <el-tag :type="getPasswordTypeTag(resetForm.passwordType)">
            {{ getPasswordTypeName(resetForm.passwordType) }}
          </el-tag>
        </el-form-item>

        <el-form-item label="密保问题">
          <el-input v-model="resetForm.securityQuestion" disabled />
        </el-form-item>

        <el-form-item label="密保答案" prop="securityAnswer">
          <el-input
            v-model="resetForm.securityAnswer"
            type="password"
            placeholder="请输入密保答案"
            show-password
          />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="resetForm.newPassword"
            type="password"
            placeholder="请输入新密码（至少6位）"
            show-password
          />
        </el-form-item>

        <el-form-item label="确认新密码" prop="confirmNewPassword">
          <el-input
            v-model="resetForm.confirmNewPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showResetPasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="submitReset" :loading="resetting">
          重置密码
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Key, Check, View, Clock, Edit, Refresh, Delete, Tools
} from '@element-plus/icons-vue'
import { passwordManagementApi, passwordUtils } from '@/utils/passwordApi'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const resetting = ref(false)
const showCreateDialog = ref(false)
const showResetPasswordDialog = ref(false)
const isEdit = ref(false)
const passwordList = ref([])
const selectedRows = ref([])

// 表单数据
const form = reactive({
  passwordType: '',
  description: '',
  password: '',
  confirmPassword: '',
  securityQuestion: '',
  securityAnswer: ''
})

const resetForm = reactive({
  id: null,
  passwordType: '',
  securityQuestion: '',
  securityAnswer: '',
  newPassword: '',
  confirmNewPassword: ''
})

// 表单引用
const formRef = ref()
const resetFormRef = ref()

// 表单验证规则
const formRules = {
  passwordType: [
    { required: true, message: '请选择密码类型', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入密码描述', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少为6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== form.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  securityQuestion: [
    { required: true, message: '请输入密保问题', trigger: 'blur' }
  ],
  securityAnswer: [
    { required: true, message: '请输入密保答案', trigger: 'blur' }
  ]
}

const resetFormRules = {
  securityAnswer: [
    { required: true, message: '请输入密保答案', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少为6位', trigger: 'blur' }
  ],
  confirmNewPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== resetForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 统计数据
const stats = computed(() => {
  const total = passwordList.value.length
  const active = passwordList.value.filter(p => p.is_active === 1).length
  const totalUseCount = passwordList.value.reduce((sum, p) => sum + (p.use_count || 0), 0)
  const recentlyUsed = passwordList.value.filter(p => {
    if (!p.last_used_at) return false
    const lastUsed = new Date(p.last_used_at)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    return lastUsed > sevenDaysAgo
  }).length

  return { total, active, totalUseCount, recentlyUsed }
})

// 工具函数
const getPasswordTypeName = passwordUtils.getPasswordTypeName
const getPasswordTypeTag = passwordUtils.getPasswordTypeTag
const formatDate = passwordUtils.formatDateTime

// 数据加载
const fetchPasswordList = async () => {
  try {
    loading.value = true

    const response = await passwordManagementApi.getPasswordList()

    // 调试日志：打印前端接收到的数据
    console.log("=== 前端接收到的密码配置数据 ===");
    console.log("响应状态码:", response.code);
    console.log("响应消息:", response.message);
    console.log("数据数组长度:", response.data?.length || 0);
    console.log("完整响应数据:", response);
    if (response.code === 200 && response.data.length > 0) {
      response.data.forEach((item, index) => {
        console.log(`前端记录 ${index + 1}:`, item);
      });
    }
    console.log("================================");

    if (response.code === 200) {
      passwordList.value = response.data || []
      console.log("设置到 passwordList 的数据:", passwordList.value);
      console.log("passwordList.value.length:", passwordList.value.length);
    } else {
      ElMessage.error(response.message || '获取密码列表失败')
    }
  } catch (error) {
    console.error('获取密码列表失败:', error)
    ElMessage.error(error.response?.data?.message || error.message || '获取密码列表失败')
  } finally {
    loading.value = false
  }
}

// 调试：获取原始数据库数据
const fetchDebugData = async () => {
  try {
    console.log('🔧 开始获取调试数据...')

    const response = await passwordManagementApi.getPasswordListDebug()
    console.log('🔧 调试API响应:', response)

    if (response.code === 200) {
      console.log('🔧 原始数据库数据:', response.data)
      console.log('🔧 数据条数:', response.data.length)

      // 详细打印原始数据
      response.data.forEach((item, index) => {
        console.log(`🔧 原始记录 ${index + 1}:`, {
          id: item.id,
          password_type: item.password_type,
          security_question: item.security_question,
          description: item.description,
          is_active: item.is_active,
          created_by: item.created_by,
          created_by_name: item.created_by_name,
          use_count: item.use_count,
          last_used_at: item.last_used_at,
          created_at: item.created_at,
          updated_at: item.updated_at
        })
      })

      ElMessage.success(`获取到 ${response.data.length} 条原始数据，请查看控制台`)
    } else {
      console.error('🔧 获取调试数据失败:', response.message)
      ElMessage.error(response.message || '获取调试数据失败')
    }
  } catch (error) {
    console.error('🔧 获取调试数据异常:', error)
    ElMessage.error('获取调试数据失败')
  }
}

// 表单操作
const resetFormData = () => {
  Object.assign(form, {
    passwordType: '',
    description: '',
    password: '',
    confirmPassword: '',
    securityQuestion: '',
    securityAnswer: ''
  })
  isEdit.value = false
  formRef.value?.clearValidate()
}

const resetResetFormData = () => {
  Object.assign(resetForm, {
    id: null,
    passwordType: '',
    securityQuestion: '',
    securityAnswer: '',
    newPassword: '',
    confirmNewPassword: ''
  })
  resetFormRef.value?.clearValidate()
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    // 根据是否为编辑模式选择不同的API
    const apiCall = isEdit.value
      ? passwordManagementApi.updatePassword
      : passwordManagementApi.createPassword

    const response = await apiCall({
      passwordType: form.passwordType,
      password: form.password,
      securityQuestion: form.securityQuestion,
      securityAnswer: form.securityAnswer,
      description: form.description
    })

    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      showCreateDialog.value = false
      resetFormData()
      fetchPasswordList()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(error.response?.data?.message || error.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

// 编辑密码
const editPassword = (row) => {
  isEdit.value = true
  form.passwordType = row.password_type
  form.description = row.description
  form.securityQuestion = row.security_question
  // 密码和密保答案需要重新输入
  form.password = ''
  form.confirmPassword = ''
  form.securityAnswer = ''
  showCreateDialog.value = true
}

// 删除密码
const deletePassword = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 "${getPasswordTypeName(row.password_type)}" 密码配置吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await passwordManagementApi.deletePassword(row.password_type)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      fetchPasswordList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 切换状态
const toggleStatus = async (row) => {
  try {
    const response = await passwordManagementApi.togglePasswordStatus(row.id)
    if (response.code === 200) {
      ElMessage.success(`${row.is_active ? '启用' : '禁用'}成功`)
      fetchPasswordList()
    }
  } catch (error) {
    console.error('切换状态失败:', error)
    ElMessage.error('操作失败')
    // 恢复原状态
    row.is_active = row.is_active ? 0 : 1
  }
}

// 显示重置密码对话框
const showResetDialog = (row) => {
  resetForm.id = row.id
  resetForm.passwordType = row.password_type
  resetForm.securityQuestion = row.security_question
  resetForm.securityAnswer = ''
  resetForm.newPassword = ''
  resetForm.confirmNewPassword = ''
  showResetPasswordDialog.value = true
}

// 提交重置密码
const submitReset = async () => {
  try {
    await resetFormRef.value.validate()
    resetting.value = true

    const response = await passwordManagementApi.resetPasswordBySecurityAnswer({
      passwordType: resetForm.passwordType,
      securityAnswer: resetForm.securityAnswer,
      newPassword: resetForm.newPassword
    })

    if (response.code === 200) {
      ElMessage.success('密码重置成功')
      showResetPasswordDialog.value = false
      resetResetFormData()
      fetchPasswordList()
    }
  } catch (error) {
    console.error('重置失败:', error)
    ElMessage.error('重置失败，请检查密保答案是否正确')
  } finally {
    resetting.value = false
  }
}

// 表格选择
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 批量操作 - 基于后端单个API循环调用
const batchToggleStatus = async (status) => {
  try {
    const ids = selectedRows.value.map(row => row.id)
    const results = await passwordManagementApi.batchToggleStatus(ids)

    // 分析结果
    const successResults = results.filter(r => r.success)
    const failResults = results.filter(r => !r.success)

    if (failResults.length === 0) {
      ElMessage.success(`批量状态切换成功，共处理 ${successResults.length} 个配置`)
    } else if (successResults.length === 0) {
      ElMessage.error(`批量状态切换失败，所有 ${failResults.length} 个配置都失败了`)
    } else {
      ElMessage.warning(`批量操作完成：成功 ${successResults.length} 个，失败 ${failResults.length} 个`)
      // 显示失败详情
      failResults.forEach(result => {
        console.error(`ID ${result.id} 操作失败:`, result.message)
      })
    }

    fetchPasswordList()
    selectedRows.value = []
  } catch (error) {
    console.error('批量操作失败:', error)
    ElMessage.error('批量操作失败')
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个密码配置吗？此操作不可恢复！`,
      '确认批量删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const passwordTypes = selectedRows.value.map(row => row.password_type)
    const results = await passwordManagementApi.batchDeletePasswords(passwordTypes)

    // 分析结果
    const successResults = results.filter(r => r.success)
    const failResults = results.filter(r => !r.success)

    if (failResults.length === 0) {
      ElMessage.success(`批量删除成功，共删除 ${successResults.length} 个配置`)
    } else if (successResults.length === 0) {
      ElMessage.error(`批量删除失败，所有 ${failResults.length} 个配置都删除失败`)
    } else {
      ElMessage.warning(`批量删除完成：成功 ${successResults.length} 个，失败 ${failResults.length} 个`)
      // 显示失败详情
      failResults.forEach(result => {
        console.error(`类型 ${result.type} 删除失败:`, result.message)
      })
    }

    fetchPasswordList()
    selectedRows.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 生命周期
onMounted(() => {
  fetchPasswordList()
})

// 更新对话框关闭事件
const updateDialogClose = () => {
  resetFormData()
}

const updateResetDialogClose = () => {
  resetResetFormData()
}
</script>

<style scoped>
.password-management {
  padding: 20px;
}

.main-card {
  max-width: 1400px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.card-header > div {
  display: flex;
  gap: 12px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

/* 统计卡片样式 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.stat-content {
  padding: 10px 0;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  color: #409EFF;
  opacity: 0.3;
}

.stat-icon.active {
  color: #67C23A;
}

.stat-icon.recent {
  color: #E6A23C;
}

/* 表格样式 */
.table-container {
  margin-top: 20px;
}

.text-gray {
  color: #909399;
}

/* 批量操作样式 */
.batch-actions {
  margin-top: 15px;
}

.batch-actions .el-alert {
  border-radius: 6px;
}

.batch-actions .el-alert__content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.batch-actions .el-button {
  margin-left: 0;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .password-management {
    padding: 10px;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  .card-header > div {
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 8px;
  }
  .el-button {
    width: 100%;
    min-width: unset;
  }
  .batch-actions .el-alert__content {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
}

/* 状态开关样式 */
.el-switch {
  --el-switch-on-color: #67C23A;
  --el-switch-off-color: #DCDFE6;
}

/* 标签样式 */
.el-tag {
  font-weight: 500;
}

/* 按钮组样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 对话框样式 */
.el-dialog {
  border-radius: 8px;
}

.el-dialog__header {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #EBEEF5;
}

.el-dialog__body {
  padding: 20px;
}

.el-dialog__footer {
  padding: 10px 20px 20px;
  border-top: 1px solid #EBEEF5;
}

/* 表格行悬停效果 */
.el-table__row:hover {
  background-color: #F5F7FA;
}

/* 选择框样式 */
.el-table__header .el-checkbox {
  --el-checkbox-checked-bg-color: #409EFF;
}

/* 加载状态样式 */
.el-loading-mask {
  border-radius: 6px;
}

/* 空状态样式 */
.el-table__empty-block {
  padding: 40px 0;
}

.el-table__empty-text {
  color: #909399;
  font-size: 14px;
}
</style>
