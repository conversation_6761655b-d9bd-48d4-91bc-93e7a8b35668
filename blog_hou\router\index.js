const Router = require("koa-router");
const router = new Router();

const users = require("./user/user");
const logs = require("./system/logs");
const articles = require("./article/articles");
const upload = require("./file/upload");
const videos = require("./media/media");
const mediaFeatures = require("./media/mediaFeatures");
const bigModel = require("./ai/bigmodel");
const show = require("./article/showwall");
const daosql = require("./system/daosql");
const jiami = require("./media/jiamitupian");
const jiamiVideos = require("./media/shipingjiami");
const resource = require("./article/resource");
const friend = require("./user/friend");
const bugsend = require("./creative/bugsend");
const create = require("./creative/create");
const music = require("./media/music");
const resourceDir = require("./file/resources");
const dashboard = require("./system/dashboard");
const passwordManage = require("./system/passwordManage");
const viewHistory = require("./system/viewHistory");
const articleLikes = require("./article/articleLikes");
const articleFavorites = require("./article/articleFavorites");
const aiWritingRouter = require("./ai/aiWritingRouter");
const ollamaManager = require("./ai/ollamaManager");
const systemMonitor = require("./system/systemMonitor");
const userLevelManage = require("./system/userLevelManage");

const websocketManagerRouter = require("./chat/websocketManager");
const webrtcRouter = require("./communication/webrtc");
const fileManager = require("./file/fileManager");
const performanceRouter = require("./system/performance");

// 注册用户管理路由
router.use("/user", users.routes(), users.allowedMethods());
// 输出日志
router.use("/logs", logs.routes(), logs.allowedMethods());
// articles
router.use("/articles", articles.routes(), articles.allowedMethods());
// 上传文件
router.use("/upload", upload.routes(), upload.allowedMethods());
//
router.use("/media", videos.routes(), videos.allowedMethods());
// 媒体功能（喜欢、收藏、播放历史等）
router.use("/media-features", mediaFeatures.routes(), mediaFeatures.allowedMethods());
// 视频管理
const videoManagement = require("./video/videoManagement");
router.use("/video-management", videoManagement.routes(), videoManagement.allowedMethods());
// 调用大模型
router.use("/bigmodel", bigModel.routes(), bigModel.allowedMethods());
// 显示墙
router.use("/showwall", show.routes(), show.allowedMethods());
// 导出数据
router.use("/daosql", daosql.routes(), daosql.allowedMethods());
// 加密图片
router.use("/jiami", jiami.routes(), jiami.allowedMethods());
// 加密视频
router.use("/jiamivideos", jiamiVideos.routes(), jiamiVideos.allowedMethods());
// 资源管理
router.use("/resource", resource.routes(), resource.allowedMethods());
// 好友管理
router.use("/friend", friend.routes(), friend.allowedMethods());
// bug 管理
router.use("/bugsend", bugsend.routes(), bugsend.allowedMethods());
// 创意管理
router.use("/creative", create.routes(), create.allowedMethods());
// 音乐管理
router.use("/music", music.routes(), music.allowedMethods());
// 资源目录
router.use("/resourcesdir", resourceDir.routes(), resourceDir.allowedMethods());
// 仪表盘
router.use("/dashboard", dashboard.routes(), dashboard.allowedMethods());
// 密码管理
router.use("/password", passwordManage.routes(), passwordManage.allowedMethods());
// AI写作助手
router.use("/ai-writing", aiWritingRouter.routes(), aiWritingRouter.allowedMethods());
// Ollama管理
router.use("/ollama", ollamaManager.routes(), ollamaManager.allowedMethods());

// WebSocket管理
router.use("/websocket", websocketManagerRouter.routes(), websocketManagerRouter.allowedMethods());
// WebRTC视频通话
router.use("/webrtc", webrtcRouter.routes(), webrtcRouter.allowedMethods());
// 文件管理
router.use("/file-manager", fileManager.routes(), fileManager.allowedMethods());
// 浏览记录
router.use("/view-history", viewHistory.routes(), viewHistory.allowedMethods());
// 文章点赞
router.use("/article-likes", articleLikes.routes(), articleLikes.allowedMethods());
// 文章收藏
router.use("/article-favorites", articleFavorites.routes(), articleFavorites.allowedMethods());
// 系统监控
router.use("/system-monitor", systemMonitor.routes(), systemMonitor.allowedMethods());
// 用户等级管理
router.use("/user-level", userLevelManage.routes(), userLevelManage.allowedMethods());
// 性能监控
router.use("/performance", performanceRouter.routes(), performanceRouter.allowedMethods());
// /chat/history
// 重定向示例（如果需要）
// router.redirect("/", "/home");

module.exports = router;
