const db = require("../utils/db");

async function checkFilesTable() {
  try {
    console.log("🔍 检查 files 表结构...\n");

    // 查看表结构
    const structure = await db.query("DESCRIBE files");
    console.log("📋 files 表字段:");
    structure.forEach((field, index) => {
      console.log(`  ${index + 1}. ${field.Field} (${field.Type}) ${field.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${field.Key ? `[${field.Key}]` : ''}`);
    });

    // 查看前几条记录
    console.log("\n📄 前5条记录:");
    const records = await db.query("SELECT * FROM files LIMIT 5");
    console.log(records);

    console.log("\n✅ 检查完成");
    
  } catch (error) {
    console.error("❌ 检查失败:", error);
  } finally {
    process.exit(0);
  }
}

// 运行检查
checkFilesTable();
