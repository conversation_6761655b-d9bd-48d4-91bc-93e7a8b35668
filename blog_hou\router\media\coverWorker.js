const { parentPort } = require('worker_threads');
const { exec } = require('child_process');
const fs = require('fs');

parentPort.on('message', async (task) => {
  const { ffmpegPath, videoPath, coverPath, timestamp = "00:00:20", quality = 80 } = task;
  
  try {
    // 检查输入文件是否存在
    if (!fs.existsSync(videoPath)) {
      throw new Error(`视频文件不存在: ${videoPath}`);
    }

    // 检查ffmpeg是否存在
    if (!fs.existsSync(ffmpegPath)) {
      throw new Error(`FFmpeg不存在: ${ffmpegPath}`);
    }

    // 构建ffmpeg命令
    const command = `"${ffmpegPath}" -y -i "${videoPath}" -ss ${timestamp} -vframes 1 -q:v ${quality} "${coverPath}"`;
    
    exec(command, { timeout: 30000 }, (err, stdout, stderr) => {
      if (err) {
        console.error(`FFmpeg错误: ${err.message}`);
        console.error(`FFmpeg stderr: ${stderr}`);
        parentPort.postMessage({ 
          coverPath, 
          success: false, 
          error: err.message 
        });
        return;
      }

      // 检查生成的封面文件
      if (fs.existsSync(coverPath)) {
        parentPort.postMessage({ 
          coverPath, 
          success: true, 
          error: null 
        });
      } else {
        parentPort.postMessage({ 
          coverPath, 
          success: false, 
          error: "封面文件生成失败" 
        });
      }
    });
  } catch (error) {
    parentPort.postMessage({ 
      coverPath, 
      success: false, 
      error: error.message 
    });
  }
});