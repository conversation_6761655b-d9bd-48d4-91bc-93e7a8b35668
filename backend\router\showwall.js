const Koa = require("koa");
const Router = require("koa-router");
const path = require("path");
const fs = require("fs");

const app = new Koa();
const show = new Router();

const imageDir = path.join(__dirname, "../public/images");
const baseUrl = "http://192.168.31.222:3000";

// 图片列表接口，支持分页，每页默认20张
show.post("/images", async (ctx) => {
  const { page = 1, limit = 20 } = ctx.request.body;
  const pageNum = parseInt(page, 10);
  const limitNum = parseInt(limit, 10);

  // 读取所有文件名
  const files = fs.readdirSync(imageDir);

  // 计算分页起止索引
  const startIndex = (pageNum - 1) * limitNum;
  const endIndex = startIndex + limitNum;

  // 取分页数据
  const pagedFiles = files.slice(startIndex, endIndex);

  // 返回带完整路径的数组（前端直接可用）
  ctx.body = pagedFiles.map((file) => `${baseUrl}/images/${file}`);
});

// 新增：受控图片访问接口  images
show.get("/images/:filename", async (ctx) => {
  const filePath = path.join(imageDir, ctx.params.filename);
  if (fs.existsSync(filePath)) {
    ctx.set("Access-Control-Allow-Origin", "*");
    ctx.type = path.extname(filePath);
    ctx.body = fs.createReadStream(filePath);
    console.log("访问图片：" + filePath);
  } else {
    ctx.status = 404;
    ctx.body = "Not Found";
  }
});

module.exports = show;
