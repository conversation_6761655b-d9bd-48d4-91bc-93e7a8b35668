<template>
  <div class="chat-box">
    <h3 class="chat-title">公共聊天室</h3>
    <div class="messages" ref="messageContainer">
      <div v-for="(msg, idx) in messages" :key="idx" class="message-item" :class="{ self: msg.id === userId, system: msg.id === 'system' }">
        <el-avatar :size="32" :src="msg.avatar || defaultAvatar" />
        <div class="message-content">
          <div class="meta">
            <span class="username">{{ msg.nickname }}</span>
            <span class="time">{{ formatTime(msg.time) }}</span>
          </div>
          <div class="text" v-html="parseEmoji(msg.message)"></div>
        </div>
      </div>
    </div>
    <div class="input-area">
      <el-input
        v-model="input"
        ref="inputRef"
        @keyup.enter.native="sendMessage"
        placeholder="输入消息，回车发送😊"
        clearable
        :disabled="sending"
      />
      <el-button type="primary" @click="sendMessage" :loading="sending">发送</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { GetChatRecordApi, SaveChatRecordApi, GetProfileApi } from '@/utils/api'
import { useRoute } from 'vue-router'

const route = useRoute()
const userId = localStorage.getItem('id') || '1'
const username = localStorage.getItem('username') || '系统'
const defaultAvatar = ref('https://placekitten.com/32/32')
const techStack = ref([])

async function getProfile() {
  const res = await GetProfileApi({ id: userId })
  if (res && res.user) {
    defaultAvatar.value = `http://**************:3000/avatars/${res.user.avatar}`
    techStack.value = JSON.parse(res.user.tech_tags || '[]')
  }
}

const input = ref('')
const messages = ref([])
const ws = ref(null)
const messageContainer = ref(null)
const inputRef = ref(null)
let sending = false
let reconnectTimer = null

// 加载历史聊天记录
async function loadHistory() {
  const res = await GetChatRecordApi(0, 0)
  messages.value = (res?.data || []).reverse()
  scrollToBottom()
}

function formatTime(ts) {
  const date = new Date(ts)
  const now = new Date()
  if (now.toDateString() === date.toDateString()) {
    return `${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`
  }
  return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`
}

function parseEmoji(text) {
  return text.replace(/:\)/g, '😊').replace(/:\(/g, '😢')
}

async function sendMessage() {
  if (sending || !input.value.trim()) return
  sending = true
  const msg = {
    id: userId,
    nickname: username,
    avatar: defaultAvatar.value,
    message: input.value,
    time: Date.now()
  }
  try {
    ws.value?.send(JSON.stringify(msg))
    await SaveChatRecordApi(0, 0, input.value)
    input.value = ''
    nextTick(() => inputRef.value?.focus())
  } finally {
    sending = false
  }
}

function scrollToBottom() {
  nextTick(() => {
    if (messageContainer.value) {
      messageContainer.value.scrollTop = messageContainer.value.scrollHeight
    }
  })
}

function connectWS() {
  ws.value = new WebSocket('ws://**************:3000')
  ws.value.onopen = () => {
    ElMessage.success('已连接到聊天室')
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
  }
  ws.value.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      messages.value.push(data)
      scrollToBottom()
    } catch {
      messages.value.push({
        id: 'system',
        nickname: '系统',
        avatar: '',
        message: event.data,
        time: Date.now()
      })
      scrollToBottom()
    }
  }
  ws.value.onclose = () => {
    ElMessage.warning('连接已断开，正在尝试重连...')
    reconnectTimer = setTimeout(connectWS, 2000)
  }
  ws.value.onerror = () => {
    ws.value?.close()
  }
}

onMounted(async () => {
  await getProfile()
  await loadHistory()
  connectWS()
  nextTick(() => inputRef.value?.focus())
})

onBeforeUnmount(() => {
  ws.value?.close()
  if (reconnectTimer) clearTimeout(reconnectTimer)
})
</script>

<style scoped>
.chat-box {
  max-width: 600px;
  margin: auto;
}

.chat-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}

.messages {
  height: 300px;
  overflow-y: auto;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 8px;
}

.message-item {
  display: flex;
  margin-bottom: 12px;
}

.message-item.self {
  flex-direction: row-reverse;
  text-align: right;
}

.message-item.system .text {
  background: #e6f7ff;
  color: #1890ff;
  font-style: italic;
}

.message-content {
  max-width: 80%;
  margin: 0 10px;
}

.meta {
  font-size: 12px;
  color: #888;
}

.username {
  font-weight: bold;
  margin-right: 5px;
}

.text {
  background: #fff;
  padding: 8px 12px;
  border-radius: 16px;
  margin-top: 2px;
  word-break: break-word;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
}

.input-area {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}
</style>