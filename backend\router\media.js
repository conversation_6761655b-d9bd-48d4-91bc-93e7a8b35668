const fs = require("fs");
const path = require("path");
const Router = require("koa-router");
const mime = require("mime-types");
const { handleResponse } = require("./responseHandler");
const { promisify } = require("util");
const exec = promisify(require("child_process").exec);

const videoRouter = new Router();
const videoDir = path.join(__dirname, "../public/media");
const coverDir = path.join(videoDir, "covers");
const ffmpegPath = "D:\\ffmpeg-7.0.2-full_build\\bin\\ffmpeg.exe"; // 更新为你的路径

// 确保封面目录存在
fs.promises.mkdir(coverDir, { recursive: true }).catch(console.error);

// 判断是否为支持的视音频文件
const isVideoFile = (file) => [".mp4", ".avi", ".mkv"].includes(path.extname(file).toLowerCase());

// 列出视频文件
videoRouter.get("/list", async (ctx) => {
  try {
    const files = await fs.promises.readdir(videoDir);
    const media_list = files.filter(isVideoFile).map((file) => ({ file_name: file }));
    ctx.body = { code: 200, data: { media_list } };
  } catch (err) {
    console.error("读取目录失败:", err);
    ctx.status = 500;
    ctx.body = { code: 500, message: "服务器内部错误", error: err.message };
  }
});

// 视频流接口 (支持 Range 请求)
videoRouter.get("/videos/:filename", async (ctx) => {
  const videoPath = path.join(videoDir, ctx.params.filename);
  if (!fs.existsSync(videoPath)) {
    return handleResponse(ctx, 404, { error: "视频文件不存在" });
  }

  try {
    const stat = await fs.promises.stat(videoPath);
    const fileSize = stat.size;
    const range = ctx.headers.range;
    const mimeType = mime.lookup(videoPath) || "application/octet-stream";

    let start = 0, end = fileSize - 1;
    let status = 200;
    const headers = {
      "Content-Type": mimeType,
      "Accept-Ranges": "bytes",
      "Content-Length": fileSize,
    };

    if (range) {
      const [startStr, endStr] = range.replace(/bytes=/, "").split("-");
      start = parseInt(startStr, 10);
      end = endStr ? parseInt(endStr, 10) : end;
      if (isNaN(start) || isNaN(end) || start > end) {
        return handleResponse(ctx, 416, { error: "无效的范围请求" });
      }
      status = 206;
      headers["Content-Range"] = `bytes ${start}-${end}/${fileSize}`;
      headers["Content-Length"] = end - start + 1;
    }

    ctx.status = status;
    ctx.set(headers);
    ctx.body = fs.createReadStream(videoPath, { start, end });
  } catch (err) {
    console.error("视频流错误:", err);
    handleResponse(ctx, 500, { error: "视频读取失败" });
  }
});

// 生成视频封面并返回列表
videoRouter.get("/listpic", async (ctx) => {
  try {
    const files = await fs.promises.readdir(videoDir);
    const media_list = [];

    for (const file of files) {
      if (!isVideoFile(file)) continue;
      const coverName = `${path.basename(file, path.extname(file))}.jpg`;
      const coverPath = path.join(coverDir, coverName);
      const videoPath = path.join(videoDir, file);

      if (!fs.existsSync(coverPath)) {
        try {
          // 在视频 10 秒处生成一帧封面
          await exec(`"${ffmpegPath}" -y -i "${videoPath}" -ss 00:00:5 -vframes 1 "${coverPath}"`);
          console.log("生成封面:", coverPath);
        } catch (e) {
          console.warn("生成封面失败:", file, e.message);
        }
      }

      media_list.push({
        file_name: file,
        cover: `http://192.168.31.222:3000/media/covers/${coverName}`,
      });
    }

    ctx.body = { code: 200, data: { media_list } };
  } catch (err) {
    console.error("封面列表接口错误:", err);
    ctx.status = 500;
    ctx.body = { code: 500, message: "服务器内部错误", error: err.message };
  }
});

module.exports = videoRouter;
