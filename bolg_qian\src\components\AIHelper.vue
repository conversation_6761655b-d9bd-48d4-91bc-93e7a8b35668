<template>
  <div class="ai-helper">
    <!-- AI助手触发按钮 -->
    <el-button 
      type="primary" 
      circle 
      size="large"
      class="ai-trigger-btn"
      @click="showPanel = true"
      v-if="!showPanel"
    >
      🤖
    </el-button>

    <!-- AI助手面板 -->
    <el-drawer
      v-model="showPanel"
      title="🤖 AI智能助手"
      direction="rtl"
      size="400px"
    >
      <div class="ai-content">
        <!-- AI状态 -->
        <div class="ai-status">
          <el-tag :type="aiStatus === 'online' ? 'success' : 'danger'" size="small">
            {{ aiStatus === 'online' ? '🟢 AI在线' : '🔴 AI离线' }}
          </el-tag>
          <span class="model-name">qwen2.5:0.5b</span>
        </div>

        <!-- 功能选项卡 -->
        <el-tabs v-model="activeTab">
          <!-- 智能聊天 -->
          <el-tab-pane label="💬 聊天" name="chat">
            <div class="chat-section">
              <div class="chat-messages" ref="messagesContainer">
                <div 
                  v-for="(msg, index) in messages" 
                  :key="index"
                  :class="['message', msg.role]"
                >
                  <div class="message-content">{{ msg.content }}</div>
                  <div class="message-time">{{ formatTime(msg.time) }}</div>
                </div>
              </div>
              
              <div class="chat-input">
                <el-input
                  v-model="chatInput"
                  type="textarea"
                  :rows="2"
                  placeholder="输入你的问题..."
                  @keydown.enter.ctrl="sendMessage"
                />
                <el-button 
                  type="primary" 
                  @click="sendMessage"
                  :loading="chatLoading"
                  style="margin-top: 8px; width: 100%;"
                >
                  发送 (Ctrl+Enter)
                </el-button>
              </div>
            </div>
          </el-tab-pane>

          <!-- 内容工具 -->
          <el-tab-pane label="🛠️ 工具" name="tools">
            <div class="tools-section">
              <!-- 标签生成 -->
              <el-card class="tool-card" shadow="hover">
                <template #header>
                  <span>🏷️ 标签生成</span>
                </template>
                <el-input
                  v-model="tagTitle"
                  placeholder="文章标题（可选）"
                  style="margin-bottom: 8px;"
                />
                <el-input
                  v-model="tagContent"
                  type="textarea"
                  :rows="3"
                  placeholder="文章内容..."
                />
                <el-button 
                  type="primary" 
                  @click="generateTags"
                  :loading="tagLoading"
                  style="margin-top: 8px; width: 100%;"
                >
                  生成标签
                </el-button>
                <div v-if="generatedTags.length > 0" class="result-tags">
                  <el-tag 
                    v-for="tag in generatedTags" 
                    :key="tag"
                    style="margin: 4px;"
                    @click="copyText(tag)"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </el-card>

              <!-- 内容摘要 -->
              <el-card class="tool-card" shadow="hover">
                <template #header>
                  <span>📝 内容摘要</span>
                </template>
                <el-input
                  v-model="summaryContent"
                  type="textarea"
                  :rows="4"
                  placeholder="输入要总结的内容..."
                />
                <el-button 
                  type="primary" 
                  @click="generateSummary"
                  :loading="summaryLoading"
                  style="margin-top: 8px; width: 100%;"
                >
                  生成摘要
                </el-button>
                <div v-if="generatedSummary" class="result-summary">
                  <el-input
                    v-model="generatedSummary"
                    type="textarea"
                    :rows="3"
                    readonly
                  />
                  <el-button 
                    size="small" 
                    @click="copyText(generatedSummary)"
                    style="margin-top: 4px;"
                  >
                    复制
                  </el-button>
                </div>
              </el-card>

              <!-- 标题生成 -->
              <el-card class="tool-card" shadow="hover">
                <template #header>
                  <span>📰 标题生成</span>
                </template>
                <el-input
                  v-model="titleContent"
                  type="textarea"
                  :rows="3"
                  placeholder="输入文章内容..."
                />
                <el-button 
                  type="primary" 
                  @click="generateTitles"
                  :loading="titleLoading"
                  style="margin-top: 8px; width: 100%;"
                >
                  生成标题
                </el-button>
                <div v-if="generatedTitles.length > 0" class="result-titles">
                  <div 
                    v-for="(title, index) in generatedTitles" 
                    :key="index"
                    class="title-item"
                    @click="copyText(title)"
                  >
                    {{ index + 1 }}. {{ title }}
                  </div>
                </div>
              </el-card>
            </div>
          </el-tab-pane>

          <!-- 内容审核 -->
          <el-tab-pane label="🔍 审核" name="moderate">
            <div class="moderate-section">
              <el-input
                v-model="moderateContent"
                type="textarea"
                :rows="6"
                placeholder="输入要审核的内容..."
              />
              <el-button 
                type="primary" 
                @click="moderateText"
                :loading="moderateLoading"
                style="margin-top: 8px; width: 100%;"
              >
                开始审核
              </el-button>
              
              <div v-if="moderationResult" class="moderation-result">
                <el-alert
                  :title="moderationResult.safe ? '✅ 内容安全' : '⚠️ 内容存在问题'"
                  :type="moderationResult.safe ? 'success' : 'warning'"
                  :description="moderationResult.suggestion"
                  show-icon
                />
                <div class="score-display">
                  安全评分: {{ moderationResult.score }}/100
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { aiApi } from '../utils/aiApi'

// 响应式数据
const showPanel = ref(false)
const activeTab = ref('chat')
const aiStatus = ref('offline')

// 聊天相关
const messages = ref([
  {
    role: 'assistant',
    content: '你好！我是AI助手，有什么可以帮助你的吗？',
    time: new Date()
  }
])
const chatInput = ref('')
const chatLoading = ref(false)
const messagesContainer = ref(null)

// 工具相关
const tagTitle = ref('')
const tagContent = ref('')
const tagLoading = ref(false)
const generatedTags = ref([])

const summaryContent = ref('')
const summaryLoading = ref(false)
const generatedSummary = ref('')

const titleContent = ref('')
const titleLoading = ref(false)
const generatedTitles = ref([])

const moderateContent = ref('')
const moderateLoading = ref(false)
const moderationResult = ref(null)

// 生命周期
onMounted(() => {
  checkAIStatus()
})

// 方法
const checkAIStatus = async () => {
  try {
    const response = await aiApi.getStatus()
    aiStatus.value = response.data.status
  } catch (error) {
    console.error('检查AI状态失败:', error)
    aiStatus.value = 'offline'
  }
}

// 聊天功能
const sendMessage = async () => {
  if (!chatInput.value.trim() || chatLoading.value) return
  
  const userMessage = {
    role: 'user',
    content: chatInput.value,
    time: new Date()
  }
  
  messages.value.push(userMessage)
  const message = chatInput.value
  chatInput.value = ''
  chatLoading.value = true
  
  try {
    const response = await aiApi.chat({
      message,
      history: messages.value.slice(-10)
    })
    
    messages.value.push({
      role: 'assistant',
      content: response.data.response,
      time: new Date()
    })
    
    nextTick(() => {
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
      }
    })
    
  } catch (error) {
    console.error('聊天失败:', error)
    ElMessage.error('聊天失败，请稍后重试')
  } finally {
    chatLoading.value = false
  }
}

// 标签生成
const generateTags = async () => {
  if (!tagContent.value.trim()) {
    ElMessage.warning('请输入文章内容')
    return
  }
  
  tagLoading.value = true
  try {
    const response = await aiApi.generateTags({
      title: tagTitle.value,
      content: tagContent.value
    })
    
    generatedTags.value = response.data.tags
    ElMessage.success('标签生成成功')
  } catch (error) {
    console.error('标签生成失败:', error)
    ElMessage.error('标签生成失败')
  } finally {
    tagLoading.value = false
  }
}

// 摘要生成
const generateSummary = async () => {
  if (!summaryContent.value.trim()) {
    ElMessage.warning('请输入要总结的内容')
    return
  }
  
  summaryLoading.value = true
  try {
    const response = await aiApi.generateSummary({
      content: summaryContent.value,
      maxLength: 200
    })
    
    generatedSummary.value = response.data.summary
    ElMessage.success('摘要生成成功')
  } catch (error) {
    console.error('摘要生成失败:', error)
    ElMessage.error('摘要生成失败')
  } finally {
    summaryLoading.value = false
  }
}

// 标题生成
const generateTitles = async () => {
  if (!titleContent.value.trim()) {
    ElMessage.warning('请输入文章内容')
    return
  }
  
  titleLoading.value = true
  try {
    const response = await aiApi.generateTitles({
      content: titleContent.value,
      count: 5
    })
    
    generatedTitles.value = response.data.titles
    ElMessage.success('标题生成成功')
  } catch (error) {
    console.error('标题生成失败:', error)
    ElMessage.error('标题生成失败')
  } finally {
    titleLoading.value = false
  }
}

// 内容审核
const moderateText = async () => {
  if (!moderateContent.value.trim()) {
    ElMessage.warning('请输入要审核的内容')
    return
  }
  
  moderateLoading.value = true
  try {
    const response = await aiApi.moderateContent({
      content: moderateContent.value,
      type: 'article'
    })
    
    moderationResult.value = response.data.moderation
    ElMessage.success('内容审核完成')
  } catch (error) {
    console.error('内容审核失败:', error)
    ElMessage.error('内容审核失败')
  } finally {
    moderateLoading.value = false
  }
}

// 复制文本
const copyText = (text) => {
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success('已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

// 格式化时间
const formatTime = (time) => {
  return new Date(time).toLocaleTimeString()
}
</script>

<style scoped>
.ai-helper {
  position: relative;
}

.ai-trigger-btn {
  position: fixed;
  bottom: 80px;
  right: 30px;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.ai-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.ai-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 8px;
}

.model-name {
  font-size: 12px;
  color: #666;
  font-family: monospace;
}

.chat-section {
  height: 500px;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 12px;
  background: #fafafa;
}

.message {
  margin-bottom: 12px;
}

.message.user {
  text-align: right;
}

.message.user .message-content {
  background: #409eff;
  color: white;
  display: inline-block;
  max-width: 80%;
}

.message.assistant .message-content {
  background: white;
  border: 1px solid #e4e7ed;
  display: inline-block;
  max-width: 80%;
}

.message-content {
  padding: 8px 12px;
  border-radius: 8px;
  word-wrap: break-word;
}

.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
}

.tools-section {
  max-height: 500px;
  overflow-y: auto;
}

.tool-card {
  margin-bottom: 16px;
}

.result-tags, .result-summary, .result-titles {
  margin-top: 12px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.result-tags .el-tag {
  cursor: pointer;
  margin: 2px;
}

.title-item {
  padding: 6px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.title-item:hover {
  background-color: #f5f7fa;
}

.moderate-section {
  max-height: 500px;
  overflow-y: auto;
}

.moderation-result {
  margin-top: 16px;
}

.score-display {
  margin-top: 8px;
  font-weight: bold;
}
</style>
