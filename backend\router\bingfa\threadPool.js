const { Worker } = require("worker_threads");
const path = require("path");

const MAX_WORKERS = require("os").cpus().length || 4;

function createWorker(file, baseDir, action) {
  return new Promise((resolve, reject) => {
    const worker = new Worker(path.resolve(__dirname, "./encryptWorker.js"), {
      workerData: { file, baseDir, action },
    });

    worker.on("message", (msg) => {
      if (msg.status === "done") {
        resolve(msg.file);
      } else if (msg.status === "error") {
        reject(new Error(msg.error));
      } else if (msg.status === "progress" && typeof threadPool.onProgress === "function") {
        threadPool.onProgress(msg.data);
      }
    });

    worker.on("error", reject);
    worker.on("exit", (code) => {
      if (code !== 0) reject(new Error(`Worker退出，退出码：${code}`));
    });
  });
}

async function processFiles(files, baseDir, action, onProgress) {
  threadPool.onProgress = onProgress;

  const results = [];
  const queue = [...files];
  const activeWorkers = [];

  while (queue.length > 0 || activeWorkers.length > 0) {
    while (activeWorkers.length < MAX_WORKERS && queue.length > 0) {
      const file = queue.shift();
      const p = createWorker(file, baseDir, action).then((res) => {
        results.push(res);
        activeWorkers.splice(activeWorkers.indexOf(p), 1);
      });
      activeWorkers.push(p);
    }
    await Promise.race(activeWorkers);
  }

  return results;
}

const threadPool = {
  processFiles,
  onProgress: null,
};

module.exports = threadPool;
