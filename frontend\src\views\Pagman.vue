<template>
  <el-table :data="deletedArticles">
    <el-table-column prop="title" label="标题" />
    <el-table-column label="操作">
      <template #default="scope">
        <el-button type="success" @click="restoreArticle(scope.row.id)">恢复</el-button>
        <el-button type="danger" @click="permanentDeleteArticle(scope.row.id)" style="margin-left: 8px">永久删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { RestoreArticleApi, DeleteRestoredArticleApi, DeleteArticleApi } from "../utils/api";

const deletedArticles = ref<any[]>([]);

async function fetchDeletedArticles(user_id: string) {
  const res = await DeleteRestoredArticleApi({ user_id });
  if (res?.data) {
    deletedArticles.value = res.data;
  }
}

async function restoreArticle(id: number) {
  const res = await RestoreArticleApi({ id });
  if (res?.message === "文章恢复成功") {
    ElMessage.success("恢复成功");
    // 恢复后刷新列表
    fetchDeletedArticles(localStorage.getItem("id")!);
  } else {
    ElMessage.error("恢复失败");
  }
}

import { ElMessageBox } from "element-plus";
async function permanentDeleteArticle(id: number) {
  try {
    await ElMessageBox.confirm("确定永久删除该文章吗?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    // 用户点击了确定，再发请求
    const res = await DeleteArticleApi({ id });
    if (res?.code === 200) {
      ElMessage.success("永久删除成功");
      fetchDeletedArticles(localStorage.getItem("id")!);
    } else {
      ElMessage.error("永久删除失败");
    }
  } catch {
    // 用户点击了取消
    ElMessage.info("已取消永久删除");
  }
}
// 页面加载时获取已删除文章
fetchDeletedArticles(localStorage.getItem("id")!);
</script>
<style scoped lang="less"></style>