<template>
  <div class="webrtc-manager">
    <el-card class="manager-card">
      <template #header>
        <div class="card-header">
          <span>WebRTC视频通话服务管理</span>
          <el-button 
            type="primary" 
            @click="refreshStatus"
            :loading="loading"
            size="small"
          >
            刷新状态
          </el-button>
        </div>
      </template>

      <!-- 服务器状态 -->
      <div class="status-section">
        <h3>服务器状态</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="运行状态">
            <el-tag :type="serverStatus.isStarted ? 'success' : 'danger'">
              {{ serverStatus.isStarted ? '运行中' : '已停止' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="活跃通话">
            {{ serverStatus.stats?.activeCalls || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="运行时长">
            {{ formatUptime(serverStatus.stats?.uptime || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="错误次数">
            {{ serverStatus.stats?.errors || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 控制按钮 -->
      <div class="control-section">
        <h3>服务控制</h3>
        <div class="control-buttons">
          <el-button 
            type="success" 
            @click="startServer"
            :loading="starting"
            :disabled="serverStatus.isStarted"
            size="large"
          >
            <el-icon><VideoCamera /></el-icon>
            启动WebRTC服务器
          </el-button>
          
          <el-button 
            type="danger" 
            @click="stopServer"
            :loading="stopping"
            :disabled="!serverStatus.isStarted"
            size="large"
          >
            <el-icon><Close /></el-icon>
            停止WebRTC服务器
          </el-button>
        </div>
      </div>

      <!-- 活跃通话列表 -->
      <div class="calls-section" v-if="serverStatus.isStarted">
        <h3>活跃通话</h3>
        <el-table :data="activeCalls" style="width: 100%">
          <el-table-column prop="callId" label="通话ID" width="200" />
          <el-table-column prop="caller" label="发起者" />
          <el-table-column prop="callee" label="接听者" />
          <el-table-column prop="type" label="类型">
            <template #default="scope">
              <el-tag :type="scope.row.type === 'video' ? 'primary' : 'info'">
                {{ scope.row.type === 'video' ? '视频' : '音频' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="时长">
            <template #default="scope">
              {{ formatDuration(scope.row.duration) }}
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button 
                type="danger" 
                size="small"
                @click="endCall(scope.row.callId)"
              >
                结束通话
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoCamera, Close } from '@element-plus/icons-vue'
import { webrtcApi } from '@/utils/webrtc-api'

// 响应式数据
const loading = ref(false)
const starting = ref(false)
const stopping = ref(false)
const serverStatus = ref({
  isStarted: false,
  stats: {
    activeCalls: 0,
    uptime: 0,
    errors: 0
  },
  activeCalls: []
})
const activeCalls = ref([])

// 定时器
let statusTimer = null

// 获取服务器状态
async function getServerStatus() {
  try {
    const response = await webrtcApi.getStatus()
    if (response && response.code === 200) {
      serverStatus.value = response.data
      activeCalls.value = response.data.activeCalls || []
    }
  } catch (error) {
    console.error('获取WebRTC状态失败:', error)
  }
}

// 刷新状态
async function refreshStatus() {
  loading.value = true
  try {
    await getServerStatus()
    ElMessage.success('状态已刷新')
  } catch (error) {
    ElMessage.error('刷新状态失败')
  } finally {
    loading.value = false
  }
}

// 启动服务器
async function startServer() {
  starting.value = true
  try {
    const response = await webrtcApi.start()
    if (response && response.code === 200) {
      ElMessage.success('WebRTC服务器启动成功')
      await getServerStatus()
    } else {
      ElMessage.error('启动失败: ' + (response.message || '未知错误'))
    }
  } catch (error) {
    console.error('启动WebRTC服务器失败:', error)
    ElMessage.error('启动失败: ' + error.message)
  } finally {
    starting.value = false
  }
}

// 停止服务器
async function stopServer() {
  stopping.value = true
  try {
    const response = await webrtcApi.stop()
    if (response && response.code === 200) {
      ElMessage.success('WebRTC服务器已停止')
      await getServerStatus()
    } else {
      ElMessage.error('停止失败: ' + (response.message || '未知错误'))
    }
  } catch (error) {
    console.error('停止WebRTC服务器失败:', error)
    ElMessage.error('停止失败: ' + error.message)
  } finally {
    stopping.value = false
  }
}

// 结束通话
async function endCall(callId) {
  try {
    const response = await webrtcApi.endCall(callId)
    if (response && response.code === 200) {
      ElMessage.success('通话已结束')
      await getServerStatus()
    } else {
      ElMessage.error('结束通话失败')
    }
  } catch (error) {
    console.error('结束通话失败:', error)
    ElMessage.error('结束通话失败')
  }
}

// 格式化运行时长
function formatUptime(uptime) {
  if (!uptime) return '0秒'
  
  const seconds = Math.floor(uptime / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

// 格式化通话时长
function formatDuration(duration) {
  if (!duration) return '0秒'
  
  const minutes = Math.floor(duration / 60)
  const seconds = duration % 60
  
  if (minutes > 0) {
    return `${minutes}分${seconds}秒`
  } else {
    return `${seconds}秒`
  }
}

// 获取状态类型
function getStatusType(status) {
  switch (status) {
    case 'connected': return 'success'
    case 'calling': 
    case 'ringing': return 'warning'
    case 'ended':
    case 'rejected': return 'info'
    default: return 'danger'
  }
}

// 获取状态文本
function getStatusText(status) {
  switch (status) {
    case 'calling': return '呼叫中'
    case 'ringing': return '振铃中'
    case 'connected': return '通话中'
    case 'ended': return '已结束'
    case 'rejected': return '已拒绝'
    case 'busy': return '忙线'
    case 'no_answer': return '无应答'
    default: return '未知'
  }
}

// 启动定时器
function startStatusTimer() {
  statusTimer = setInterval(() => {
    getServerStatus()
  }, 5000) // 每5秒刷新一次
}

// 停止定时器
function stopStatusTimer() {
  if (statusTimer) {
    clearInterval(statusTimer)
    statusTimer = null
  }
}

// 生命周期
onMounted(() => {
  getServerStatus()
  startStatusTimer()
})

onUnmounted(() => {
  stopStatusTimer()
})
</script>

<style scoped>
.webrtc-manager {
  padding: 20px;
}

.manager-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-section,
.control-section,
.calls-section {
  margin-bottom: 30px;
}

.status-section h3,
.control-section h3,
.calls-section h3 {
  margin-bottom: 15px;
  color: #303133;
}

.control-buttons {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.control-buttons .el-button {
  min-width: 200px;
}
</style>
