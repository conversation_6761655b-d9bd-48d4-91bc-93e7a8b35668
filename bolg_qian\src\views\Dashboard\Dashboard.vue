<template>
  <div class="dashboard-layout">
    <!-- 侧边栏 -->
    <aside class="dashboard-sidebar" :class="{ collapsed: sidebarCollapsed }">
      <!-- 侧边栏头部 -->
      <div class="sidebar-header">
        <div class="logo-section">
          <el-icon class="logo-icon">
            <DataBoard />
          </el-icon>
          <span class="logo-text" v-show="!sidebarCollapsed">管理后台</span>
        </div>
        <el-button class="collapse-btn" :icon="sidebarCollapsed ? Expand : Fold" @click="toggleSidebar" circle
          size="small" />
      </div>

      <!-- 用户信息卡片 -->
      <div class="user-card" v-show="!sidebarCollapsed">
        <div class="user-info">
          <el-avatar :size="40" class="user-avatar">
            {{ username?.charAt(0)?.toUpperCase() }}
          </el-avatar>
          <div class="user-details">
            <div class="user-name">{{ username }}</div>
            <div class="user-role">系统管理员</div>
          </div>
        </div>
        <div class="user-status">
          <el-tag type="success" size="small">在线</el-tag>
        </div>
      </div>

      <!-- 导航菜单 -->
      <nav class="sidebar-nav">
        <el-menu :default-active="currentRoute" class="nav-menu" :collapse="sidebarCollapsed" router unique-opened>
          <!-- 概览仪表盘 -->
          <el-menu-item index="board" :route="{ path: '/index/dashboard/board' }" class="nav-item">
            <el-icon>
              <DataBoard />
            </el-icon>
            <span>概览仪表盘</span>
          </el-menu-item>

          <!-- 监控管理 -->
          <el-sub-menu index="monitor" class="nav-submenu">
            <template #title>
              <el-icon>
                <Monitor />
              </el-icon>
              <span>监控管理</span>
            </template>
            <el-menu-item index="systemMonitor" :route="{ path: '/index/dashboard/systemMonitor' }">
              <el-icon>
                <Monitor />
              </el-icon>
              <span>系统监控</span>
            </el-menu-item>
            <el-menu-item index="logs" :route="{ path: '/index/dashboard/logs' }">
              <el-icon>
                <List />
              </el-icon>
              <span>日志监控</span>
            </el-menu-item>
          </el-sub-menu>
          <!-- 用户管理 -->
          <el-sub-menu index="user" class="nav-submenu">
            <template #title>
              <el-icon>
                <User />
              </el-icon>
              <span>用户管理</span>
            </template>
            <el-menu-item index="intro" :route="{ path: '/index/dashboard/intro' }">
              <el-icon>
                <User />
              </el-icon>
              <span>个人信息</span>
            </el-menu-item>
            <el-menu-item index="passwordManagement" :route="{ path: '/index/dashboard/passwordManagement' }">
              <el-icon>
                <Key />
              </el-icon>
              <span>密码管理</span>
            </el-menu-item>
            <el-menu-item index="userLevelManage" :route="{ path: '/index/dashboard/userLevelManage' }">
              <el-icon>
                <UserFilled />
              </el-icon>
              <span>用户等级</span>
            </el-menu-item>
            <el-menu-item index="friends" :route="{ path: '/index/dashboard/friends' }">
              <el-icon>
                <UserFilled />
              </el-icon>
              <span>好友管理</span>
            </el-menu-item>


          </el-sub-menu>

          <!-- 内容管理 -->
          <el-sub-menu index="content" class="nav-submenu">
            <template #title>
              <el-icon>
                <Document />
              </el-icon>
              <span>文章管理</span>
            </template>
            <el-menu-item index="user-viewhistory" :route="{ path: '/index/dashboard/viewhistory' }">
              <el-icon>
                <Clock />
              </el-icon>
              <span>浏览记录</span>
            </el-menu-item>
            <el-menu-item index="user-likehistory" :route="{ path: '/index/dashboard/likehistory' }">
              <el-icon>
                <StarFilled />
              </el-icon>
              <span>点赞记录</span>
            </el-menu-item>
            <el-menu-item index="user-favoritehistory" :route="{ path: '/index/dashboard/favoritehistory' }">
              <el-icon>
                <Collection />
              </el-icon>
              <span>收藏记录</span>
            </el-menu-item>
            <el-menu-item index="articleManage" :route="{ path: '/index/dashboard/articleManage' }">
              <el-icon>
                <Edit />
              </el-icon>
              <span>文章回收站</span>
            </el-menu-item>
          </el-sub-menu>

          <!-- 文件管理 -->
          <el-sub-menu index="files" class="nav-submenu">
            <template #title>
              <el-icon>
                <Folder />
              </el-icon>
              <span>文件管理</span>
            </template>
            <el-menu-item index="files-browse" :route="{ path: '/index/dashboard/files' }">
              <el-icon>
                <FolderOpened />
              </el-icon>
              <span>文件浏览</span>
            </el-menu-item>
            <el-menu-item index="uploads" :route="{ path: '/index/dashboard/uploads' }">
              <el-icon>
                <Upload />
              </el-icon>
              <span>文件上传</span>
            </el-menu-item>
            <el-menu-item index="fileManager" :route="{ path: '/index/dashboard/fileManager' }">
              <el-icon>
                <FolderOpened />
              </el-icon>
              <span>文件管理器</span>
            </el-menu-item>
            <el-menu-item index="videoManagement" :route="{ path: '/index/dashboard/videoManagement' }">
              <el-icon>
                <VideoPlay />
              </el-icon>
              <span>视频管理</span>
            </el-menu-item>
            <el-menu-item index="share" :route="{ path: '/index/dashboard/share' }">
              <el-icon>
                <Share />
              </el-icon>
              <span>资源分享</span>
            </el-menu-item>
          </el-sub-menu>

          <!-- 安全工具 -->
          <el-sub-menu index="security" class="nav-submenu">
            <template #title>
              <el-icon>
                <Lock />
              </el-icon>
              <span>安全工具</span>
            </template>
            <el-menu-item index="jiami" :route="{ path: '/index/dashboard/jiami' }">
              <el-icon>
                <Picture />
              </el-icon>
              <span>图片加密</span>
            </el-menu-item>
            <el-menu-item index="shipinjiami" :route="{ path: '/index/dashboard/shipinjiami' }">
              <el-icon>
                <VideoPlay />
              </el-icon>
              <span>视频加密</span>
            </el-menu-item>
          </el-sub-menu>

          <!-- 系统工具 -->
          <el-sub-menu index="tools" class="nav-submenu">
            <template #title>
              <el-icon>
                <Tools />
              </el-icon>
              <span>系统工具</span>
            </template>
            <el-menu-item index="effects" :route="{ path: '/index/dashboard/effects' }">
              <el-icon>
                <MagicStick />
              </el-icon>
              <span>特效管理</span>
            </el-menu-item>
            <el-menu-item index="daochusql" :route="{ path: '/index/dashboard/daochusql' }">
              <el-icon>
                <Download />
              </el-icon>
              <span>数据导出</span>
            </el-menu-item>
            <el-menu-item index="fileManagerDebug" :route="{ path: '/index/dashboard/fileManagerDebug' }">
              <el-icon>
                <Setting />
              </el-icon>
              <span>调试工具</span>
            </el-menu-item>
            <el-menu-item index="downloadTest" :route="{ path: '/index/dashboard/downloadTest' }">
              <el-icon>
                <Download />
              </el-icon>
              <span>下载测试</span>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </nav>
    </aside>

    <!-- 主内容区域 -->
    <main class="main-content">
      <!-- 顶部工具栏 -->
      <header class="content-header">
        <div class="header-left">
          <div class="page-title">
            <el-icon class="title-icon">
              <DataBoard />
            </el-icon>
            <span class="title-text">{{ currentPageTitle }}</span>
          </div>
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item>管理后台</el-breadcrumb-item>
            <el-breadcrumb-item v-for="item in breadcrumbItems" :key="item.path">
              {{ item.name }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="header-right">
          <div class="header-actions">
            <!-- 快速操作按钮 -->
            <el-tooltip content="刷新页面" placement="bottom">
              <el-button :icon="Refresh" circle @click="refreshPage" />
            </el-tooltip>
            <el-tooltip content="全屏显示" placement="bottom">
              <el-button :icon="FullScreen" circle @click="toggleFullscreen" />
            </el-tooltip>
            <el-tooltip content="主题切换" placement="bottom">
              <el-button :icon="Moon" circle @click="toggleTheme" />
            </el-tooltip>
          </div>

          <!-- 用户信息 -->
          <div class="user-info">
            <el-dropdown trigger="click" placement="bottom-end" class="user-dropdown-container">
              <div class="user-dropdown">
                <el-avatar :size="32" class="user-avatar">
                  {{ username?.charAt(0)?.toUpperCase() }}
                </el-avatar>
                <span class="username">{{ username }}</span>
                <el-icon class="dropdown-icon">
                  <ArrowDown />
                </el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="custom-dropdown-menu">
                  <el-dropdown-item @click="goToProfile" class="dropdown-item">
                    <el-icon>
                      <User />
                    </el-icon>
                    <span>个人资料</span>
                  </el-dropdown-item>
                  <el-dropdown-item @click="goToSettings" class="dropdown-item">
                    <el-icon>
                      <Setting />
                    </el-icon>
                    <span>系统设置</span>
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="logout" class="dropdown-item logout-item">
                    <el-icon>
                      <SwitchButton />
                    </el-icon>
                    <span>退出登录</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <div class="page-container">
        <keep-alive>
          <router-view v-slot="{ Component }">
            <component :is="Component" />
          </router-view>
        </keep-alive>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  DataBoard,
  User,
  Document,
  Edit,
  Star,
  Folder,
  FolderOpened,
  Upload,
  Share,
  Setting,
  Warning,
  List,
  Tools,
  Picture,
  VideoPlay,
  Download,
  Lock,
  Key,
  UserFilled,
  Monitor,
  Expand,
  Fold,
  Refresh,
  FullScreen,
  Moon,
  ArrowDown,
  SwitchButton,
  Clock,
  StarFilled,
  Collection,
  MagicStick
} from '@element-plus/icons-vue'

const router = useRouter();
const route = useRoute();

// 基础状态
const username = localStorage.getItem('username') || '游客';
const sidebarCollapsed = ref(false);
const currentRoute = ref('board');

// 页面标题映射
const pageTitleMap = {
  'board': '概览仪表盘',
  'systemMonitor': '系统监控',
  'logs': '日志监控',
  'intro': '个人信息',
  'passwordManagement': '密码管理',
  'userLevelManage': '用户等级管理',
  'friends': '好友管理',
  'viewhistory': '浏览记录',
  'likehistory': '点赞记录',
  'favoritehistory': '收藏记录',
  'content-viewhistory': '文章浏览记录',
  'content-likehistory': '文章点赞记录',
  'content-favoritehistory': '文章收藏记录',
  'user-viewhistory': '用户浏览记录',
  'user-likehistory': '用户点赞记录',
  'user-favoritehistory': '用户收藏记录',
  'effects': '特效管理',
  'articleManage': '文章管理',
  'creative': '创意中心',
  'bug': 'Bug管理',
  'files': '文件浏览',
  'files-browse': '文件浏览',
  'uploads': '文件上传',
  'fileManager': '文件管理器',
  'videoManagement': '视频管理',
  'share': '资源分享',
  'jiami': '图片加密',
  'shipinjiami': '视频加密',
  'daochusql': '数据导出',
  'fileManagerDebug': '调试工具',
  'downloadTest': '下载测试'
};

// 计算属性
const currentPageTitle = computed(() => {
  const routeName = route.name || route.path.split('/').pop();
  return pageTitleMap[routeName] || '管理后台';
});

const breadcrumbItems = computed(() => {
  const pathSegments = route.path.split('/').filter(Boolean);
  const items = [];

  if (pathSegments.includes('dashboard')) {
    const lastSegment = pathSegments[pathSegments.length - 1];
    if (lastSegment !== 'dashboard') {
      items.push({
        name: pageTitleMap[lastSegment] || lastSegment,
        path: route.path
      });
    }
  }

  return items;
});

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
};

const refreshPage = () => {
  window.location.reload();
};

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen();
  } else {
    document.exitFullscreen();
  }
};

const toggleTheme = () => {
  ElMessage.info('主题切换功能开发中...');
};

const goToProfile = () => {
  router.push('/index/dashboard/intro');
};

const goToSettings = () => {
  router.push('/index/dashboard/passwordManagement');
};

const logout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '确认退出', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    localStorage.clear();
    router.push('/login');
    ElMessage.success('已退出登录');
  } catch {
    // 用户取消
  }
};

// 生命周期
onMounted(() => {
  // 根据当前路由设置活跃菜单项
  const pathSegments = route.path.split('/');
  const lastSegment = pathSegments[pathSegments.length - 1];

  // 为不同部分使用不同的前缀防止重复激活
  if (route.path.includes('content-')) {
    currentRoute.value = 'content-' + lastSegment;
  } else if (route.path.includes('user-')) {
    currentRoute.value = 'user-' + lastSegment;
  } else if (lastSegment === 'files') {
    currentRoute.value = 'files-browse';
  } else {
    currentRoute.value = lastSegment || 'board';
  }
});
</script>

<style lang="less" scoped>
.dashboard-layout {
  display: flex;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  /* 防止整个布局出现滚动条 */
  background: #f0f2f5;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;

  /* 确保子元素不会溢出 */
  * {
    box-sizing: border-box;
  }
}

/* 侧边栏样式 */
.dashboard-sidebar {
  width: 260px;
  background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
  color: white;
  transition: all 0.3s ease;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* 防止侧边栏出现滚动条 */

  &.collapsed {
    width: 64px;

    .user-card {
      display: none;
    }
  }

  .sidebar-header {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .logo-section {
      display: flex;
      align-items: center;
      gap: 12px;

      .logo-icon {
        font-size: 24px;
        color: #60a5fa;
      }

      .logo-text {
        font-size: 18px;
        font-weight: 700;
        color: white;
      }
    }

    .collapse-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }

  .user-card {
    margin: 20px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);

    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;

      .user-avatar {
        background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
        color: white;
        font-weight: 600;
      }

      .user-details {
        flex: 1;

        .user-name {
          font-weight: 600;
          font-size: 14px;
          color: white;
        }

        .user-role {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
        }
      }
    }

    .user-status {
      text-align: center;
    }
  }

  .sidebar-nav {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    /* 防止水平滚动条 */
    padding: 8px 0;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 2px;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }

    .nav-menu {
      border: none;
      background: transparent;
      width: 100%;

      :deep(.el-menu-item),
      :deep(.el-sub-menu__title) {
        color: rgba(255, 255, 255, 0.8);
        border: none;
        margin: 2px 12px;
        border-radius: 8px;
        transition: all 0.3s ease;
        white-space: nowrap;
        /* 防止文字换行 */
        overflow: hidden;
        /* 隐藏溢出内容 */
        text-overflow: ellipsis;
        /* 显示省略号 */

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: white;
        }
      }

      :deep(.is-active) {
        background: rgba(96, 165, 250, 0.3) !important;
        color: white !important;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 20px;
          background: #60a5fa;
          border-radius: 0 2px 2px 0;
        }
      }

      :deep(.el-sub-menu) {
        .el-menu-item {
          padding-left: 48px !important;
          font-size: 13px;
          color: rgba(255, 255, 255, 0.7);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          &:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
          }
        }

        .el-sub-menu__title {
          &:hover {
            background: rgba(255, 255, 255, 0.1);
          }
        }

        /* 展开状态的子菜单背景 */
        .el-menu {
          background: rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #f0f2f5;
  position: relative;
  /* 为下拉菜单提供定位上下文 */

  .content-header {
    height: 64px;
    background: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-bottom: 1px solid #e5e7eb;

    .header-left {
      display: flex;
      align-items: center;
      gap: 24px;

      .page-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-icon {
          font-size: 20px;
          color: #1e40af;
        }

        .title-text {
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
        }
      }

      .breadcrumb {
        :deep(.el-breadcrumb__item) {
          .el-breadcrumb__inner {
            color: #6b7280;
            font-weight: 500;

            &:hover {
              color: #1e40af;
            }
          }

          &:last-child .el-breadcrumb__inner {
            color: #1e40af;
          }
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 16px;

      .header-actions {
        display: flex;
        gap: 8px;

        .el-button {
          border: 1px solid #e5e7eb;
          color: #6b7280;

          &:hover {
            color: #1e40af;
            border-color: #1e40af;
          }
        }
      }

      .user-info {
        .user-dropdown-container {

          /* 确保下拉菜单样式正确 */
          :deep(.el-dropdown-menu) {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 8px 0;
            min-width: 160px;
            position: fixed !important;
            /* 使用固定定位，避免影响父容器 */
            z-index: 9999;
            /* 确保在最顶层 */
            max-height: 300px;
            /* 限制最大高度 */
            overflow-y: auto;
            /* 内容过多时显示滚动条 */
          }

          :deep(.el-dropdown-menu__item) {
            color: #374151 !important;
            font-size: 14px;
            padding: 10px 16px;
            transition: all 0.3s ease;

            &:hover {
              background: #f3f4f6 !important;
              color: #1e40af !important;
            }

            .el-icon {
              color: #6b7280;
              margin-right: 8px;
              font-size: 16px;
            }

            &:hover .el-icon {
              color: #1e40af;
            }

            &.is-divided {
              border-top: 1px solid #e5e7eb;
              margin-top: 4px;
              padding-top: 12px;
            }
          }

          /* 退出登录项特殊样式 */
          :deep(.el-dropdown-menu__item:last-child) {
            color: #dc2626 !important;

            &:hover {
              background: #fef2f2 !important;
              color: #dc2626 !important;
            }

            .el-icon {
              color: #dc2626;
            }

            &:hover .el-icon {
              color: #dc2626;
            }
          }
        }

        .user-dropdown {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: #f3f4f6;
          }

          .user-avatar {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            font-weight: 600;
          }

          .username {
            font-weight: 500;
            color: #1f2937;
            font-size: 14px;
          }

          .dropdown-icon {
            color: #6b7280;
            font-size: 12px;
            transition: transform 0.3s ease;
          }

          &:hover .dropdown-icon {
            transform: rotate(180deg);
          }
        }
      }
    }
  }

  .page-container {
    flex: 1;
    overflow-y: auto;
    padding: 24px;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 3px;

      &:hover {
        background: #94a3b8;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .dashboard-sidebar {
    width: 200px;

    &.collapsed {
      width: 64px;
    }
  }

  .content-header {
    padding: 0 16px;

    .header-left {
      gap: 16px;

      .page-title .title-text {
        font-size: 16px;
      }
    }

    .header-right {
      gap: 12px;

      .header-actions {
        gap: 4px;
      }
    }
  }

  .page-container {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .dashboard-layout {
    flex-direction: column;
  }

  .dashboard-sidebar {
    width: 100%;
    height: auto;
    max-height: 60px;
    overflow: hidden;

    &.collapsed {
      width: 100%;
    }

    .sidebar-header {
      height: 60px;
    }

    .user-card,
    .sidebar-nav {
      display: none;
    }
  }

  .content-header {
    height: 56px;
    padding: 0 12px;

    .header-left {
      .breadcrumb {
        display: none;
      }
    }

    .header-right {
      .header-actions {
        display: none;
      }
    }
  }

  .page-container {
    padding: 12px;
  }
}

/* 动画效果 */
.dashboard-sidebar {
  .nav-menu {

    :deep(.el-menu-item),
    :deep(.el-sub-menu__title) {
      position: relative;
      overflow: hidden;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s ease;
      }

      &:hover::after {
        left: 100%;
      }
    }
  }
}

/* 滚动条美化 */
.dashboard-sidebar::-webkit-scrollbar {
  width: 4px;
}

.dashboard-sidebar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.dashboard-sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;

  &:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}

/* 确保样式优先级 */
:deep(.el-menu) {
  border: none !important;
}

:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  border: none !important;
}

/* 全局重置 */
* {
  box-sizing: border-box;
}

/* 确保布局正确 */
:deep(.content-layer) {
  height: 100vh !important;
  width: 100vw !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

/* 全局下拉菜单样式修复 */
:deep(.el-dropdown-menu) {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
  position: fixed !important;
  /* 使用固定定位 */
  z-index: 9999 !important;
  /* 确保在最顶层 */
  max-height: 300px !important;
  /* 限制最大高度 */
  overflow-y: auto !important;
  /* 内容过多时显示滚动条 */

  /* 自定义下拉菜单滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;

    &:hover {
      background: #94a3b8;
    }
  }
}

:deep(.el-dropdown-menu__item) {
  color: #374151 !important;
  font-size: 14px !important;

  &:hover {
    background: #f3f4f6 !important;
    color: #1e40af !important;
  }

  .el-icon {
    color: #6b7280 !important;
  }

  &:hover .el-icon {
    color: #1e40af !important;
  }
}

/* 侧边栏子菜单样式 */
:deep(.el-sub-menu .el-menu) {
  background: rgba(0, 0, 0, 0.1) !important;
}

:deep(.el-sub-menu .el-menu-item) {
  color: rgba(255, 255, 255, 0.7) !important;

  &:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
  }

  &.is-active {
    background: rgba(96, 165, 250, 0.3) !important;
    color: white !important;
  }
}
</style>
