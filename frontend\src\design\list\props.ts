// 定义 props 的默认值
export const listProps = {
  list: {
    type: Array as PropType<ListType>,
    default: () => [],
  },
  width: {
    type: String,
    default: "auto",
  },
  height: {
    type: String,
    default: "auto",
  },
  showIcon: {
    type: Boolean,
    default: true,
  },
};

// ListType 定义
export type ListType = string[];
// ListProps 定义
export interface ListProps {
  list: ListType;
  width: string;
  height: string;
  showIcon: boolean;
}
