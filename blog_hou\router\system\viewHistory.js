const Router = require("koa-router");
const { handleResponse } = require("../../middlewares/responseHandler");
const verifyToken = require("../../middlewares/koaJwtMiddleware");
const UAParser = require('ua-parser-js');
const viewHistoryService = require("../../utils/sql/viewHistoryService");

const viewHistory = new Router();

// 记录浏览记录（通过JWT白名单允许匿名访问）
viewHistory.post("/record", async (ctx) => {
  console.log('【浏览记录】收到POST /record 请求，body:', ctx.request.body);
  try {
    const { article_id, view_duration = 0, scroll_depth = 0 } = ctx.request.body;
    const ip_address = ctx.request.ip || ctx.request.header['x-forwarded-for'] || ctx.request.socket.remoteAddress;
    const user_agent = ctx.request.header['user-agent'] || '';
    const referrer = ctx.request.header['referer'] || '';
    
    // 解析用户代理
    const parser = new UAParser(user_agent);
    const result = parser.getResult();
    
    const device_type = result.device.type === 'mobile' ? 'mobile' : 
                       result.device.type === 'tablet' ? 'tablet' : 'desktop';
    const browser = result.browser.name || '';
    const os = result.os.name || '';
    
    // 获取用户ID（如果已登录）
    let user_id = null;
    const token = ctx.request.header.authorization?.replace('Bearer ', '');
    if (token) {
      try {
        const jwt = require('jsonwebtoken');
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
        user_id = decoded.id;
      } catch (err) {
        // 忽略token验证失败，作为匿名用户处理
      }
    }
    
    // 生成会话ID（基于IP和用户代理的简单hash）
    const crypto = require('crypto');
    const session_id = crypto.createHash('md5')
      .update(ip_address + user_agent + new Date().toDateString())
      .digest('hex');

    // 检查是否在短时间内重复访问同一文章（防止刷浏览量）
    const recentView = await viewHistoryService.checkRecentView(article_id, ip_address);

    if (recentView) {
      console.log('【浏览记录】5分钟内重复访问，更新记录:', recentView.id);
      // 更新现有记录的浏览时长和滚动深度
      await viewHistoryService.updateViewRecord(recentView.id, view_duration, scroll_depth);

      handleResponse(ctx, 200, { message: "浏览记录已更新" });
    } else {
      console.log('【浏览记录】插入新浏览记录，article_id:', article_id, 'user_id:', user_id);
      // 插入新的浏览记录
      await viewHistoryService.insertViewRecord(user_id, article_id, ip_address, user_agent, referrer, session_id, view_duration, scroll_depth, device_type, browser, os);

      // 更新文章浏览量
      await viewHistoryService.updateArticleViews(article_id);

      // 更新文章统计
      await viewHistoryService.updateArticleStats(article_id);

      // 更新用户统计（如果已登录）
      if (user_id) {
        await viewHistoryService.updateUserStats(user_id);
      }

      handleResponse(ctx, 200, { message: "浏览记录已保存" });
    }
  } catch (err) {
    console.error("记录浏览历史失败:", err);
    handleResponse(ctx, 500, { error: "记录浏览历史失败" });
  }
});

// 获取用户统计信息
viewHistory.get("/user/:userId/stats", verifyToken, async (ctx) => {
  console.log('【浏览记录】收到GET /user/:userId/stats 请求，userId:', ctx.params.userId);
  try {
    const { userId } = ctx.params;
    const user = ctx.state.user;

    // JWT payload中使用的是userId字段
    const currentUserId = user?.userId || user?.id;

    if (!user || (currentUserId !== parseInt(userId) && user.role !== 'admin')) {
      return handleResponse(ctx, 403, { error: "无权限查看此用户的统计信息" });
    }

    // 查询用户统计信息
    const stats = await viewHistoryService.getUserStats(userId);

    if (!stats) {
      // 如果没有统计数据，返回默认值
      return handleResponse(ctx, 200, {
        stats: {
          total_views: 0,
          unique_articles: 0,
          avg_view_duration: 0,
          last_view_at: null
        }
      });
    }

    return handleResponse(ctx, 200, { stats });
  } catch (err) {
    console.error("获取用户统计失败:", err);
    return handleResponse(ctx, 500, { error: "获取用户统计失败" });
  }
});

// 获取用户浏览历史
viewHistory.get("/user/:userId", verifyToken, async (ctx) => {
  console.log('【浏览记录】收到GET /user/:userId 请求，userId:', ctx.params.userId, 'query:', ctx.query);
  try {
    const { userId } = ctx.params;
    const { page = 1, limit = 20 } = ctx.query;
    const offset = (page - 1) * limit;

    // 验证用户权限
    const user = ctx.state.user;

    // JWT payload中使用的是userId字段
    const currentUserId = user?.userId || user?.id;

    if (!user || (currentUserId !== parseInt(userId) && user.role !== 'admin')) {
      return handleResponse(ctx, 403, { error: "无权限查看此用户的浏览记录" });
    }

    const history = await viewHistoryService.getUserViewHistory(userId, limit, offset);
    const total = await viewHistoryService.getUserViewHistoryCount(userId);

    console.log('【浏览记录】查询结果数量:', history.length);
    handleResponse(ctx, 200, {
      data: history,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    console.error("获取浏览历史失败:", err);
    handleResponse(ctx, 500, { error: "获取浏览历史失败" });
  }
});

// 获取文章浏览统计
viewHistory.get("/article/:articleId/stats", async (ctx) => {
  console.log('【浏览记录】收到GET /article/:articleId/stats 请求，articleId:', ctx.params.articleId, 'query:', ctx.query);
  try {
    const { articleId } = ctx.params;
    const { period = '7d' } = ctx.query;

    let dateCondition = '';
    switch (period) {
      case '1d':
        dateCondition = 'AND vh.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)';
        break;
      case '7d':
        dateCondition = 'AND vh.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        break;
      case '30d':
        dateCondition = 'AND vh.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
        break;
      default:
        dateCondition = '';
    }

    const stats = await viewHistoryService.getArticleViewStats(articleId, dateCondition);
    const dailyTrend = await viewHistoryService.getArticleDailyTrend(articleId, dateCondition);

    console.log('【浏览记录】统计数据:', stats, '趋势数据条数:', dailyTrend.length);
    handleResponse(ctx, 200, {
      stats: stats,
      dailyTrend: dailyTrend
    });
  } catch (err) {
    console.error("获取文章浏览统计失败:", err);
    handleResponse(ctx, 500, { error: "获取文章浏览统计失败" });
  }
});



// 清理旧的浏览记录（可选，用于定期清理）
viewHistory.delete("/cleanup", verifyToken, async (ctx) => {
  console.log('【浏览记录】收到DELETE /cleanup 请求，user:', ctx.state.user, 'query:', ctx.query);
  try {
    // 只有管理员可以执行清理
    const user = ctx.state.user;
    if (!user || user.role !== 'admin') {
      return handleResponse(ctx, 403, { error: "无权限执行此操作" });
    }

    const { days = 90 } = ctx.query;
    
    const result = await viewHistoryService.cleanupOldRecords(days);

    console.log('【浏览记录】清理结果:', result.affectedRows);
    handleResponse(ctx, 200, { 
      message: `已清理 ${result.affectedRows} 条 ${days} 天前的浏览记录` 
    });
  } catch (err) {
    console.error("清理浏览记录失败:", err);
    handleResponse(ctx, 500, { error: "清理浏览记录失败" });
  }
});

module.exports = viewHistory;
