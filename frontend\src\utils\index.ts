import axios from "axios";

// 配置项
const axiosOption = {
  baseURL: "/api",
  withCredentials: true,
  timeout: 1000000, // 10秒超时
};

// 创建axios实例
const instance = axios.create(axiosOption);

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// 响应拦截器
instance.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem("token");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

export default instance;
