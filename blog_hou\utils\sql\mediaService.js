const db = require("../db");

// 检查视频管理状态
async function getVideoStatus(fileName) {
  const sql = "SELECT status FROM video_management WHERE file_name = ?";
  const result = await db.query(sql, [fileName]);
  return result.length > 0 ? result[0].status : 'online'; // 默认为上架状态
}

// 获取媒体统计信息
async function getMediaStats(fileName) {
  const sql = "SELECT * FROM media_stats WHERE file_name = ?";
  const result = await db.query(sql, [decodeURIComponent(fileName)]);
  return result.length > 0 ? result[0] : null;
}

// 检查用户是否喜欢媒体文件
async function checkUserLike(userId, fileName) {
  const sql = 'SELECT id FROM media_likes WHERE user_id = ? AND file_name = ?';
  const result = await db.query(sql, [userId, fileName]);
  return result.length > 0;
}

// 检查用户是否收藏媒体文件
async function checkUserCollection(userId, fileName) {
  const sql = 'SELECT id FROM media_collections WHERE user_id = ? AND file_name = ?';
  const result = await db.query(sql, [userId, fileName]);
  return result.length > 0;
}

module.exports = {
  getVideoStatus,
  getMediaStats,
  checkUserLike,
  checkUserCollection,
};
