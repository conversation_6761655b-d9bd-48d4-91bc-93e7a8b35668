<template>
  <canvas ref="canvas" class="particles-canvas"></canvas>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

const props = defineProps({
  particleCount: { type: Number, default: 60 },
  linkDistance: { type: Number, default: 120 },
  particleSpeed: { type: Number, default: 0.8 }
})

const canvas = ref(null)
let ctx
let particles = []
let animationId
let mouse = { x: null, y: null }
let WIDTH = window.innerWidth
let HEIGHT = window.innerHeight

const colors = ['#ff9a9e', '#fad0c4', '#a18cd1', '#fbc2eb', '#cfd9df', '#ffecd2', '#fcb69f']

class Particle {
  constructor() {
    this.reset()
  }

  reset() {
    this.x = Math.random() * WIDTH
    this.y = Math.random() * HEIGHT
    this.vx = (Math.random() - 0.5) * props.particleSpeed
    this.vy = (Math.random() - 0.5) * props.particleSpeed
    this.radius = Math.random() * 3 + 1
    this.color = colors[Math.floor(Math.random() * colors.length)]
    this.alpha = Math.random() * 0.5 + 0.5
    this.originalAlpha = this.alpha
  }

  update() {
    this.x += this.vx
    this.y += this.vy

    // 边界检测
    if (this.x < 0 || this.x > WIDTH) this.vx *= -1
    if (this.y < 0 || this.y > HEIGHT) this.vy *= -1

    // 鼠标交互 - 优化性能
    if (mouse.x && mouse.y) {
      const dx = mouse.x - this.x
      const dy = mouse.y - this.y
      const distance = Math.sqrt(dx * dx + dy * dy)

      if (distance < 80) {
        this.alpha = this.originalAlpha * (1 + (80 - distance) / 80 * 0.5)
        // 减少吸引效果强度
        this.vx += dx * 0.00005
        this.vy += dy * 0.00005
      } else {
        this.alpha = this.originalAlpha
      }
    }

    // 限制速度
    const maxSpeed = props.particleSpeed * 2
    if (Math.abs(this.vx) > maxSpeed) this.vx = this.vx > 0 ? maxSpeed : -maxSpeed
    if (Math.abs(this.vy) > maxSpeed) this.vy = this.vy > 0 ? maxSpeed : -maxSpeed
  }

  draw(ctx) {
    ctx.save()
    ctx.globalAlpha = this.alpha
    ctx.fillStyle = this.color
    ctx.beginPath()
    ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2)
    ctx.fill()
    ctx.restore()
  }
}

function createParticles() {
  particles = []
  for (let i = 0; i < props.particleCount; i++) {
    particles.push(new Particle())
  }
}

function drawLinks() {
  for (let i = 0; i < particles.length; i++) {
    for (let j = i + 1; j < particles.length; j++) {
      const dx = particles[i].x - particles[j].x
      const dy = particles[i].y - particles[j].y
      const distance = Math.sqrt(dx * dx + dy * dy)

      if (distance < props.linkDistance) {
        const opacity = (1 - distance / props.linkDistance) * 0.3
        ctx.save()
        ctx.globalAlpha = opacity
        ctx.strokeStyle = '#cccccc'
        ctx.lineWidth = 1
        ctx.beginPath()
        ctx.moveTo(particles[i].x, particles[i].y)
        ctx.lineTo(particles[j].x, particles[j].y)
        ctx.stroke()
        ctx.restore()
      }
    }
  }
}

function animate() {
  ctx.clearRect(0, 0, WIDTH, HEIGHT)

  // 更新和绘制粒子
  particles.forEach(particle => {
    particle.update()
    particle.draw(ctx)
  })

  // 绘制连线
  drawLinks()

  animationId = requestAnimationFrame(animate)
}

function resizeCanvas() {
  WIDTH = window.innerWidth
  HEIGHT = window.innerHeight
  const canvasEl = canvas.value
  if (canvasEl) {
    canvasEl.width = WIDTH
    canvasEl.height = HEIGHT

    // 重新创建粒子以适应新尺寸
    createParticles()
  }
}

function onMouseMove(event) {
  mouse.x = event.clientX
  mouse.y = event.clientY
}

function onClick(event) {
  // 只有当点击的不是交互元素时才添加粒子
  const target = event.target
  const isInteractiveElement = target.tagName === 'BUTTON' ||
                              target.tagName === 'A' ||
                              target.tagName === 'INPUT' ||
                              target.tagName === 'SELECT' ||
                              target.tagName === 'TEXTAREA' ||
                              target.closest('.el-button') ||
                              target.closest('.el-menu-item') ||
                              target.closest('.el-card') ||
                              target.closest('.el-dialog') ||
                              target.closest('.el-dropdown') ||
                              target.closest('.el-select') ||
                              target.closest('.effect-controls') ||
                              target.closest('[role="button"]') ||
                              target.closest('[clickable]') ||
                              target.style.cursor === 'pointer' ||
                              getComputedStyle(target).cursor === 'pointer'

  if (!isInteractiveElement) {
    // 点击时添加新粒子，但数量减少以提高性能
    for (let i = 0; i < 2; i++) {
      const particle = new Particle()
      particle.x = event.clientX + (Math.random() - 0.5) * 15
      particle.y = event.clientY + (Math.random() - 0.5) * 15
      particles.push(particle)
    }

    // 限制粒子总数
    if (particles.length > props.particleCount * 1.3) {
      particles.splice(0, 2)
    }
  }
}

onMounted(async () => {
  await nextTick()
  const canvasEl = canvas.value
  resizeCanvas()
  ctx = canvasEl.getContext('2d')

  createParticles()
  animate()

  // 使用全局事件监听器，不阻止页面交互
  window.addEventListener('resize', resizeCanvas)
  window.addEventListener('mousemove', onMouseMove)
  window.addEventListener('click', onClick)

  // 鼠标离开时清除鼠标位置
  window.addEventListener('mouseleave', () => {
    mouse.x = null
    mouse.y = null
  })
})

onUnmounted(() => {
  cancelAnimationFrame(animationId)
  window.removeEventListener('resize', resizeCanvas)
  window.removeEventListener('mousemove', onMouseMove)
  window.removeEventListener('click', onClick)
  window.removeEventListener('mouseleave', () => {
    mouse.x = null
    mouse.y = null
  })
})
</script>

<style scoped>
.particles-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
  cursor: none;
}
</style>
