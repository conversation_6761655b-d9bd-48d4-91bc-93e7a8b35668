const Router = require("koa-router");
const { spawn } = require("child_process");

const bigModel = new Router();
let ollamaProcess = null;

bigModel.post("/chatai", async (ctx) => {
  const { ok } = ctx.request.body;
  console.log("收到大模型请求", ok);

  if (ok === 1) {
    if (ollamaProcess && !ollamaProcess.killed) {
      ctx.body = { message: "大模型服务已经启动", code: 1 };
      return;
    }

    try {
      ollamaProcess = spawn("ollama", ["run", "llama3.1"]);

      ollamaProcess.stdout.on("data", (data) => {
        console.log(`ollama stdout: ${data.toString()}`);
      });

      ollamaProcess.stderr.on("data", (data) => {
        console.error(`ollama stderr: ${data.toString()}`);
      });

      ollamaProcess.on("close", (code) => {
        console.log(`ollama 进程退出，退出码 ${code}`);
        ollamaProcess = null;
      });

      ctx.body = { message: "大模型服务启动中", code: 0 };
    } catch (err) {
      ctx.body = { message: "大模型启动失败", error: err.message, code: -1 };
    }
  } else {
    ctx.body = { message: "未触发启动" };
  }
});

module.exports = bigModel;