<template>
  <div class="particle-test">
    <div class="test-content">
      <h1>粒子特效交互测试</h1>
      
      <div class="test-section">
        <h2>点击测试区域</h2>
        <p>测试页面元素的点击功能是否正常工作</p>
        
        <div class="button-grid">
          <el-button type="primary" @click="handleClick('主要按钮')">
            主要按钮
          </el-button>
          <el-button type="success" @click="handleClick('成功按钮')">
            成功按钮
          </el-button>
          <el-button type="warning" @click="handleClick('警告按钮')">
            警告按钮
          </el-button>
          <el-button type="danger" @click="handleClick('危险按钮')">
            危险按钮
          </el-button>
        </div>
        
        <div class="interactive-elements">
          <el-card class="test-card" @click="handleClick('卡片')">
            <h3>可点击卡片</h3>
            <p>这是一个可点击的卡片，测试点击事件是否正常</p>
          </el-card>
          
          <div class="form-section">
            <el-input 
              v-model="inputValue" 
              placeholder="测试输入框"
              @focus="handleFocus('输入框')"
            />
            
            <el-select v-model="selectValue" placeholder="测试选择器">
              <el-option label="选项1" value="1" />
              <el-option label="选项2" value="2" />
              <el-option label="选项3" value="3" />
            </el-select>
          </div>
        </div>
        
        <div class="link-section">
          <a href="#" @click.prevent="handleClick('链接1')">测试链接1</a>
          <a href="#" @click.prevent="handleClick('链接2')">测试链接2</a>
          <router-link to="/index/home" @click="handleClick('路由链接')">
            返回首页
          </router-link>
        </div>
      </div>
      
      <div class="test-results">
        <h3>点击记录</h3>
        <div class="click-log">
          <div v-for="(log, index) in clickLogs" :key="index" class="log-item">
            {{ log }}
          </div>
        </div>
        <el-button @click="clearLogs" size="small">清空记录</el-button>
      </div>
      
      <div class="instructions">
        <h3>测试说明</h3>
        <ul>
          <li>✅ 所有按钮、链接、输入框都应该正常响应点击</li>
          <li>✅ 粒子特效应该在空白区域点击时产生粒子</li>
          <li>✅ 鼠标移动时粒子应该有轻微的交互效果</li>
          <li>✅ 页面交互不应该被特效阻止</li>
        </ul>
      </div>
    </div>
    
    <!-- 粒子特效 -->
    <Particle />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Particle from '@/components/texiao/Particle.vue'

const inputValue = ref('')
const selectValue = ref('')
const clickLogs = ref([])

const handleClick = (element) => {
  const timestamp = new Date().toLocaleTimeString()
  clickLogs.value.unshift(`${timestamp} - 点击了: ${element}`)
  
  // 限制日志数量
  if (clickLogs.value.length > 10) {
    clickLogs.value = clickLogs.value.slice(0, 10)
  }
}

const handleFocus = (element) => {
  const timestamp = new Date().toLocaleTimeString()
  clickLogs.value.unshift(`${timestamp} - 聚焦: ${element}`)
}

const clearLogs = () => {
  clickLogs.value = []
}
</script>

<style scoped>
.particle-test {
  min-height: 100vh;
  background: var(--bg-secondary);
  position: relative;
}

.test-content {
  position: relative;
  z-index: 10;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  margin-top: var(--spacing-lg);
  box-shadow: var(--shadow-lg);
}

.test-content h1 {
  text-align: center;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xl);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.test-section {
  margin-bottom: var(--spacing-xl);
}

.test-section h2 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.interactive-elements {
  margin-bottom: var(--spacing-lg);
}

.test-card {
  margin-bottom: var(--spacing-md);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.test-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.form-section {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.form-section .el-input,
.form-section .el-select {
  flex: 1;
}

.link-section {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.link-section a {
  color: var(--primary-color);
  text-decoration: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal);
}

.link-section a:hover {
  background: var(--primary-color);
  color: white;
}

.test-results {
  background: var(--bg-tertiary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
}

.test-results h3 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.click-log {
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: var(--spacing-md);
}

.log-item {
  padding: var(--spacing-xs);
  border-bottom: 1px solid var(--border-light);
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.instructions {
  background: var(--bg-tertiary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--success-color);
}

.instructions h3 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.instructions ul {
  margin: 0;
  padding-left: var(--spacing-lg);
}

.instructions li {
  margin-bottom: var(--spacing-xs);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .test-content {
    margin: var(--spacing-md);
    padding: var(--spacing-lg);
  }
  
  .button-grid {
    grid-template-columns: 1fr;
  }
  
  .form-section {
    flex-direction: column;
  }
  
  .link-section {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}
</style>
