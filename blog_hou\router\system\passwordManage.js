const Router = require("koa-router");
const bodyParser = require("koa-bodyparser");
const passwordService = require("../../utils/sql/passwordService");

const passwordManage = new Router();
passwordManage.use(bodyParser());

/**
 * 管理员权限验证中间件
 * 注意：登录状态已在全局中间件中验证，此处只验证管理员权限
 */
async function requireAdmin(ctx, next) {
  try {
    const userId = ctx.state.user?.userId || ctx.state.user?.id;
    console.log("当前用户ID:", userId);

    // 检查管理员权限
    const isAdmin = await passwordService.isAdminUser(userId);

    if (!isAdmin) {
      ctx.body = {
        code: 403,
        message: "权限不足，只有管理员可以访问"
      };
      return;
    }

    await next();
  } catch (error) {
    console.error("管理员权限验证失败:", error);
    ctx.body = {
      code: 500,
      message: "权限验证失败",
      error: error.message
    };
  }
}

// 获取所有密码配置（仅admin）
passwordManage.get("/list", requireAdmin, async (ctx) => {
  try {
    const passwords = await passwordService.getAllPasswords();

    // 调试日志：打印查询结果
    console.log("=== 密码配置查询结果 ===");
    console.log("查询到的记录数:", passwords.length);
    console.log("当前时间:", new Date().toISOString());

    // 详细打印每条记录
    passwords.forEach((password, index) => {
      console.log(`\n--- 记录 ${index + 1} ---`);
      console.log("完整记录:", JSON.stringify(password, null, 2));
    });
    console.log("\n========================");

    ctx.body = {
      code: 200,
      message: "获取密码配置成功",
      data: passwords
    };
  } catch (error) {
    console.error("获取密码配置失败:", error);
    ctx.body = {
      code: 500,
      message: "获取密码配置失败",
      error: error.message
    };
  }
});

// 调试接口：直接查看数据库数据（完全绕过缓存）
passwordManage.get("/debug-raw", async (ctx) => {
  try {
    // 完全绕过缓存和认证，直接查询数据库
    ctx.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    ctx.set('Pragma', 'no-cache');
    ctx.set('Expires', '0');

    const passwords = await passwordService.getAllPasswords();

    console.log("=== 原始数据库查询结果 ===");
    console.log("查询时间:", new Date().toISOString());
    console.log("查询到的记录数:", passwords.length);

    passwords.forEach((password, index) => {
      console.log(`\n--- 记录 ${index + 1} ---`);
      console.log("ID:", password.id);
      console.log("密码类型:", password.password_type);
      console.log("密保问题:", password.security_question);
      console.log("描述:", password.description);
      console.log("是否启用:", password.is_active);
      console.log("创建者ID:", password.created_by);
      console.log("创建者名称:", password.created_by_name);
      console.log("使用次数:", password.use_count);
      console.log("最后使用时间:", password.last_used_at);
      console.log("创建时间:", password.created_at);
      console.log("更新时间:", password.updated_at);
    });

    console.log("\n=== JSON格式完整数据 ===");
    console.log(JSON.stringify(passwords, null, 2));
    console.log("========================");

    ctx.body = {
      code: 200,
      message: "原始数据获取成功",
      data: passwords,
      debug: {
        count: passwords.length,
        timestamp: new Date().toISOString(),
        raw: true
      }
    };
  } catch (error) {
    console.error("原始数据查询失败:", error);
    ctx.body = {
      code: 500,
      message: "原始数据查询失败",
      error: error.message,
      stack: error.stack
    };
  }
});

// 创建密码配置（仅admin）
passwordManage.post("/create", requireAdmin, async (ctx) => {
  try {
    const userId = ctx.state.user.userId || ctx.state.user.id;
    const { passwordType, password, securityQuestion, securityAnswer, description } = ctx.request.body;

    // 验证必填字段
    if (!passwordType || !password || !securityQuestion || !securityAnswer) {
      ctx.body = {
        code: 400,
        message: "密码类型、密码、密保问题和密保答案都是必填的"
      };
      return;
    }

    // 验证密码类型
    const allowedTypes = ['photo_wall', 'media', 'admin', 'system'];
    if (!allowedTypes.includes(passwordType)) {
      ctx.body = {
        code: 400,
        message: `密码类型只能是: ${allowedTypes.join(', ')}`
      };
      return;
    }

    // 验证密码强度
    if (password.length < 6) {
      ctx.body = {
        code: 400,
        message: "密码长度至少为6位"
      };
      return;
    }

    const result = await passwordService.createPassword({
      passwordType,
      password,
      securityQuestion,
      securityAnswer,
      description: description || `${passwordType} 访问密码`
    }, userId);

    ctx.body = {
      code: 200,
      message: "密码配置创建成功",
      data: result
    };
  } catch (error) {
    console.error("创建密码配置失败:", error);
    ctx.body = {
      code: 500,
      message: error.message || "创建失败",
      error: error.message
    };
  }
});

// 更新密码配置（仅admin）
passwordManage.put("/update", requireAdmin, async (ctx) => {
  try {
    const userId = ctx.state.user.userId || ctx.state.user.id;
    const { passwordType, password, securityQuestion, securityAnswer, description } = ctx.request.body;

    // 验证必填字段
    if (!passwordType || !password || !securityQuestion || !securityAnswer) {
      ctx.body = {
        code: 400,
        message: "密码类型、密码、密保问题和密保答案都是必填的"
      };
      return;
    }

    // 验证密码强度
    if (password.length < 6) {
      ctx.body = {
        code: 400,
        message: "密码长度至少为6位"
      };
      return;
    }

    const result = await passwordService.updatePassword({
      passwordType,
      password,
      securityQuestion,
      securityAnswer,
      description
    }, userId);

    ctx.body = {
      code: 200,
      message: "密码配置更新成功",
      data: result
    };
  } catch (error) {
    console.error("更新密码配置失败:", error);
    ctx.body = {
      code: 500,
      message: error.message || "更新失败",
      error: error.message
    };
  }
});

// 验证密码（公开接口）
passwordManage.post("/verify", async (ctx) => {
  try {
    const { passwordType, password } = ctx.request.body;

    if (!passwordType || !password) {
      ctx.body = {
        code: 400,
        message: "密码类型和密码都是必填的"
      };
      return;
    }

    const result = await passwordService.verifyPassword(passwordType, password);

    ctx.body = {
      code: 200,
      message: result.message,
      data: {
        isValid: result.valid,
        configId: result.configId
      }
    };
  } catch (error) {
    console.error("密码验证失败:", error);
    ctx.body = {
      code: 500,
      message: "密码验证失败",
      error: error.message
    };
  }
});

// 获取密码配置详情（仅admin）
passwordManage.get("/detail/:id", requireAdmin, async (ctx) => {
  try {
    const { id } = ctx.params;
    const config = await passwordService.getPasswordById(id);

    if (!config) {
      ctx.body = {
        code: 404,
        message: "密码配置不存在"
      };
      return;
    }

    // 不返回敏感信息
    const { password_hash, security_answer_hash, ...safeConfig } = config;

    ctx.body = {
      code: 200,
      message: "获取密码配置详情成功",
      data: safeConfig
    };
  } catch (error) {
    console.error("获取密码配置详情失败:", error);
    ctx.body = {
      code: 500,
      message: "获取密码配置详情失败",
      error: error.message
    };
  }
});

// 获取密保问题（公开接口）
passwordManage.get("/security-question/:passwordType", async (ctx) => {
  try {
    const { passwordType } = ctx.params;
    const config = await passwordService.getPasswordByType(passwordType);

    if (!config) {
      ctx.body = {
        code: 404,
        message: "未找到该类型的密码配置"
      };
      return;
    }

    ctx.body = {
      code: 200,
      message: "获取密保问题成功",
      data: { securityQuestion: config.security_question }
    };
  } catch (error) {
    console.error("获取密保问题失败:", error);
    ctx.body = {
      code: 500,
      message: "获取密保问题失败",
      error: error.message
    };
  }
});

// 切换密码配置状态（仅admin）
passwordManage.put("/toggle/:id", requireAdmin, async (ctx) => {
  try {
    const { id } = ctx.params;
    const result = await passwordService.togglePasswordStatus(id);

    if (!result) {
      ctx.body = {
        code: 404,
        message: "密码配置不存在"
      };
      return;
    }

    ctx.body = {
      code: 200,
      message: "密码配置状态切换成功"
    };
  } catch (error) {
    console.error("切换密码配置状态失败:", error);
    ctx.body = {
      code: 500,
      message: "切换密码配置状态失败",
      error: error.message
    };
  }
});

// 通过密保重置密码（仅admin）
passwordManage.post("/reset-by-security", requireAdmin, async (ctx) => {
  try {
    const userId = ctx.state.user.userId || ctx.state.user.id;
    const { passwordType, securityAnswer, newPassword } = ctx.request.body;

    if (!passwordType || !securityAnswer || !newPassword) {
      ctx.body = {
        code: 400,
        message: "密码类型、密保答案和新密码都是必填的"
      };
      return;
    }

    // 验证新密码强度
    if (newPassword.length < 6) {
      ctx.body = {
        code: 400,
        message: "新密码长度至少为6位"
      };
      return;
    }

    const result = await passwordService.resetPasswordBySecurityAnswer(
      passwordType,
      securityAnswer,
      newPassword,
      userId
    );

    ctx.body = {
      code: 200,
      message: "密码重置成功"
    };
  } catch (error) {
    console.error("密码重置失败:", error);
    ctx.body = {
      code: 500,
      message: error.message || "密码重置失败",
      error: error.message
    };
  }
});

// 删除密码配置（仅admin）
passwordManage.delete("/delete/:passwordType", requireAdmin, async (ctx) => {
  try {
    const { passwordType } = ctx.params;
    const result = await passwordService.deletePassword(passwordType);

    if (!result) {
      ctx.body = {
        code: 404,
        message: "密码配置不存在或已删除"
      };
      return;
    }

    ctx.body = {
      code: 200,
      message: "密码配置删除成功"
    };
  } catch (error) {
    console.error("删除密码配置失败:", error);
    ctx.body = {
      code: 500,
      message: "删除密码配置失败",
      error: error.message
    };
  }
});

// 获取密码使用统计（仅admin）
passwordManage.get("/stats", requireAdmin, async (ctx) => {
  try {
    const passwords = await passwordService.getAllPasswords();

    const stats = passwords.map(p => ({
      id: p.id,
      password_type: p.password_type,
      description: p.description,
      use_count: p.use_count,
      last_used_at: p.last_used_at,
      is_active: p.is_active,
      created_at: p.created_at
    }));

    ctx.body = {
      code: 200,
      message: "获取密码使用统计成功",
      data: {
        total: stats.length,
        active: stats.filter(s => s.is_active).length,
        stats
      }
    };
  } catch (error) {
    console.error("获取密码使用统计失败:", error);
    ctx.body = {
      code: 500,
      message: "获取密码使用统计失败",
      error: error.message
    };
  }
});

module.exports = passwordManage;