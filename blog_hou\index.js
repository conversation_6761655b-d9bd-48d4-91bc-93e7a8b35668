require('./utils/healthCheck.js'); // 健康检查
const app = require("./app");
const logger = require("./plugin/logger");
const http = require('http');
const setupSocketIOChat = require('./communication/socketio-chat');
const webrtcManager = require('./communication/webrtc-manager');
const redisCache = require('./utils/redisCache');

// 只创建一个 server
const server = http.createServer(app.callback());

// Socket.IO聊天管理器
let ioServer = null;
let isSocketIOStarted = false;

// 启动Socket.IO聊天服务器的函数
function startSocketIOServer() {
  if (!isSocketIOStarted) {
    logger.info('🚀 启动Socket.IO聊天服务器...');
    ioServer = setupSocketIOChat(server);
    isSocketIOStarted = true;
    logger.info('✅ Socket.IO聊天服务器已启动');
    return ioServer;
  }
  return ioServer;
}

// 停止Socket.IO服务器的函数
function stopSocketIOServer() {
  if (isSocketIOStarted && ioServer) {
    logger.info('🛑 停止Socket.IO聊天服务器...');
    ioServer.close();
    ioServer = null;
    isSocketIOStarted = false;
    logger.info('✅ Socket.IO聊天服务器已停止');
  }
}

// 获取Socket.IO状态
function getSocketIOStatus() {
  return {
    isStarted: isSocketIOStarted,
    stats: ioServer ? ioServer.getStats() : null,
    onlineUsers: ioServer ? ioServer.getOnlineUsers() : []
  };
}

// 将Socket.IO管理函数挂载到app上，供路由使用
app.context.webSocketManager = {
  start: startSocketIOServer,
  stop: stopSocketIOServer,
  getStatus: getSocketIOStatus,
  getInstance: () => ioServer
};

// 将WebRTC管理器挂载到app上，供路由使用
app.context.webrtcManager = webrtcManager;
app.server = server; // 将server实例挂载到app上，供WebRTC使用

// 自动启动WebRTC服务器
function autoStartWebRTC() {
  try {
    const result = webrtcManager.start(server);
    if (result.success) {
      logger.info('🎉 WebRTC服务器自动启动成功');
    } else {
      logger.warn('WebRTC服务器自动启动失败:', result.message);
    }
  } catch (error) {
    logger.error('WebRTC服务器自动启动异常:', error.message);
  }
}

server.listen(3000, '0.0.0.0', async () => {
  logger.info('🌐 HTTP服务器已启动: http://192.168.31.222:3000');
  logger.info('💬 Socket.IO聊天服务器将在用户进入聊天室时启动');
  logger.info('📹 WebRTC视频通话服务器正在自动启动...');

  // 手动初始化Redis缓存
  setTimeout(async () => {
    try {
      const success = await redisCache.manualInit();
      if (success) {
        logger.info('✅ Redis缓存初始化成功');
      } else {
        logger.warn('⚠️ Redis缓存初始化失败，将使用内存缓存');
      }
    } catch (error) {
      logger.error('Redis初始化异常:', error.message);
    }

    // 触发应用ready事件
    app.emit('ready');
  }, 2000);

  // 延迟启动WebRTC服务器，确保HTTP服务器完全启动
  setTimeout(() => {
    autoStartWebRTC();
  }, 1000);
});

