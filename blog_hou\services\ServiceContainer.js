// 服务容器 - 依赖注入管理
const logger = require("../plugin/logger");

class ServiceContainer {
  constructor() {
    this.services = new Map();
    this.singletons = new Map();
    this.factories = new Map();
    this.dependencies = new Map();
  }

  // 注册单例服务
  registerSingleton(name, serviceClass, dependencies = []) {
    this.singletons.set(name, {
      serviceClass,
      dependencies,
      instance: null
    });
    
    logger.debug('注册单例服务', { name, dependencies });
    return this;
  }

  // 注册瞬态服务
  registerTransient(name, serviceClass, dependencies = []) {
    this.services.set(name, {
      serviceClass,
      dependencies
    });
    
    logger.debug('注册瞬态服务', { name, dependencies });
    return this;
  }

  // 注册工厂服务
  registerFactory(name, factory, dependencies = []) {
    this.factories.set(name, {
      factory,
      dependencies
    });
    
    logger.debug('注册工厂服务', { name, dependencies });
    return this;
  }

  // 注册实例
  registerInstance(name, instance) {
    this.singletons.set(name, {
      serviceClass: null,
      dependencies: [],
      instance
    });
    
    logger.debug('注册实例', { name });
    return this;
  }

  // 解析服务
  resolve(name) {
    try {
      // 检查是否为单例服务
      if (this.singletons.has(name)) {
        const singleton = this.singletons.get(name);
        
        if (singleton.instance) {
          return singleton.instance;
        }
        
        // 创建单例实例
        if (singleton.serviceClass) {
          const dependencies = this.resolveDependencies(singleton.dependencies);
          singleton.instance = new singleton.serviceClass(...dependencies);
        }
        
        return singleton.instance;
      }

      // 检查是否为瞬态服务
      if (this.services.has(name)) {
        const service = this.services.get(name);
        const dependencies = this.resolveDependencies(service.dependencies);
        return new service.serviceClass(...dependencies);
      }

      // 检查是否为工厂服务
      if (this.factories.has(name)) {
        const factory = this.factories.get(name);
        const dependencies = this.resolveDependencies(factory.dependencies);
        return factory.factory(...dependencies);
      }

      throw new Error(`服务未注册: ${name}`);
    } catch (error) {
      logger.error('服务解析失败', { name, error: error.message });
      throw error;
    }
  }

  // 解析依赖
  resolveDependencies(dependencies) {
    return dependencies.map(dep => {
      if (typeof dep === 'string') {
        return this.resolve(dep);
      }
      return dep;
    });
  }

  // 检查服务是否已注册
  has(name) {
    return this.singletons.has(name) || 
           this.services.has(name) || 
           this.factories.has(name);
  }

  // 获取所有已注册的服务名称
  getRegisteredServices() {
    const services = [];
    
    for (const name of this.singletons.keys()) {
      services.push({ name, type: 'singleton' });
    }
    
    for (const name of this.services.keys()) {
      services.push({ name, type: 'transient' });
    }
    
    for (const name of this.factories.keys()) {
      services.push({ name, type: 'factory' });
    }
    
    return services;
  }

  // 清理所有服务
  clear() {
    this.services.clear();
    this.singletons.clear();
    this.factories.clear();
    this.dependencies.clear();
    
    logger.info('服务容器已清理');
  }

  // 验证依赖关系
  validateDependencies() {
    const errors = [];
    
    // 检查单例服务的依赖
    for (const [name, singleton] of this.singletons.entries()) {
      for (const dep of singleton.dependencies) {
        if (typeof dep === 'string' && !this.has(dep)) {
          errors.push(`单例服务 ${name} 的依赖 ${dep} 未注册`);
        }
      }
    }
    
    // 检查瞬态服务的依赖
    for (const [name, service] of this.services.entries()) {
      for (const dep of service.dependencies) {
        if (typeof dep === 'string' && !this.has(dep)) {
          errors.push(`瞬态服务 ${name} 的依赖 ${dep} 未注册`);
        }
      }
    }
    
    // 检查工厂服务的依赖
    for (const [name, factory] of this.factories.entries()) {
      for (const dep of factory.dependencies) {
        if (typeof dep === 'string' && !this.has(dep)) {
          errors.push(`工厂服务 ${name} 的依赖 ${dep} 未注册`);
        }
      }
    }
    
    if (errors.length > 0) {
      logger.error('依赖验证失败', { errors });
      throw new Error(`依赖验证失败: ${errors.join(', ')}`);
    }
    
    logger.info('依赖验证通过');
    return true;
  }

  // 检测循环依赖
  detectCircularDependencies() {
    const visited = new Set();
    const recursionStack = new Set();
    
    const hasCycle = (serviceName, path = []) => {
      if (recursionStack.has(serviceName)) {
        const cycle = [...path, serviceName];
        logger.error('检测到循环依赖', { cycle });
        return true;
      }
      
      if (visited.has(serviceName)) {
        return false;
      }
      
      visited.add(serviceName);
      recursionStack.add(serviceName);
      
      let dependencies = [];
      
      if (this.singletons.has(serviceName)) {
        dependencies = this.singletons.get(serviceName).dependencies;
      } else if (this.services.has(serviceName)) {
        dependencies = this.services.get(serviceName).dependencies;
      } else if (this.factories.has(serviceName)) {
        dependencies = this.factories.get(serviceName).dependencies;
      }
      
      for (const dep of dependencies) {
        if (typeof dep === 'string' && hasCycle(dep, [...path, serviceName])) {
          return true;
        }
      }
      
      recursionStack.delete(serviceName);
      return false;
    };
    
    for (const serviceName of this.singletons.keys()) {
      if (hasCycle(serviceName)) {
        throw new Error('检测到循环依赖');
      }
    }
    
    for (const serviceName of this.services.keys()) {
      if (hasCycle(serviceName)) {
        throw new Error('检测到循环依赖');
      }
    }
    
    for (const serviceName of this.factories.keys()) {
      if (hasCycle(serviceName)) {
        throw new Error('检测到循环依赖');
      }
    }
    
    logger.info('循环依赖检测通过');
    return false;
  }

  // 获取服务统计信息
  getStats() {
    const singletonInstances = Array.from(this.singletons.values())
      .filter(s => s.instance !== null).length;
    
    return {
      totalServices: this.singletons.size + this.services.size + this.factories.size,
      singletons: this.singletons.size,
      transients: this.services.size,
      factories: this.factories.size,
      singletonInstances,
      registeredServices: this.getRegisteredServices()
    };
  }
}

// 创建全局服务容器实例
const container = new ServiceContainer();

// 注册基础服务
const ArticleService = require('./ArticleService');
const UserService = require('./UserService');
const CommentService = require('./CommentService');

// 注册服务
container.registerSingleton('articleService', ArticleService);
container.registerSingleton('userService', UserService);
container.registerSingleton('commentService', CommentService);

// 注册数据库和日志服务
container.registerInstance('db', require('../utils/db'));
container.registerInstance('logger', require('../plugin/logger'));

module.exports = container;
