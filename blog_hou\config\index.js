/**
 * 配置管理入口文件
 * 统一导出所有配置模块
 */

const path = require('path');
const fs = require('fs');
const logger = require("../plugin/logger");

// 导入配置模块
const { getDatabaseConfig, getExportConfig, getHealthCheckConfig, getCacheConfig } = require('./database');
const { getConfig: getAppConfig, validateConfig: validateAppConfig } = require('./app');

/**
 * 环境变量配置文件路径
 */
const ENV_FILES = [
  '.env',
  `.env.${process.env.NODE_ENV || 'development'}`,
  '.env.local'
];

/**
 * 加载环境变量文件
 */
function loadEnvFiles() {
  const rootDir = path.join(__dirname, '..');
  
  ENV_FILES.forEach(envFile => {
    const envPath = path.join(rootDir, envFile);
    
    if (fs.existsSync(envPath)) {
      try {
        require('dotenv').config({ path: envPath });
        logger.info(`环境变量文件加载成功: ${envFile}`);
      } catch (error) {
        logger.warn(`环境变量文件加载失败: ${envFile}`, { error: error.message });
      }
    }
  });
}

/**
 * 创建示例环境变量文件
 */
function createEnvExample() {
  const rootDir = path.join(__dirname, '..');
  const examplePath = path.join(rootDir, '.env.example');
  
  if (!fs.existsSync(examplePath)) {
    const exampleContent = `# 环境配置
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password_here
DB_NAME=myblog
DB_CONNECTION_LIMIT=20
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000
DB_SSL=false
DB_DEBUG=false
SLOW_QUERY_THRESHOLD=1000

# 测试数据库配置
TEST_DB_HOST=localhost
TEST_DB_USER=root
TEST_DB_PASSWORD=your_test_password_here
TEST_DB_NAME=myblog_test

# 服务器配置
PORT=3000
HOST=0.0.0.0

# CORS配置
CORS_ORIGINS=http://localhost:5173,http://localhost:5174,http://localhost:5175

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d
JWT_ALGORITHM=HS256
JWT_ISSUER=myblog-app
JWT_AUDIENCE=myblog-users

# 文件上传配置
MAX_FILE_SIZE=524288000
MAX_IMAGE_SIZE=10485760
MAX_DOCUMENT_SIZE=52428800
MAX_VIDEO_SIZE=524288000
MAX_ARCHIVE_SIZE=104857600
MAX_AUDIO_SIZE=52428800

# 分块上传配置
CHUNKED_UPLOAD=true
CHUNK_SIZE=1048576
MAX_CHUNKS=1000

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=combined
FILE_LOGGING=true
CONSOLE_LOGGING=true
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5
LOG_DATE_PATTERN=YYYY-MM-DD

# 安全配置
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
PASSWORD_MIN_LENGTH=6
PASSWORD_REQUIRE_UPPERCASE=false
PASSWORD_REQUIRE_LOWERCASE=false
PASSWORD_REQUIRE_NUMBERS=false
PASSWORD_REQUIRE_SYMBOLS=false
SESSION_MAX_AGE=86400000

# WebRTC配置
WEBRTC_ENABLED=true
MAX_ROOM_PARTICIPANTS=10
ROOM_TIMEOUT=1800000

# AI配置
AI_ENABLED=true
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_TIMEOUT=30000
OLLAMA_DEFAULT_MODEL=qwen2.5:7b
AI_MAX_HISTORY=50
AI_MAX_TOKENS=2000
AI_TEMPERATURE=0.7

# 缓存配置
CACHE_ENABLED=true
CACHE_DEFAULT_TTL=300000
CACHE_MAX_SIZE=1000
CACHE_CLEANUP_INTERVAL=600000

# 数据库健康检查配置
DB_HEALTH_CHECK=true
DB_HEALTH_CHECK_INTERVAL=30000
DB_HEALTH_CHECK_TIMEOUT=5000
DB_HEALTH_CHECK_RETRIES=3

# 压缩配置
COMPRESSION_THRESHOLD=2048
COMPRESSION_LEVEL=6

# 请求体配置
MAX_FIELD_SIZE=20971520
MAX_FIELDS=1000
`;

    try {
      fs.writeFileSync(examplePath, exampleContent);
      logger.info('环境变量示例文件创建成功: .env.example');
    } catch (error) {
      logger.error('环境变量示例文件创建失败', { error: error.message });
    }
  }
}

/**
 * 验证必需的环境变量
 */
function validateRequiredEnvVars() {
  const requiredVars = [];
  
  // 生产环境必需的环境变量
  if (process.env.NODE_ENV === 'production') {
    requiredVars.push(
      'DB_PASSWORD',
      'JWT_SECRET'
    );
  }

  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    const error = `缺少必需的环境变量: ${missing.join(', ')}`;
    logger.error(error);
    throw new Error(error);
  }
}

/**
 * 初始化配置系统
 */
function initializeConfig() {
  try {
    // 1. 加载环境变量文件
    loadEnvFiles();
    
    // 2. 创建示例文件
    createEnvExample();
    
    // 3. 验证必需的环境变量
    validateRequiredEnvVars();
    
    // 4. 验证应用配置
    validateAppConfig();
    
    logger.info('配置系统初始化完成', {
      environment: process.env.NODE_ENV || 'development',
      configFiles: ENV_FILES.filter(file => 
        fs.existsSync(path.join(__dirname, '..', file))
      )
    });
    
    return true;
  } catch (error) {
    logger.error('配置系统初始化失败', { error: error.message });
    throw error;
  }
}

/**
 * 获取所有配置
 */
function getAllConfig() {
  return {
    database: getDatabaseConfig(),
    app: getAppConfig(),
    export: getExportConfig(),
    healthCheck: getHealthCheckConfig(),
    cache: getCacheConfig()
  };
}

/**
 * 获取配置摘要（用于日志记录，不包含敏感信息）
 */
function getConfigSummary() {
  const config = getAllConfig();
  
  return {
    environment: process.env.NODE_ENV || 'development',
    server: {
      port: config.app.server.port,
      host: config.app.server.host
    },
    database: {
      host: config.database.host,
      database: config.database.database,
      connectionLimit: config.database.connectionLimit,
      ssl: !!config.database.ssl
    },
    features: {
      ai: config.app.ai.enabled,
      webrtc: config.app.webrtc.enabled,
      cache: config.cache.enabled,
      healthCheck: config.healthCheck.enabled
    }
  };
}

// 导出配置管理器
module.exports = {
  // 初始化函数
  initializeConfig,
  
  // 配置获取函数
  getAllConfig,
  getConfigSummary,
  getDatabaseConfig,
  getAppConfig,
  getExportConfig,
  getHealthCheckConfig,
  getCacheConfig,
  
  // 工具函数
  loadEnvFiles,
  createEnvExample,
  validateRequiredEnvVars
};
