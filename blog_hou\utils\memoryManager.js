/**
 * 内存管理工具
 * 提供内存监控、清理和优化功能
 */

const logger = require('../plugin/logger');

class MemoryManager {
  constructor() {
    this.memoryThreshold = 80; // 内存使用率阈值（百分比）
    this.cleanupInterval = 30 * 1000; // 清理间隔（30秒）
    this.forceGCInterval = 60 * 1000; // 强制垃圾回收间隔（1分钟）
    this.isMonitoring = false;
    
    this.cleanupCallbacks = new Set();
    this.memoryStats = {
      lastCleanup: Date.now(),
      cleanupCount: 0,
      gcCount: 0,
      peakMemory: 0
    };
  }

  /**
   * 开始内存监控
   */
  startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    logger.info('内存管理器启动');

    // 定期内存检查和清理
    this.cleanupTimer = setInterval(() => {
      this.checkAndCleanup();
    }, this.cleanupInterval);

    // 定期强制垃圾回收
    this.gcTimer = setInterval(() => {
      this.forceGarbageCollection();
    }, this.forceGCInterval);

    // 监控内存峰值
    this.peakTimer = setInterval(() => {
      this.updatePeakMemory();
    }, 10 * 1000); // 每10秒检查一次
  }

  /**
   * 停止内存监控
   */
  stopMonitoring() {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    
    if (this.cleanupTimer) clearInterval(this.cleanupTimer);
    if (this.gcTimer) clearInterval(this.gcTimer);
    if (this.peakTimer) clearInterval(this.peakTimer);
    
    logger.info('内存管理器停止');
  }

  /**
   * 注册清理回调函数
   */
  registerCleanupCallback(callback) {
    this.cleanupCallbacks.add(callback);
  }

  /**
   * 注销清理回调函数
   */
  unregisterCleanupCallback(callback) {
    this.cleanupCallbacks.delete(callback);
  }

  /**
   * 获取内存使用情况
   */
  getMemoryUsage() {
    const usage = process.memoryUsage();
    const usageRatio = Math.round((usage.heapUsed / usage.heapTotal) * 100);
    
    return {
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
      external: Math.round(usage.external / 1024 / 1024), // MB
      rss: Math.round(usage.rss / 1024 / 1024), // MB
      usageRatio: usageRatio,
      isHigh: usageRatio > this.memoryThreshold
    };
  }

  /**
   * 检查并清理内存
   */
  checkAndCleanup() {
    const memUsage = this.getMemoryUsage();
    
    if (memUsage.isHigh) {
      logger.warn('内存使用率过高，开始清理', {
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        usageRatio: `${memUsage.usageRatio}%`
      });
      
      this.performCleanup();
    }
  }

  /**
   * 执行内存清理
   */
  performCleanup() {
    const beforeMemory = this.getMemoryUsage();
    
    // 执行所有注册的清理回调
    for (const callback of this.cleanupCallbacks) {
      try {
        callback();
      } catch (error) {
        logger.error('清理回调执行失败', error);
      }
    }
    
    // 强制垃圾回收
    this.forceGarbageCollection();
    
    const afterMemory = this.getMemoryUsage();
    const freed = beforeMemory.heapUsed - afterMemory.heapUsed;
    
    this.memoryStats.lastCleanup = Date.now();
    this.memoryStats.cleanupCount++;
    
    logger.info('内存清理完成', {
      freedMB: freed,
      beforeMB: beforeMemory.heapUsed,
      afterMB: afterMemory.heapUsed,
      usageRatio: `${afterMemory.usageRatio}%`
    });
  }

  /**
   * 强制垃圾回收
   */
  forceGarbageCollection() {
    if (global.gc) {
      const beforeMemory = process.memoryUsage().heapUsed;
      global.gc();
      const afterMemory = process.memoryUsage().heapUsed;
      const freed = beforeMemory - afterMemory;
      
      this.memoryStats.gcCount++;
      
      if (freed > 1024 * 1024) { // 释放超过1MB时记录
        logger.debug('强制垃圾回收', {
          freedMB: Math.round(freed / 1024 / 1024),
          heapUsedMB: Math.round(afterMemory / 1024 / 1024)
        });
      }
    }
  }

  /**
   * 更新内存峰值
   */
  updatePeakMemory() {
    const current = this.getMemoryUsage();
    if (current.heapUsed > this.memoryStats.peakMemory) {
      this.memoryStats.peakMemory = current.heapUsed;
    }
  }

  /**
   * 获取内存统计信息
   */
  getStats() {
    return {
      ...this.memoryStats,
      currentMemory: this.getMemoryUsage(),
      isMonitoring: this.isMonitoring,
      registeredCallbacks: this.cleanupCallbacks.size
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.memoryStats = {
      lastCleanup: Date.now(),
      cleanupCount: 0,
      gcCount: 0,
      peakMemory: this.getMemoryUsage().heapUsed
    };
  }
}

// 创建全局实例
const memoryManager = new MemoryManager();

// 进程退出时停止监控
process.on('exit', () => {
  memoryManager.stopMonitoring();
});

process.on('SIGINT', () => {
  memoryManager.stopMonitoring();
  process.exit(0);
});

process.on('SIGTERM', () => {
  memoryManager.stopMonitoring();
  process.exit(0);
});

module.exports = memoryManager;
