const db = require("./utils/db");

async function debugUsers() {
  try {
    console.log("=== 调试用户数据 ===");
    
    // 1. 检查用户表是否存在
    console.log("\n1. 检查用户表结构:");
    const tableInfo = await db.query("DESCRIBE users");
    console.log("用户表字段:", tableInfo.map(field => `${field.Field} (${field.Type})`));
    
    // 2. 检查用户总数
    console.log("\n2. 检查用户总数:");
    const userCount = await db.query("SELECT COUNT(*) as total FROM users");
    console.log("用户总数:", userCount[0].total);
    
    // 3. 检查前5个用户的基本信息
    console.log("\n3. 检查前5个用户:");
    const users = await db.query("SELECT id, username, email, level, role, status, created_at FROM users LIMIT 5");
    console.log("用户列表:", users);
    
    // 4. 检查speed_limit_configs表是否存在
    console.log("\n4. 检查speed_limit_configs表:");
    try {
      const speedConfigs = await db.query("SELECT * FROM speed_limit_configs");
      console.log("速度限制配置:", speedConfigs);
    } catch (error) {
      console.log("speed_limit_configs表不存在或查询失败:", error.message);
    }
    
    // 5. 测试JOIN查询
    console.log("\n5. 测试JOIN查询:");
    try {
      const joinQuery = `
        SELECT
          u.id,
          u.username,
          u.email,
          u.level,
          u.role,
          u.status,
          u.created_at,
          u.updated_at,
          slc.download_speed,
          slc.concurrent_downloads,
          slc.daily_download_limit,
          slc.description
        FROM users u
        LEFT JOIN speed_limit_configs slc ON u.level COLLATE utf8mb4_unicode_ci = slc.level COLLATE utf8mb4_unicode_ci
        WHERE 1=1
        ORDER BY u.created_at DESC
        LIMIT 5
      `;
      const joinResult = await db.query(joinQuery);
      console.log("JOIN查询结果:", joinResult);
    } catch (error) {
      console.log("JOIN查询失败:", error.message);
    }
    
    // 6. 检查字符集和排序规则
    console.log("\n6. 检查表的字符集:");
    const usersCharset = await db.query("SELECT TABLE_SCHEMA, TABLE_NAME, TABLE_COLLATION FROM information_schema.TABLES WHERE TABLE_NAME = 'users'");
    console.log("users表字符集:", usersCharset);
    
    try {
      const speedCharset = await db.query("SELECT TABLE_SCHEMA, TABLE_NAME, TABLE_COLLATION FROM information_schema.TABLES WHERE TABLE_NAME = 'speed_limit_configs'");
      console.log("speed_limit_configs表字符集:", speedCharset);
    } catch (error) {
      console.log("无法获取speed_limit_configs表字符集信息");
    }
    
  } catch (error) {
    console.error("调试过程中出错:", error);
  } finally {
    process.exit(0);
  }
}

debugUsers();
