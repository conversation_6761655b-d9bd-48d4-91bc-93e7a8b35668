const logger = require('../plugin/logger');
const redisCache = require('../utils/redisCache');

// 响应优化中间件
function responseOptimizationMiddleware() {
  return async (ctx, next) => {
    const start = Date.now();
    
    // 检查是否是可缓存的GET请求
    if (ctx.method === 'GET' && shouldCache(ctx.path)) {
      const cacheKey = generateCacheKey(ctx);
      
      // 尝试从Redis获取缓存
      if (redisCache.isAvailable()) {
        const cached = await redisCache.get(cacheKey);
        if (cached) {
          ctx.status = cached.status;
          ctx.body = cached.body;
          ctx.set(cached.headers);
          ctx.set('X-Cache', 'HIT');
          ctx.set('X-Cache-Source', 'Redis');
          
          logger.debug('Redis缓存命中', {
            path: ctx.path,
            cacheKey: cacheKey.substring(0, 20) + '...'
          });
          
          return;
        }
      }
    }
    
    await next();
    
    const duration = Date.now() - start;
    
    // 设置性能响应头
    ctx.set('X-Response-Time', `${duration}ms`);
    ctx.set('X-Powered-By', 'Koa2-Blog');
    
    // 缓存响应（如果适用）
    if (ctx.method === 'GET' && ctx.status === 200 && shouldCache(ctx.path)) {
      const cacheKey = generateCacheKey(ctx);
      const ttl = getCacheTTL(ctx.path);
      
      const cacheData = {
        status: ctx.status,
        body: ctx.body,
        headers: { ...ctx.response.headers }
      };
      
      // 存储到Redis
      if (redisCache.isAvailable()) {
        await redisCache.set(cacheKey, cacheData, ttl);
        ctx.set('X-Cache', 'MISS');
        ctx.set('X-Cache-TTL', ttl.toString());
      }
    }
    
    // 响应压缩提示
    if (ctx.body && typeof ctx.body === 'object') {
      const bodySize = JSON.stringify(ctx.body).length;
      if (bodySize > 1024) {
        ctx.set('X-Original-Size', bodySize.toString());
      }
    }
  };
}

// 判断是否应该缓存
function shouldCache(path) {
  const cacheablePaths = [
    '/articles',
    '/user/profile',
    '/media',
    '/dashboard',
    '/showwall'
  ];
  
  const nonCacheablePaths = [
    '/logs',
    '/system-monitor',
    '/websocket',
    '/upload'
  ];
  
  // 排除不可缓存的路径
  if (nonCacheablePaths.some(p => path.includes(p))) {
    return false;
  }
  
  // 包含可缓存的路径
  return cacheablePaths.some(p => path.includes(p));
}

// 生成缓存键
function generateCacheKey(ctx) {
  const { method, path, query } = ctx;
  const userId = ctx.state.user?.id || 'anonymous';

  const keyData = {
    method,
    path,
    query: JSON.stringify(query),
    userId
  };

  // 生成简短的缓存键
  const hash = require('crypto').createHash('md5').update(JSON.stringify(keyData)).digest('hex');
  return `api:${hash}`;
}

// 获取缓存TTL（秒）
function getCacheTTL(path) {
  const ttlMap = {
    '/articles': 600,        // 文章缓存10分钟
    '/user/profile': 300,    // 用户信息缓存5分钟
    '/media': 1800,          // 媒体信息缓存30分钟
    '/dashboard': 120,       // 仪表盘缓存2分钟
    '/showwall': 900         // 展示墙缓存15分钟
  };
  
  for (const [pathPattern, ttl] of Object.entries(ttlMap)) {
    if (path.includes(pathPattern)) {
      return ttl;
    }
  }
  
  return 300; // 默认5分钟
}

// 缓存统计
function getCacheStats() {
  return {
    redisAvailable: redisCache.isAvailable(),
    timestamp: new Date().toISOString()
  };
}

// 清除缓存
async function clearCache(pattern = '*') {
  if (redisCache.isAvailable()) {
    if (pattern === '*') {
      await redisCache.flushAll();
    } else {
      // 这里可以实现模式匹配删除
      logger.info('模式删除缓存:', pattern);
    }
    return true;
  }
  return false;
}

module.exports = {
  responseOptimizationMiddleware,
  getCacheStats,
  clearCache
};
