const axios = require("axios");
const db = require("../db");

// AI写作助手服务
class AIWritingService {
  constructor() {
    this.config = {
      baseURL: 'http://localhost:11434',
      defaultModel: 'qwen2.5:0.5b', // 测试结果最佳模型
      fallbackModel: 'qwen2.5:1.5b', // 备选模型
      timeout: 15000, // 减少超时时间
      maxRetries: 3
    };

    this.functionTypes = {
      TITLE_GENERATION: 'title_generation',
      CONTENT_OPTIMIZATION: 'content_optimization',
      GRAMMAR_CHECK: 'grammar_check',
      SUMMARY_GENERATION: 'summary_generation',
      TAG_SUGGESTION: 'tag_suggestion',
      OUTLINE_CREATION: 'outline_creation',
      TRANSLATION: 'translation',
      STYLE_IMPROVEMENT: 'style_improvement'
    };
  }

  // 检查Ollama服务状态
  async checkOllamaService() {
    try {
      const response = await axios.get(`${this.config.baseURL}/api/tags`, {
        timeout: 5000
      });
      return {
        available: true,
        models: response.data.models || []
      };
    } catch (error) {
      return {
        available: false,
        error: error.message
      };
    }
  }

  // 调用Ollama API
  async callOllama(model, prompt, options = {}) {
    const requestData = {
      model: model || this.config.defaultModel,
      prompt,
      stream: false,
      options: {
        temperature: options.temperature || 0.7,
        top_p: options.top_p || 0.9,
        max_tokens: options.max_tokens || 500,
        ...options
      }
    };

    const response = await axios.post(`${this.config.baseURL}/api/generate`, requestData, {
      timeout: this.config.timeout
    });

    return response.data.response;
  }

  // 获取服务状态
  async getServiceStatus() {
    const serviceStatus = await this.checkOllamaService();
    return {
      available: serviceStatus.available,
      models: serviceStatus.models || [],
      currentModel: this.config.defaultModel,
      error: serviceStatus.error || null
    };
  }

  // 生成文章标题
  async generateTitle({ content, category, keywords, count = 3, userId }) {
    const serviceStatus = await this.checkOllamaService();
    if (!serviceStatus.available) {
      throw new Error("AI服务暂不可用: " + serviceStatus.error);
    }

    const prompt = `
请根据以下文章内容生成${count}个吸引人的标题：

文章分类：${category || '通用'}
关键词：${keywords || '无'}
文章内容：${content.substring(0, 500)}...

要求：
1. 标题要简洁有力，不超过30个字
2. 要体现文章的核心内容
3. 要有吸引力，能激发读者兴趣
4. 每个标题单独一行
5. 只返回标题，不要其他解释

标题：
`;

    const startTime = Date.now();
    const response = await this.callOllama(this.config.defaultModel, prompt, {
      temperature: 0.8,
      max_tokens: 200
    });
    const responseTime = Date.now() - startTime;

    // 解析标题
    const titles = response
      .split('\n')
      .filter(line => line.trim())
      .map(line => line.replace(/^\d+\.?\s*/, '').trim())
      .filter(title => title.length > 0)
      .slice(0, count);

    // 保存AI使用记录
    await this.saveAIUsage(userId, this.functionTypes.TITLE_GENERATION, prompt, response, responseTime);

    return {
      titles,
      responseTime,
      model: this.config.defaultModel
    };
  }

  // 内容优化
  async optimizeContent({ content, style = 'professional', target = 'general', userId }) {
    const serviceStatus = await this.checkOllamaService();
    if (!serviceStatus.available) {
      throw new Error("AI服务暂不可用: " + serviceStatus.error);
    }

    const styleMap = {
      'professional': '专业严谨',
      'casual': '轻松随意',
      'academic': '学术正式',
      'creative': '创意生动'
    };

    const prompt = `
请优化以下文本内容，使其更加${styleMap[style] || '专业'}，适合${target}读者：

原文：
${content}

优化要求：
1. 保持原意不变
2. 提升表达的准确性和流畅性
3. 调整语言风格为${styleMap[style] || '专业'}
4. 修正语法错误
5. 增强可读性

优化后的内容：
`;

    const startTime = Date.now();
    const response = await this.callOllama(this.config.defaultModel, prompt, {
      temperature: 0.5,
      max_tokens: 800
    });
    const responseTime = Date.now() - startTime;

    await this.saveAIUsage(userId, this.functionTypes.CONTENT_OPTIMIZATION, prompt, response, responseTime);

    return {
      optimizedContent: response.trim(),
      originalLength: content.length,
      optimizedLength: response.trim().length,
      responseTime,
      model: this.config.defaultModel
    };
  }

  // 生成文章大纲
  async generateOutline({ topic, category, depth = 'medium', userId }) {
    const serviceStatus = await this.checkOllamaService();
    if (!serviceStatus.available) {
      throw new Error("AI服务暂不可用: " + serviceStatus.error);
    }

    const depthMap = {
      'simple': '简单（3-5个要点）',
      'medium': '中等（5-8个要点）',
      'detailed': '详细（8-12个要点）'
    };

    const prompt = `
请为以下主题生成一个${depthMap[depth]}的文章大纲：

主题：${topic}
分类：${category || '通用'}

要求：
1. 大纲要逻辑清晰，层次分明
2. 每个要点要简洁明了
3. 要点之间要有逻辑关联
4. 适合${category || '通用'}类型的文章
5. 使用markdown格式

大纲：
`;

    const startTime = Date.now();
    const response = await this.callOllama(this.config.defaultModel, prompt, {
      temperature: 0.6,
      max_tokens: 600
    });
    const responseTime = Date.now() - startTime;

    await this.saveAIUsage(userId, this.functionTypes.OUTLINE_CREATION, prompt, response, responseTime);

    return {
      outline: response.trim(),
      responseTime,
      model: this.config.defaultModel
    };
  }

  // 标签建议
  async suggestTags({ title, content, category, maxTags = 5, userId }) {
    const serviceStatus = await this.checkOllamaService();
    if (!serviceStatus.available) {
      throw new Error("AI服务暂不可用: " + serviceStatus.error);
    }

    const prompt = `
请根据以下文章信息推荐${maxTags}个合适的标签：

标题：${title || '无'}
分类：${category || '无'}
内容：${content ? content.substring(0, 300) + '...' : '无'}

要求：
1. 标签要准确反映文章内容
2. 标签要简洁，1-3个字为佳
3. 优先选择常用的技术或主题标签
4. 每个标签单独一行
5. 只返回标签，不要其他解释

推荐标签：
`;

    const startTime = Date.now();
    const response = await this.callOllama(this.config.defaultModel, prompt, {
      temperature: 0.4,
      max_tokens: 150
    });
    const responseTime = Date.now() - startTime;

    // 解析标签
    const tags = response
      .split('\n')
      .filter(line => line.trim())
      .map(line => line.replace(/^\d+\.?\s*/, '').trim())
      .filter(tag => tag.length > 0 && tag.length <= 10)
      .slice(0, maxTags);

    await this.saveAIUsage(userId, this.functionTypes.TAG_SUGGESTION, prompt, response, responseTime);

    return {
      tags,
      responseTime,
      model: this.config.defaultModel
    };
  }

  // 获取使用统计
  async getUsageStats(userId, days = 7) {
    try {
      // 确保表存在
      await this.ensureUsageStatsTable();

      const sql = `
        SELECT
          function_type,
          COUNT(*) as usage_count,
          AVG(response_time) as avg_response_time,
          SUM(prompt_length) as total_prompt_length,
          SUM(response_length) as total_response_length,
          DATE(created_at) as usage_date
        FROM ai_usage_stats
        WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY function_type, DATE(created_at)
        ORDER BY usage_date DESC, function_type
      `;

      const stats = await db.query(sql, [userId, days]);

      // 汇总统计
      const summary = {
        totalUsage: 0,
        avgResponseTime: 0,
        functionStats: {},
        dailyStats: []
      };

      stats.forEach(stat => {
        summary.totalUsage += stat.usage_count;
        
        if (!summary.functionStats[stat.function_type]) {
          summary.functionStats[stat.function_type] = {
            count: 0,
            avgResponseTime: 0
          };
        }
        
        summary.functionStats[stat.function_type].count += stat.usage_count;
        summary.functionStats[stat.function_type].avgResponseTime = stat.avg_response_time;
      });

      // 计算平均响应时间
      if (stats.length > 0) {
        summary.avgResponseTime = stats.reduce((sum, stat) => sum + stat.avg_response_time, 0) / stats.length;
      }

      return {
        summary,
        details: stats
      };

    } catch (error) {
      console.error("获取使用统计失败:", error);
      throw error;
    }
  }

  // 保存AI使用记录
  async saveAIUsage(userId, functionType, prompt, response, responseTime) {
    try {
      await this.ensureUsageStatsTable();

      const insertSQL = `
        INSERT INTO ai_usage_stats (user_id, function_type, prompt_length, response_length, response_time, model_used)
        VALUES (?, ?, ?, ?, ?, ?)
      `;
      
      await db.query(insertSQL, [
        userId || 1,
        functionType,
        prompt.length,
        response.length,
        responseTime,
        this.config.defaultModel
      ]);

    } catch (error) {
      console.error("保存AI使用记录失败:", error);
    }
  }

  // 确保使用统计表存在
  async ensureUsageStatsTable() {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS ai_usage_stats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        function_type VARCHAR(50),
        prompt_length INT,
        response_length INT,
        response_time INT,
        model_used VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_function (user_id, function_type),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await db.query(createTableSQL);
  }
}

module.exports = new AIWritingService();
