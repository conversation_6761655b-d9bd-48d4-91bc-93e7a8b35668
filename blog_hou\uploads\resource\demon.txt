<template>
  <div class="dashboard-wrapper">
    <!-- 顶部统计 -->
    <el-row :gutter="16" class="stats-row">
      <el-col :span="4" v-for="item in stats" :key="item.label">
        <el-card class="stat-card" shadow="hover">
          <div class="label">{{ item.label }}</div>
          <div class="value">{{ item.value }}</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表类型切换 -->
    <div class="toolbar">
      <ChartTypeSwitcher v-model="chartType" />
      <el-select v-model="dateRange" size="small" style="width: 120px">
        <el-option label="近7天" value="7" />
        <el-option label="近30天" value="30" />
        <el-option label="全部" value="all" />
      </el-select>
    </div>

    <!-- 三个图表 -->
    <el-row :gutter="16" class="charts-row">
      <!-- 用户增长 -->
      <el-col :span="8">
        <el-card shadow="hover" class="chart-card">
          <div class="chart-title">用户增长趋势</div>
          <div v-if="userDates.length" ref="userChart" class="chart"></div>
          <el-empty v-else description="暂无数据~" />
        </el-card>
      </el-col>

      <!-- 文章发布 -->
      <el-col :span="8">
        <el-card shadow="hover" class="chart-card">
          <div class="chart-title">文章发布趋势</div>
          <div v-if="articleDates.length" ref="articleChart" class="chart"></div>
          <el-empty v-else description="暂无数据~" />
        </el-card>
      </el-col>

      <!-- 下载统计 -->
      <el-col :span="8">
        <el-card shadow="hover" class="chart-card">
          <div class="chart-title">资源下载统计</div>
          <div v-if="downloadData.length" ref="downloadChart" class="chart"></div>
          <el-empty v-else description="暂无数据~" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 下载明细 -->
    <el-card class="logs-card" shadow="hover">
      <div class="logs-header">
        <h3>资源下载明细</h3>
        <el-select v-model="filterType" size="small" style="width: 140px" @change="currentPage = 1">
          <el-option label="全部" value="" />
          <el-option label="图片" value="图片" />
          <el-option label="视频" value="视频" />
          <el-option label="文档" value="文档" />
          <el-option label="音频" value="音频" />
          <el-option label="其他" value="其他" />
        </el-select>
      </div>

      <el-table :data="pagedLogs" :empty-text="'暂无数据~'" style="width: 100%">
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="file_name" label="文件名" />
        <el-table-column label="类型" width="100">
          <template #default="scope">{{ mapFileType(scope.row.file_type) }}</template>
        </el-table-column>
        <el-table-column prop="count" label="下载次数" width="100" />
      </el-table>

      <el-pagination v-model:current-page="currentPage" :total="filteredLogs.length" :page-size="pageSize"
        layout="prev, pager, next" background style="margin-top: 12px; text-align: right" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import ChartTypeSwitcher from '@/components/ChartTypeSwitcher.vue'
import { dashboardApi, getCountApi } from '@/utils/api'

const stats = ref([])
const chartType = ref('line') // 默认折线图
const dateRange = ref('7')

const userChart = ref()
const articleChart = ref()
const downloadChart = ref()

const userDates = ref<string[]>([])
const articleDates = ref<string[]>([])
const downloadData = ref<any[]>([])

const downloadLogs = ref([])
const filterType = ref('')
const currentPage = ref(1)
const pageSize = 10

// 保存 ECharts 实例，避免重复初始化
let userChartInstance: echarts.ECharts | null = null
let articleChartInstance: echarts.ECharts | null = null
let downloadChartInstance: echarts.ECharts | null = null

function mapFileType(ext: string) {
  ext = ext.toLowerCase()
  if (['jpg', 'png', 'webp'].includes(ext)) return '图片'
  if (['mp4', 'mov'].includes(ext)) return '视频'
  if (['doc', 'pdf', 'xls'].includes(ext)) return '文档'
  if (['mp3', 'wav'].includes(ext)) return '音频'
  return '其他'
}

let userCounts: number[] = []
let articleCounts: number[] = []

async function fetchData() {
  const [statRes, trendRes] = await Promise.all([
    dashboardApi(),
    getCountApi(dateRange.value),
  ])

  stats.value = [
    { label: '用户总数', value: statRes.data.userCount },
    { label: '文章数量', value: statRes.data.articleCount },
    { label: '分享数量', value: statRes.data.shareCount },
    { label: '资源数量', value: statRes.data.resourceCount },
    { label: '资源下载次数', value: statRes.data.downloadCount },
  ]

  userDates.value = trendRes.data.userGrowth.map(i => i.date) || []
  userCounts = trendRes.data.userGrowth.map(i => i.count) || []

  articleDates.value = trendRes.data.articlePublish.map(i => i.date) || []
  articleCounts = trendRes.data.articlePublish.map(i => i.count) || []

  downloadData.value = trendRes.data.downloadData || []
  downloadLogs.value = downloadData.value

  await nextTick()
  renderChart()
}

function renderChart() {
  // 用户增长
  if (userChart.value) {
    if (userChartInstance) userChartInstance.dispose()
    userChartInstance = echarts.init(userChart.value)
    userChartInstance.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: userDates.value },
      yAxis: { type: 'value' },
      series: [{
        name: '用户数',
        data: userCounts, // 修正
        type: chartType.value,
        smooth: chartType.value === 'line',
        areaStyle: chartType.value === 'line' ? { color: '#67C23A' } : undefined,
        itemStyle: chartType.value === 'bar' ? { color: '#67C23A' } : undefined,
      }],
    })
  }
  // 文章发布
  if (articleChart.value) {
    if (articleChartInstance) articleChartInstance.dispose()
    articleChartInstance = echarts.init(articleChart.value)
    articleChartInstance.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: articleDates.value },
      yAxis: { type: 'value' },
      series: [{
        name: '文章数',
        data: articleCounts, // 修正
        type: chartType.value,
        smooth: chartType.value === 'line',
        areaStyle: chartType.value === 'line' ? { color: '#409EFF' } : undefined,
        itemStyle: chartType.value === 'bar' ? { color: '#409EFF' } : undefined,
      }],
    })
  }
  // 下载统计
  if (downloadChart.value) {
    if (downloadChartInstance) downloadChartInstance.dispose()
    downloadChartInstance = echarts.init(downloadChart.value)
    const typeMap = { 图片: 0, 视频: 0, 文档: 0, 音频: 0, 其他: 0 }
    downloadData.value.forEach(i => {
      const type = mapFileType(i.file_type)
      typeMap[type] += i.count
    })
    downloadChartInstance.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: Object.keys(typeMap) },
      yAxis: { type: 'value' },
      series: [{
        name: '下载次数',
        data: Object.values(typeMap),
        type: chartType.value,
        smooth: chartType.value === 'line',
        areaStyle: chartType.value === 'line' ? { color: '#909399' } : undefined,
        itemStyle: chartType.value === 'bar' ? { color: '#909399' } : undefined,
      }],
    })
  }
}

const filteredLogs = computed(() =>
  filterType.value
    ? downloadLogs.value.filter(i => mapFileType(i.file_type) === filterType.value)
    : downloadLogs.value
)

const pagedLogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  return filteredLogs.value.slice(start, start + pageSize)
})

// 监听 chartType 和 dateRange，变化时重新渲染图表
watch([chartType, dateRange], async () => {
  await fetchData()
})

// 页面初次加载
onMounted(async () => {
  await fetchData()
})
</script>

<style scoped lang="less">
.dashboard-wrapper {
  padding: 20px;
  .stats-row {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;

      .label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }

      .value {
        font-size: 22px;
        color: #409eff;
        font-weight: bold;
      }
    }
  }

  .toolbar {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 20px;
    gap: 12px;
  }

  .charts-row {
    margin-bottom: 20px;

    .chart-card {
      height: 360px;

      .chart-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
      }

      .chart {
        width: 100%;
        height: 280px;
      }
    }
  }

  .logs-card {
    .logs-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
  }
}
</style>
