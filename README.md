前端文档说明
一、项目简介
本项目为博客系统的前端，基于 Vue 3 + TypeScript + Vite 技术栈开发，配合 Element Plus、Vant、TailwindCSS 等 UI 框架，支持文章管理、用户中心、文件上传、资源预览、聊天室、AI对话等功能。前后端分离，所有数据通过 RESTful API 或 WebSocket 与后端交互。

二、目录结构说明
目录/文件	说明
index.html	项目入口 HTML 文件
package.json	项目依赖与脚本配置
vite.config.ts	Vite 构建与开发配置
src/	源码目录
├─ main.ts	应用入口，挂载 Vue 实例
├─ App.vue	根组件，包含路由出口
├─ assets/	静态资源（图片等）
├─ components/	公共组件（如评论、加密、导出SQL等）
├─ composables/	组合式 API 封装（如图片预览）
├─ config/	配置文件（如表单校验规则）
├─ css/	样式文件（如 TailwindCSS）
├─ design/	设计相关页面或组件
├─ plug/	通用 JS 工具（如复制功能）
├─ router/	路由配置
├─ utils/	工具函数、API 封装
├─ views/	业务页面（如首页、文章、资源、上传、AI模型、聊天室等）

三、主要功能模块
用户中心：注册、登录、个人信息、头像上传、标签管理等
文章管理：文章列表、详情、编辑、发布、评论、标签、分类、回收站
文件与资源：文件上传、分片上传、资源列表、下载、预览
聊天室：WebSocket 实时聊天
AI大模型：与后端大模型接口对话
创意中心：创意/灵感记录与管理
Bug管理：Bug 列表、增删改查、批量操作
日志监控：查看后端日志
加密/解密：图片、视频加解密操作

四、技术栈
Vue 3 + TypeScript
Vite（开发/构建工具）
Element Plus、Vant（UI 组件库）
TailwindCSS（原子化 CSS 框架）
Axios（HTTP 请求）
Socket.io-client（WebSocket 通信）
highlight.js、markdown-it（代码高亮与 Markdown 渲染）

五、开发与运行
pnpm install
# 或 npm install

pnpm dev
# 或 npm run dev

访问地址
默认 http://localhost:5173

六、API 交互说明
所有 API 统一通过 /api 前缀代理到后端（见 vite.config.ts 的 proxy 配置）
登录后接口需携带 Authorization: Bearer <token>，token 存储于 localStorage
统一封装在 api.ts，便于维护和调用

七、常见问题
接口 401 未授权：请重新登录，token 失效会自动跳转登录页
静态资源无法访问：请检查后端静态目录和代理配置