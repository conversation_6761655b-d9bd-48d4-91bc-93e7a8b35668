const Router = require("koa-router");
const fs = require("fs");
const path = require("path");
const send = require("koa-send");
const logger = require("../../plugin/logger");
const exportService = require("../../utils/sql/exportService");

const daosql = new Router();

daosql.get("/export/download", async (ctx) => {
  const connection = exportService.createConnection();

  try {
    connection.connect();

    const tableNames = await exportService.getAllTables(connection);

    let sqlOutput = "";
    sqlOutput += `-- 导出时间: ${new Date().toLocaleString()}\n`;
    sqlOutput += `-- 数据库: ${exportService.dbConfig.database}\n`;
    sqlOutput += `-- ---------------------------------------------\n\n`;

    for (const tableName of tableNames) {
      const createTableSql = await exportService.getTableStructure(connection, tableName);

      sqlOutput += `-- ----------------------------\n`;
      sqlOutput += `-- Table structure for \`${tableName}\`\n`;
      sqlOutput += `-- ----------------------------\n`;
      sqlOutput += `DROP TABLE IF EXISTS \`${tableName}\`;\n`;
      sqlOutput += createTableSql + ";\n\n";

      // 只导出前2条数据
      const rows = await exportService.getTableData(connection, tableName);
      if (rows.length > 0) {
        sqlOutput += `-- ----------------------------\n`;
        sqlOutput += `-- Records of \`${tableName}\`\n`;
        sqlOutput += `-- ----------------------------\n`;

        rows.forEach((row) => {
          const keys = Object.keys(row)
            .map((k) => `\`${k}\``)
            .join(", ");
          const values = Object.values(row).map(exportService.escapeValue).join(", ");
          sqlOutput += `INSERT INTO \`${tableName}\` (${keys}) VALUES (${values});\n`;
        });
        sqlOutput += "\n";
      }
    }

    connection.end();

    // 优化：统一导出目录到 backend/export
    const exportDir = path.join(__dirname, "../../export");
    if (!fs.existsSync(exportDir)) fs.mkdirSync(exportDir, { recursive: true });

    const sqlFile = path.join(exportDir, "latest_export.sql");

    fs.writeFileSync(sqlFile, sqlOutput, "utf8"); // 每次覆盖旧文件

    // 直接发送文件给客户端，触发下载
    await send(ctx, "latest_export.sql", { root: exportDir });
    logger.info(`导出sql成功 - IP: ${ctx.ip}`);
  } catch (err) {
    connection.end();
    ctx.status = 500;
    ctx.body = { error: err.message };
  }
});

module.exports = daosql;
