<template>
  <div class="photo-wall" @scroll.passive="onScroll" ref="container">
    <span class="text-lg font-bold">照片墙</span>
    <el-button style="margin-left: 10px" @click="$router.back()"
      >返回</el-button
    >

    <div class="photo-list">
      <div v-for="(img, index) in images" :key="index" class="photo-item">
        <!-- // img 现在是 /showwall/image/xxx.jpg -->
        <img
          v-if="!img.endsWith('.enc')"
          :data-src="img"  
          alt="photo"
          class="lazy-img"
        />
        <div v-else class="encrypted-placeholder">
          <span class="encrypted-text">已加密</span>
        </div>
        <div
          class="zoom-icon"
          @click="showPreview(img)"
          title="点击放大"
        >
          🔍
        </div>
      </div>
    </div>
    <div v-if="loading" class="loading">加载中...</div>
    <div v-if="!loadingMore && !loading" class="no-more">没有更多图片了</div>
    <div v-if="!images.length && !loading" class="no-images">暂无图片</div>

    <!-- 放大预览遮罩 -->
    <div v-if="previewVisible" class="preview-mask" @click="closePreview">
      <img :src="previewSrc" class="preview-image" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import { ShowWallApi } from "../utils/api";
import { useImagePreview } from "../composables/useImagePreview";

const images = ref([]);
const page = ref(1);
const limit = 20;
const loading = ref(false);
const loadingMore = ref(true);
const container = ref(null);

const { previewVisible, previewSrc, showPreview, closePreview } =
  useImagePreview();

// 懒加载 IntersectionObserver
let observer = null;
const initLazyObserver = () => {
  observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        observer.unobserve(img);
      }
    });
  });
  document
    .querySelectorAll(".lazy-img")
    .forEach((img) => observer.observe(img));
};

const fetchMoreImages = async () => {
  if (!loadingMore.value || loading.value) return;
  loading.value = true;
  try {
    const res = await ShowWallApi({ page: page.value, limit });
    console.log("获取图片列表", res);
    if (res.length) {
      images.value.push(...res);
      page.value++;
      await nextTick();
      initLazyObserver(); // 初始化懒加载
    } else {
      loadingMore.value = false;
    }
  } catch (error) {
    console.error("获取图片列表失败", error);
  } finally {
    loading.value = false;
  }
};

// 滚动节流处理
let scrollThrottleTimer = null;
const onScroll = () => {
  if (scrollThrottleTimer) return;
  scrollThrottleTimer = setTimeout(() => {
    scrollThrottleTimer = null;
    if (!container.value) return;
    const scrollBottom =
      container.value.scrollHeight -
      container.value.scrollTop -
      container.value.clientHeight;
    if (scrollBottom < 150) fetchMoreImages();
  }, 200);
};

onMounted(() => {
  fetchMoreImages();
});
</script>

<style scoped>
.photo-wall {
  height: 100vh;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #ddd;
  position: relative;
  background: #f7f7f7;
}

.photo-list {
  column-gap: 12px;
  column-count: 1;
}

@media (min-width: 480px) {
  .photo-list {
    column-count: 2;
  }
}
@media (min-width: 768px) {
  .photo-list {
    column-count: 3;
  }
}
@media (min-width: 1024px) {
  .photo-list {
    column-count: 4;
  }
}

.photo-item {
  break-inside: avoid;
  margin-bottom: 12px;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgb(0 0 0 / 0.1);
  background: #fff;
  position: relative;
}

.photo-item img {
  width: 100%;
  display: block;
  object-fit: cover;
  aspect-ratio: 1/1;
  border-radius: 6px;
}

/* 放大图标 */
.zoom-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.4);
  color: white;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  font-size: 18px;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.3s;
}
.zoom-icon:hover {
  background: rgba(0, 0, 0, 0.7);
}

/* 放大预览遮罩 */
.preview-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  cursor: zoom-out;
}
.preview-image {
  max-width: 90vw;
  max-height: 90vh;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.loading,
.no-more,
.no-images {
  text-align: center;
  padding: 10px 0;
  color: #666;
}
.encrypted-placeholder {
  width: 100%;
  height: auto;
  aspect-ratio: 1/1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #eee;
  border-radius: 6px;
  font-size: 16px;
  color: #999;
  position: relative;
}
.encrypted-text {
  font-weight: bold;
}
</style>
