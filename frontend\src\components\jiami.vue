<template>
  <div class="encrypt-container">
    <h2>图片加/解密</h2>

    <el-radio-group v-model="mode" size="large" class="mode-toggle">
      <el-radio-button label="1">加密</el-radio-button>
      <el-radio-button label="0">解密</el-radio-button>
    </el-radio-group>

    <el-button
      type="primary"
      :loading="loading"
      size="large"
      class="action-button"
      @click="handleProcess"
    >
      <el-icon class="mr-1">
        <component :is="mode === '1' ? 'Lock' : 'Unlock'" />
      </el-icon>
      {{
        loading
          ? "处理中..."
          : mode === "1"
          ? "加密图片文件夹"
          : "解密图片文件夹"
      }}
    </el-button>

    <p class="status-text">{{ status }}</p>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { EncryptImagesApi } from "../utils/api";
import { Lock, Unlock } from "@element-plus/icons-vue";

const loading = ref(false);
const status = ref("");
const mode = ref("1"); // 默认加密（"1" 为加密，"0" 为解密）

const handleProcess = async () => {
  loading.value = true;
  status.value = mode.value === "1" ? "正在加密图片..." : "正在解密图片...";

  try {
    const res = await EncryptImagesApi({ type: mode.value });
    status.value =
      res.message || (mode.value === "1" ? "加密完成！" : "解密完成！");
  } catch (err) {
    console.error(err);
    status.value =
      mode.value === "1" ? "加密失败，请检查后端" : "解密失败，请检查后端";
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.encrypt-container {
  max-width: 500px;
  margin: 60px auto;
  text-align: center;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  background-color: #fafafa;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

h2 {
  margin-bottom: 20px;
  font-weight: 600;
  color: #333;
}

.mode-toggle {
  margin-bottom: 24px;
}

.action-button {
  width: 100%;
  font-size: 16px;
  height: 48px;
}

.status-text {
  margin-top: 20px;
  color: #666;
  font-size: 14px;
  min-height: 24px;
}

.mr-1 {
  margin-right: 6px;
}
</style>
