const Router = require("koa-router");
const aiWritingService = require("../../utils/ai/aiWritingService");

const aiWritingRouter = new Router();

// 获取AI服务状态
aiWritingRouter.get("/status", async (ctx) => {
  try {
    const status = await aiWritingService.getServiceStatus();
    ctx.body = {
      code: 200,
      message: "获取状态成功",
      data: status
    };
  } catch (error) {
    console.error("获取AI状态失败:", error);
    ctx.body = {
      code: 500,
      message: "获取AI状态失败",
      error: error.message
    };
  }
});

// 生成文章标题
aiWritingRouter.post("/generate-title", async (ctx) => {
  try {
    const { content, category, keywords, count = 3 } = ctx.request.body;
    const userId = ctx.state.user?.userId || ctx.state.user?.id;
    
    if (!content) {
      ctx.body = { code: 400, message: "文章内容不能为空" };
      return;
    }

    const result = await aiWritingService.generateTitle({
      content,
      category,
      keywords,
      count,
      userId
    });

    ctx.body = {
      code: 200,
      message: "标题生成成功",
      data: result
    };

  } catch (error) {
    console.error("生成标题失败:", error);
    ctx.body = {
      code: 500,
      message: error.message || "生成标题失败",
      error: error.message
    };
  }
});

// 内容优化
aiWritingRouter.post("/optimize-content", async (ctx) => {
  try {
    const { content, style = 'professional', target = 'general' } = ctx.request.body;
    const userId = ctx.state.user?.userId || ctx.state.user?.id;
    
    if (!content) {
      ctx.body = { code: 400, message: "内容不能为空" };
      return;
    }

    const result = await aiWritingService.optimizeContent({
      content,
      style,
      target,
      userId
    });

    ctx.body = {
      code: 200,
      message: "内容优化成功",
      data: result
    };

  } catch (error) {
    console.error("内容优化失败:", error);
    ctx.body = {
      code: 500,
      message: error.message || "内容优化失败",
      error: error.message
    };
  }
});

// 生成文章大纲
aiWritingRouter.post("/generate-outline", async (ctx) => {
  try {
    const { topic, category, depth = 'medium' } = ctx.request.body;
    const userId = ctx.state.user?.userId || ctx.state.user?.id;
    
    if (!topic) {
      ctx.body = { code: 400, message: "主题不能为空" };
      return;
    }

    const result = await aiWritingService.generateOutline({
      topic,
      category,
      depth,
      userId
    });

    ctx.body = {
      code: 200,
      message: "大纲生成成功",
      data: result
    };

  } catch (error) {
    console.error("生成大纲失败:", error);
    ctx.body = {
      code: 500,
      message: error.message || "生成大纲失败",
      error: error.message
    };
  }
});

// 标签建议
aiWritingRouter.post("/suggest-tags", async (ctx) => {
  try {
    const { title, content, category, maxTags = 5 } = ctx.request.body;
    const userId = ctx.state.user?.userId || ctx.state.user?.id;
    
    if (!title && !content) {
      ctx.body = { code: 400, message: "标题或内容不能为空" };
      return;
    }

    const result = await aiWritingService.suggestTags({
      title,
      content,
      category,
      maxTags,
      userId
    });

    ctx.body = {
      code: 200,
      message: "标签推荐成功",
      data: result
    };

  } catch (error) {
    console.error("标签推荐失败:", error);
    ctx.body = {
      code: 500,
      message: error.message || "标签推荐失败",
      error: error.message
    };
  }
});

// 获取AI使用统计
aiWritingRouter.get("/usage-stats", async (ctx) => {
  try {
    const userId = ctx.state.user?.userId || ctx.state.user?.id;
    const { days = 7 } = ctx.query;

    const stats = await aiWritingService.getUsageStats(userId, days);

    ctx.body = {
      code: 200,
      message: "获取使用统计成功",
      data: stats
    };

  } catch (error) {
    console.error("获取使用统计失败:", error);
    ctx.body = {
      code: 500,
      message: "获取使用统计失败",
      error: error.message
    };
  }
});

module.exports = aiWritingRouter;
