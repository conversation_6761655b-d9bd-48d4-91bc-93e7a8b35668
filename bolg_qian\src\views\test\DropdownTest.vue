<template>
  <div class="dropdown-test-page">
    <el-card class="test-card">
      <template #header>
        <h2>🎨 下拉菜单样式测试</h2>
      </template>
      
      <div class="test-content">
        <div class="test-section">
          <h3>📋 测试说明</h3>
          <p>此页面用于测试仪表盘下拉菜单的颜色和样式修复效果。</p>
          
          <el-alert type="success" :closable="false" style="margin: 16px 0;">
            <template #title>
              <strong>✅ 已修复的问题</strong>
            </template>
            <ul style="margin: 8px 0; padding-left: 20px;">
              <li>下拉菜单背景色：白色</li>
              <li>菜单项文字颜色：深灰色 (#374151)</li>
              <li>悬停效果：浅灰背景 + 蓝色文字</li>
              <li>图标颜色：灰色，悬停时变蓝色</li>
              <li>退出登录项：红色文字和图标</li>
            </ul>
          </el-alert>
        </div>

        <div class="test-section">
          <h3>🧪 测试用例</h3>
          
          <div class="test-case">
            <h4>1. 用户下拉菜单测试</h4>
            <p>点击下方的用户头像测试下拉菜单：</p>
            <div class="test-dropdown">
              <el-dropdown trigger="click" placement="bottom-start">
                <div class="test-user-dropdown">
                  <el-avatar :size="32" style="background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);">
                    {{ username?.charAt(0)?.toUpperCase() }}
                  </el-avatar>
                  <span style="margin-left: 8px; color: #1f2937;">{{ username }}</span>
                  <el-icon style="margin-left: 8px; color: #6b7280;"><ArrowDown /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item>
                      <el-icon><User /></el-icon>
                      <span>个人资料</span>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-icon><Setting /></el-icon>
                      <span>系统设置</span>
                    </el-dropdown-item>
                    <el-dropdown-item divided>
                      <el-icon><SwitchButton /></el-icon>
                      <span>退出登录</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <div class="test-case">
            <h4>2. 普通下拉菜单测试</h4>
            <p>测试其他类型的下拉菜单：</p>
            <div class="test-dropdown">
              <el-dropdown trigger="click">
                <el-button type="primary">
                  操作菜单
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item>
                      <el-icon><Edit /></el-icon>
                      <span>编辑</span>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-icon><View /></el-icon>
                      <span>查看</span>
                    </el-dropdown-item>
                    <el-dropdown-item divided>
                      <el-icon><Delete /></el-icon>
                      <span>删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>

        <div class="test-section">
          <h3>🎨 样式对比</h3>
          <div class="style-comparison">
            <div class="before-after">
              <div class="before">
                <h4>❌ 修复前</h4>
                <ul>
                  <li>背景：白色</li>
                  <li>文字：白色（不可见）</li>
                  <li>图标：白色（不可见）</li>
                  <li>悬停：无效果</li>
                </ul>
              </div>
              <div class="after">
                <h4>✅ 修复后</h4>
                <ul>
                  <li>背景：白色</li>
                  <li>文字：深灰色（可见）</li>
                  <li>图标：灰色（可见）</li>
                  <li>悬停：蓝色高亮</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div class="test-section">
          <h3>🔧 技术实现</h3>
          <el-collapse>
            <el-collapse-item title="查看修复代码" name="code">
              <pre class="code-preview"><code>/* 全局下拉菜单样式修复 */
:deep(.el-dropdown-menu) {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
}

:deep(.el-dropdown-menu__item) {
  color: #374151 !important;
  font-size: 14px !important;
  
  &:hover {
    background: #f3f4f6 !important;
    color: #1e40af !important;
  }
  
  .el-icon {
    color: #6b7280 !important;
  }
  
  &:hover .el-icon {
    color: #1e40af !important;
  }
}</code></pre>
            </el-collapse-item>
          </el-collapse>
        </div>

        <div class="test-actions">
          <el-button type="primary" @click="goToDashboard">
            <el-icon><DataBoard /></el-icon>
            前往仪表盘
          </el-button>
          <el-button type="success" @click="refreshPage">
            <el-icon><Refresh /></el-icon>
            刷新页面
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { 
  ArrowDown, User, Setting, SwitchButton, Edit, View, Delete,
  DataBoard, Refresh
} from '@element-plus/icons-vue';

const router = useRouter();
const username = localStorage.getItem('username') || '测试用户';

const goToDashboard = () => {
  router.push('/index/dashboard');
};

const refreshPage = () => {
  window.location.reload();
};
</script>

<style scoped>
.dropdown-test-page {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.test-card h2 {
  margin: 0;
  color: #409eff;
  text-align: center;
}

.test-content {
  padding: 20px 0;
}

.test-section {
  margin-bottom: 32px;
}

.test-section h3 {
  color: #2c3e50;
  margin-bottom: 16px;
  font-size: 18px;
}

.test-case {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.test-case h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.test-dropdown {
  margin-top: 12px;
}

.test-user-dropdown {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  border: 1px solid #e5e7eb;
}

.test-user-dropdown:hover {
  background: #f3f4f6;
}

.style-comparison {
  margin-top: 16px;
}

.before-after {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.before, .after {
  padding: 16px;
  border-radius: 8px;
}

.before {
  background: #fef2f2;
  border-left: 4px solid #ef4444;
}

.after {
  background: #f0fdf4;
  border-left: 4px solid #22c55e;
}

.before h4, .after h4 {
  margin: 0 0 12px 0;
}

.before ul, .after ul {
  margin: 0;
  padding-left: 20px;
}

.code-preview {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #2c3e50;
  overflow-x: auto;
}

.test-actions {
  text-align: center;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.test-actions .el-button {
  margin: 0 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dropdown-test-page {
    padding: 10px;
  }
  
  .before-after {
    grid-template-columns: 1fr;
  }
  
  .test-actions .el-button {
    width: 100%;
    margin: 4px 0;
  }
}
</style>
