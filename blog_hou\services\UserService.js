// 用户服务类
const BaseService = require('./BaseService');
const bcrypt = require('bcrypt');

class UserService extends BaseService {
  constructor() {
    super('users');
  }

  // 用户注册
  async register(userData) {
    try {
      const { username, password, email, nickname } = userData;

      // 检查用户名是否已存在
      const existingUser = await this.findWhere({ username }, 'id', 1);
      if (existingUser.length > 0) {
        throw new Error('用户名已存在');
      }

      // 检查邮箱是否已存在
      const existingEmail = await this.findWhere({ email }, 'id', 1);
      if (existingEmail.length > 0) {
        throw new Error('邮箱已被注册');
      }

      // 加密密码
      const hashedPassword = await bcrypt.hash(password, 10);

      // 创建用户
      const userId = await this.create({
        username,
        password: hashedPassword,
        email,
        nickname: nickname || username,
        role: 'user',
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      });

      this.logger.info('用户注册成功', { userId, username, email });

      return userId;
    } catch (error) {
      this.logger.error('用户注册失败', { userData: { ...userData, password: '[REDACTED]' }, error: error.message });
      throw error;
    }
  }

  // 用户登录验证
  async authenticate(username, password) {
    try {
      // 查找用户
      const user = await this.findWhere({ username }, 'id', 1);
      if (user.length === 0) {
        throw new Error('用户不存在');
      }

      const userData = user[0];

      // 检查用户状态
      if (userData.status !== 1) {
        throw new Error('用户账户已被禁用');
      }

      // 验证密码
      const isPasswordValid = await bcrypt.compare(password, userData.password);
      if (!isPasswordValid) {
        throw new Error('密码错误');
      }

      // 更新最后登录时间
      await this.update(userData.id, {
        last_login_at: new Date(),
        login_count: (userData.login_count || 0) + 1
      });

      // 返回用户信息（不包含密码）
      const { password: _, ...userInfo } = userData;
      
      this.logger.info('用户登录成功', { userId: userData.id, username });

      return userInfo;
    } catch (error) {
      this.logger.error('用户登录失败', { username, error: error.message });
      throw error;
    }
  }

  // 获取用户信息
  async getUserById(id, includePrivate = false) {
    try {
      const user = await this.findById(id);
      
      if (!user) {
        return null;
      }

      // 移除敏感信息
      const { password, ...userInfo } = user;
      
      if (!includePrivate) {
        delete userInfo.email;
        delete userInfo.phone;
      }

      return userInfo;
    } catch (error) {
      this.logger.error('获取用户信息失败', { id, error: error.message });
      throw error;
    }
  }

  // 更新用户信息
  async updateUser(id, updateData) {
    try {
      const allowedFields = ['nickname', 'email', 'phone', 'avatar', 'bio'];
      const data = {};

      // 只允许更新指定字段
      for (const field of allowedFields) {
        if (updateData[field] !== undefined) {
          data[field] = updateData[field];
        }
      }

      if (Object.keys(data).length === 0) {
        throw new Error('没有可更新的字段');
      }

      data.updated_at = new Date();

      const success = await this.update(id, data);

      if (success) {
        this.logger.info('用户信息更新成功', { id, updateData });
      }

      return success;
    } catch (error) {
      this.logger.error('更新用户信息失败', { id, updateData, error: error.message });
      throw error;
    }
  }

  // 修改密码
  async changePassword(id, oldPassword, newPassword) {
    try {
      // 获取用户当前密码
      const user = await this.findById(id);
      if (!user) {
        throw new Error('用户不存在');
      }

      // 验证旧密码
      const isOldPasswordValid = await bcrypt.compare(oldPassword, user.password);
      if (!isOldPasswordValid) {
        throw new Error('原密码错误');
      }

      // 加密新密码
      const hashedNewPassword = await bcrypt.hash(newPassword, 10);

      // 更新密码
      const success = await this.update(id, {
        password: hashedNewPassword,
        updated_at: new Date()
      });

      if (success) {
        this.logger.info('用户密码修改成功', { id });
      }

      return success;
    } catch (error) {
      this.logger.error('修改密码失败', { id, error: error.message });
      throw error;
    }
  }

  // 获取用户列表
  async getUsers(options = {}) {
    const {
      page = 1,
      limit = 10,
      role = null,
      status = null,
      search = null,
      orderBy = 'created_at DESC'
    } = options;

    try {
      let whereConditions = [];
      let params = [];

      // 角色筛选
      if (role) {
        whereConditions.push('role = ?');
        params.push(role);
      }

      // 状态筛选
      if (status !== null) {
        whereConditions.push('status = ?');
        params.push(status);
      }

      // 搜索功能
      if (search) {
        whereConditions.push('(username LIKE ? OR nickname LIKE ? OR email LIKE ?)');
        params.push(`%${search}%`, `%${search}%`, `%${search}%`);
      }

      const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';
      
      const baseQuery = `
        SELECT id, username, nickname, email, role, status, avatar, created_at, updated_at, last_login_at
        FROM ${this.tableName} 
        ${whereClause}
        ORDER BY ${orderBy}
      `;

      const result = await this.paginate(baseQuery, params, page, limit);

      // 格式化时间
      result.data = result.data.map(user => ({
        ...user,
        created_at: new Date(user.created_at).toISOString(),
        updated_at: new Date(user.updated_at).toISOString(),
        last_login_at: user.last_login_at ? new Date(user.last_login_at).toISOString() : null
      }));

      return result;
    } catch (error) {
      this.logger.error('获取用户列表失败', { options, error: error.message });
      throw error;
    }
  }

  // 禁用/启用用户
  async toggleUserStatus(id) {
    try {
      const user = await this.findById(id);
      if (!user) {
        throw new Error('用户不存在');
      }

      const newStatus = user.status === 1 ? 0 : 1;
      const success = await this.update(id, {
        status: newStatus,
        updated_at: new Date()
      });

      if (success) {
        this.logger.info('用户状态切换成功', { id, oldStatus: user.status, newStatus });
      }

      return success;
    } catch (error) {
      this.logger.error('切换用户状态失败', { id, error: error.message });
      throw error;
    }
  }

  // 获取用户统计信息
  async getUserStats() {
    try {
      const sql = `
        SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active,
          SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as inactive,
          SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admins,
          SUM(CASE WHEN role = 'user' THEN 1 ELSE 0 END) as users,
          SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_users_30d
        FROM ${this.tableName}
      `;

      const result = await this.queryWithCache(sql, [], 'user_stats', 300000);

      return result[0];
    } catch (error) {
      this.logger.error('获取用户统计失败', { error: error.message });
      throw error;
    }
  }

  // 检查用户权限
  async checkPermission(userId, permission) {
    try {
      const user = await this.findById(userId);
      if (!user) {
        return false;
      }

      // 管理员拥有所有权限
      if (user.role === 'admin') {
        return true;
      }

      // 这里可以扩展更复杂的权限系统
      const userPermissions = {
        'user': ['read', 'create_comment'],
        'moderator': ['read', 'create_comment', 'moderate_comment'],
        'admin': ['*']
      };

      const rolePermissions = userPermissions[user.role] || [];
      return rolePermissions.includes(permission) || rolePermissions.includes('*');
    } catch (error) {
      this.logger.error('检查用户权限失败', { userId, permission, error: error.message });
      return false;
    }
  }

  // 搜索用户
  async searchUsers(keyword, limit = 10) {
    try {
      const sql = `
        SELECT id, username, nickname, avatar
        FROM ${this.tableName} 
        WHERE status = 1 AND (username LIKE ? OR nickname LIKE ?)
        ORDER BY username
        LIMIT ?
      `;

      const searchParam = `%${keyword}%`;
      const result = await this.query(sql, [searchParam, searchParam, limit]);

      return result;
    } catch (error) {
      this.logger.error('搜索用户失败', { keyword, error: error.message });
      throw error;
    }
  }
}

module.exports = UserService;
