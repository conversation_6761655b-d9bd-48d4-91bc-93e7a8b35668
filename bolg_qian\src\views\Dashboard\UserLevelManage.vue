<template>
  <div class="user-level-manage">
    <!-- 等级统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="16">
        <el-col :span="6" v-for="stat in levelStats" :key="stat.level">
          <el-card class="stat-card" :class="`level-${stat.level}`">
            <div class="stat-content">
              <div class="stat-header">
                <span class="level-name">{{ getLevelName(stat.level) }}</span>
                <el-tag :type="getLevelTagType(stat.level)" size="small">
                  {{ stat.user_count }}人
                </el-tag>
              </div>
              <div class="stat-details">
                <div class="stat-item">
                  <span class="label">下载速度:</span>
                  <span class="value">{{ stat.download_speed }}KB/s</span>
                </div>
                <div class="stat-item">
                  <span class="label">今日活跃:</span>
                  <span class="value">{{ stat.today_active_users }}人</span>
                </div>
                <div class="stat-item">
                  <span class="label">今日下载:</span>
                  <span class="value">{{ (stat.today_downloaded / 1024 / 1024).toFixed(2) }}MB</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <div class="search-bar">
        <el-row :gutter="16" align="middle">
          <el-col :span="6">
            <el-input
                v-model="searchForm.search"
                placeholder="搜索用户名或邮箱"
                clearable
                @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select
                v-model="searchForm.level"
                placeholder="选择等级"
                clearable
                @change="handleSearch"
            >
              <el-option label="全部等级" value="all" />
              <el-option label="免费用户" value="free" />
              <el-option label="基础会员" value="basic" />
              <el-option label="高级会员" value="premium" />
              <el-option label="VIP会员" value="vip" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button
                type="warning"
                @click="showBatchChangeDialog"
                :disabled="selectedUsers.length === 0"
            >
              <el-icon><Edit /></el-icon>
              批量修改等级
            </el-button>
          </el-col>
          <el-col :span="8" class="text-right">
            <el-button type="info" @click="showLevelLogs">
              <el-icon><Document /></el-icon>
              查看变更日志
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>用户列表</span>
          <span class="total-count">共 {{ pagination.total }} 个用户</span>
        </div>
      </template>

      <el-table
          :data="userList"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column label="当前等级" width="120">
          <template #default="scope">
            <el-tag :type="getLevelTagType(scope.row.level)">
              {{ getLevelName(scope.row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="等级权限" width="200">
          <template #default="scope">
            <div class="level-info">
              <div>下载: {{ scope.row.download_speed }}KB/s</div>
              <div>并发: {{ scope.row.concurrent_downloads }}</div>
              <div>
                限额: {{ scope.row.daily_download_limit
                  ? (scope.row.daily_download_limit / 1024 / 1024).toFixed(0) + 'MB'
                  : '无限制' }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="使用统计" width="150">
          <template #default="scope">
            <div class="usage-stats">
              <div>文章: {{ scope.row.article_count }}</div>
              <div>文件: {{ scope.row.file_count }}</div>
              <div>下载: {{ scope.row.download_count }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="注册时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button
                type="primary"
                size="small"
                @click="showChangeLevelDialog(scope.row)"
            >
              修改等级
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.limit"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 修改等级对话框 -->
    <el-dialog
        v-model="changeLevelDialog.visible"
        :title="changeLevelDialog.isBatch ? '批量修改用户等级' : '修改用户等级'"
        width="500px"
    >
      <el-form :model="changeLevelForm" label-width="100px">
        <el-form-item label="用户信息" v-if="!changeLevelDialog.isBatch">
          <div class="user-info">
            <div><strong>用户名:</strong> {{ changeLevelDialog.user?.username }}</div>
            <div><strong>邮箱:</strong> {{ changeLevelDialog.user?.email }}</div>
            <div><strong>当前等级:</strong>
              <el-tag :type="getLevelTagType(changeLevelDialog.user?.level)">
                {{ getLevelName(changeLevelDialog.user?.level) }}
              </el-tag>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="选中用户" v-else>
          <div class="batch-info">
            已选择 {{ selectedUsers.length }} 个用户
          </div>
        </el-form-item>
        <el-form-item label="新等级" required>
          <el-select v-model="changeLevelForm.newLevel" placeholder="选择新等级">
            <el-option label="免费用户" value="free" />
            <el-option label="基础会员" value="basic" />
            <el-option label="高级会员" value="premium" />
            <el-option label="VIP会员" value="vip" />
          </el-select>
        </el-form-item>
        <el-form-item label="变更原因">
          <el-input
              v-model="changeLevelForm.reason"
              type="textarea"
              :rows="3"
              placeholder="请输入变更原因（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="changeLevelDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="handleChangeLevel" :loading="changeLevelDialog.loading">
          确认修改
        </el-button>
      </template>
    </el-dialog>

    <!-- 等级变更日志对话框 -->
    <el-dialog
        v-model="levelLogsDialog.visible"
        title="等级变更日志"
        width="800px"
    >
      <el-table :data="levelLogs" v-loading="levelLogsDialog.loading">
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column label="等级变更" width="200">
          <template #default="scope">
            <div class="level-change">
              <el-tag :type="getLevelTagType(scope.row.old_level)" size="small">
                {{ getLevelName(scope.row.old_level) }}
              </el-tag>
              <el-icon style="margin: 0 8px;"><Right /></el-icon>
              <el-tag :type="getLevelTagType(scope.row.new_level)" size="small">
                {{ getLevelName(scope.row.new_level) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="admin_username" label="操作人" width="120" />
        <el-table-column prop="reason" label="变更原因" />
        <el-table-column prop="created_at" label="变更时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
            v-model:current-page="levelLogsPagination.page"
            v-model:page-size="levelLogsPagination.limit"
            :total="levelLogsPagination.total"
            layout="prev, pager, next"
            @current-change="handleLogsPageChange"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Refresh, Edit, Document, Right } from '@element-plus/icons-vue';
import { userLevelApi } from '../../utils/api';

// 响应式数据
const loading = ref(false);
const levelStats = ref<any[]>([]);
const userList = ref<any[]>([]);
const selectedUsers = ref<any[]>([]);
const levelLogs = ref<any[]>([]);

// 搜索表单
const searchForm = reactive({
  search: '',
  level: 'all'
});

// 分页
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
});

const levelLogsPagination = reactive({
  page: 1,
  limit: 10,
  total: 0
});

// 对话框状态
const changeLevelDialog = reactive({
  visible: false,
  loading: false,
  isBatch: false,
  user: null as any
});

const levelLogsDialog = reactive({
  visible: false,
  loading: false
});

// 修改等级表单
const changeLevelForm = reactive({
  newLevel: '',
  reason: ''
});

// 初始化
onMounted(() => {
  // 调试：检查当前用户状态
  console.log('🔍 当前用户调试信息:');
  console.log('localStorage token:', localStorage.getItem('token'));
  console.log('localStorage username:', localStorage.getItem('username'));
  console.log('localStorage role:', localStorage.getItem('role'));
  console.log('localStorage id:', localStorage.getItem('id'));

  loadLevelStats();
  loadUserList();
});

// 加载等级统计
async function loadLevelStats() {
  try {
    console.log('🔄 开始加载等级统计...');
    const res = await userLevelApi.getLevelStats();
    console.log('📊 等级统计API响应:', res);
    if (res.data && Array.isArray(res.data)) {
      levelStats.value = res.data;
      console.log('✅ 等级统计数据设置成功(数组格式):', levelStats.value);
    } else if (res.data && res.data.code === 200) {
      levelStats.value = res.data.data;
      console.log('✅ 等级统计数据设置成功(标准格式):', levelStats.value);
    } else {
      console.error('❌ 等级统计API返回错误:', res.data);
    }
  } catch (error: any) {
    // 兼容：HTTP 500 但业务数据正常
    const data = error?.response?.data;
    if (data && data.code === 200 && data.data) {
      levelStats.value = data.data;
      console.warn('⚠️ HTTP 500 但业务成功，已兼容:', levelStats.value);
      return;
    }
    console.error('❌ 加载等级统计失败:', error);
    if (error.response?.status === 403) {
      ElMessage.error('权限不足，只有管理员可以访问此功能');
    } else if (error.response?.status === 401) {
      ElMessage.error('登录已过期，请重新登录');
    } else {
      ElMessage.error('加载等级统计失败');
    }
  }
}

// 加载用户列表
async function loadUserList() {
  loading.value = true;
  try {
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      level: searchForm.level === 'all' ? undefined : searchForm.level,
      search: searchForm.search || undefined
    };

    const res = await userLevelApi.getUsers(params);
    console.log('API响应:', res);

    userList.value = res.data.users;
    pagination.total = res.data.pagination.total;
  } catch (error) {
    console.error('❌ 加载用户列表失败:', error);
    if (error.response?.status === 403) {
      ElMessage.error('权限不足，只有管理员可以访问此功能');
    } else if (error.response?.status === 401) {
      ElMessage.error('登录已过期，请重新登录');
    } else {
      ElMessage.error('加载用户列表失败');
    }
  } finally {
    loading.value = false;
  }
}

// 搜索处理
function handleSearch() {
  pagination.page = 1;
  loadUserList();
}

// 刷新数据
function refreshData() {
  loadLevelStats();
  loadUserList();
}

// 选择变更处理
function handleSelectionChange(selection: any[]) {
  selectedUsers.value = selection;
}

// 分页处理
function handlePageChange(page: number) {
  pagination.page = page;
  loadUserList();
}

function handleSizeChange(size: number) {
  pagination.limit = size;
  pagination.page = 1;
  loadUserList();
}

// 显示修改等级对话框
function showChangeLevelDialog(user: any) {
  changeLevelDialog.user = user;
  changeLevelDialog.isBatch = false;
  changeLevelDialog.visible = true;
  changeLevelForm.newLevel = '';
  changeLevelForm.reason = '';
}

// 显示批量修改对话框
function showBatchChangeDialog() {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择要修改的用户');
    return;
  }

  changeLevelDialog.isBatch = true;
  changeLevelDialog.visible = true;
  changeLevelForm.newLevel = '';
  changeLevelForm.reason = '';
}

// 处理等级修改
async function handleChangeLevel() {
  if (!changeLevelForm.newLevel) {
    ElMessage.warning('请选择新等级');
    return;
  }

  changeLevelDialog.loading = true;

  try {
    if (changeLevelDialog.isBatch) {
      // 批量修改
      const userIds = selectedUsers.value.map(user => user.id);
      await userLevelApi.batchChangeLevel({
        userIds,
        newLevel: changeLevelForm.newLevel,
        reason: changeLevelForm.reason
      });

      ElMessage.success('批量修改成功');
    } else {
      // 单个修改
      await userLevelApi.changeLevel({
        userId: changeLevelDialog.user.id,
        newLevel: changeLevelForm.newLevel,
        reason: changeLevelForm.reason
      });

      ElMessage.success('修改成功');
    }

    changeLevelDialog.visible = false;
    refreshData();

  } catch (error: any) {
    console.error('修改等级失败:', error);
    ElMessage.error(error.response?.data?.message || '修改失败');
  } finally {
    changeLevelDialog.loading = false;
  }
}

// 显示等级变更日志
async function showLevelLogs() {
  levelLogsDialog.visible = true;
  levelLogsDialog.loading = true;

  try {
    const res = await userLevelApi.getLevelLogs({
      page: levelLogsPagination.page,
      limit: levelLogsPagination.limit
    });

    if (res.data.code === 200) {
      levelLogs.value = res.data.data.list;
      levelLogsPagination.total = res.data.data.pagination.total;
    }
  } catch (error) {
    console.error('加载变更日志失败:', error);
    ElMessage.error('加载变更日志失败');
  } finally {
    levelLogsDialog.loading = false;
  }
}

// 日志分页处理
function handleLogsPageChange(page: number) {
  levelLogsPagination.page = page;
  showLevelLogs();
}

// 工具函数
function getLevelName(level: string) {
  const names: Record<string, string> = {
    free: '免费用户',
    basic: '基础会员',
    premium: '高级会员',
    vip: 'VIP会员'
  };
  return names[level] || level;
}

function getLevelTagType(level: string) {
  const types: Record<string, string> = {
    basic: 'warning',
    premium: 'success',
    vip: 'danger'
  };
  return types[level];
}

function formatTime(time: string) {
  return new Date(time).toLocaleString();
}
</script>

<style scoped lang="less">
.user-level-manage {
  padding: 20px;

  .stats-cards {
    margin-bottom: 20px;

    .stat-card {
      height: 140px;

      &.level-free {
        border-left: 4px solid #909399;
      }

      &.level-basic {
        border-left: 4px solid #e6a23c;
      }

      &.level-premium {
        border-left: 4px solid #67c23a;
      }

      &.level-vip {
        border-left: 4px solid #f56c6c;
      }

      .stat-content {
        .stat-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;

          .level-name {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
        }

        .stat-details {
          .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;

            .label {
              color: #909399;
            }

            .value {
              color: #303133;
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  .search-card {
    margin-bottom: 20px;

    .search-bar {
      .text-right {
        text-align: right;
      }
    }
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .total-count {
        color: #909399;
        font-size: 14px;
      }
    }

    .level-info {
      font-size: 12px;
      line-height: 1.4;

      div {
        margin-bottom: 2px;
      }
    }

    .usage-stats {
      font-size: 12px;
      line-height: 1.4;

      div {
        margin-bottom: 2px;
      }
    }

    .pagination-wrapper {
      margin-top: 20px;
      text-align: center;
    }
  }

  .user-info {
    background: #f5f7fa;
    padding: 15px;
    border-radius: 4px;

    div {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .batch-info {
    background: #e1f3d8;
    padding: 10px;
    border-radius: 4px;
    color: #67c23a;
    font-weight: 500;
  }

  .level-change {
    display: flex;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .user-level-manage {
    padding: 10px;

    .stats-cards {
      .el-col {
        margin-bottom: 16px;
      }
    }

    .search-bar {
      .el-col {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
