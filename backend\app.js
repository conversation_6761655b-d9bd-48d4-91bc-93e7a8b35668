const Koa = require("koa");
const cors = require("@koa/cors");
const koaBody = require("koa-body");
const path = require("path");
const router = require("./router/index");
const authMiddleware = require("./middlewares/koaJwtMiddleware");
const errorHandler = require("./middlewares/errorHandler");
const loggerMiddleware = require("./middlewares/loggerMiddleware");
const serve = require("koa-static");
const mount = require("koa-mount");
 
// 创建 Koa 实例
const app = new Koa();



// 只开放封面目录为静态资源
const staticDirs = [
  { url: "/media/covers", dir: path.join(__dirname, "public", "media", "covers") },
  { url: "/images", dir: path.join(__dirname, "public", "images") },
  { url: "/articles", dir: path.join(__dirname, "uploads", "articles") },
  { url: "/avatars", dir: path.join(__dirname, "uploads", "avatars") },
  { url: "/resource", dir: path.join(__dirname, "uploads", "resource") },
];

staticDirs.forEach(({ url, dir }) => {
  app.use(mount(url, serve(dir)));
});

// CORS 和请求体解析中间件
app.use(cors());
app.use(loggerMiddleware);
app.use(
  koaBody({
    multipart: true,
    formidable: {
      maxFileSize: 500 * 1024 * 1024 * 1024, // 设置文件上传大小限制为500GB
      keepExtensions: true,
    },
  })
);

// 使用身份验证中间件
app.use(authMiddleware);

// 引入错误处理中间件
errorHandler(app);

// 路由
app.use(router.routes()).use(router.allowedMethods());

module.exports = app;
