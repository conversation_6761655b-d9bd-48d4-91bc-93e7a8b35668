const Router = require("koa-router");
const articles = new Router();
const bodyParser = require("koa-bodyparser");
const article = require("../../utils/sql/articleService");
const { handleResponse } = require("../../middlewares/responseHandler");
const path = require("path");
const fs = require("fs");

articles.use(bodyParser());

// 获取文章列表
articles.post("/pages", async (ctx) => {
  const { user_id } = ctx.request.body;
  const result = await article.getArticleById(user_id);
  if (result) {
    return handleResponse(ctx, 200, {
      data: result,
    });
  }
});

// 分页获取文章列表
articles.post("/pagesNumber", async (ctx) => {
  const { user_id, page, limit } = ctx.request.body;
  // 参数验证
  if (isNaN(page) || isNaN(limit) || page <= 0 || limit <= 0) {
    return handleResponse(ctx, 400, {
      message: "Invalid page or limit value",
    });
  }

  const result = await article.getArticleByPage({ user_id, page, limit });
  if (result) {
    return handleResponse(ctx, 200, {
      data: result,
    });
  } else {
    return handleResponse(ctx, 400, {
      message: "获取文章列表失败",
    });
  }
});

articles.post("/add", async (ctx) => {
  console.log('📝 收到文章发布请求');
  console.log('请求文件:', ctx.request.files);
  console.log('请求体:', ctx.request.body);

  const { file } = ctx.request.files || {};
  const { user_id, title, tags, category, content, cover_image } =
    ctx.request.body || {};

  // 详细的参数检查
  const missingParams = [];
  if (!file) missingParams.push('file');
  if (!user_id) missingParams.push('user_id');
  if (!title) missingParams.push('title');
  if (!tags) missingParams.push('tags');
  if (!category) missingParams.push('category');
  if (!content) missingParams.push('content');

  if (missingParams.length > 0) {
    console.log('❌ 缺少必要参数:', missingParams);
    return handleResponse(ctx, 400, {
      error: `缺少必要参数: ${missingParams.join(', ')}`,
      received: {
        hasFile: !!file,
        user_id: !!user_id,
        title: !!title,
        tags: !!tags,
        category: !!category,
        content: !!content
      }
    });
  }

  console.log('✅ 参数检查通过，开始处理文件上传...');

  try {
    // 上传文件处理
    const uploadDir = path.join(__dirname, "../../uploads/articles");
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
      console.log('📁 创建上传目录:', uploadDir);
    }

    console.log('📄 文件信息:', {
      name: file.name,
      size: file.size,
      type: file.type,
      path: file.path
    });

    const reader = fs.createReadStream(file.path);
    const avatarFilename = file.name;
    const avatarPath = path.join(uploadDir, avatarFilename);
    const stream = fs.createWriteStream(avatarPath);

    // 使用Promise包装文件复制过程
    await new Promise((resolve, reject) => {
      reader.pipe(stream);
      stream.on('finish', () => {
        console.log('✅ 文件复制完成:', avatarPath);
        resolve();
      });
      stream.on('error', reject);
      reader.on('error', reject);
    });

    // 保存文章到数据库
    console.log('💾 保存文章到数据库...');
    const summary = content.slice(0, 100);
    const result = await article.addArticle(
      user_id,
      title,
      summary,
      tags,
      category,
      content,
      cover_image || avatarFilename
    );

    if (result) {
      console.log('✅ 文章发布成功');
      return handleResponse(ctx, 200, {
        message: "文章发布成功",
        data: {
          articleId: result.insertId,
          coverImage: avatarFilename
        }
      });
    } else {
      console.log('❌ 数据库保存失败');
      return handleResponse(ctx, 500, {
        message: "文章发布失败：数据库保存失败",
      });
    }
  } catch (error) {
    console.error('❌ 文章发布过程中出错:', error);
    return handleResponse(ctx, 500, {
      message: "文章发布失败",
      error: error.message
    });
  }
});

// 更新文章
articles.post("/update", async (ctx) => {
  const { id, user_id, title, summary, content, cover_image } =
    ctx.request.body;
  const result = await article.updateArticle(
    id,
    user_id,
    title,
    summary,
    content,
    cover_image
  );
  if (result) {
    return handleResponse(ctx, 200, {
      message: "文章更新成功",
    });
  }
});

// 真删除文章
articles.post("/delete", async (ctx) => {
  const { id } = ctx.request.body;
  const result = await article.deleteArticle(id);
  if (result) {
    return handleResponse(ctx, 200, {
      message: "文章删除成功",
    });
  }
});

// 假删除文章 将文章的is_delete字段设置为1 获取文章参数id
articles.post("/fakeDelete", async (ctx) => {
  const { id } = ctx.request.body;

  const result = await article.fakeDeleteArticle(id);
  if (result) {
    return handleResponse(ctx, 200, {
      message: "文章假删除成功",
    });
  }
});

// 搜索文章
articles.post("/search", async (ctx) => {
  const { keyword } = ctx.request.body;
  const result = await article.searchArticle(keyword);
  if (result) {
    return handleResponse(ctx, 200, {
      data: result,
    });
  }
});

// 获取文章的评论
articles.post("/comments", async (ctx) => {
  const { article_id } = ctx.request.body;

  const result = await article.commentArticle(article_id);
  if (result) {
    return handleResponse(ctx, 200, {
      data: result,
    });
  }
});

// 添加评论 addComment(username,article_id, content,parent_id)
articles.post("/addComment", async (ctx) => {
  const { username, article_id, content, parent_id } = ctx.request.body;
  const result = await article.addComment(
    username,
    article_id,
    content,
    parent_id
  );
  if (result) {
    return handleResponse(ctx, 200, {
      message: "评论添加成功",
    });
  }
});
//  文章详情 文章id
articles.post("/detail", async (ctx) => {
  const { id } = ctx.request.body;
  const result = await article.getArticleById(id);
  if (result) {
    return handleResponse(ctx, 200, {
      data: result,
    });
  }
});

// 设置文章共享状态
articles.post("/share", async (ctx) => {
  const { id, is_share } = ctx.request.body;
  if (typeof id === "undefined" || typeof is_share === "undefined") {
    return handleResponse(ctx, 400, { message: "缺少必要参数" });
  }
  const result = await article.setArticleShare(id, is_share);
  if (result) {
    return handleResponse(ctx, 200, {
      message: "共享状态更新成功",
    });
  } else {
    return handleResponse(ctx, 500, {
      message: "共享状态更新失败",
    });
  }
});

// 获取所有共享文章
articles.get("/shared", async (ctx) => {
  const result = await article.getSharedArticles();
  if (result) {
    return handleResponse(ctx, 200, {
      data: result,
    });
  } else {
    return handleResponse(ctx, 500, {
      message: "获取共享文章失败",
    });
  }
});

// 恢复文章
articles.post("/restore", async (ctx) => {
  const { id } = ctx.request.body;
  if (!id) {
    return handleResponse(ctx, 400, { message: "缺少文章ID" });
  }
  const result = await article.restoreArticle(id);
  if (result) {
    return handleResponse(ctx, 200, {
      message: "文章恢复成功",
    });
  } else {
    return handleResponse(ctx, 500, {
      message: "文章恢复失败",
    });
  }
});
// 获取用户删除的文章
articles.post("/deletedByUser", async (ctx) => {
  const { user_id } = ctx.request.body;
  if (!user_id) {
    return handleResponse(ctx, 400, { message: "缺少用户ID" });
  }
  const result = await article.getDeletedArticlesByUserId(user_id);
  if (result) {
    return handleResponse(ctx, 200, {
      data: result,
    });
  } else {
    return handleResponse(ctx, 500, {
      message: "获取用户删除的文章失败",
    });
  }
});

// 公开接口：获取所有文章列表（无需登录）
articles.post("/public/pages", async (ctx) => {
  try {
    const { page = 1, limit = 10 } = ctx.request.body;
    const result = await article.getAllPublicArticles({ page, limit });
    const total = await article.getPublicArticlesCount();
    
    ctx.body = {
      code: 200,
      message: "获取文章列表成功",
      data: {
        articles: result,
        total,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    };
  } catch (error) {
    ctx.body = {
      code: 500,
      message: "获取文章列表失败",
      error: error.message
    };
  }
});

// 公开接口：获取文章详情（无需登录）
articles.post("/public/detail", async (ctx) => {
  try {
    const { id } = ctx.request.body;
    if (!id) {
      ctx.body = {
        code: 400,
        message: "文章ID不能为空"
      };
      return;
    }
    
    const result = await article.getPublicArticleById(id);
    if (!result) {
      ctx.body = {
        code: 404,
        message: "文章不存在"
      };
      return;
    }
    
    ctx.body = {
      code: 200,
      message: "获取文章详情成功",
      data: result
    };
  } catch (error) {
    ctx.body = {
      code: 500,
      message: "获取文章详情失败",
      error: error.message
    };
  }
});

// 公开接口：获取文章评论（无需登录）
articles.post("/public/comments", async (ctx) => {
  try {
    const { article_id } = ctx.request.body;
    if (!article_id) {
      ctx.body = {
        code: 400,
        message: "文章ID不能为空"
      };
      return;
    }
    
    const result = await article.getPublicArticleComments(article_id);
    ctx.body = {
      code: 200,
      message: "获取评论成功",
      data: result
    };
  } catch (error) {
    ctx.body = {
      code: 500,
      message: "获取评论失败",
      error: error.message
    };
  }
});

// 公开接口：提交评论（需要用户名，但不需要登录验证）
articles.post("/public/addComment", async (ctx) => {
  try {
    const { username, article_id, content, parent_id } = ctx.request.body;
    
    if (!username || !article_id || !content) {
      ctx.body = {
        code: 400,
        message: "用户名、文章ID和评论内容不能为空"
      };
      return;
    }
    
    const commentId = await article.addComment(username, article_id, content, parent_id);
    ctx.body = {
      code: 200,
      message: "评论提交成功",
      data: { commentId }
    };
  } catch (error) {
    ctx.body = {
      code: 500,
      message: "评论提交失败",
      error: error.message
    };
  }
});

module.exports = articles;
