const Router = require("koa-router");
const bugsend = new Router();

const logger = require("../plugin/logger");
const {
  getBugList,
  addBug,
  updateBugStatusAndSeverity,
  deleteBug,
  deleteBugs,
  updateBug,
} = require("../utils/sql/bugsendService");
const bodyParser = require("koa-bodyparser");

bugsend.use(bodyParser());

// 通用响应处理
function handleResponse(ctx, code, data, msg = "ok") {
  ctx.status = code;
  ctx.body = {
    code,
    data,
    msg,
  };
}

// 1. 获取 bug 列表
bugsend.post("/list", async (ctx) => {
  try {
    const result = await getBugList();
    logger.info(`获取 bug 列表成功 - IP: ${ctx.ip}`);
    return handleResponse(ctx, 200, result, "获取成功");
  } catch (err) {
    logger.error("获取 bug 列表失败", err);
    return handleResponse(ctx, 500, null, "获取失败");
  }
});

// 2. 新增 bug
bugsend.post("/add", async (ctx) => {
  const { title, description, status, severity } = ctx.request.body;
  if (!title || !description || !status || !severity) {
    return handleResponse(ctx, 400, null, "参数不完整");
  }
  try {
    const success = await addBug(title, description, status, severity);
    if (success) {
      logger.info(`新增 bug 成功 - 状态: ${status}, 程度: ${severity}`);
      return handleResponse(ctx, 200, null, "新增成功");
    } else {
      return handleResponse(ctx, 500, null, "新增失败");
    }
  } catch (err) {
    logger.error("新增 bug 失败", err);
    return handleResponse(ctx, 500, null, "新增失败");
  }
});

// 3. 修改 bug 状态和程度
bugsend.post("/updateStatus", async (ctx) => {
  const { id, status, severity } = ctx.request.body;
  if (!id || typeof status === "undefined" || !severity) {
    return handleResponse(ctx, 400, null, "参数不完整");
  }
  try {
    const success = await updateBugStatusAndSeverity(id, status, severity);
    if (success) {
      logger.info(
        `修改 bug 状态和程度成功 - id: ${id}, status: ${status}, 程度: ${severity}`
      );
      return handleResponse(ctx, 200, null, "修改成功");
    } else {
      return handleResponse(ctx, 500, null, "修改失败");
    }
  } catch (err) {
    logger.error("修改 bug 状态和程度失败", err);
    return handleResponse(ctx, 500, null, "修改失败");
  }
});

// 4. 删除 bug
bugsend.post("/delete", async (ctx) => {
  const { id } = ctx.request.body;
  if (!id) {
    return handleResponse(ctx, 400, null, "参数不完整");
  }
  try {
    const success = await deleteBug(id);
    if (success) {
      logger.info(`删除 bug 成功 - id: ${id}`);
      return handleResponse(ctx, 200, null, "删除成功");
    } else {
      return handleResponse(ctx, 500, null, "删除失败");
    }
  } catch (err) {
    logger.error("删除 bug 失败", err);
    return handleResponse(ctx, 500, null, "删除失败");
  }
});
// 批量删除
bugsend.post("/deleteBatch", async (ctx) => {
  const { ids } = ctx.request.body;
  if (!ids) {
    return handleResponse(ctx, 400, null, "参数不完整");
  }
  try {
    const success = await deleteBugs(ids);
    if (success) {
      logger.info(`批量删除 bug 成功 - ids: ${ids}`);
      return handleResponse(ctx, 200, null, "删除成功");
    } else {
      return handleResponse(ctx, 500, null, "删除失败");
    }
  } catch (err) {
    logger.error("批量删除 bug 失败", err);
    return handleResponse(ctx, 500, null, "删除失败");
  }
});

// 5. 更新 bug 记录（编辑）
bugsend.post("/update", async (ctx) => {
  const { id, title, description, status, severity } = ctx.request.body;
  console.log(id, title, description, status, severity);
  if (!id || !title || !description || typeof status === "undefined" || !severity) {
    return handleResponse(ctx, 400, null, "参数不完整");
  }
  try {
    // 你需要在 bugsendService.js 中实现 updateBug 方法
    const success = await updateBug(
      id, title, description, status, severity 
    );
    if (success) {
      logger.info(`更新 bug 成功 - id: ${id}`);
      return handleResponse(ctx, 200, null, "更新成功");
    } else {
      return handleResponse(ctx, 500, null, "更新失败");
    }
  } catch (err) {
    logger.error("更新 bug 失败", err);
    return handleResponse(ctx, 500, null, "更新失败");
  }
});

module.exports = bugsend;
