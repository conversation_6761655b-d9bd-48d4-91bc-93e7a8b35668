{"name": "myblog", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@kangc/v-md-editor": "^2.3.18", "@tailwindcss/vite": "^4.1.3", "@tsparticles/vue3": "^3.0.1", "axios": "^1.8.1", "chart.js": "^4.5.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.7", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "marked": "^15.0.12", "masonry-layout": "^4.2.2", "particles.js": "^2.0.0", "particles.vue3": "^2.12.0", "pdfjs-dist": "^5.3.31", "pinia": "^3.0.3", "pnpm": "^10.11.1", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.3", "three": "^0.177.0", "tsparticles": "^3.8.1", "tsparticles-engine": "^2.12.0", "tsparticles-slim": "^2.12.0", "vant": "^3.4.3", "vue": "^3.5.13", "vue-files-preview": "^1.0.38", "vue-markdown-editor": "^0.2.0", "vue-router": "^4.5.0", "vue3-particles": "^2.12.0"}, "devDependencies": {"@iconify/json": "^2.2.352", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^5.2.4", "@vue/tsconfig": "^0.7.0", "less": "^4.2.2", "typescript": "~5.7.2", "unplugin-auto-import": "^19.1.1", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.0", "vite-plugin-style-import": "^2.0.0", "vue-tsc": "^2.2.4"}}