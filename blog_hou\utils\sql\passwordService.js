// 密码管理服务 - 基于数据库表结构重新实现
const db = require("../db");
const { hashPassword, comparePassword } = require("../../plugin/crypto");

/**
 * 检查用户是否为管理员
 */
async function isAdminUser(userId) {
  try {
    const sql = "SELECT role FROM users WHERE id = ?";
    const result = await db.query(sql, [userId]);
    return result.length > 0 && result[0].role === 'admin';
  } catch (error) {
    console.error("检查管理员权限失败:", error);
    return false;
  }
}

/**
 * 获取所有密码配置（管理员专用）
 * 返回所有配置，包括禁用的，用于管理界面显示
 */
async function getAllPasswords() {
  try {
    const sql = `
      SELECT
        ap.id,
        ap.password_type,
        ap.security_question,
        ap.description,
        ap.is_active,
        ap.created_by,
        ap.created_at,
        ap.updated_at,
        ap.last_used_at,
        ap.use_count,
        u.username as created_by_name
      FROM access_passwords ap
      LEFT JOIN users u ON ap.created_by = u.id
      ORDER BY ap.created_at DESC
    `;
    const result = await db.query(sql);
    return result;
  } catch (error) {
    console.error("获取密码配置失败:", error);
    throw new Error("获取密码配置失败");
  }
}

/**
 * 根据类型获取启用的密码配置（用于验证）
 */
async function getPasswordByType(passwordType) {
  try {
    const sql = "SELECT * FROM access_passwords WHERE password_type = ? AND is_active = 1";
    const result = await db.query(sql, [passwordType]);
    return result[0] || null;
  } catch (error) {
    console.error("获取密码配置失败:", error);
    throw new Error("获取密码配置失败");
  }
}

/**
 * 根据类型获取密码配置（包括禁用的，用于管理）
 */
async function getPasswordByTypeAll(passwordType) {
  try {
    const sql = "SELECT * FROM access_passwords WHERE password_type = ?";
    const result = await db.query(sql, [passwordType]);
    return result[0] || null;
  } catch (error) {
    console.error("获取密码配置失败:", error);
    throw new Error("获取密码配置失败");
  }
}

/**
 * 根据ID获取密码配置
 */
async function getPasswordById(id) {
  try {
    const sql = "SELECT * FROM access_passwords WHERE id = ?";
    const result = await db.query(sql, [id]);
    return result[0] || null;
  } catch (error) {
    console.error("获取密码配置失败:", error);
    throw new Error("获取密码配置失败");
  }
}

/**
 * 更新使用统计
 */
async function updateUsageStats(id) {
  try {
    const sql = `
      UPDATE access_passwords 
      SET use_count = use_count + 1, last_used_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `;
    await db.query(sql, [id]);
  } catch (error) {
    console.error("更新使用统计失败:", error);
  }
}

/**
 * 创建密码配置
 * 基于数据库唯一约束，password_type 必须唯一
 */
async function createPassword(data, userId) {
  try {
    const { passwordType, password, securityQuestion, securityAnswer, description } = data;

    // 检查是否已存在
    const existing = await getPasswordByTypeAll(passwordType);
    if (existing) {
      throw new Error(`密码类型 ${passwordType} 已存在，请使用更新功能`);
    }

    // 加密密码和密保答案
    const passwordHash = await hashPassword(password);
    const answerHash = await hashPassword(securityAnswer);

    // 插入新记录
    const sql = `
      INSERT INTO access_passwords
      (password_type, password_hash, security_question, security_answer_hash, description, created_by, is_active, use_count)
      VALUES (?, ?, ?, ?, ?, ?, 1, 0)
    `;
    const result = await db.query(sql, [
      passwordType, passwordHash, securityQuestion, answerHash, description || `${passwordType} 访问密码`, userId
    ]);

    return {
      success: true,
      id: result.insertId,
      action: 'created',
      passwordType
    };
  } catch (error) {
    console.error("创建密码配置失败:", error);
    if (error.code === 'ER_DUP_ENTRY') {
      throw new Error(`密码类型 ${data.passwordType} 已存在`);
    }
    throw error;
  }
}

/**
 * 更新密码配置
 */
async function updatePassword(data, userId) {
  try {
    const { passwordType, password, securityQuestion, securityAnswer, description } = data;

    // 检查配置是否存在
    const existing = await getPasswordByTypeAll(passwordType);
    if (!existing) {
      throw new Error(`密码类型 ${passwordType} 不存在`);
    }

    // 加密密码和密保答案
    const passwordHash = await hashPassword(password);
    const answerHash = await hashPassword(securityAnswer);

    // 更新记录
    const sql = `
      UPDATE access_passwords
      SET password_hash = ?, security_question = ?, security_answer_hash = ?,
          description = ?, created_by = ?, updated_at = CURRENT_TIMESTAMP
      WHERE password_type = ?
    `;
    const result = await db.query(sql, [
      passwordHash, securityQuestion, answerHash, description, userId, passwordType
    ]);

    return {
      success: true,
      id: existing.id,
      action: 'updated',
      passwordType
    };
  } catch (error) {
    console.error("更新密码配置失败:", error);
    throw error;
  }
}

/**
 * 验证密码（公开接口）
 * 只验证启用状态的密码配置
 */
async function verifyPassword(passwordType, password) {
  try {
    const config = await getPasswordByType(passwordType);
    if (!config) {
      return { valid: false, message: '密码配置不存在或已禁用' };
    }

    const isValid = await comparePassword(password, config.password_hash);

    // 如果验证成功，更新使用统计
    if (isValid) {
      await updateUsageStats(config.id);
    }

    return {
      valid: isValid,
      message: isValid ? '验证成功' : '密码错误',
      configId: config.id
    };
  } catch (error) {
    console.error("验证密码失败:", error);
    return { valid: false, message: '验证失败' };
  }
}

/**
 * 验证密保答案
 */
async function verifySecurityAnswer(passwordType, securityAnswer) {
  try {
    const config = await getPasswordByType(passwordType);
    if (!config) {
      return false;
    }
    
    return await comparePassword(securityAnswer, config.security_answer_hash);
  } catch (error) {
    console.error("验证密保答案失败:", error);
    return false;
  }
}

/**
 * 通过密保重置密码
 */
async function resetPasswordBySecurityAnswer(passwordType, securityAnswer, newPassword, userId) {
  try {
    const isValid = await verifySecurityAnswer(passwordType, securityAnswer);
    if (!isValid) {
      throw new Error("密保答案错误");
    }
    
    const passwordHash = await hashPassword(newPassword);
    const sql = `
      UPDATE access_passwords 
      SET password_hash = ?, created_by = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE password_type = ?
    `;
    const result = await db.query(sql, [passwordHash, userId, passwordType]);
    return result.affectedRows > 0;
  } catch (error) {
    console.error("重置密码失败:", error);
    throw error;
  }
}

/**
 * 删除密码配置（硬删除）
 * 基于数据库约束，直接删除记录
 */
async function deletePassword(passwordType) {
  try {
    // 先检查是否存在
    const existing = await getPasswordByTypeAll(passwordType);
    if (!existing) {
      throw new Error(`密码类型 ${passwordType} 不存在`);
    }

    const sql = "DELETE FROM access_passwords WHERE password_type = ?";
    const result = await db.query(sql, [passwordType]);

    return {
      success: result.affectedRows > 0,
      deletedId: existing.id,
      passwordType
    };
  } catch (error) {
    console.error("删除密码配置失败:", error);
    throw error;
  }
}

/**
 * 切换密码配置状态
 * 基于数据库字段 is_active (tinyint(1))
 */
async function togglePasswordStatus(id) {
  try {
    // 先检查记录是否存在
    const existing = await getPasswordById(id);
    if (!existing) {
      throw new Error(`ID为 ${id} 的密码配置不存在`);
    }

    const sql = "UPDATE access_passwords SET is_active = !is_active, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    const result = await db.query(sql, [id]);

    return {
      success: result.affectedRows > 0,
      id: id,
      newStatus: existing.is_active ? 0 : 1,
      passwordType: existing.password_type
    };
  } catch (error) {
    console.error("切换密码状态失败:", error);
    throw error;
  }
}

module.exports = {
  isAdminUser,
  getAllPasswords,
  getPasswordByType,
  getPasswordByTypeAll,
  getPasswordById,
  createPassword,
  updatePassword,
  verifyPassword,
  verifySecurityAnswer,
  resetPasswordBySecurityAnswer,
  deletePassword,
  togglePasswordStatus,
  updateUsageStats
};
