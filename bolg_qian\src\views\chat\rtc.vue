<template>
  <div>
    <!-- 房间入口 -->
    <div style="margin-bottom: 16px">
      <input v-model="roomId" placeholder="房间ID" />
      <input v-model="username" placeholder="用户名" />
      <el-button type="primary" @click="joinRoom">加入会议</el-button>
    </div>

    <!-- 当前人数 -->
    <div style="margin-bottom: 10px">
      当前在线人数：{{ Object.keys(remoteVideos).length + (joined ? 1 : 0) }} 👥
    </div>

    <!-- 视频区 -->
    <div class="videos">
      <!-- 本地视频 -->
      <video ref="localVideo" autoplay muted playsinline></video>

      <!-- 远程视频 -->
      <div v-for="(v, id) in remoteVideos" :key="id">
        <video :ref="el => setRemoteVideoRef(id, el)" autoplay playsinline></video>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import io from 'socket.io-client'

const socket = io('http://**************:3000') // 替换为你实际后端地址
const roomId = ref('')
const username = ref(localStorage.getItem('username') || '')
const localVideo = ref(null)
const remoteVideos = ref({}) // { socketId: MediaStream }
let remoteVideoRefs = {}
let localStream
let peers = {} // { socketId: RTCPeerConnection }
const joined = ref(false)

const config = {
  iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
}

// 设置远程视频 ref
function setRemoteVideoRef(socketId, el) {
  if (el) {
    remoteVideoRefs[socketId] = el
    if (remoteVideos.value[socketId]) {
      el.srcObject = remoteVideos.value[socketId]
    }
  }
}

// 创建 Peer 连接
function createPeer(socketId) {
  const pc = new RTCPeerConnection(config)

  pc.onicecandidate = event => {
    if (event.candidate) {
      socket.emit('ice-candidate', { to: socketId, candidate: event.candidate })
    }
  }

  pc.ontrack = event => {
    remoteVideos.value[socketId] = event.streams[0]
    if (remoteVideoRefs[socketId]) {
      remoteVideoRefs[socketId].srcObject = event.streams[0]
    }
  }

  return pc
}

// 加入会议
async function joinRoom() {
  if (!roomId.value || !username.value) {
    alert('请输入房间ID和用户名')
    return
  }

  // 保存用户名
  localStorage.setItem('username', username.value)

  // 获取本地视频
  localStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true })
  localVideo.value.srcObject = localStream

  // 加入房间
  socket.emit('join-room', { roomId: roomId.value, username: username.value })
  joined.value = true

  // 接收已有用户，发起 offer
  socket.on('all-users', users => {
    users.forEach(socketId => {
      const pc = createPeer(socketId)
      peers[socketId] = pc

      localStream.getTracks().forEach(track => {
        pc.addTrack(track, localStream)
      })

      pc.createOffer().then(offer => {
        pc.setLocalDescription(offer)
        socket.emit('offer', { to: socketId, from: socket.id, offer })
      })
    })
  })

  // 新用户加入，准备连接
  socket.on('user-joined', ({ socketId }) => {
    const pc = createPeer(socketId)
    peers[socketId] = pc
    localStream.getTracks().forEach(track => pc.addTrack(track, localStream))
  })

  // 收到 offer
  socket.on('offer', async ({ from, offer }) => {
    const pc = createPeer(from)
    peers[from] = pc

    await pc.setRemoteDescription(new RTCSessionDescription(offer))
    localStream.getTracks().forEach(track => pc.addTrack(track, localStream))

    const answer = await pc.createAnswer()
    await pc.setLocalDescription(answer)

    socket.emit('answer', { to: from, from: socket.id, answer })
  })

  // 收到 answer
  socket.on('answer', ({ from, answer }) => {
    peers[from].setRemoteDescription(new RTCSessionDescription(answer))
  })

  // ICE
  socket.on('ice-candidate', ({ from, candidate }) => {
    if (peers[from]) {
      peers[from].addIceCandidate(new RTCIceCandidate(candidate))
    }
  })

  // 用户离开
  socket.on('user-left', socketId => {
    if (peers[socketId]) {
      peers[socketId].close()
      delete peers[socketId]
      delete remoteVideos.value[socketId]
    }
  })
}
</script>

<style scoped>
.videos {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-top: 10px;
}
video {
  width: 100%;
  aspect-ratio: 16/9;
  background: #000;
  border-radius: 8px;
  object-fit: cover;
}
</style>
