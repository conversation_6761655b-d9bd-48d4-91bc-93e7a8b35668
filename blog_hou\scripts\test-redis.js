#!/usr/bin/env node

/**
 * Redis连接测试脚本
 */

const redis = require('redis');

async function testRedisConnection() {
  console.log('🔍 开始测试Redis连接...');
  
  const config = {
    socket: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      connectTimeout: 10000
    },
    password: process.env.REDIS_PASSWORD || undefined,
    database: parseInt(process.env.REDIS_DB) || 0
  };
  
  console.log('📋 Redis配置:', {
    host: config.socket.host,
    port: config.socket.port,
    password: config.password ? '***' : 'none',
    database: config.database
  });
  
  let client;
  
  try {
    // 创建Redis客户端
    client = redis.createClient(config);
    
    // 监听事件
    client.on('connect', () => {
      console.log('✅ Redis连接建立');
    });
    
    client.on('ready', () => {
      console.log('✅ Redis客户端就绪');
    });
    
    client.on('error', (err) => {
      console.error('❌ Redis错误:', err.message);
    });
    
    // 连接
    console.log('🔌 正在连接Redis...');
    await client.connect();
    
    // 测试PING
    console.log('🏓 测试PING命令...');
    const pong = await client.ping();
    console.log('📨 PING响应:', pong);
    
    // 测试SET/GET
    console.log('💾 测试SET/GET命令...');
    const testKey = 'test:connection';
    const testValue = JSON.stringify({ 
      timestamp: Date.now(), 
      test: true,
      message: 'Redis连接测试成功'
    });
    
    await client.set(testKey, testValue);
    console.log('✅ SET命令成功');
    
    const getValue = await client.get(testKey);
    console.log('✅ GET命令成功:', JSON.parse(getValue));
    
    // 清理测试数据
    await client.del(testKey);
    console.log('🧹 清理测试数据完成');
    
    // 获取Redis信息
    console.log('📊 获取Redis信息...');
    const info = await client.info('server');
    const lines = info.split('\r\n').filter(line => line && !line.startsWith('#'));
    const serverInfo = {};
    lines.forEach(line => {
      const [key, value] = line.split(':');
      if (key && value) {
        serverInfo[key] = value;
      }
    });
    
    console.log('🖥️ Redis服务器信息:', {
      version: serverInfo.redis_version,
      mode: serverInfo.redis_mode,
      os: serverInfo.os,
      uptime: serverInfo.uptime_in_seconds + '秒'
    });
    
    console.log('🎉 Redis连接测试完全成功！');
    
  } catch (error) {
    console.error('❌ Redis连接测试失败:', {
      message: error.message,
      code: error.code,
      errno: error.errno
    });
    
    // 提供解决建议
    console.log('\n🔧 可能的解决方案:');
    console.log('1. 检查Redis服务是否运行: Get-Service Redis');
    console.log('2. 检查Redis配置文件');
    console.log('3. 检查防火墙设置');
    console.log('4. 检查Redis端口是否被占用: netstat -an | findstr 6379');
    
    process.exit(1);
  } finally {
    if (client) {
      await client.quit();
      console.log('🔌 Redis连接已关闭');
    }
  }
}

// 运行测试
testRedisConnection().catch(console.error);
