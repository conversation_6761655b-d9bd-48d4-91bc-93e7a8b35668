# Ollama 本地大模型配置指南

本项目使用 Ollama 作为本地大模型服务，为英文翻译练习提供 AI 支持。

## Ollama 配置

**优点：** 安装简单，模型管理方便，性能优秀
**适用场景：** 个人学习，轻量级部署

#### 安装步骤：

1. 下载并安装 Ollama
   ```bash
   # Windows/Mac: 访问 https://ollama.ai 下载安装包
   # Linux:
   curl -fsSL https://ollama.ai/install.sh | sh
   ```

2. 启动 Ollama 服务
   ```bash
   ollama serve
   ```

3. 下载推荐模型
   ```bash
   # 轻量级模型（推荐）
   ollama pull qwen2.5:1.5b
   
   # 或者其他模型
   ollama pull llama3.2:3b
   ollama pull gemma2:2b
   ```

4. 测试连接
   ```bash
   curl http://localhost:11434/api/chat -d '{
     "model": "qwen2.5:1.5b",
     "messages": [{"role": "user", "content": "你好"}],
     "stream": false
   }'
   ```

### 2. LM Studio

**优点：** 图形界面友好，模型选择丰富
**适用场景：** 桌面用户，需要图形界面管理

#### 安装步骤：

1. 下载 LM Studio：https://lmstudio.ai/
2. 安装并启动 LM Studio
3. 在 "Discover" 页面下载模型（推荐 3B-7B 参数的模型）
4. 在 "Local Server" 页面启动服务器
5. 确保服务运行在 `http://localhost:1234`

### 3. Text Generation WebUI

**优点：** 功能丰富，支持多种模型格式
**适用场景：** 高级用户，需要详细配置

#### 安装步骤：

1. 克隆仓库
   ```bash
   git clone https://github.com/oobabooga/text-generation-webui.git
   cd text-generation-webui
   ```

2. 安装依赖
   ```bash
   # Windows
   start_windows.bat
   
   # Linux/Mac
   ./start_linux.sh
   ```

3. 启动 WebUI 并启用 API
   ```bash
   python server.py --api --listen
   ```

### 4. llama.cpp Server

**优点：** 轻量级，CPU 友好
**适用场景：** 资源受限环境，CPU 推理

#### 安装步骤：

1. 编译 llama.cpp
   ```bash
   git clone https://github.com/ggerganov/llama.cpp.git
   cd llama.cpp
   make
   ```

2. 下载 GGUF 格式模型
3. 启动服务器
   ```bash
   ./server -m path/to/model.gguf --host 0.0.0.0 --port 8080
   ```

## 配置说明

### 修改配置

编辑 `src/config/llm-config.ts` 文件来自定义配置：

```typescript
export const LLM_PROVIDERS: Record<string, LLMConfig> = {
  ollama: {
    name: 'Ollama',
    apiBase: 'http://localhost:11434/api/chat',
    modelName: 'qwen2.5:1.5b', // 修改为你的模型名
    timeout: 30000,
    maxRetries: 3,
    requestFormat: 'ollama',
    description: 'Ollama 本地部署'
  }
  // ... 其他配置
};
```

### 模型推荐

根据硬件配置选择合适的模型：

| 内存 | 推荐模型 | 说明 |
|------|----------|------|
| 4-8GB | qwen2.5:1.5b, gemma2:2b | 轻量级，响应快 |
| 8-16GB | llama3.2:3b, qwen2.5:3b | 平衡性能和质量 |
| 16GB+ | llama3.1:7b, qwen2.5:7b | 高质量输出 |

## 故障排除

### 常见问题

1. **连接超时**
   - 检查服务是否正常启动
   - 确认端口号是否正确
   - 检查防火墙设置

2. **模型响应慢**
   - 尝试使用更小的模型
   - 检查系统资源使用情况
   - 调整 timeout 设置

3. **翻译质量不佳**
   - 尝试不同的模型
   - 调整提示词模板
   - 检查模型是否支持中英文

### 性能优化

1. **硬件优化**
   - 使用 GPU 加速（如果支持）
   - 增加系统内存
   - 使用 SSD 存储模型

2. **软件优化**
   - 调整模型参数（temperature, max_tokens）
   - 使用量化模型减少内存占用
   - 启用模型缓存

## 开发说明

### 添加新的提供商

1. 在 `llm-config.ts` 中添加配置
2. 在 `LLMService` 中添加对应的请求格式处理
3. 测试连接和功能

### 自定义提示词

修改 `PROMPT_TEMPLATES` 对象来自定义提示词：

```typescript
export const PROMPT_TEMPLATES = {
  chineseGeneration: [
    "你的自定义中文生成提示词..."
  ],
  translationInstruction: (chinese: string) => `你的自定义翻译提示词: ${chinese}`
};
```

## 许可证

本项目遵循 MIT 许可证。使用的模型请遵循各自的许可证要求。
