<template>
  <div class="user-level-management">
    <el-card class="management-card">
      <div slot="header" class="card-header">
        <span class="card-title">
          <i class="el-icon-user"></i>
          用户等级管理
        </span>
        <div class="header-actions">
          <el-button 
            type="primary" 
            size="small" 
            @click="showUpgradeDialog"
          >
            <i class="el-icon-top"></i>
            升级用户
          </el-button>
          <el-button 
            type="default" 
            size="small" 
            @click="showBatchUpgradeDialog"
          >
            <i class="el-icon-s-tools"></i>
            批量升级
          </el-button>
        </div>
      </div>

      <!-- 用户搜索 -->
      <div class="search-section">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-input
              v-model="searchForm.userId"
              placeholder="请输入用户ID"
              clearable
              @keyup.enter.native="searchUser"
            >
              <el-button 
                slot="append" 
                icon="el-icon-search"
                @click="searchUser"
              ></el-button>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select 
              v-model="searchForm.level" 
              placeholder="选择等级"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="config in speedConfigs"
                :key="config.level"
                :label="config.description"
                :value="config.level"
              >
                <span style="float: left">{{ config.description }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  {{ config.level.toUpperCase() }}
                </span>
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="searchUser">
              <i class="el-icon-search"></i>
              搜索
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 用户信息显示 -->
      <div v-if="currentUser" class="user-info-section">
        <el-card class="user-card">
          <div class="user-header">
            <div class="user-basic">
              <el-avatar 
                :size="60" 
                :src="currentUser.avatar"
                icon="el-icon-user-solid"
              ></el-avatar>
              <div class="user-details">
                <h3>{{ currentUser.username || currentUser.nickname || '未知用户' }}</h3>
                <p class="user-id">ID: {{ currentUser.userId }}</p>
                <el-tag 
                  :type="getLevelTagType(currentUser.speedLimit?.level || 'free')"
                  size="medium"
                >
                  {{ getUserLevelLabel(currentUser.speedLimit?.level || 'free') }}
                </el-tag>
              </div>
            </div>
            <div class="user-actions">
              <el-button 
                type="primary" 
                size="small"
                @click="upgradeCurrentUser"
              >
                <i class="el-icon-top"></i>
                升级等级
              </el-button>
            </div>
          </div>

          <!-- 用户限速信息 -->
          <div class="speed-info">
            <el-row :gutter="16">
              <el-col :span="6">
                <div class="info-item">
                  <div class="info-label">下载速度</div>
                  <div class="info-value">
                    {{ currentUser.speedLimit?.downloadSpeedFormatted || '未知' }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="info-item">
                  <div class="info-label">上传速度</div>
                  <div class="info-value">
                    {{ currentUser.speedLimit?.uploadSpeedFormatted || '未知' }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="info-item">
                  <div class="info-label">并发下载</div>
                  <div class="info-value">
                    {{ currentUser.speedLimit?.concurrentDownloads || 0 }} 个
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="info-item">
                  <div class="info-label">每日限额</div>
                  <div class="info-value">
                    {{ currentUser.speedLimit?.dailyDownloadLimitFormatted || '未知' }}
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 今日使用情况 -->
          <div class="usage-info">
            <h4>今日使用情况</h4>
            <el-row :gutter="16">
              <el-col :span="12">
                <div class="usage-item">
                  <div class="usage-header">
                    <span>下载使用量</span>
                    <span class="usage-percentage">
                      {{ calculateUsagePercentage(
                        currentUser.dailyLimits?.download?.totalDownloaded || 0,
                        currentUser.dailyLimits?.download?.dailyLimit || 0
                      ) }}%
                    </span>
                  </div>
                  <el-progress 
                    :percentage="calculateUsagePercentage(
                      currentUser.dailyLimits?.download?.totalDownloaded || 0,
                      currentUser.dailyLimits?.download?.dailyLimit || 0
                    )"
                    :color="getProgressColor(calculateUsagePercentage(
                      currentUser.dailyLimits?.download?.totalDownloaded || 0,
                      currentUser.dailyLimits?.download?.dailyLimit || 0
                    ))"
                  ></el-progress>
                  <div class="usage-detail">
                    已用: {{ currentUser.dailyLimits?.download?.totalDownloadedFormatted || '0B' }} / 
                    限额: {{ currentUser.dailyLimits?.download?.remainingFormatted || '无限制' }}
                  </div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="usage-item">
                  <div class="usage-header">
                    <span>上传使用量</span>
                    <span class="usage-percentage">
                      {{ calculateUsagePercentage(
                        currentUser.dailyLimits?.upload?.totalUploaded || 0,
                        currentUser.dailyLimits?.upload?.dailyLimit || 0
                      ) }}%
                    </span>
                  </div>
                  <el-progress 
                    :percentage="calculateUsagePercentage(
                      currentUser.dailyLimits?.upload?.totalUploaded || 0,
                      currentUser.dailyLimits?.upload?.dailyLimit || 0
                    )"
                    :color="getProgressColor(calculateUsagePercentage(
                      currentUser.dailyLimits?.upload?.totalUploaded || 0,
                      currentUser.dailyLimits?.upload?.dailyLimit || 0
                    ))"
                  ></el-progress>
                  <div class="usage-detail">
                    已用: {{ currentUser.dailyLimits?.upload?.totalUploadedFormatted || '0B' }} / 
                    限额: {{ currentUser.dailyLimits?.upload?.remainingFormatted || '无限制' }}
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>

      <!-- 空状态 -->
      <div v-if="!currentUser && !loading" class="empty-state">
        <i class="el-icon-search"></i>
        <p>请输入用户ID进行搜索</p>
      </div>
    </el-card>

    <!-- 升级用户对话框 -->
    <el-dialog
      title="升级用户等级"
      :visible.sync="upgradeDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="upgradeForm" :rules="upgradeRules" ref="upgradeForm" label-width="100px">
        <el-form-item label="用户ID" prop="userId">
          <el-input 
            v-model="upgradeForm.userId" 
            placeholder="请输入用户ID"
            :disabled="!!currentUser"
          ></el-input>
        </el-form-item>
        <el-form-item label="新等级" prop="newLevel">
          <el-select v-model="upgradeForm.newLevel" placeholder="选择新等级" style="width: 100%">
            <el-option
              v-for="config in speedConfigs"
              :key="config.level"
              :label="config.description"
              :value="config.level"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>{{ config.description }}</span>
                <el-tag size="mini" :type="getLevelTagType(config.level)">
                  {{ config.level.toUpperCase() }}
                </el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="升级原因">
          <el-input 
            v-model="upgradeForm.reason" 
            type="textarea" 
            :rows="3"
            placeholder="请输入升级原因（可选）"
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="upgradeDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmUpgrade"
          :loading="upgrading"
        >
          确认升级
        </el-button>
      </div>
    </el-dialog>

    <!-- 批量升级对话框 -->
    <el-dialog
      title="批量升级用户等级"
      :visible.sync="batchUpgradeDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="batchUpgradeForm" label-width="120px">
        <el-form-item label="用户ID列表">
          <el-input 
            v-model="batchUpgradeForm.userIds" 
            type="textarea" 
            :rows="4"
            placeholder="请输入用户ID，每行一个或用逗号分隔"
          ></el-input>
          <div class="form-tip">
            支持多种格式：每行一个ID，或用逗号、分号、空格分隔
          </div>
        </el-form-item>
        <el-form-item label="目标等级">
          <el-select v-model="batchUpgradeForm.newLevel" placeholder="选择目标等级" style="width: 100%">
            <el-option
              v-for="config in speedConfigs"
              :key="config.level"
              :label="config.description"
              :value="config.level"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>{{ config.description }}</span>
                <el-tag size="mini" :type="getLevelTagType(config.level)">
                  {{ config.level.toUpperCase() }}
                </el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="升级原因">
          <el-input 
            v-model="batchUpgradeForm.reason" 
            type="textarea" 
            :rows="2"
            placeholder="请输入批量升级原因（可选）"
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="batchUpgradeDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmBatchUpgrade"
          :loading="batchUpgrading"
        >
          批量升级
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fileManagementApi, dashboardFileApi, fileUtils } from '@/utils/fileManagementApi'

export default {
  name: 'UserLevelManagement',
  data() {
    return {
      loading: false,
      speedConfigs: [],
      currentUser: null,
      
      // 搜索表单
      searchForm: {
        userId: '',
        level: ''
      },
      
      // 升级对话框
      upgradeDialogVisible: false,
      upgrading: false,
      upgradeForm: {
        userId: '',
        newLevel: '',
        reason: ''
      },
      upgradeRules: {
        userId: [
          { required: true, message: '请输入用户ID', trigger: 'blur' }
        ],
        newLevel: [
          { required: true, message: '请选择新等级', trigger: 'change' }
        ]
      },
      
      // 批量升级对话框
      batchUpgradeDialogVisible: false,
      batchUpgrading: false,
      batchUpgradeForm: {
        userIds: '',
        newLevel: '',
        reason: ''
      }
    }
  },
  mounted() {
    this.loadSpeedConfigs()
  },
  methods: {
    // 加载限速配置
    async loadSpeedConfigs() {
      try {
        const response = await dashboardFileApi.getSpeedLimitConfig()
        if (response.code === 200) {
          this.speedConfigs = response.data || []
        }
      } catch (error) {
        console.error('获取限速配置失败:', error)
      }
    },

    // 搜索用户
    async searchUser() {
      if (!this.searchForm.userId.trim()) {
        this.$message.warning('请输入用户ID')
        return
      }

      this.loading = true
      try {
        const response = await fileManagementApi.getUserSpeedLimit(this.searchForm.userId.trim())
        if (response.code === 200) {
          this.currentUser = response.data
        } else {
          this.$message.error(response.message || '获取用户信息失败')
          this.currentUser = null
        }
      } catch (error) {
        console.error('搜索用户失败:', error)
        this.$message.error('搜索用户失败')
        this.currentUser = null
      } finally {
        this.loading = false
      }
    },

    // 显示升级对话框
    showUpgradeDialog() {
      this.upgradeForm = {
        userId: this.currentUser ? this.currentUser.userId : '',
        newLevel: '',
        reason: ''
      }
      this.upgradeDialogVisible = true
    },

    // 升级当前用户
    upgradeCurrentUser() {
      if (!this.currentUser) {
        this.$message.warning('请先搜索用户')
        return
      }
      this.showUpgradeDialog()
    },

    // 确认升级
    async confirmUpgrade() {
      this.$refs.upgradeForm.validate(async (valid) => {
        if (!valid) return

        this.upgrading = true
        try {
          const response = await dashboardFileApi.upgradeUser({
            userId: this.upgradeForm.userId,
            newLevel: this.upgradeForm.newLevel
          })

          if (response.code === 200) {
            this.$message.success('用户等级升级成功')
            this.upgradeDialogVisible = false
            
            // 如果是当前用户，重新加载用户信息
            if (this.currentUser && this.currentUser.userId === this.upgradeForm.userId) {
              this.searchUser()
            }
          } else {
            this.$message.error(response.message || '升级失败')
          }
        } catch (error) {
          console.error('升级用户等级失败:', error)
          this.$message.error('升级失败')
        } finally {
          this.upgrading = false
        }
      })
    },

    // 显示批量升级对话框
    showBatchUpgradeDialog() {
      this.batchUpgradeForm = {
        userIds: '',
        newLevel: '',
        reason: ''
      }
      this.batchUpgradeDialogVisible = true
    },

    // 确认批量升级
    async confirmBatchUpgrade() {
      if (!this.batchUpgradeForm.userIds.trim()) {
        this.$message.warning('请输入用户ID列表')
        return
      }
      if (!this.batchUpgradeForm.newLevel) {
        this.$message.warning('请选择目标等级')
        return
      }

      // 解析用户ID列表
      const userIds = this.parseUserIds(this.batchUpgradeForm.userIds)
      if (userIds.length === 0) {
        this.$message.warning('未找到有效的用户ID')
        return
      }

      this.$confirm(`确认将 ${userIds.length} 个用户升级到 ${this.getUserLevelLabel(this.batchUpgradeForm.newLevel)}？`, '批量升级确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.batchUpgrading = true
        
        let successCount = 0
        let failCount = 0
        
        for (const userId of userIds) {
          try {
            const response = await dashboardFileApi.upgradeUser({
              userId: userId,
              newLevel: this.batchUpgradeForm.newLevel
            })
            
            if (response.code === 200) {
              successCount++
            } else {
              failCount++
            }
          } catch (error) {
            failCount++
          }
        }
        
        this.batchUpgrading = false
        this.batchUpgradeDialogVisible = false
        
        this.$message.success(`批量升级完成：成功 ${successCount} 个，失败 ${failCount} 个`)
      }).catch(() => {
        // 用户取消
      })
    },

    // 解析用户ID列表
    parseUserIds(text) {
      return text
        .split(/[,;\s\n]+/)
        .map(id => id.trim())
        .filter(id => id.length > 0)
    },

    // 计算使用百分比
    calculateUsagePercentage(used, total) {
      if (total === 0 || total === -1) return 0
      return Math.min(Math.round((used / total) * 100), 100)
    },

    // 获取进度条颜色
    getProgressColor(percentage) {
      if (percentage < 50) return '#67C23A'
      if (percentage < 80) return '#E6A23C'
      return '#F56C6C'
    },

    // 获取等级标签类型
    getLevelTagType(level) {
      const typeMap = {
        free: 'info',
        basic: 'success',
        premium: 'warning',
        vip: 'danger',
        unlimited: ''
      }
      return typeMap[level] || 'info'
    },

    // 获取用户等级标签
    getUserLevelLabel(level) {
      return fileUtils.getUserLevelLabel(level)
    }
  }
}
</script>

<style scoped>
.user-level-management {
  height: 100%;
}

.management-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-title i {
  margin-right: 8px;
  color: #409EFF;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.search-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #F8F9FA;
  border-radius: 8px;
}

.user-info-section {
  margin-top: 16px;
}

.user-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.user-basic {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-details h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.user-id {
  margin: 0 0 8px 0;
  color: #909399;
  font-size: 14px;
}

.speed-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #F8F9FA;
  border-radius: 8px;
}

.info-item {
  text-align: center;
}

.info-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.info-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.usage-info h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
}

.usage-item {
  padding: 16px;
  background-color: #FFFFFF;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
}

.usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.usage-percentage {
  font-weight: 600;
  color: #409EFF;
}

.usage-detail {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.empty-state {
  text-align: center;
  padding: 60px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
