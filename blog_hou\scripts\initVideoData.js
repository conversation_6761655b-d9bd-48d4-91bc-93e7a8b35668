const db = require("../utils/db");

async function initVideoData() {
  try {
    console.log("🎬 初始化视频管理数据...\n");

    // 1. 添加默认分类
    console.log("1. 添加默认分类...");
    const defaultCategories = [
      { name: '其他', description: '未分类的视频', sort_order: 999 },
      { name: '电影', description: '电影类视频', sort_order: 1 },
      { name: '电视剧', description: '电视剧类视频', sort_order: 2 },
      { name: '综艺', description: '综艺节目', sort_order: 3 },
      { name: '动漫', description: '动画片和动漫', sort_order: 4 },
      { name: '纪录片', description: '纪录片类视频', sort_order: 5 },
      { name: '音乐', description: '音乐视频和MV', sort_order: 6 },
      { name: '体育', description: '体育赛事和运动', sort_order: 7 },
      { name: '游戏', description: '游戏相关视频', sort_order: 8 },
      { name: '教育', description: '教育和学习类视频', sort_order: 9 },
      { name: '生活', description: '生活类视频', sort_order: 10 }
    ];

    for (const category of defaultCategories) {
      try {
        // 检查是否已存在
        const existing = await db.query("SELECT id FROM video_categories WHERE name = ?", [category.name]);
        if (existing.length === 0) {
          await db.query(
            "INSERT INTO video_categories (name, description, sort_order, is_active) VALUES (?, ?, ?, TRUE)",
            [category.name, category.description, category.sort_order]
          );
          console.log(`  ✅ 添加分类: ${category.name}`);
        } else {
          console.log(`  ⚠️  分类已存在: ${category.name}`);
        }
      } catch (error) {
        console.log(`  ❌ 添加分类失败: ${category.name} - ${error.message}`);
      }
    }

    // 2. 添加默认标签
    console.log("\n2. 添加默认标签...");
    const defaultTags = [
      { name: '高清', color: '#52c41a' },
      { name: '4K', color: '#1890ff' },
      { name: '中文字幕', color: '#722ed1' },
      { name: '英文字幕', color: '#eb2f96' },
      { name: '无字幕', color: '#fa8c16' },
      { name: '国语', color: '#13c2c2' },
      { name: '粤语', color: '#52c41a' },
      { name: '英语', color: '#1890ff' },
      { name: '日语', color: '#722ed1' },
      { name: '韩语', color: '#eb2f96' },
      { name: '热门', color: '#f5222d' },
      { name: '经典', color: '#fa541c' },
      { name: '新片', color: '#faad14' },
      { name: '推荐', color: '#52c41a' },
      { name: '收藏', color: '#1890ff' }
    ];

    for (const tag of defaultTags) {
      try {
        // 检查是否已存在
        const existing = await db.query("SELECT id FROM video_tags WHERE name = ?", [tag.name]);
        if (existing.length === 0) {
          await db.query(
            "INSERT INTO video_tags (name, color, usage_count) VALUES (?, ?, 0)",
            [tag.name, tag.color]
          );
          console.log(`  ✅ 添加标签: ${tag.name}`);
        } else {
          console.log(`  ⚠️  标签已存在: ${tag.name}`);
        }
      } catch (error) {
        console.log(`  ❌ 添加标签失败: ${tag.name} - ${error.message}`);
      }
    }

    // 3. 统计当前数据
    console.log("\n3. 统计当前数据...");
    const categoryCount = await db.query("SELECT COUNT(*) as count FROM video_categories WHERE is_active = TRUE");
    const tagCount = await db.query("SELECT COUNT(*) as count FROM video_tags");
    const videoCount = await db.query("SELECT COUNT(*) as count FROM video_management");

    console.log(`  📊 分类数量: ${categoryCount[0].count}`);
    console.log(`  📊 标签数量: ${tagCount[0].count}`);
    console.log(`  📊 视频数量: ${videoCount[0].count}`);

    // 4. 显示分类列表
    console.log("\n4. 当前分类列表:");
    const categories = await db.query("SELECT name, description FROM video_categories WHERE is_active = TRUE ORDER BY sort_order ASC");
    categories.forEach((cat, index) => {
      console.log(`  ${index + 1}. ${cat.name} - ${cat.description}`);
    });

    // 5. 显示标签列表
    console.log("\n5. 当前标签列表:");
    const tags = await db.query("SELECT name, color FROM video_tags ORDER BY name ASC");
    tags.forEach((tag, index) => {
      console.log(`  ${index + 1}. ${tag.name} (${tag.color})`);
    });

    console.log("\n🎉 视频管理数据初始化完成！");

  } catch (error) {
    console.error("❌ 初始化失败:", error);
  } finally {
    process.exit(0);
  }
}

// 运行初始化
initVideoData();
