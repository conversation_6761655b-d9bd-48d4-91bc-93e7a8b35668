// utils/validation.ts

interface Errors {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
};

export const validateField = (
  field: keyof Errors,
  username: string,
  email: string,
  password: string,
  confirmPassword: string,
  errors: Errors
): void => {
  switch (field) {
    case "username":
      errors.username = username.length < 3 ? "用户名至少3个字符" : "";
      break;
    case "email":
      errors.email = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
        ? ""
        : "邮箱格式不正确";
      break;
    case "password":
      errors.password = password.length < 6 ? "密码至少6个字符" : "";
      break;
    case "confirmPassword":
      errors.confirmPassword =
        password === confirmPassword ? "" : "两次密码输入不一致";
      break;
  }
};
