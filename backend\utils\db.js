const mysql = require("mysql");

const config = {
  host: "localhost",
  user: "root",
  password: "0519",
  database: "myblog",
};

const pool = mysql.createPool(config);

// 启动时测试一次连接
pool.getConnection((err, connection) => {
  if (err) {
    console.error("数据库连接失败:", err.message);
  } else {
    console.log("数据库连接成功！");
    connection.release();
  }
});

module.exports = {
  query(sql, args) {
    return new Promise((resolve, reject) => {
      pool.getConnection(function (err, connection) {
        if (err) {
          reject(err);
        } else {
          connection.query(sql, args, function (err, rows) {
            if (err) {
              reject(err);
            } else {
              resolve(rows);
            }
            connection.release();
          });
        }
      });
    });
  },
};