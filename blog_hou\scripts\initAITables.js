const db = require('../utils/db');

async function initAITables() {
  try {
    console.log('🤖 开始初始化AI相关数据表...\n');
    
    // 1. 创建AI聊天历史表
    console.log('📝 创建AI聊天历史表...');
    const createChatHistoryTable = `
      CREATE TABLE IF NOT EXISTS ai_chat_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        user_message TEXT NOT NULL,
        bot_response TEXT NOT NULL,
        session_id VARCHAR(100) DEFAULT NULL COMMENT '会话ID',
        model_used VARCHAR(50) DEFAULT 'qwen2.5:7b' COMMENT '使用的模型',
        response_time INT DEFAULT NULL COMMENT '响应时间(毫秒)',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_session_id (session_id),
        INDEX idx_created_at (created_at),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI聊天历史表'
    `;
    
    await db.query(createChatHistoryTable);
    console.log('✅ AI聊天历史表创建成功');
    
    // 2. 创建AI内容分析表
    console.log('📝 创建AI内容分析表...');
    const createContentAnalysisTable = `
      CREATE TABLE IF NOT EXISTS ai_content_analysis (
        id INT AUTO_INCREMENT PRIMARY KEY,
        content_id INT NOT NULL,
        content_type ENUM('article', 'comment', 'chat') NOT NULL,
        analysis_type ENUM('tags', 'summary', 'moderation', 'sentiment') NOT NULL,
        input_content TEXT NOT NULL,
        ai_result JSON NOT NULL,
        model_used VARCHAR(50) DEFAULT 'qwen2.5:7b',
        processing_time INT DEFAULT NULL COMMENT '处理时间(毫秒)',
        confidence_score DECIMAL(3,2) DEFAULT NULL COMMENT '置信度分数',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_content (content_id, content_type),
        INDEX idx_analysis_type (analysis_type),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI内容分析表'
    `;
    
    await db.query(createContentAnalysisTable);
    console.log('✅ AI内容分析表创建成功');
    
    // 3. 创建AI推荐记录表
    console.log('📝 创建AI推荐记录表...');
    const createRecommendationTable = `
      CREATE TABLE IF NOT EXISTS ai_recommendations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        recommendation_type ENUM('content', 'tags', 'friends', 'articles') NOT NULL,
        input_data JSON NOT NULL COMMENT '输入数据',
        recommendations JSON NOT NULL COMMENT '推荐结果',
        user_feedback ENUM('like', 'dislike', 'neutral') DEFAULT NULL,
        click_count INT DEFAULT 0 COMMENT '点击次数',
        model_used VARCHAR(50) DEFAULT 'qwen2.5:7b',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_recommendation_type (recommendation_type),
        INDEX idx_created_at (created_at),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI推荐记录表'
    `;
    
    await db.query(createRecommendationTable);
    console.log('✅ AI推荐记录表创建成功');
    
    // 4. 创建AI模型配置表
    console.log('📝 创建AI模型配置表...');
    const createModelConfigTable = `
      CREATE TABLE IF NOT EXISTS ai_model_configs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        model_name VARCHAR(100) NOT NULL UNIQUE,
        model_type ENUM('chat', 'embedding', 'completion') NOT NULL,
        endpoint_url VARCHAR(255) NOT NULL,
        max_tokens INT DEFAULT 2000,
        temperature DECIMAL(3,2) DEFAULT 0.7,
        is_active TINYINT(1) DEFAULT 1,
        description TEXT,
        capabilities JSON COMMENT '模型能力配置',
        performance_metrics JSON COMMENT '性能指标',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_model_name (model_name),
        INDEX idx_model_type (model_type),
        INDEX idx_is_active (is_active)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI模型配置表'
    `;
    
    await db.query(createModelConfigTable);
    console.log('✅ AI模型配置表创建成功');
    
    // 5. 插入默认模型配置
    console.log('📊 插入默认模型配置...');
    const defaultModels = [
      {
        model_name: 'qwen2.5:7b',
        model_type: 'chat',
        endpoint_url: 'http://localhost:11434/api/chat',
        max_tokens: 2000,
        temperature: 0.7,
        description: '通义千问2.5 7B模型，适合中文对话和内容生成',
        capabilities: JSON.stringify({
          languages: ['zh', 'en'],
          tasks: ['chat', 'content_generation', 'summarization', 'translation'],
          context_length: 32768
        })
      },
      {
        model_name: 'llama3.1:8b',
        model_type: 'chat',
        endpoint_url: 'http://localhost:11434/api/chat',
        max_tokens: 2000,
        temperature: 0.7,
        description: 'Llama 3.1 8B模型，适合英文对话和推理',
        capabilities: JSON.stringify({
          languages: ['en', 'zh'],
          tasks: ['chat', 'reasoning', 'code_generation', 'analysis'],
          context_length: 128000
        })
      },
      {
        model_name: 'gemma2:9b',
        model_type: 'chat',
        endpoint_url: 'http://localhost:11434/api/chat',
        max_tokens: 2000,
        temperature: 0.7,
        description: 'Google Gemma2 9B模型，平衡性能和效果',
        capabilities: JSON.stringify({
          languages: ['en', 'zh'],
          tasks: ['chat', 'content_moderation', 'classification'],
          context_length: 8192
        })
      }
    ];
    
    for (const model of defaultModels) {
      // 检查模型是否已存在
      const existingModel = await db.query(
        'SELECT id FROM ai_model_configs WHERE model_name = ?',
        [model.model_name]
      );
      
      if (existingModel.length === 0) {
        await db.query(`
          INSERT INTO ai_model_configs 
          (model_name, model_type, endpoint_url, max_tokens, temperature, description, capabilities)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
          model.model_name,
          model.model_type,
          model.endpoint_url,
          model.max_tokens,
          model.temperature,
          model.description,
          model.capabilities
        ]);
        
        console.log(`✅ 模型配置插入成功: ${model.model_name}`);
      } else {
        console.log(`✅ 模型配置已存在: ${model.model_name}`);
      }
    }
    
    // 6. 创建AI使用统计表
    console.log('📝 创建AI使用统计表...');
    const createUsageStatsTable = `
      CREATE TABLE IF NOT EXISTS ai_usage_stats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        date DATE NOT NULL,
        model_name VARCHAR(100) NOT NULL,
        feature_type VARCHAR(50) NOT NULL,
        request_count INT DEFAULT 0,
        success_count INT DEFAULT 0,
        error_count INT DEFAULT 0,
        avg_response_time DECIMAL(8,2) DEFAULT NULL,
        total_tokens_used INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY uk_date_model_feature (date, model_name, feature_type),
        INDEX idx_date (date),
        INDEX idx_model_name (model_name)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI使用统计表'
    `;
    
    await db.query(createUsageStatsTable);
    console.log('✅ AI使用统计表创建成功');
    
    // 7. 验证表创建结果
    console.log('\n🔍 验证AI表创建结果...');
    const aiTables = [
      'ai_chat_history',
      'ai_content_analysis', 
      'ai_recommendations',
      'ai_model_configs',
      'ai_usage_stats'
    ];
    
    for (const tableName of aiTables) {
      const tableExists = await db.query(`
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
      `, [tableName]);
      
      if (tableExists.length > 0) {
        const countResult = await db.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`✅ ${tableName}: 存在，${countResult[0].count} 行数据`);
      } else {
        console.log(`❌ ${tableName}: 不存在`);
      }
    }
    
    console.log('\n🎉 AI相关数据表初始化完成！');
    
    // 8. 输出使用说明
    console.log('\n📋 AI功能说明:');
    console.log('   🤖 智能客服: /ai/chat');
    console.log('   🏷️  自动标签: /ai/generate-tags');
    console.log('   📝 内容摘要: /ai/summarize');
    console.log('   🔍 内容审核: /ai/moderate');
    console.log('   💡 内容推荐: /ai/recommend');
    console.log('   📰 标题生成: /ai/generate-titles');
    console.log('   ⚙️  服务状态: /ai/status');
    
    console.log('\n🔧 Ollama配置:');
    console.log('   📍 默认地址: http://localhost:11434');
    console.log('   🤖 默认模型: qwen2.5:7b');
    console.log('   ⏱️  超时时间: 30秒');
    
  } catch (error) {
    console.error('❌ AI表初始化失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initAITables()
    .then(() => {
      console.log('\n✅ 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { initAITables };
