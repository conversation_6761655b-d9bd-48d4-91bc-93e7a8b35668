<template>
  <div class="register-container">
    <van-nav-bar title="用户注册" />
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <van-field
          v-model="username"
          name="用户名"
          label="用户名"
          placeholder="用户名"
          :rules="[{ required: true, message: '请填写用户名' }]"
        />
        <van-field
          v-model="email"
          name="邮箱"
          label="邮箱"
          placeholder="请输入邮箱"
          :rules="[
            { required: true, message: '请填写邮箱' },
            { type: 'email', message: '邮箱格式不正确' },
          ]"
        />
      </van-cell-group>
      <van-cell-group inset>
        <van-field
          v-model="password"
          type="password"
          name="密码"
          label="密码"
          placeholder="请输入密码"
          :rules="[
            { required: true, message: '请填写密码' },
            { min: 6, message: '密码至少6个字符' },
          ]"
        />
        <van-field
          v-model="confirmPassword"
          type="password"
          name="确认密码"
          label="确认密码"
          placeholder="请再次输入密码"
          :rules="[
            { required: true, message: '请确认密码' },
            { min: 6, message: '密码至少6个字符' },
          ]"
        />
      </van-cell-group>
      <div class="btn-group">
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          :loading="isSubmitting"
        >
          注册
        </van-button>
        <van-button round block type="default" @click="$router.push('/login')">
          登录
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script setup lang="ts">
import { registerApi } from "../../utils/api.ts";
import { ref } from "vue";
import { useRouter } from "vue-router";
const router = useRouter();

const username = ref("");
const email = ref("");
const password = ref("");
const confirmPassword = ref("");
const isSubmitting = ref(false);

const onSubmit = async () => {
  if (password.value !== confirmPassword.value) {
    alert("两次输入的密码不一致");
    return;
  }
  isSubmitting.value = true;
  const res = await registerApi({
    username: username.value,
    email: email.value,
    password: password.value,
  });
  if (res.code === 200) {
    alert("注册成功，请登录");
    router.push("/");
  } else {
    alert("注册失败，请重试");
  }
  isSubmitting.value = false;
};
</script>

<style scoped>
.register-container {
  max-width: 380px;
  margin: 40px auto 0 auto;
  padding: 32px 24px 24px 24px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
}

.van-nav-bar {
  margin-bottom: 24px;
}

.btn-group {
  margin: 24px 0 0 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.van-button {
  font-size: 16px;
  letter-spacing: 1px;
}
</style>