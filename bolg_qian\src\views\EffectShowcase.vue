<template>
  <div class="effect-showcase">
    <!-- 页面标题 -->
    <div class="showcase-header">
      <h1 class="showcase-title">✨ 页面特效展示</h1>
      <p class="showcase-subtitle">体验不同的页面特效，选择您喜欢的视觉效果</p>
    </div>

    <!-- 特效卡片列表 -->
    <div class="effect-cards">
      <div 
        v-for="effect in effectList" 
        :key="effect.type"
        class="effect-card"
        :class="{ active: currentPreview === effect.type }"
        @click="previewEffect(effect.type)"
      >
        <div class="card-header">
          <h3 class="card-title">{{ effect.name }}</h3>
          <el-tag 
            :type="getPerformanceTagType(effect.performance)" 
            size="small"
          >
            {{ getPerformanceText(effect.performance) }}
          </el-tag>
        </div>
        
        <div class="card-preview">
          <div class="preview-container" :id="`preview-${effect.type}`">
            <div class="preview-placeholder">
              <el-icon><VideoPlay /></el-icon>
              <span>点击预览</span>
            </div>
          </div>
        </div>
        
        <div class="card-content">
          <p class="card-description">{{ effect.description }}</p>
          
          <div class="card-features">
            <el-tag 
              v-for="feature in effect.features" 
              :key="feature"
              size="small"
              effect="plain"
            >
              {{ feature }}
            </el-tag>
          </div>
        </div>
        
        <div class="card-actions">
          <el-button 
            size="small" 
            @click.stop="previewEffect(effect.type)"
            :loading="currentPreview === effect.type && isLoading"
          >
            {{ currentPreview === effect.type ? '预览中' : '预览效果' }}
          </el-button>
          <el-button 
            type="primary" 
            size="small"
            @click.stop="applyEffect(effect.type)"
          >
            应用此特效
          </el-button>
        </div>
      </div>
    </div>

    <!-- 当前预览区域 -->
    <div class="preview-section" v-if="currentPreview !== 'none'">
      <div class="preview-header">
        <h2>正在预览：{{ getCurrentEffectName() }}</h2>
        <div class="preview-controls">
          <el-button size="small" @click="stopPreview">停止预览</el-button>
          <el-button type="primary" size="small" @click="applyEffect(currentPreview)">
            应用此特效
          </el-button>
        </div>
      </div>
      
      <!-- 特效渲染区域 -->
      <div class="preview-area">
        <Meteor 
          v-if="currentPreview === 'meteor'"
          :count="25"
          :speed="4"
        />
        <NightSky v-if="currentPreview === 'nightsky'" />
        <Particle v-if="currentPreview === 'particle'" />
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="showcase-footer">
      <el-button @click="goBack" size="large">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { VideoPlay, ArrowLeft } from '@element-plus/icons-vue'
import Meteor from '@/components/texiao/Meteor.vue'
import NightSky from '@/components/texiao/NightSky.vue'
import Particle from '@/components/texiao/Particle.vue'
import { 
  effectConfig, 
  EFFECT_TYPES, 
  EFFECT_NAMES,
  EFFECT_DESCRIPTIONS,
  EFFECT_PERFORMANCE
} from '@/utils/effectConfig.js'

const router = useRouter()

// 响应式数据
const currentPreview = ref('none')
const isLoading = ref(false)

// 特效列表
const effectList = ref([
  {
    type: EFFECT_TYPES.METEOR,
    name: EFFECT_NAMES[EFFECT_TYPES.METEOR],
    description: EFFECT_DESCRIPTIONS[EFFECT_TYPES.METEOR],
    performance: EFFECT_PERFORMANCE[EFFECT_TYPES.METEOR],
    features: ['流畅动画', '可配置参数', '低内存占用']
  },
  {
    type: EFFECT_TYPES.NIGHTSKY,
    name: EFFECT_NAMES[EFFECT_TYPES.NIGHTSKY],
    description: EFFECT_DESCRIPTIONS[EFFECT_TYPES.NIGHTSKY],
    performance: EFFECT_PERFORMANCE[EFFECT_TYPES.NIGHTSKY],
    features: ['星空背景', '交互效果', '月亮装饰']
  },
  {
    type: EFFECT_TYPES.PARTICLE,
    name: EFFECT_NAMES[EFFECT_TYPES.PARTICLE],
    description: EFFECT_DESCRIPTIONS[EFFECT_TYPES.PARTICLE],
    performance: EFFECT_PERFORMANCE[EFFECT_TYPES.PARTICLE],
    features: ['粒子连线', '鼠标交互', '动态效果']
  }
])

// 方法
const previewEffect = (effectType) => {
  if (currentPreview.value === effectType) {
    stopPreview()
    return
  }
  
  isLoading.value = true
  currentPreview.value = effectType
  
  setTimeout(() => {
    isLoading.value = false
  }, 1000)
}

const stopPreview = () => {
  currentPreview.value = 'none'
  isLoading.value = false
}

const applyEffect = (effectType) => {
  effectConfig.setCurrentEffect(effectType)
  ElMessage.success(`已应用${EFFECT_NAMES[effectType]}特效`)
  
  // 延迟跳转，让用户看到成功消息
  setTimeout(() => {
    router.push('/index/home')
  }, 1500)
}

const getCurrentEffectName = () => {
  return EFFECT_NAMES[currentPreview.value] || '未知特效'
}

const getPerformanceTagType = (performance) => {
  if (performance <= 1) return 'success'
  if (performance <= 2) return 'warning'
  return 'danger'
}

const getPerformanceText = (performance) => {
  if (performance <= 1) return '轻量'
  if (performance <= 2) return '中等'
  return '重度'
}

const goBack = () => {
  router.go(-1)
}

// 生命周期
onMounted(() => {
  // 页面加载时显示提示
  ElMessage.info('点击特效卡片可以预览效果')
})

onUnmounted(() => {
  // 清理预览状态
  stopPreview()
})
</script>

<style scoped>
.effect-showcase {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
}

/* 页面标题 */
.showcase-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.showcase-title {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.showcase-subtitle {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin: 0;
}

/* 特效卡片 */
.effect-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
  max-width: 1200px;
  margin: 0 auto var(--spacing-2xl);
}

.effect-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 2px solid var(--border-light);
  overflow: hidden;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.effect-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.effect-card.active {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.card-preview {
  height: 200px;
  background: var(--bg-tertiary);
  position: relative;
  overflow: hidden;
}

.preview-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-tertiary);
  font-size: var(--text-lg);
}

.preview-placeholder .el-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-sm);
}

.card-content {
  padding: var(--spacing-lg);
}

.card-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: var(--leading-relaxed);
}

.card-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-md);
}

.card-actions {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
  background: var(--bg-tertiary);
}

/* 预览区域 */
.preview-section {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
}

.preview-header {
  position: absolute;
  top: var(--spacing-lg);
  left: var(--spacing-lg);
  right: var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  z-index: 1001;
}

.preview-header h2 {
  color: var(--text-primary);
  margin: 0;
}

.preview-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.preview-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

/* 页面底部 */
.showcase-footer {
  text-align: center;
  margin-top: var(--spacing-2xl);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .effect-showcase {
    padding: var(--spacing-md);
  }
  
  .effect-cards {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .showcase-title {
    font-size: var(--text-2xl);
  }
  
  .card-actions {
    flex-direction: column;
  }
  
  .preview-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .preview-controls {
    justify-content: center;
  }
}
</style>
