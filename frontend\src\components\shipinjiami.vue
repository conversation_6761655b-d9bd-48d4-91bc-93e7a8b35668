<template>
  <div class="video-encryptor">
    <h2>视频加密/解密管理</h2>

    <!-- 选择文件输入 -->
    <input v-model="filename" placeholder="请输入文件名（单文件）" />
    <br />

    <!-- 选择类型 -->
    <label>
      <input type="radio" value="1" v-model="type" /> 加密
    </label>
    <label>
      <input type="radio" value="0" v-model="type" /> 解密
    </label>

    <br />

    <!-- 单文件操作按钮 -->
    <el-button type="primary" @click="handleSingleFile" :loading="loadingSingle">
      处理单个文件
    </el-button>
    <el-progress v-if="loadingSingle" :percentage="progressSingle"
      :status="progressSingle === 100 ? 'success' : 'active'" style="margin-top: 8px;" />

    <hr />

    <!-- 批量操作 -->
    <el-button type="success" @click="handleBatch" :loading="loadingBatch">
      批量处理
    </el-button>
    <el-progress v-if="loadingBatch" :percentage="progressBatch" :status="progressBatch === 100 ? 'success' : 'active'"
      style="margin-top: 8px;" />

    <hr />

    <!-- 日志输出 -->
    <div v-if="logs.length" class="log-box">
      <h3>日志信息</h3>
      <ul>
        <li v-for="(log, index) in logs" :key="index">{{ log }}</li>
      </ul>
    </div>

    <!-- 结果反馈 -->
    <div v-if="resultMessage" class="result-msg">{{ resultMessage }}</div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { EncryptVideosApi, EncryptSingleVideoApi } from "@/utils/api";

const type = ref("1"); // 默认加密
const filename = ref("");
const loadingSingle = ref(false);
const loadingBatch = ref(false);
const progressSingle = ref(0);
const progressBatch = ref(0);
const logs = ref([]);
const resultMessage = ref("");

function appendLog(msg) {
  logs.value.push(msg);
}

async function handleSingleFile() {
  if (!filename.value.trim()) {
    alert("请输入文件名！");
    return;
  }
  loadingSingle.value = true;
  progressSingle.value = 0;
  logs.value = [];
  resultMessage.value = "";
  try {
    appendLog(`开始处理文件：${filename.value}, 类型: ${type.value === "1" ? "加密" : "解密"}`);

    // 模拟进度，真实情况需后端返回进度或者使用 WebSocket 等推送
    let fakeProgress = 0;
    const interval = setInterval(() => {
      if (fakeProgress >= 90) {
        clearInterval(interval);
      } else {
        fakeProgress += 10;
        progressSingle.value = fakeProgress;
      }
    }, 300);

    const res = await EncryptSingleVideoApi({ type: type.value, filename: filename.value });
    progressSingle.value = 100;

    if (res.success) {
      appendLog("操作成功！");
      if (res.logs && res.logs.length) {
        res.logs.forEach((l) => appendLog(l));
      }
      resultMessage.value = res.message || "单文件操作完成";
    } else {
      appendLog("操作失败: " + (res.error || "未知错误"));
      resultMessage.value = "单文件操作失败";
    }
  } catch (err) {
    appendLog("异常错误: " + err.message);
    resultMessage.value = "请求异常";
  } finally {
    loadingSingle.value = false;
  }
}

async function handleBatch() {
  loadingBatch.value = true;
  progressBatch.value = 0;
  logs.value = [];
  resultMessage.value = "";
  appendLog(`开始批量处理类型: ${type.value === "1" ? "加密" : "解密"}`);

  // 模拟进度，真实场景也需要后端支持或者推送
  let fakeProgress = 0;
  const interval = setInterval(() => {
    if (fakeProgress >= 90) {
      clearInterval(interval);
    } else {
      fakeProgress += 5;
      progressBatch.value = fakeProgress;
    }
  }, 300);

  try {
    const res = await EncryptVideosApi({ type: type.value });
    progressBatch.value = 100;
    if (res.success) {
      appendLog("批量操作成功！");
      resultMessage.value = res.message || "批量处理完成";
    } else {
      appendLog("批量操作失败: " + (res.error || "未知错误"));
      resultMessage.value = "批量操作失败";
    }
  } catch (err) {
    appendLog("异常错误: " + err.message);
    resultMessage.value = "请求异常";
  } finally {
    loadingBatch.value = false;
  }
}
</script>

<style scoped>
.video-encryptor {
  max-width: 600px;
  margin: 20px auto;
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #fff;
}

.log-box {
  background: #f0f0f0;
  padding: 8px;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
}

.result-msg {
  margin-top: 12px;
  font-weight: bold;
  color: #42b983;
}
</style>
