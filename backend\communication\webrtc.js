const socketIO = require('socket.io');

function setupWebRTCSignalServer(server) {
  const io = socketIO(server, {
    cors: {
      origin: '*',
    },
  });

  io.on('connection', (socket) => {
    console.log('🔌 A user connected:', socket.id);

    // 加入房间
    socket.on('join', (roomId) => {
      socket.join(roomId);
      socket.to(roomId).emit('user-joined', socket.id); // 通知对方
    });

    // 信令中转：发送 offer / answer / ice-candidate
    socket.on('signal', ({ roomId, data }) => {
      socket.to(roomId).emit('signal', {
        from: socket.id,
        data,
      });
    });

    socket.on('disconnect', () => {
      console.log('❌ A user disconnected:', socket.id);
    });
  });
}

module.exports = setupWebRTCSignalServer;