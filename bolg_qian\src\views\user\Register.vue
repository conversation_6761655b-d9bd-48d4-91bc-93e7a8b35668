<template>
  <div class="register-page">
    <!-- 粒子背景 -->
    <!-- <Particle /> -->
     <!-- <Meteor :count="30" :speed="3" color-start="#ffffff" color-end="#ff00ff" /> -->
      <NightSky />
    <div class="register-container" v-if="!showExplosion">
      <h2 class="register-title">用户注册</h2>
      <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" autocomplete="off" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" autocomplete="off" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" type="password" autocomplete="off" />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="form.confirmPassword" type="password" autocomplete="off" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="isSubmitting" @click="onSubmit" :disabled="isSubmitting">
            注册
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 爆炸粒子动画 -->
    <ParticleExplosion v-if="showExplosion" @done="onExplosionDone" />
  </div>
</template>

<script setup lang="ts">
import Particle from "@/components/texiao/Particle.vue"
import ParticleExplosion from "@/components/texiao/ParticleExplosion.vue"
import NightSky from "@/components/texiao/NightSky.vue"
import { ref } from "vue"
import { useRouter } from "vue-router"
import { registerApi } from "@/utils/api"
import { useUserStore } from "@/stores/user"
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()
const isSubmitting = ref(false)
const formRef = ref()
const showExplosion = ref(false)
const form = ref({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

const rules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: (rule, value) => value === form.value.password, message: '两次密码不一致', trigger: 'blur' }
  ]
}

const onSubmit = () => {
  formRef.value.validate(async (valid) => {
    if (!valid) return
    isSubmitting.value = true
    try {
      const res = await registerApi({
        username: form.value.username,
        email: form.value.email,
        password: form.value.password
      })
      if (res.code === 200) {
        ElMessage.success('注册成功，请登录')
        router.push('/login')
      } else {
        ElMessage.error(res.message || '注册失败，请重试')
      }
    } catch (e) {
      ElMessage.error('请求失败，请稍后重试')
    } finally {
      isSubmitting.value = false
    }
  })
}

// 爆炸动画结束后调用
function onExplosionDone() {
  router.push("/login")
}
</script>


<style scoped>
.register-page {
  position: relative;
  z-index: 2;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;

  /* 合并背景图和颜色，解决 background 被覆盖的问题 */
  background: rgba(255, 255, 255, 0.5) url("http://**************:3000/images/hun.jpg") center/cover no-repeat;
}

.register-container {
  width: 90%;
  max-width: 400px;
  padding: 32px 24px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16px;
  backdrop-filter: blur(12px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  opacity: 0.75;
}

.register-title {
  text-align: center;
  font-size: 22px;
  margin-bottom: 24px;
  color: #333;
  font-weight: 600;
}

.btn-group {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;

}
</style>
