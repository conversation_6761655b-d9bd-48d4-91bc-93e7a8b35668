# 双通道上传组件测试指南

## 🧪 测试步骤

### 1. 基础功能测试

#### 访问测试
1. 打开浏览器访问: `http://localhost:5175/index/dashboard/uploads`
2. 确认页面正常加载，显示双通道选择器
3. 检查是否显示"文档通道"和"媒体通道"两个选项

#### 界面测试
1. **通道切换测试**
   - 点击"文档通道"按钮，确认显示文档上传区域
   - 点击"媒体通道"按钮，确认显示媒体上传区域
   - 检查按钮状态变化和视觉反馈

2. **上传区域测试**
   - 确认每个通道都有独立的拖拽上传区域
   - 检查上传区域的提示文字是否正确
   - 验证拖拽悬停效果

### 2. 文件选择测试

#### 文档通道测试
1. 选择文档通道
2. 测试支持的文件类型：
   - 📄 文档: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT
   - 🖼️ 图片: JPG, PNG, GIF, BMP, WEBP, SVG
   - 📦 压缩包: ZIP, RAR, 7Z, TAR, GZ
3. 确认文件添加到列表中，显示正确的通道标签

#### 媒体通道测试
1. 选择媒体通道
2. 测试支持的文件类型：
   - 🎥 视频: MP4, AVI, MOV, WMV, FLV, MKV, WEBM
   - 🎵 音频: MP3, WAV, FLAC, AAC, OGG, WMA, M4A
3. 确认文件添加到列表中，显示正确的通道标签

### 3. 文件管理测试

#### 文件列表功能
1. **多文件添加**
   - 同时选择多个文件
   - 在不同通道间切换添加文件
   - 确认文件列表正确显示所有文件

2. **文件信息显示**
   - 检查文件名显示
   - 验证文件大小格式化
   - 确认文件类型标签
   - 验证通道标签（文档/媒体）

3. **文件操作**
   - 测试单个文件删除功能
   - 测试清空所有文件功能
   - 确认操作后列表状态正确

### 4. 上传功能测试

#### 小文件上传测试 (< 10MB)
1. 添加小于10MB的文件
2. 点击"开始上传"按钮
3. 观察上传进度：
   - 进度条显示
   - 上传速度显示
   - 状态变化（上传中 → 成功/失败）

#### 大文件上传测试 (≥ 10MB)
1. 添加大于10MB的文件
2. 开始上传，观察分片上传过程
3. 验证分片上传的进度显示

#### 并发上传测试
1. 同时添加多个文件（建议3-5个）
2. 开始上传，观察并发控制
3. 确认最多同时上传3个文件

### 5. 结果验证测试

#### 上传统计
1. 检查上传统计信息：
   - 总文件数
   - 已完成数
   - 失败数
   - 总文件大小

#### 上传结果
1. 验证上传结果列表：
   - 成功文件显示访问链接
   - 失败文件显示错误信息
   - 结果状态图标正确

#### 文件访问测试
1. **媒体文件访问**
   - 点击媒体文件的访问链接
   - 确认可以直接访问（如：`http://192.168.31.222:3000/media/filename`）

2. **文档文件访问**
   - 点击文档文件的访问链接
   - 确认通过API访问（需要权限验证）

### 6. 错误处理测试

#### 文件类型限制
1. 尝试上传不支持的文件类型
2. 确认系统正确拒绝并显示错误信息

#### 网络错误模拟
1. 断开网络连接后尝试上传
2. 确认错误处理和用户提示

#### 服务器错误模拟
1. 停止后端服务后尝试上传
2. 验证错误处理机制

### 7. 响应式测试

#### 桌面端测试
1. 在不同分辨率下测试界面适配
2. 确认所有功能正常工作

#### 移动端测试
1. 使用浏览器开发者工具模拟移动设备
2. 测试触摸操作和界面适配

## ✅ 预期结果

### 成功标准
- [ ] 页面正常加载，无JavaScript错误
- [ ] 双通道切换功能正常
- [ ] 文件选择和列表管理功能正常
- [ ] 上传进度和状态显示正确
- [ ] 并发控制工作正常（最多3个同时上传）
- [ ] 上传结果正确显示
- [ ] 文件访问链接有效
- [ ] 错误处理机制正常
- [ ] 响应式设计适配良好

### 性能指标
- [ ] 页面加载时间 < 3秒
- [ ] 文件选择响应时间 < 1秒
- [ ] 小文件上传速度合理
- [ ] 大文件分片上传稳定
- [ ] 内存使用合理，无内存泄漏

## 🐛 常见问题排查

### 页面无法加载
1. 检查前端服务器是否运行（端口5175）
2. 检查浏览器控制台是否有错误
3. 确认路由配置正确

### 上传失败
1. 检查后端服务器是否运行（端口3000）
2. 确认网络连接正常
3. 检查文件大小和类型限制
4. 查看浏览器网络面板的请求详情

### 文件访问问题
1. 媒体文件：确认文件已保存到 `public/media` 目录
2. 文档文件：确认API权限和路径配置
3. 检查服务器静态文件服务配置

## 📝 测试报告模板

```
测试日期: ___________
测试人员: ___________
浏览器版本: ___________

基础功能测试: ✅/❌
文件选择测试: ✅/❌
上传功能测试: ✅/❌
结果验证测试: ✅/❌
错误处理测试: ✅/❌
响应式测试: ✅/❌

发现问题:
1. ___________
2. ___________
3. ___________

总体评价: ___________
```
