const Router = require("koa-router");
const fs = require("fs");
const path = require("path");
const musicService = require("../../utils/sql/musicService");

const music = new Router();

// 获取音乐列表接口
music.get("/list", async (ctx) => {
  const musicDir = path.join(__dirname, "../../public/music");
  try {
    const files = fs.readdirSync(musicDir).filter((file) => {
      return /\.(mp3|wav|flac|ogg)$/i.test(file);
    });
    ctx.body = {
      code: 0,
      data: files,
      msg: "success",
    };
  } catch (err) {
    ctx.status = 500;
    ctx.body = {
      code: 1,
      msg: "读取音乐列表失败",
    };
  }
});

// 通过 GET 流式播放音频
music.get("/stream/:filename", async (ctx) => {
  const filename = ctx.params.filename;
  const filePath = path.join(__dirname, "../../public/music", filename);

  if (!fs.existsSync(filePath)) {
    ctx.status = 404;
    ctx.body = {
      code: 1,
      msg: "文件不存在",
    };
    return;
  }

  const stat = fs.statSync(filePath);
  const total = stat.size;
  const range = ctx.headers.range;

  const ext = path.extname(filename).toLowerCase();
  let contentType = "application/octet-stream";
  if (ext === ".mp3") contentType = "audio/mpeg";
  else if (ext === ".wav") contentType = "audio/wav";
  else if (ext === ".ogg") contentType = "audio/ogg";
  else if (ext === ".flac") contentType = "audio/flac";

  if (range) {
    // 处理 Range 请求
    const parts = range.replace(/bytes=/, "").split("-");
    const start = parseInt(parts[0], 10);
    const end = parts[1] ? parseInt(parts[1], 10) : total - 1;
    const chunkSize = end - start + 1;

    ctx.status = 206;
    ctx.set("Content-Range", `bytes ${start}-${end}/${total}`);
    ctx.set("Accept-Ranges", "bytes");
    ctx.set("Content-Length", chunkSize);
    ctx.set("Content-Type", contentType);

    ctx.body = fs.createReadStream(filePath, { start, end });
  } else {
    // 普通请求，返回整个文件
    ctx.set("Content-Length", total);
    ctx.set("Content-Type", contentType);
    ctx.set("Accept-Ranges", "bytes");
    ctx.body = fs.createReadStream(filePath);
  }

  // 添加播放历史
  const userId = ctx.query.userId; // 假设用户ID通过查询参数传递
  if (userId) {
    try {
      await musicService.addPlayHistory(userId, filename);
    } catch (error) {
      console.error("插入播放历史记录失败:", error);
    }
  }
});

// 获取歌词
music.get("/lyrics/:filename", async (ctx) => {
  const filename = ctx.params.filename;
  const filePath = path.join(
    __dirname,
    "../../public/music/geci",
    filename.replace(/\.(mp3|wav|flac|ogg)$/i, ".lrc")
  );
  // 默认歌词路径
  const defaultLyricsPath = path.join(
    __dirname,
    "../../public/music/geci/moren.lrc"
  );

  if (fs.existsSync(filePath)) {
    ctx.body = fs.createReadStream(filePath);
  } else if (fs.existsSync(defaultLyricsPath)) {
    ctx.body = fs.createReadStream(defaultLyricsPath);
  } else {
    ctx.status = 404;
    ctx.body = {
      code: 1,
      msg: "歌词文件不存在",
    };
  }
});

// 获取音乐封面
music.get("/cover/:filename", async (ctx) => {
  const filename = ctx.params.filename.replace(/\.(mp3|wav|flac|ogg)$/i, "");
  const coverDir = path.join(__dirname, "../../public/music/cover");
  const jpgPath = path.join(coverDir, filename + ".jpg");
  const pngPath = path.join(coverDir, filename + ".png");
  // 如果没有找到封面，则返回默认封面
  const defaultCoverPath = path.join(coverDir, "moren.jpg");

  if (fs.existsSync(jpgPath)) {
    ctx.type = "image/jpeg";
    ctx.body = fs.createReadStream(jpgPath);
  } else if (fs.existsSync(pngPath)) {
    ctx.type = "image/png";
    ctx.body = fs.createReadStream(pngPath);
  } else if (fs.existsSync(defaultCoverPath)) {
    ctx.type = "image/jpeg"; // 假设默认封面是 jpg 格式
    ctx.body = fs.createReadStream(defaultCoverPath);
  } else {
    ctx.status = 404;
    ctx.body = {
      code: 1,
      msg: "封面文件不存在",
    };
  }
});

// 喜欢接口
music.post("/like", async (ctx) => {
  const { userId, musicId } = ctx.request.body;
  try {
    await musicService.addLike(userId, musicId);
    ctx.body = {
      code: 0,
      msg: "音乐已添加到喜欢列表",
    };
  } catch (error) {
    console.error("插入喜欢记录失败:", error);
    ctx.status = 500;
    ctx.body = {
      code: 1,
      msg: "插入喜欢记录失败",
    };
  }
});

// 收藏接口
music.post("/collect", async (ctx) => {
  const { userId, musicId } = ctx.request.body;
  try {
    await musicService.addCollection(userId, musicId);
    ctx.body = {
      code: 0,
      msg: "音乐已添加到收藏列表",
    };
  } catch (error) {
    console.error("插入收藏记录失败:", error);
    ctx.status = 500;
    ctx.body = {
      code: 1,
      msg: "插入收藏记录失败",
    };
  }
});

// 获取播放历史记录接口
music.get("/play-history", async (ctx) => {
  const userId = ctx.query.userId; // 假设用户ID通过查询参数传递
  try {
    const results = await musicService.getPlayHistory(userId);
    ctx.body = {
      code: 0,
      data: results,
      msg: "success",
    };
  } catch (error) {
    console.error("获取播放历史记录失败:", error);
    ctx.status = 500;
    ctx.body = {
      code: 1,
      msg: "获取播放历史记录失败",
    };
  }
});

module.exports = music;
