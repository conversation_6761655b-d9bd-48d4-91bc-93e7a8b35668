require('dotenv').config();
const appConfig = require('./config/app');

console.log("=== JWT配置调试 ===");
console.log("环境变量 JWT_SECRET:", process.env.JWT_SECRET);
console.log("环境变量 JWT_EXPIRES_IN:", process.env.JWT_EXPIRES_IN);

console.log("\n=== 应用配置 ===");
console.log("JWT配置:", appConfig.jwt);

console.log("\n=== 配置验证 ===");
const jwtConfigured = appConfig.jwt.secret !== 'your-super-secret-jwt-key-change-in-production';
console.log("JWT是否配置:", jwtConfigured);

// 测试JWT工具
try {
  const jwtUtils = require('./utils/jwtUtils');
  console.log("\n=== JWT工具测试 ===");
  
  // 测试生成token
  const testPayload = { userId: 19, username: 'admin123', role: 'admin' };
  const token = jwtUtils.generateToken(testPayload);
  console.log("生成的token:", token.substring(0, 50) + "...");
  
  // 测试验证token
  const decoded = jwtUtils.verifyToken(token);
  console.log("解码的payload:", decoded);
  
} catch (error) {
  console.error("JWT工具测试失败:", error.message);
}

process.exit(0);
