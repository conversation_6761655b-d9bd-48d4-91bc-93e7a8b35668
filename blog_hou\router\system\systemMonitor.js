// 系统监控路由
const Router = require("koa-router");
const os = require("os");
const fs = require("fs");
const { handleResponse } = require("../../middlewares/responseHandler");
const db = require("../../utils/db");
const logger = require("../../plugin/logger");

const router = new Router();

// 获取系统概览信息
router.get("/overview", async (ctx) => {
  try {
    const startTime = Date.now();
    
    // 1. 系统基本信息
    const systemInfo = {
      platform: os.platform(),
      arch: os.arch(),
      hostname: os.hostname(),
      uptime: os.uptime(),
      nodeVersion: process.version,
      processUptime: process.uptime()
    };

    // 2. CPU信息
    const cpus = os.cpus();
    const cpuInfo = {
      model: cpus[0].model,
      cores: cpus.length,
      speed: cpus[0].speed,
      loadAvg: os.loadavg()
    };

    // 3. 内存信息
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;
    const memoryInfo = {
      total: totalMem,
      free: freeMem,
      used: usedMem,
      usagePercent: ((usedMem / totalMem) * 100).toFixed(2)
    };

    // 4. 进程内存信息
    const processMemoryInfo = process.memoryUsage();

    // 5. 数据库状态检查
    let dbStatus = { status: 'healthy', message: '数据库连接正常' };
    try {
      const start = Date.now();
      await db.query('SELECT 1');
      const responseTime = Date.now() - start;
      dbStatus.responseTime = responseTime;
    } catch (error) {
      dbStatus = {
        status: 'error',
        message: '数据库连接失败',
        error: error.message
      };
    }

    // 6. 磁盘使用情况（简化版）
    let diskInfo = {};
    try {
      const stats = fs.statSync(process.cwd());
      diskInfo = {
        available: true,
        path: process.cwd()
      };
    } catch (error) {
      diskInfo = {
        available: false,
        error: error.message
      };
    }

    const responseTime = Date.now() - startTime;

    return handleResponse(ctx, 200, {
      message: "获取系统概览成功",
      data: {
        system: systemInfo,
        cpu: cpuInfo,
        memory: memoryInfo,
        processMemory: processMemoryInfo,
        database: dbStatus,
        disk: diskInfo,
        responseTime,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error("获取系统概览失败:", error);
    return handleResponse(ctx, 500, { message: "获取系统概览失败" });
  }
});

// 获取数据库统计信息
router.get("/database-stats", async (ctx) => {
  try {
    // 1. 数据库基本信息
    const dbVersion = await db.query('SELECT VERSION() as version');
    
    // 2. 表统计信息
    const tableStats = await db.query(`
      SELECT 
        TABLE_NAME as table_name,
        TABLE_ROWS as row_count,
        ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) as size_mb,
        ROUND((DATA_LENGTH / 1024 / 1024), 2) as data_mb,
        ROUND((INDEX_LENGTH / 1024 / 1024), 2) as index_mb,
        ENGINE as engine
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = DATABASE()
      ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC
    `);

    // 3. 数据库大小统计
    const dbSize = await db.query(`
      SELECT 
        ROUND(SUM(DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024, 2) as total_size_mb,
        ROUND(SUM(DATA_LENGTH) / 1024 / 1024, 2) as data_size_mb,
        ROUND(SUM(INDEX_LENGTH) / 1024 / 1024, 2) as index_size_mb,
        COUNT(*) as table_count
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = DATABASE()
    `);

    // 4. 连接状态
    const connectionStats = await db.query('SHOW STATUS LIKE "Threads_connected"');
    const maxConnections = await db.query('SHOW VARIABLES LIKE "max_connections"');

    return handleResponse(ctx, 200, {
      message: "获取数据库统计成功",
      data: {
        version: dbVersion[0]?.version || 'Unknown',
        tables: tableStats,
        summary: dbSize[0] || {},
        connections: {
          current: parseInt(connectionStats[0]?.Value || 0),
          max: parseInt(maxConnections[0]?.Value || 0)
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error("获取数据库统计失败:", error);
    return handleResponse(ctx, 500, { message: "获取数据库统计失败" });
  }
});

// 获取API性能统计
router.get("/api-stats", async (ctx) => {
  try {
    // 这里可以从日志文件或内存中获取API统计信息
    // 暂时返回模拟数据，实际项目中需要实现API监控中间件
    
    const apiStats = {
      totalRequests: 1250,
      successRequests: 1180,
      errorRequests: 70,
      averageResponseTime: 145,
      slowestEndpoint: '/user-level/users',
      fastestEndpoint: '/health',
      topEndpoints: [
        { path: '/user/login', count: 320, avgTime: 89 },
        { path: '/articles/list', count: 280, avgTime: 156 },
        { path: '/files/list', count: 210, avgTime: 134 },
        { path: '/user-level/users', count: 45, avgTime: 234 },
        { path: '/health', count: 395, avgTime: 12 }
      ],
      errorsByStatus: {
        '400': 15,
        '401': 25,
        '403': 8,
        '404': 12,
        '500': 10
      }
    };

    return handleResponse(ctx, 200, {
      message: "获取API统计成功",
      data: apiStats
    });

  } catch (error) {
    console.error("获取API统计失败:", error);
    return handleResponse(ctx, 500, { message: "获取API统计失败" });
  }
});

// 获取实时监控数据
router.get("/realtime", async (ctx) => {
  try {
    // 1. 当前CPU负载
    const loadAvg = os.loadavg();
    
    // 2. 当前内存使用
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;
    
    // 3. 进程内存
    const processMemory = process.memoryUsage();
    
    // 4. 数据库连接测试
    let dbResponseTime = 0;
    try {
      const start = Date.now();
      await db.query('SELECT 1');
      dbResponseTime = Date.now() - start;
    } catch (error) {
      dbResponseTime = -1;
    }

    // 5. 活跃用户统计（简化版）
    const activeUsers = await db.query(`
      SELECT COUNT(DISTINCT user_id) as count 
      FROM view_history 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
    `).catch(() => [{ count: 0 }]);

    return handleResponse(ctx, 200, {
      message: "获取实时监控数据成功",
      data: {
        cpu: {
          loadAvg: loadAvg,
          cores: os.cpus().length
        },
        memory: {
          total: totalMem,
          used: usedMem,
          free: freeMem,
          usagePercent: ((usedMem / totalMem) * 100).toFixed(2)
        },
        process: {
          heapUsed: processMemory.heapUsed,
          heapTotal: processMemory.heapTotal,
          heapUsagePercent: ((processMemory.heapUsed / processMemory.heapTotal) * 100).toFixed(2),
          uptime: process.uptime()
        },
        database: {
          responseTime: dbResponseTime,
          status: dbResponseTime >= 0 ? 'healthy' : 'error'
        },
        users: {
          activeInLast5Min: activeUsers[0]?.count || 0
        },
        timestamp: Date.now()
      }
    });

  } catch (error) {
    console.error("获取实时监控数据失败:", error);
    return handleResponse(ctx, 500, { message: "获取实时监控数据失败" });
  }
});

// 获取系统日志
router.get("/logs", async (ctx) => {
  try {
    const { level = 'all', limit = 100 } = ctx.query;
    
    // 这里应该从日志文件中读取，暂时返回模拟数据
    const logs = [
      {
        timestamp: new Date().toISOString(),
        level: 'info',
        message: '系统启动成功',
        module: 'system'
      },
      {
        timestamp: new Date(Date.now() - 60000).toISOString(),
        level: 'warn',
        message: '内存使用率较高',
        module: 'monitor'
      },
      {
        timestamp: new Date(Date.now() - 120000).toISOString(),
        level: 'error',
        message: '数据库连接超时',
        module: 'database'
      }
    ];

    const filteredLogs = level === 'all' ? logs : logs.filter(log => log.level === level);
    const limitedLogs = filteredLogs.slice(0, parseInt(limit));

    return handleResponse(ctx, 200, {
      message: "获取系统日志成功",
      data: limitedLogs
    });

  } catch (error) {
    console.error("获取系统日志失败:", error);
    return handleResponse(ctx, 500, { message: "获取系统日志失败" });
  }
});

module.exports = router;
