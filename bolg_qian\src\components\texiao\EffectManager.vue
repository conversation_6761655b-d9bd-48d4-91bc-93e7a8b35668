<template>
  <div class="effect-manager">
    <!-- 特效控制面板 -->
    <div class="effect-controls" v-if="showControls">
      <div class="controls-header">
        <h3>✨ 页面特效</h3>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            circle
            @click="showPanel = !showPanel"
          >
            <el-icon>
              <ArrowUp v-if="showPanel" />
              <ArrowDown v-else />
            </el-icon>
          </el-button>
          <el-button
            size="small"
            circle
            @click="closeControls"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
      
      <div class="controls-panel" v-show="showPanel">
        <div class="effect-options">
          <div class="option-group">
            <label>特效类型：</label>
            <el-select v-model="currentEffect" @change="changeEffect" size="small">
              <el-option label="无特效" value="none" />
              <el-option label="流星雨" value="meteor" />
              <el-option label="夜空星辰" value="nightsky" />
              <el-option label="粒子连线" value="particle" />
            </el-select>
          </div>

          <!-- 性能警告 -->
          <div class="performance-warning" v-if="showPerformanceWarning">
            <el-alert
              title="性能提醒"
              description="检测到当前特效可能影响页面性能，建议选择更轻量的特效"
              type="warning"
              size="small"
              :closable="false"
            />
          </div>
          
          <div class="option-group" v-if="currentEffect === 'meteor'">
            <label>流星数量：</label>
            <el-slider v-model="meteorConfig.count" :min="5" :max="50" size="small" />
          </div>
          
          <div class="option-group" v-if="currentEffect === 'meteor'">
            <label>流星速度：</label>
            <el-slider v-model="meteorConfig.speed" :min="1" :max="10" size="small" />
          </div>
          
          <div class="option-group">
            <el-button size="small" @click="resetToDefault">重置默认</el-button>
            <el-button size="small" type="success" @click="saveSettings">保存设置</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 特效渲染区域 -->
    <div class="effect-container">
      <!-- 流星雨特效 -->
      <Meteor 
        v-if="currentEffect === 'meteor'"
        :count="meteorConfig.count"
        :speed="meteorConfig.speed"
        :color-start="meteorConfig.colorStart"
        :color-end="meteorConfig.colorEnd"
      />
      
      <!-- 夜空特效 -->
      <NightSky v-if="currentEffect === 'nightsky'" />
      
      <!-- 粒子特效 -->
      <Particle v-if="currentEffect === 'particle'" />
    </div>

    <!-- 快速切换按钮 -->
    <div class="quick-toggle" v-if="!showControls">
      <el-button 
        type="primary" 
        circle 
        size="small"
        @click="toggleControls"
        class="toggle-btn"
        title="特效设置"
      >
        ✨
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Close, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import Meteor from './Meteor.vue'
import NightSky from './NightSky.vue'
import Particle from './Particle.vue'
// 简化版本，不依赖复杂的配置文件
const EFFECT_TYPES = {
  NONE: 'none',
  METEOR: 'meteor',
  NIGHTSKY: 'nightsky',
  PARTICLE: 'particle'
}

// 响应式数据
const currentEffect = ref(EFFECT_TYPES.NONE)
const showControls = ref(false)
const showPanel = ref(false)
const isFirstVisit = ref(true)

// 特效配置
const meteorConfig = ref({
  count: 25,
  speed: 4,
  colorStart: '#ffffff',
  colorEnd: '#00ffff'
})

// 性能监控
const showPerformanceWarning = ref(false)

// 方法
const toggleControls = () => {
  showControls.value = !showControls.value
  if (showControls.value) {
    showPanel.value = true
  }
}

const closeControls = () => {
  showPanel.value = false
  setTimeout(() => {
    showControls.value = false
  }, 300)
}

const changeEffect = (effect) => {
  if (effect === currentEffect.value) return

  // 更新特效
  currentEffect.value = effect
  localStorage.setItem('currentEffect', effect)

  // 更新配置
  if (effect === EFFECT_TYPES.METEOR) {
    localStorage.setItem('meteorConfig', JSON.stringify(meteorConfig.value))
  }

  // 显示消息
  const effectNames = {
    none: '无特效',
    meteor: '流星雨',
    nightsky: '夜空星辰',
    particle: '粒子连线'
  }

  if (effect !== 'none') {
    ElMessage.success(`已切换到${effectNames[effect]}特效`)

    // 延迟关闭控制面板
    setTimeout(() => {
      showPanel.value = false
      setTimeout(() => {
        showControls.value = false
      }, 300)
    }, 1500)
  } else {
    ElMessage.info('已关闭页面特效')
  }

}

const resetToDefault = () => {
  currentEffect.value = EFFECT_TYPES.NONE
  meteorConfig.value = {
    count: 25,
    speed: 4,
    colorStart: '#ffffff',
    colorEnd: '#00ffff'
  }
  localStorage.removeItem('currentEffect')
  localStorage.removeItem('meteorConfig')
  ElMessage.success('已重置为默认设置')
}

const saveSettings = () => {
  localStorage.setItem('meteorConfig', JSON.stringify(meteorConfig.value))
  ElMessage.success('设置已保存')
}

const loadSettings = () => {
  // 加载特效类型
  const savedEffect = localStorage.getItem('currentEffect')
  if (savedEffect) {
    currentEffect.value = savedEffect
  }

  // 加载流星配置
  const savedMeteorConfig = localStorage.getItem('meteorConfig')
  if (savedMeteorConfig) {
    try {
      meteorConfig.value = JSON.parse(savedMeteorConfig)
    } catch (e) {
      console.warn('加载流星配置失败:', e)
    }
  }

  // 检查是否首次访问
  const hasVisited = localStorage.getItem('effectManagerVisited')
  if (!hasVisited) {
    isFirstVisit.value = true
    localStorage.setItem('effectManagerVisited', 'true')
  } else {
    isFirstVisit.value = false
  }
}

// 监听特效变化
watch(currentEffect, (newEffect, oldEffect) => {
  // 如果特效发生了实际变化，自动隐藏控制面板
  if (newEffect !== oldEffect && newEffect !== EFFECT_TYPES.NONE) {
    setTimeout(() => {
      showPanel.value = false
      setTimeout(() => {
        showControls.value = false
      }, 500)
    }, 2000)
  }
})

// 监听流星配置变化
watch(meteorConfig, (newConfig) => {
  if (currentEffect.value === EFFECT_TYPES.METEOR) {
    localStorage.setItem('meteorConfig', JSON.stringify(newConfig))
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  loadSettings()

  // 延迟显示控制按钮（仅首次访问或无特效时）
  setTimeout(() => {
    if (currentEffect.value === EFFECT_TYPES.NONE || isFirstVisit.value) {
      showControls.value = true
      showPanel.value = true
    }
  }, 2000)
})
</script>

<style scoped>
.effect-manager {
  position: relative;
  z-index: 1;
}

/* 特效控制面板 */
.effect-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
  z-index: 9998;
  min-width: 280px;
  transition: all var(--transition-normal);
}

.controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.controls-header h3 {
  margin: 0;
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.controls-panel {
  padding: var(--spacing-md);
}

.effect-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.option-group label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.option-group .el-select {
  width: 100%;
}

/* 快速切换按钮 */
.quick-toggle {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 9998;
}

.toggle-btn {
  width: 50px;
  height: 50px;
  font-size: 20px;
  box-shadow: var(--shadow-lg);
  background: var(--primary-gradient);
  border: none;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

/* 特效容器 */
.effect-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .effect-controls {
    top: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
  }
  
  .quick-toggle {
    bottom: 20px;
    right: 20px;
  }
  
  .toggle-btn {
    width: 45px;
    height: 45px;
    font-size: 18px;
  }
}
</style>
