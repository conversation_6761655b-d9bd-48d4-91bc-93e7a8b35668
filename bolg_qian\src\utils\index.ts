import axios from "axios";

// 配置项 - 使用相对路径，通过vite代理访问
const axiosOption = {
  baseURL: "/api",
  withCredentials: true,
  timeout: 1000000, // 10秒超时
};

// 创建axios实例
const instance = axios.create(axiosOption);

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    // 对于Blob类型的响应，返回完整的response对象
    if (response.data instanceof Blob) {
      return response;
    }
    // 对于其他类型的响应，返回response.data
    return response.data;
  },
  (error) => {
    // 后端已经全局处理了401，这里不需要再处理
    return Promise.reject(error);
  }
);

/**
 * 格式化日期时间
 * @param {number|Date} timestamp - 时间戳或日期对象
 * @param {string} format - 输出格式
 * @returns {string} 格式化后的日期字符串
 * 
 * 支持的格式：
 * - YYYY: 四位年份
 * - MM: 两位月份
 * - DD: 两位日期
 * - HH: 两位小时（24小时制）
 * - mm: 两位分钟
 * - ss: 两位秒数
 * 
 * 示例:
 * formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss') => 2023-06-01 14:30:45
 * formatDate(Date.now(), 'HH:mm') => 14:30
 */
export function formatDate(timestamp: number | Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!timestamp) {
    return '';
  }
  
  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
  
  if (isNaN(date.getTime())) {
    console.error('Invalid date:', timestamp);
    return '';
  }
  
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();
  
  return format
    .replace('YYYY', year.toString())
    .replace('MM', month.toString().padStart(2, '0'))
    .replace('DD', day.toString().padStart(2, '0'))
    .replace('HH', hours.toString().padStart(2, '0'))
    .replace('mm', minutes.toString().padStart(2, '0'))
    .replace('ss', seconds.toString().padStart(2, '0'));
}

export default instance;
