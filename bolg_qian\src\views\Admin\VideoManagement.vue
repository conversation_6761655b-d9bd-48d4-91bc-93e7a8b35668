<template>
  <div class="video-management">
    <div class="header">
      <h2>视频管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="refreshList">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filters">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索视频标题或文件名"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.category" placeholder="选择分类" clearable @change="handleSearch">
            <el-option label="全部分类" value="" />
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.name"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable @change="handleSearch">
            <el-option label="全部状态" value="" />
            <el-option label="已上架" value="online" />
            <el-option label="已下架" value="offline" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.sortBy" placeholder="排序方式" @change="handleSearch">
            <el-option label="上传时间" value="upload_time" />
            <el-option label="文件大小" value="file_size" />
            <el-option label="播放次数" value="play_count" />
            <el-option label="喜欢数" value="like_count" />
            <el-option label="标题" value="title" />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="searchForm.sortOrder" placeholder="排序" @change="handleSearch">
            <el-option label="降序" value="desc" />
            <el-option label="升序" value="asc" />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 批量操作 -->
    <div class="batch-actions" v-if="selectedVideos.length > 0">
      <el-alert
        :title="`已选择 ${selectedVideos.length} 个视频`"
        type="info"
        show-icon
        :closable="false"
      />
      <div class="batch-buttons">
        <el-button type="success" @click="batchOnline">批量上架</el-button>
        <el-button type="warning" @click="batchOffline">批量下架</el-button>
        <el-button type="danger" @click="batchDelete">批量删除</el-button>
      </div>
    </div>

    <!-- 视频列表 -->
    <div class="video-list">
      <el-table
        :data="videoList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        row-key="file_name"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="封面" width="120">
          <template #default="{ row }">
            <div class="video-cover">
              <img :src="row.cover_url" :alt="row.title" @error="handleImageError" />
              <div class="video-duration" v-if="row.duration_formatted">
                {{ row.duration_formatted }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="视频信息" min-width="300">
          <template #default="{ row }">
            <div class="video-info">
              <div class="video-title">{{ row.title || row.file_name }}</div>
              <div class="video-meta">
                <el-tag size="small" type="info">{{ row.category }}</el-tag>
                <span class="file-size">{{ row.file_size_mb }}MB</span>
                <span class="upload-time">{{ row.upload_time_formatted }}</span>
              </div>
              <div class="video-tags" v-if="row.tags && row.tags.length > 0">
                <el-tag
                  v-for="tag in row.tags"
                  :key="tag"
                  size="small"
                  class="tag-item"
                >
                  {{ tag }}
                </el-tag>
              </div>
              <div class="video-description" v-if="row.description">
                {{ row.description }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="统计" width="120">
          <template #default="{ row }">
            <div class="video-stats">
              <div class="stat-item">
                <el-icon><VideoPlay /></el-icon>
                {{ row.play_count || 0 }}
              </div>
              <div class="stat-item">
                <el-icon><Star /></el-icon>
                {{ row.like_count || 0 }}
              </div>
              <div class="stat-item">
                <el-icon><Collection /></el-icon>
                {{ row.collect_count || 0 }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'online' ? 'success' : 'danger'">
              {{ row.status === 'online' ? '已上架' : '已下架' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button size="small" @click="editVideo(row)">编辑</el-button>
              <el-button
                size="small"
                :type="row.status === 'online' ? 'warning' : 'success'"
                @click="toggleVideoStatus(row)"
              >
                {{ row.status === 'online' ? '下架' : '上架' }}
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="deleteVideo(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[12, 24, 48, 96]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 编辑视频对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑视频信息"
      width="600px"
      @close="resetEditForm"
    >
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="标题">
          <el-input v-model="editForm.title" placeholder="请输入视频标题" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入视频描述"
          />
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="editForm.category" placeholder="选择分类">
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签">
          <el-select
            v-model="editForm.tags"
            multiple
            filterable
            allow-create
            placeholder="选择或输入标签"
          >
            <el-option
              v-for="tag in tags"
              :key="tag.id"
              :label="tag.name"
              :value="tag.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="editForm.status">
            <el-radio value="online">上架</el-radio>
            <el-radio value="offline">下架</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveVideoInfo" :loading="saveLoading">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Search,
  VideoPlay,
  Star,
  Collection
} from '@element-plus/icons-vue'
import {
  getVideoManagementListApi,
  getVideoCategoriesApi,
  getVideoTagsApi,
  updateVideoInfoApi,
  onlineVideoApi,
  offlineVideoApi,
  batchVideoStatusApi,
  deleteVideoApi
} from '@/utils/api'

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const videoList = ref<any[]>([])
const categories = ref<any[]>([])
const tags = ref<any[]>([])
const selectedVideos = ref<any[]>([])
const editDialogVisible = ref(false)

// 搜索表单
const searchForm = reactive({
  search: '',
  category: '',
  status: '',
  sortBy: 'upload_time',
  sortOrder: 'desc'
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 12,
  total: 0
})

// 编辑表单
const editForm = reactive({
  fileName: '',
  title: '',
  description: '',
  category: '',
  tags: [] as string[],
  status: 'online'
})

// 获取视频列表
const fetchVideoList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    const res = await getVideoManagementListApi(params)
    console.log('视频列表:', res)
    if (res.code === 200) {
      // API直接返回list和total，不是嵌套在data中
      videoList.value = res.list || []
      pagination.total = res.total || 0
      console.log('设置视频列表:', videoList.value.length, '个视频')
      console.log('设置总数:', pagination.total)
    }
  } catch (error) {
    console.error('获取视频列表失败:', error)
    ElMessage.error('获取视频列表失败')
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const res = await getVideoCategoriesApi()
    console.log('分类列表:', res)
    if (res.code === 200) {
      // 处理对象格式的响应
      const categoriesData = res.data
      const categoriesList = []
      for (let key in categoriesData) {
        if (key !== 'code' && key !== 'message' && categoriesData[key].name) {
          categoriesList.push(categoriesData[key])
        }
      }
      categories.value = categoriesList
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 获取标签列表
const fetchTags = async () => {
  try {
    const res = await getVideoTagsApi()
    console.log('标签列表:', res)
    if (res.code === 200) {
      // 处理对象格式的响应
      const tagsData = res.data
      const tagsList = []
      for (let key in tagsData) {
        if (key !== 'code' && key !== 'message' && tagsData[key].name) {
          tagsList.push(tagsData[key])
        }
      }
      tags.value = tagsList
    }
  } catch (error) {
    console.error('获取标签列表失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchVideoList()
}

// 刷新列表
const refreshList = () => {
  fetchVideoList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchVideoList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchVideoList()
}

// 选择处理
const handleSelectionChange = (selection: any[]) => {
  selectedVideos.value = selection
}

// 编辑视频
const editVideo = (video: any) => {
  editForm.fileName = video.file_name
  editForm.title = video.title || ''
  editForm.description = video.description || ''
  editForm.category = video.category || ''
  editForm.tags = video.tags || []
  editForm.status = video.status
  editDialogVisible.value = true
}

// 保存视频信息
const saveVideoInfo = async () => {
  saveLoading.value = true
  try {
    const { fileName, ...updateData } = editForm
    await updateVideoInfoApi(fileName, updateData)
    ElMessage.success('视频信息更新成功')
    editDialogVisible.value = false
    fetchVideoList()
  } catch (error) {
    console.error('更新视频信息失败:', error)
    ElMessage.error('更新视频信息失败')
  } finally {
    saveLoading.value = false
  }
}

// 重置编辑表单
const resetEditForm = () => {
  Object.assign(editForm, {
    fileName: '',
    title: '',
    description: '',
    category: '',
    tags: [],
    status: 'online'
  })
}

// 切换视频状态
const toggleVideoStatus = async (video: any) => {
  try {
    if (video.status === 'online') {
      await offlineVideoApi(video.file_name)
      ElMessage.success('视频下架成功')
    } else {
      await onlineVideoApi(video.file_name)
      ElMessage.success('视频上架成功')
    }
    fetchVideoList()
  } catch (error) {
    console.error('切换视频状态失败:', error)
    ElMessage.error('操作失败')
  }
}

// 删除视频
const deleteVideo = async (video: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除视频 "${video.title || video.file_name}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteVideoApi(video.file_name)
    ElMessage.success('视频删除成功')
    fetchVideoList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除视频失败:', error)
      ElMessage.error('删除视频失败')
    }
  }
}

// 批量上架
const batchOnline = async () => {
  try {
    const fileNames = selectedVideos.value.map(v => v.file_name)
    await batchVideoStatusApi({ fileNames, status: 'online' })
    ElMessage.success('批量上架成功')
    fetchVideoList()
  } catch (error) {
    console.error('批量上架失败:', error)
    ElMessage.error('批量上架失败')
  }
}

// 批量下架
const batchOffline = async () => {
  try {
    const fileNames = selectedVideos.value.map(v => v.file_name)
    await batchVideoStatusApi({ fileNames, status: 'offline' })
    ElMessage.success('批量下架成功')
    fetchVideoList()
  } catch (error) {
    console.error('批量下架失败:', error)
    ElMessage.error('批量下架失败')
  }
}

// 批量删除
const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedVideos.value.length} 个视频吗？此操作不可恢复！`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 逐个删除（因为后端没有批量删除接口）
    for (const video of selectedVideos.value) {
      await deleteVideoApi(video.file_name)
    }
    
    ElMessage.success('批量删除成功')
    fetchVideoList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/default-cover.jpg' // 默认封面图片
}

// 初始化
onMounted(() => {
  fetchVideoList()
  fetchCategories()
  fetchTags()
})
</script>

<style scoped>
.video-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.filters {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.batch-actions {
  margin-bottom: 20px;
  padding: 15px;
  background: #e6f7ff;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-buttons {
  display: flex;
  gap: 10px;
}

.video-cover {
  position: relative;
  width: 80px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
}

.video-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-duration {
  position: absolute;
  bottom: 2px;
  right: 2px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 1px 4px;
  font-size: 10px;
  border-radius: 2px;
}

.video-info {
  padding: 5px 0;
}

.video-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #909399;
}

.video-tags {
  margin-bottom: 8px;
}

.tag-item {
  margin-right: 5px;
  margin-bottom: 3px;
}

.video-description {
  font-size: 12px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 250px;
}

.video-stats {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #606266;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.action-buttons .el-button {
  width: 100%;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-dialog .el-form {
  padding: 0 20px;
}
</style>
