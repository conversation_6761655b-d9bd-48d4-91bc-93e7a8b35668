<template>
  <div class="comment-item">
    <el-avatar :size="40" :src="getAvatarUrl(comment.avatar)" />
    <div class="comment-content">
      <div class="comment-meta">
        <span class="comment-author">{{ comment.author || comment.username }}</span>
        <span class="comment-time">{{ formatDate(comment.created_at) }}</span>
        <el-tag v-if="comment.parent_id" size="small" type="info" class="reply-tag">
          回复
        </el-tag>
      </div>
      <div class="comment-text">{{ comment.content }}</div>
      <div class="comment-actions">
        <el-button
          size="small"
          type="text"
          @click="showReplyInput(comment.id, comment.author || comment.username)"
          :disabled="replyToId === comment.id"
        >
          <el-icon><ChatDotRound /></el-icon>
          {{ replyToId === comment.id ? '回复中...' : '回复' }}
        </el-button>
        <span class="comment-id">#{{ comment.id }}</span>
      </div>

      <!-- 回复表单 -->
      <div v-if="replyToId === comment.id" class="reply-form">
        <el-input
          :model-value="replyContent"
          type="textarea"
          :rows="3"
          :placeholder="`回复 @${replyToAuthor}...`"
          @update:model-value="val => $emit('update:replyContent', val)"
          maxlength="500"
          show-word-limit
        />
        <div class="form-actions">
          <el-button type="primary" size="small" @click="submitReply(comment.id)">
            <el-icon><Promotion /></el-icon>
            发送回复
          </el-button>
          <el-button size="small" @click="cancelReply">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
        </div>
      </div>

      <!-- 递归渲染子评论 -->
      <div v-if="comment.children && comment.children.length > 0" class="comment-children">
        <div class="children-header">
          <el-divider content-position="left">
            <span class="children-count">{{ comment.children.length }} 条回复</span>
          </el-divider>
        </div>
        <CommentItem
          v-for="child in comment.children"
          :key="`child-${child.id}`"
          :comment="child"
          :get-avatar-url="getAvatarUrl"
          :format-date="formatDate"
          :reply-to-id="replyToId"
          :reply-to-author="replyToAuthor"
          :reply-content="replyContent"
          @update:replyContent="$emit('update:replyContent', $event)"
          :show-reply-input="showReplyInput"
          :submit-reply="submitReply"
          :cancel-reply="cancelReply"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ChatDotRound, Promotion, Close } from '@element-plus/icons-vue';

defineProps([
  "comment",
  "getAvatarUrl",
  "formatDate",
  "replyToId",
  "replyToAuthor",
  "replyContent",
  "showReplyInput",
  "submitReply",
  "cancelReply"
]);
defineEmits(['update:replyContent']);

// 递归组件需要显式声明名称
defineOptions({
  name: 'CommentItem'
});
</script>

<style scoped lang="less">
.comment-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  min-height: 48px;
  position: relative;

  .el-avatar {
    margin-right: 12px;
    flex-shrink: 0;
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      transform: scale(1.05);
    }
  }

  .comment-content {
    flex: 1;
    background: #fafbfc;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      border-color: #e0e0e0;
    }

    .comment-meta {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 13px;
      color: #888;
      gap: 10px;

      .comment-author {
        font-weight: 600;
        color: #333;
        font-size: 14px;
      }

      .comment-time {
        font-size: 12px;
        color: #999;
      }

      .reply-tag {
        margin-left: auto;
      }
    }

    .comment-text {
      font-size: 15px;
      color: #333;
      margin-bottom: 12px;
      line-height: 1.6;
      word-break: break-word;
      white-space: pre-wrap;
    }

    .comment-actions {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .el-button {
        font-size: 13px;
        padding: 4px 8px;

        &[type="text"] {
          color: #409eff;

          &:hover {
            background: #ecf5ff;
          }

          &:disabled {
            color: #c0c4cc;
          }
        }
      }

      .comment-id {
        font-size: 11px;
        color: #c0c4cc;
        font-family: monospace;
      }
    }

    .reply-form {
      margin-top: 12px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 6px;
      padding: 12px;
      border: 1px solid #e9ecef;
      animation: slideDown 0.3s ease;

      .form-actions {
        margin-top: 10px;
        display: flex;
        gap: 10px;
        justify-content: flex-end;
      }
    }

    .comment-children {
      margin-top: 16px;

      .children-header {
        margin-bottom: 12px;

        .children-count {
          font-size: 12px;
          color: #666;
          font-weight: 500;
        }
      }

      .comment-item {
        margin-left: 20px;
        padding-left: 16px;
        border-left: 3px solid #e9ecef;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: -3px;
          top: 0;
          width: 3px;
          height: 100%;
          background: linear-gradient(to bottom, #409eff, #67c23a);
          border-radius: 2px;
          opacity: 0.6;
        }

        .comment-content {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
        }
      }
    }
  }
}

// 动画效果
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .comment-item {
    .comment-content {
      padding: 12px;

      .comment-children .comment-item {
        margin-left: 10px;
        padding-left: 10px;
      }
    }
  }
}
</style>