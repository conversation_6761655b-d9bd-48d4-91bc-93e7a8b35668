<template>
  <div class="upload-container">
    <!-- 通道选择器 -->
    <div class="channel-selector">
      <el-radio-group v-model="selectedChannel" class="channel-group">
        <el-radio-button label="document" class="channel-btn document-btn">
          <el-icon><Document /></el-icon>
          <span>文档通道</span>
          <div class="channel-desc">支持文档、图片、压缩包等文件</div>
        </el-radio-button>
        <el-radio-button label="media" class="channel-btn media-btn">
          <el-icon><VideoPlay /></el-icon>
          <span>媒体通道</span>
          <div class="channel-desc">专门用于视频、音频等媒体文件</div>
        </el-radio-button>
      </el-radio-group>
    </div>

    <!-- 文档上传区域 -->
    <el-card v-show="selectedChannel === 'document'" class="upload-card document-card">
      <template #header>
        <div class="card-header">
          <el-icon><Document /></el-icon>
          <span>文档文件上传</span>
          <el-tag type="info" size="small">支持: PDF, DOC, XLS, PPT, TXT, 图片, 压缩包等</el-tag>
        </div>
      </template>

      <el-upload 
        :auto-upload="false" 
        :show-file-list="false" 
        :on-change="onDocumentChange" 
        multiple 
        class="upload-area"
        :accept="documentAccept"
        drag>
        <el-icon class="upload-icon"><Upload /></el-icon>
        <div class="upload-text">
          <p>将文档文件拖拽到此处，或<em>点击选择文件</em></p>
          <p class="upload-tip">支持多文件上传，单文件最大 100MB</p>
        </div>
      </el-upload>
    </el-card>

    <!-- 媒体上传区域 -->
    <el-card v-show="selectedChannel === 'media'" class="upload-card media-card">
      <template #header>
        <div class="card-header">
          <el-icon><VideoPlay /></el-icon>
          <span>媒体文件上传</span>
          <el-tag type="success" size="small">支持: MP4, AVI, MOV, MP3, WAV, FLAC等</el-tag>
        </div>
      </template>

      <el-upload 
        :auto-upload="false" 
        :show-file-list="false" 
        :on-change="onMediaChange" 
        multiple 
        class="upload-area"
        :accept="mediaAccept"
        drag>
        <el-icon class="upload-icon"><VideoPlay /></el-icon>
        <div class="upload-text">
          <p>将媒体文件拖拽到此处，或<em>点击选择文件</em></p>
          <p class="upload-tip">支持大文件上传，自动分片处理</p>
        </div>
      </el-upload>
    </el-card>

    <!-- 文件列表 -->
    <div v-if="allFileList.length" class="file-list-container">
      <div class="list-header">
        <h3>待上传文件 ({{ allFileList.length }})</h3>
        <div class="list-actions">
          <el-button size="small" @click="clearAllFiles" :disabled="uploading">
            <el-icon><Delete /></el-icon>
            清空列表
          </el-button>
          <el-button type="primary" size="small" :loading="uploading" @click="handleUpload" :disabled="!allFileList.length">
            <el-icon><Upload /></el-icon>
            开始上传 ({{ allFileList.length }})
          </el-button>
        </div>
      </div>

      <div class="file-list">
        <transition-group name="fade" tag="div">
          <div class="file-item" v-for="(item, idx) in allFileList" :key="item.id">
            <div class="file-icon">
              <el-icon v-if="item.channel === 'document'">
                <Document />
              </el-icon>
              <el-icon v-else>
                <VideoPlay />
              </el-icon>
            </div>
            
            <div class="file-content">
              <div class="file-info">
                <div class="file-name">{{ item.name }}</div>
                <div class="file-meta">
                  <el-tag :type="item.channel === 'document' ? 'info' : 'success'" size="small">
                    {{ item.channel === 'document' ? '文档' : '媒体' }}
                  </el-tag>
                  <span class="file-size">{{ formatFileSize(item.file.size) }}</span>
                  <span class="file-type">{{ getFileExtension(item.name) }}</span>
                </div>
              </div>
              
              <!-- 图片预览 -->
              <div v-if="item.previewUrl" class="image-preview">
                <img :src="item.previewUrl" alt="预览" />
              </div>
              
              <!-- 上传进度 -->
              <div v-if="item.percent !== undefined" class="progress-container">
                <el-progress 
                  :percentage="item.percent" 
                  :status="getProgressStatus(item)" 
                  :stroke-width="6"
                  :show-text="true"
                />
                <div class="progress-info">
                  <span v-if="item.speed">{{ item.speed }}</span>
                  <span v-if="item.status === 'uploading'">上传中...</span>
                  <span v-else-if="item.status === 'success'" class="success-text">上传成功</span>
                  <span v-else-if="item.status === 'error'" class="error-text">上传失败</span>
                </div>
              </div>
            </div>
            
            <div class="file-actions">
              <el-button 
                type="danger" 
                size="small" 
                circle 
                @click="removeFile(idx)" 
                :disabled="uploading && item.status === 'uploading'">
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
          </div>
        </transition-group>
      </div>
    </div>

    <!-- 上传统计 -->
    <div v-if="uploadStats.total > 0" class="upload-stats">
      <el-card>
        <div class="stats-content">
          <div class="stat-item">
            <span class="label">总文件:</span>
            <span class="value">{{ uploadStats.total }}</span>
          </div>
          <div class="stat-item">
            <span class="label">已完成:</span>
            <span class="value success">{{ uploadStats.completed }}</span>
          </div>
          <div class="stat-item">
            <span class="label">失败:</span>
            <span class="value error">{{ uploadStats.failed }}</span>
          </div>
          <div class="stat-item">
            <span class="label">总大小:</span>
            <span class="value">{{ formatFileSize(uploadStats.totalSize) }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 上传结果 -->
    <div v-if="uploadResults.length" class="upload-results">
      <el-card>
        <template #header>
          <div class="results-header">
            <h3>上传结果</h3>
            <el-button size="small" @click="clearResults">清空结果</el-button>
          </div>
        </template>
        <div class="results-list">
          <div v-for="(result, idx) in uploadResults" :key="idx" class="result-item">
            <el-icon class="result-icon" :class="result.success ? 'success' : 'error'">
              <Check v-if="result.success" />
              <Close v-else />
            </el-icon>
            <div class="result-content">
              <div class="result-name">{{ result.fileName }}</div>
              <div class="result-info">
                <el-tag :type="result.success ? 'success' : 'danger'" size="small">
                  {{ result.success ? '成功' : '失败' }}
                </el-tag>
                <span v-if="result.success" class="result-url">
                  <a :href="result.filePath" target="_blank">{{ result.filePath }}</a>
                </span>
                <span v-else class="result-error">{{ result.error }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { 
  Document, 
  VideoPlay, 
  Upload, 
  Delete, 
  Close, 
  Check 
} from '@element-plus/icons-vue'
import { UploadApi, UploadChunkApi, MergeChunksApi } from "../../utils/api";

// 状态变量
const selectedChannel = ref('document');
const documentFiles = ref([]);
const mediaFiles = ref([]);
const uploading = ref(false);
const uploadResults = ref([]);
const user_id = localStorage.getItem("id");
const username = localStorage.getItem("username");

// 文件类型配置
const documentAccept = '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.rtf,.zip,.rar,.7z,.tar,.gz,.jpg,.jpeg,.png,.gif,.bmp,.webp,.svg';
const mediaAccept = '.mp4,.avi,.mov,.wmv,.flv,.mkv,.webm,.m4v,.3gp,.mp3,.wav,.flac,.aac,.ogg,.wma,.m4a';

// 合并所有文件列表
const allFileList = computed(() => {
  return [...documentFiles.value, ...mediaFiles.value].sort((a, b) => a.addTime - b.addTime);
});

// 上传统计
const uploadStats = computed(() => {
  const total = allFileList.value.length;
  const completed = allFileList.value.filter(f => f.status === 'success').length;
  const failed = allFileList.value.filter(f => f.status === 'error').length;
  const totalSize = allFileList.value.reduce((sum, f) => sum + f.file.size, 0);
  
  return { total, completed, failed, totalSize };
});

// 工具函数
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const getFileExtension = (filename) => {
  return filename.split('.').pop().toUpperCase();
};

const getProgressStatus = (item) => {
  if (item.status === 'success') return 'success';
  if (item.status === 'error') return 'exception';
  return undefined;
};

const generateFileId = () => {
  return Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

// 文档文件处理
const onDocumentChange = (uploadFile) => {
  addFilesToList(uploadFile, 'document', documentFiles);
};

// 媒体文件处理
const onMediaChange = (uploadFile) => {
  addFilesToList(uploadFile, 'media', mediaFiles);
};

// 通用文件添加函数
const addFilesToList = (uploadFile, channel, targetList) => {
  let files = [];

  if (Array.isArray(uploadFile)) {
    files = uploadFile.map(f => f.raw);
  } else if (uploadFile.raw) {
    files = [uploadFile.raw];
  }

  for (const file of files) {
    // 检查文件是否已存在
    const exists = allFileList.value.some(
      (item) => item.name === file.name && item.file.size === file.size
    );

    if (!exists) {
      const fileItem = {
        id: generateFileId(),
        file,
        name: file.name,
        channel,
        addTime: Date.now(),
        previewUrl: file.type.startsWith("image/") ? URL.createObjectURL(file) : "",
        percent: undefined,
        status: 'pending',
        speed: ''
      };

      targetList.value.push(fileItem);
      ElMessage.success(`已添加文件: ${file.name}`);
    } else {
      ElMessage.warning(`文件 ${file.name} 已存在`);
    }
  }
};

// 删除文件
const removeFile = (index) => {
  const item = allFileList.value[index];
  if (item.previewUrl) {
    URL.revokeObjectURL(item.previewUrl);
  }

  // 从对应的列表中删除
  if (item.channel === 'document') {
    const docIndex = documentFiles.value.findIndex(f => f.id === item.id);
    if (docIndex > -1) documentFiles.value.splice(docIndex, 1);
  } else {
    const mediaIndex = mediaFiles.value.findIndex(f => f.id === item.id);
    if (mediaIndex > -1) mediaFiles.value.splice(mediaIndex, 1);
  }
};

// 清空所有文件
const clearAllFiles = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有文件吗？', '确认操作', {
      type: 'warning'
    });

    // 清理预览URL
    allFileList.value.forEach(item => {
      if (item.previewUrl) {
        URL.revokeObjectURL(item.previewUrl);
      }
    });

    documentFiles.value = [];
    mediaFiles.value = [];
    ElMessage.success('已清空文件列表');
  } catch {
    // 用户取消
  }
};

// 清空结果
const clearResults = () => {
  uploadResults.value = [];
};

// 主上传逻辑
const handleUpload = async () => {
  if (!allFileList.value.length) {
    ElMessage.warning("请先选择文件");
    return;
  }

  uploading.value = true;
  uploadResults.value = [];

  // 重置所有文件状态
  allFileList.value.forEach(item => {
    item.percent = 0;
    item.status = 'pending';
    item.speed = '';
  });

  ElMessage.info(`开始上传 ${allFileList.value.length} 个文件...`);

  // 并发上传文件（限制并发数）
  const concurrentLimit = 3;
  const uploadQueue = [...allFileList.value];
  const activeUploads = [];

  while (uploadQueue.length > 0 || activeUploads.length > 0) {
    // 启动新的上传任务
    while (activeUploads.length < concurrentLimit && uploadQueue.length > 0) {
      const fileItem = uploadQueue.shift();
      const uploadPromise = uploadSingleFile(fileItem);
      activeUploads.push(uploadPromise);
    }

    // 等待任意一个上传完成
    if (activeUploads.length > 0) {
      const completedIndex = await Promise.race(
        activeUploads.map((promise, index) =>
          promise.then(() => index).catch(() => index)
        )
      );
      activeUploads.splice(completedIndex, 1);
    }
  }

  uploading.value = false;

  const stats = uploadStats.value;
  if (stats.failed === 0) {
    ElMessage.success(`全部上传完成！成功 ${stats.completed} 个文件`);
  } else {
    ElMessage.warning(`上传完成！成功 ${stats.completed} 个，失败 ${stats.failed} 个`);
  }
};

// 单文件上传
const uploadSingleFile = async (fileItem) => {
  try {
    fileItem.status = 'uploading';
    const startTime = Date.now();

    // 根据文件大小选择上传方式
    if (fileItem.file.size < 10 * 1024 * 1024) { // 10MB以下普通上传
      await normalUpload(fileItem, startTime);
    } else { // 10MB以上分片上传
      await chunkUpload(fileItem, startTime);
    }

    fileItem.status = 'success';
    fileItem.percent = 100;

  } catch (error) {
    console.error(`文件 ${fileItem.name} 上传失败:`, error);
    fileItem.status = 'error';
    fileItem.percent = 0;

    uploadResults.value.push({
      fileName: fileItem.name,
      success: false,
      error: error.message || '上传失败'
    });
  }
};

// 普通上传
const normalUpload = async (fileItem, startTime) => {
  const formData = new FormData();
  formData.append("file", fileItem.file);
  formData.append("user_id", user_id);
  formData.append("username", username);
  formData.append("channel", fileItem.channel); // 添加通道参数

  const res = await UploadApi(formData, {
    onUploadProgress: (progressEvent) => {
      if (progressEvent.total) {
        fileItem.percent = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        );

        // 计算上传速度
        const elapsed = (Date.now() - startTime) / 1000;
        const uploaded = (progressEvent.loaded / 1024 / 1024);
        const speed = (uploaded / elapsed).toFixed(2);
        fileItem.speed = `${speed} MB/s`;
      }
    },
  });

  // 处理上传结果
  if (res && res.files && res.files.length > 0) {
    const uploadedFile = res.files[0];
    uploadResults.value.push({
      fileName: fileItem.name,
      success: true,
      filePath: uploadedFile.filePath,
      category: uploadedFile.category,
      channel: fileItem.channel
    });
  } else {
    throw new Error('上传响应格式错误');
  }
};

// 分片上传
const chunkUpload = async (fileItem, startTime) => {
  const chunkSize = 2 * 1024 * 1024; // 2MB per chunk
  const chunks = createChunks(fileItem.file, chunkSize);
  const fileHash = getFileHash(fileItem.file);
  const totalChunks = chunks.length;

  // 上传所有分片
  for (let i = 0; i < totalChunks; i++) {
    const form = new FormData();
    form.append("chunk", chunks[i]);
    form.append("chunkIndex", i);
    form.append("fileHash", fileHash);
    form.append("fileName", fileItem.file.name);
    form.append("user_id", user_id);
    form.append("totalChunks", totalChunks);
    form.append("channel", fileItem.channel); // 添加通道参数

    await UploadChunkApi(form);

    // 更新进度
    const progress = Math.round(((i + 1) / totalChunks) * 90); // 分片上传占90%
    fileItem.percent = progress;

    // 计算上传速度
    const elapsed = (Date.now() - startTime) / 1000;
    const uploaded = ((i + 1) * chunkSize / 1024 / 1024);
    const speed = (uploaded / elapsed).toFixed(2);
    fileItem.speed = `${speed} MB/s`;
  }

  // 合并分片
  fileItem.speed = '合并文件中...';
  const mergeRes = await MergeChunksApi({
    fileHash,
    fileName: fileItem.file.name,
    user_id,
    totalChunks,
    fileType: fileItem.file.type,
    fileSize: fileItem.file.size,
    channel: fileItem.channel // 添加通道参数
  });

  // 处理合并结果
  if (mergeRes && mergeRes.filePath) {
    uploadResults.value.push({
      fileName: fileItem.name,
      success: true,
      filePath: mergeRes.filePath,
      category: mergeRes.category || fileItem.channel,
      channel: fileItem.channel
    });
  } else {
    throw new Error('文件合并失败');
  }
};

// 创建文件分片
const createChunks = (file, chunkSize) => {
  const chunks = [];
  let start = 0;
  while (start < file.size) {
    const end = Math.min(start + chunkSize, file.size);
    chunks.push(file.slice(start, end));
    start = end;
  }
  return chunks;
};

// 生成文件哈希
const getFileHash = (file) => {
  return `${file.name}_${file.size}_${file.lastModified}`;
};
</script>

<style scoped>
.upload-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 通道选择器 */
.channel-selector {
  margin-bottom: 30px;
  text-align: center;
}

.channel-group {
  display: inline-flex;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.channel-btn {
  padding: 20px 30px !important;
  border: none !important;
  background: white !important;
  color: #666 !important;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 200px;
}

.channel-btn:hover {
  background: #f8f9fa !important;
  transform: translateY(-2px);
}

.channel-btn.is-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.document-btn.is-active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

.media-btn.is-active {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
}

.channel-desc {
  font-size: 12px;
  opacity: 0.8;
  margin-top: 4px;
}

/* 上传卡片 */
.upload-card {
  margin-bottom: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.document-card {
  border-left: 4px solid #4facfe;
}

.media-card {
  border-left: 4px solid #43e97b;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  font-size: 16px;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  transition: all 0.3s ease;
  background: #fafafa;
}

.upload-area:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 8px 0;
  color: #666;
}

.upload-text em {
  color: #409eff;
  font-style: normal;
}

.upload-tip {
  font-size: 12px;
  color: #999;
}

/* 文件列表 */
.file-list-container {
  margin: 30px 0;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 10px;
}

.list-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.list-actions {
  display: flex;
  gap: 12px;
}

.file-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.file-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:hover {
  background: #f8f9fa;
}

.file-icon {
  margin-right: 16px;
  font-size: 24px;
  color: #409eff;
  min-width: 24px;
}

.file-content {
  flex: 1;
  min-width: 0;
}

.file-info {
  margin-bottom: 12px;
}

.file-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  word-break: break-all;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

.file-size, .file-type {
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 4px;
}

.image-preview {
  margin-top: 12px;
}

.image-preview img {
  max-width: 80px;
  max-height: 80px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  object-fit: cover;
}

.progress-container {
  margin-top: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
}

.success-text {
  color: #67c23a;
  font-weight: 600;
}

.error-text {
  color: #f56c6c;
  font-weight: 600;
}

.file-actions {
  margin-left: 16px;
}

/* 上传统计 */
.upload-stats {
  margin: 30px 0;
}

.stats-content {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 20px;
}

.stat-item {
  text-align: center;
}

.stat-item .label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-item .value {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.stat-item .value.success {
  color: #67c23a;
}

.stat-item .value.error {
  color: #f56c6c;
}

/* 上传结果 */
.upload-results {
  margin-top: 30px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-header h3 {
  margin: 0;
  font-size: 16px;
}

.results-list {
  max-height: 300px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-icon {
  margin-right: 12px;
  font-size: 16px;
}

.result-icon.success {
  color: #67c23a;
}

.result-icon.error {
  color: #f56c6c;
}

.result-content {
  flex: 1;
}

.result-name {
  font-weight: 600;
  margin-bottom: 4px;
  color: #333;
}

.result-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.result-url a {
  color: #409eff;
  text-decoration: none;
}

.result-url a:hover {
  text-decoration: underline;
}

.result-error {
  color: #f56c6c;
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-container {
    padding: 15px;
  }

  .channel-group {
    flex-direction: column;
  }

  .channel-btn {
    min-width: auto;
    padding: 15px 20px !important;
  }

  .list-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .list-actions {
    justify-content: center;
  }

  .file-item {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .file-actions {
    margin-left: 0;
    text-align: center;
  }

  .stats-content {
    flex-wrap: wrap;
    gap: 20px;
  }
}
</style>
