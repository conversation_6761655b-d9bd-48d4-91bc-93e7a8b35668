const db = require("../db");

// 获取仪表板基础数据统计
async function getDashboardData() {
  const userCount = await db.query("SELECT COUNT(*) AS count FROM users");
  const articleCount = await db.query("SELECT COUNT(*) AS count FROM articles");
  const shareCount = await db.query("SELECT COUNT(*) AS count FROM files WHERE is_share = 1");
  const resourceCount = await db.query("SELECT COUNT(*) AS count FROM files");
  const downloadCount = await db.query("SELECT COUNT(*) AS count FROM download_logs");
  
  // 添加总浏览量统计
  const totalViews = await db.query(
    "SELECT COALESCE(SUM(views), 0) AS total_views FROM articles WHERE is_deleted = 0"
  );

  return {
    userCount: userCount[0]?.count || 0,
    articleCount: articleCount[0]?.count || 0,
    shareCount: shareCount[0]?.count || 0,
    resourceCount: resourceCount[0]?.count || 0,
    downloadCount: downloadCount[0]?.count || 0,
    totalViews: totalViews[0]?.total_views || 0,
  };
}

// 获取用户增长趋势
async function getUserGrowthTrend() {
  const result = await db.query(
    "SELECT DATE_FORMAT(created_at, '%Y-%m') AS date, COUNT(*) AS count FROM users GROUP BY date"
  );
  return result.map((item) => ({
    date: item.date,
    count: item.count,
  }));
}

// 获取文章发布趋势
async function getArticlePublishTrend() {
  const result = await db.query(
    "SELECT DATE_FORMAT(created_at, '%Y-%m') AS date, COUNT(*) AS count FROM articles GROUP BY date"
  );
  return result.map((item) => ({
    date: item.date,
    count: item.count,
  }));
}

// 获取资源下载量统计
async function getDownloadTrend() {
  const result = await db.query(
    "SELECT DATE_FORMAT(download_time, '%Y-%m') AS date, file_name, file_type, COUNT(*) AS count FROM download_logs GROUP BY date, file_name, file_type"
  );
  return result.map((item) => ({
    date: item.date,
    count: item.count,
    file_name: item.file_name,
    file_type: item.file_type,
  }));
}

module.exports = {
  getDashboardData,
  getUserGrowthTrend,
  getArticlePublishTrend,
  getDownloadTrend,
};
