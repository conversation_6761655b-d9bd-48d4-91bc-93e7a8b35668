const log4js = require('log4js');
const path = require('path');
const fs = require('fs');

// 自动创建 logs 目录
const logDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 配置 log4js
log4js.configure({
  appenders: {
    // 控制台输出
    console: { type: 'console' },

    // 按天拆分 info 日志
    infoFile: {
      type: 'dateFile',
      filename: path.join(logDir, 'info.log'),
      pattern: 'yyyy-MM-dd',
      numBackups: 30, // ✅ 替代 daysToKeep
      keepFileExt: true,
      compress: false,
    },

    // 按天拆分 error 日志
    errorFile: {
      type: 'dateFile',
      filename: path.join(logDir, 'error.log'),
      pattern: 'yyyy-MM-dd',
      numBackups: 30, // ✅ 替代 daysToKeep
      keepFileExt: true,
      compress: false,
    },

    // 只记录 error 级别
    errorFilter: {
      type: 'logLevelFilter',
      level: 'error',
      appender: 'errorFile'
    },

    // 只记录 info 到 warn
    infoFilter: {
      type: 'logLevelFilter',
      level: 'info',
      maxLevel: 'warn',
      appender: 'infoFile'
    },
  },
  categories: {
    default: {
      appenders: ['console', 'infoFilter', 'errorFilter'],
      level: 'debug',
    },
  },
});

// 获取 logger 实例
const logger = log4js.getLogger();

// 捕获未处理异常
process.on('uncaughtException', (err) => {
  logger.fatal('未捕获异常:', err);
});
process.on('unhandledRejection', (reason, promise) => {
  logger.fatal('未处理的 Promise 拒绝:', reason);
});

// 示例日志
// logger.debug('这是 debug 日志噜~');
// logger.info('这是 info 日志噜~');
// logger.warn('这是 warning 日志噜~');
// logger.error('这是 error 日志噜~');
// logger.fatal('这是 fatal 日志噜~');

module.exports = logger;
