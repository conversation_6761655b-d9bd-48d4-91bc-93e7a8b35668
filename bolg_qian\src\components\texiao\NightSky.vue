<template>
  <canvas ref="canvas" class="night-canvas"></canvas>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

const canvas = ref(null)
let ctx
let WIDTH = window.innerWidth
let HEIGHT = window.innerHeight
let animationId

let stars = []
let meteors = []
let explosionParticles = []

const STAR_COUNT = 80
const METEOR_COUNT = 6

// 星星类 - 简化版本
class Star {
  constructor() {
    this.x = Math.random() * WIDTH
    this.y = Math.random() * HEIGHT
    this.radius = Math.random() * 1.2 + 0.5
    this.alpha = Math.random() * 0.8 + 0.2
    this.twinkleSpeed = Math.random() * 0.02 + 0.01
    this.twinkleDirection = Math.random() > 0.5 ? 1 : -1
  }

  update() {
    this.alpha += this.twinkleSpeed * this.twinkleDirection
    if (this.alpha >= 1) {
      this.alpha = 1
      this.twinkleDirection = -1
    } else if (this.alpha <= 0.2) {
      this.alpha = 0.2
      this.twinkleDirection = 1
    }
  }

  draw(ctx) {
    ctx.save()
    ctx.globalAlpha = this.alpha
    ctx.fillStyle = '#ffffff'
    ctx.beginPath()
    ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2)
    ctx.fill()
    ctx.restore()
  }
}

class Meteor {
  constructor() {
    this.reset()
  }
  reset() {
    this.x = Math.random() * WIDTH
    this.y = Math.random() * -HEIGHT
    this.length = Math.random() * 100 + 80
    this.speed = Math.random() * 3 + 3
    this.angle = Math.PI / 4
    this.alpha = Math.random() * 0.5 + 0.5
    this.width = Math.random() * 1.5 + 0.5
    this.colorStart = '#ffffff'
    this.colorEnd = '#00ffff'
  }
  update() {
    this.x += this.speed * Math.cos(this.angle)
    this.y += this.speed * Math.sin(this.angle)
    if (this.x > WIDTH || this.y > HEIGHT) this.reset()
  }
  draw(ctx) {
    const xEnd = this.x - this.length * Math.cos(this.angle)
    const yEnd = this.y - this.length * Math.sin(this.angle)
    const gradient = ctx.createLinearGradient(this.x, this.y, xEnd, yEnd)
    gradient.addColorStop(0, this.colorStart)
    gradient.addColorStop(1, this.colorEnd)

    ctx.save()
    ctx.beginPath()
    ctx.globalAlpha = this.alpha
    ctx.strokeStyle = gradient
    ctx.lineWidth = this.width
    ctx.moveTo(this.x, this.y)
    ctx.lineTo(xEnd, yEnd)
    ctx.stroke()
    ctx.restore()
  }
}

// 爆炸粒子类
class ExplosionParticle {
  constructor(x, y) {
    this.x = x
    this.y = y
    this.radius = Math.random() * 2 + 1
    this.color = this.getRandomColor()
    this.speedX = (Math.random() - 0.5) * 8
    this.speedY = (Math.random() - 0.5) * 8
    this.life = 60
    this.maxLife = 60
  }

  getRandomColor() {
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff']
    return colors[Math.floor(Math.random() * colors.length)]
  }

  update() {
    this.x += this.speedX
    this.y += this.speedY
    this.speedX *= 0.98
    this.speedY *= 0.98
    this.life--
  }

  draw(ctx) {
    ctx.save()
    ctx.globalAlpha = this.life / this.maxLife
    ctx.fillStyle = this.color
    ctx.beginPath()
    ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2)
    ctx.fill()
    ctx.restore()
  }
}

// 移除了drawMoon和drawBackground函数以避免样式冲突

// getRandomColor函数已移到ExplosionParticle类内部

function createAll() {
  stars = []
  meteors = []
  for (let i = 0; i < STAR_COUNT; i++) stars.push(new Star())
  for (let i = 0; i < METEOR_COUNT; i++) meteors.push(new Meteor())
}

function animate() {
  // 清除画布，使用透明背景
  ctx.clearRect(0, 0, WIDTH, HEIGHT)

  // 绘制星星
  stars.forEach(s => {
    s.update()
    s.draw(ctx)
  })

  // 绘制流星
  meteors.forEach(m => {
    m.update()
    m.draw(ctx)
  })

  // 绘制爆炸粒子
  for (let i = explosionParticles.length - 1; i >= 0; i--) {
    const p = explosionParticles[i]
    p.update()
    p.draw(ctx)
    if (p.life <= 0) {
      explosionParticles.splice(i, 1)
    }
  }

  animationId = requestAnimationFrame(animate)
}

function handleClick(e) {
  // 检查点击的是否是交互元素
  const target = e.target
  const isInteractiveElement = target.tagName === 'BUTTON' ||
                              target.tagName === 'A' ||
                              target.tagName === 'INPUT' ||
                              target.tagName === 'SELECT' ||
                              target.tagName === 'TEXTAREA' ||
                              target.closest('.el-button') ||
                              target.closest('.el-menu-item') ||
                              target.closest('.el-card') ||
                              target.closest('.el-dialog') ||
                              target.closest('.el-dropdown') ||
                              target.closest('.el-select') ||
                              target.closest('.effect-controls') ||
                              target.closest('[role="button"]') ||
                              target.closest('[clickable]') ||
                              target.style.cursor === 'pointer' ||
                              getComputedStyle(target).cursor === 'pointer'

  // 只有在点击空白区域时才产生爆炸效果
  if (!isInteractiveElement) {
    const x = e.clientX
    const y = e.clientY
    for (let i = 0; i < 20; i++) {
      explosionParticles.push(new ExplosionParticle(x, y))
    }
  }
}

function resizeCanvas() {
  WIDTH = window.innerWidth
  HEIGHT = window.innerHeight
  const canvasEl = canvas.value
  canvasEl.width = WIDTH
  canvasEl.height = HEIGHT
}

onMounted(async () => {
  await nextTick()
  const canvasEl = canvas.value
  if (!canvasEl) return

  resizeCanvas()
  ctx = canvasEl.getContext('2d')

  // 设置canvas样式确保不影响页面
  ctx.globalCompositeOperation = 'source-over'

  createAll()
  animate()

  // 使用全局事件监听器，不阻止页面交互
  window.addEventListener('resize', resizeCanvas)
  window.addEventListener('click', handleClick)
})

onUnmounted(() => {
  cancelAnimationFrame(animationId)
  window.removeEventListener('resize', resizeCanvas)
  window.removeEventListener('click', handleClick)
})
</script>

<style scoped>
.night-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
  background: transparent;
}
</style>
