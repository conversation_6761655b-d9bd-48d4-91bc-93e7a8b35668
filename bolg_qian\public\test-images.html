<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片路径测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-item {
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        img {
            max-width: 200px;
            max-height: 150px;
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>图片路径测试</h1>
    
    <div class="test-item">
        <h3>测试1: 直接访问后端 (http://192.168.31.222:3000/articles/liked_img_52.jpg)</h3>
        <img src="http://192.168.31.222:3000/articles/liked_img_52.jpg" 
             alt="liked_img_52.jpg" 
             onload="this.nextElementSibling.innerHTML='<span class=success>✅ 加载成功</span>'"
             onerror="this.nextElementSibling.innerHTML='<span class=error>❌ 加载失败</span>'">
        <div></div>
    </div>
    
    <div class="test-item">
        <h3>测试2: 通过前端代理 (/articles/liked_img_52.jpg)</h3>
        <img src="/articles/liked_img_52.jpg" 
             alt="liked_img_52.jpg" 
             onload="this.nextElementSibling.innerHTML='<span class=success>✅ 加载成功</span>'"
             onerror="this.nextElementSibling.innerHTML='<span class=error>❌ 加载失败</span>'">
        <div></div>
    </div>
    
    <div class="test-item">
        <h3>测试3: 直接访问后端 (http://192.168.31.222:3000/articles/liked_img_20.jpg)</h3>
        <img src="http://192.168.31.222:3000/articles/liked_img_20.jpg" 
             alt="liked_img_20.jpg" 
             onload="this.nextElementSibling.innerHTML='<span class=success>✅ 加载成功</span>'"
             onerror="this.nextElementSibling.innerHTML='<span class=error>❌ 加载失败</span>'">
        <div></div>
    </div>
    
    <div class="test-item">
        <h3>测试4: 通过前端代理 (/articles/liked_img_20.jpg)</h3>
        <img src="/articles/liked_img_20.jpg" 
             alt="liked_img_20.jpg" 
             onload="this.nextElementSibling.innerHTML='<span class=success>✅ 加载成功</span>'"
             onerror="this.nextElementSibling.innerHTML='<span class=error>❌ 加载失败</span>'">
        <div></div>
    </div>
    
    <script>
        console.log('图片路径测试页面已加载');
        
        // 检查所有图片的加载状态
        setTimeout(() => {
            const images = document.querySelectorAll('img');
            images.forEach((img, index) => {
                console.log(`图片 ${index + 1}:`, {
                    src: img.src,
                    complete: img.complete,
                    naturalWidth: img.naturalWidth,
                    naturalHeight: img.naturalHeight
                });
            });
        }, 3000);
    </script>
</body>
</html>
