<template>
  <div class="bug-container">
    <el-card>
      <div class="header">
        <span class="title">Bug 提交记录</span>
        <el-button type="primary" @click="dialogVisible = true" size="small">新增 Bug</el-button>
      </div>

      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form" size="small">
        <el-form-item label="标题">
          <el-input v-model="searchForm.title" placeholder="输入标题" clearable />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="searchForm.description" placeholder="输入描述" clearable />
        </el-form-item>
        <el-form-item label="程度">
          <el-select v-model="searchForm.severity" placeholder="全部" clearable>
            <el-option label="一般" value="一般" />
            <el-option label="严重" value="严重" />
            <el-option label="致命" value="致命" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部" clearable>
            <el-option label="新增" value="2" />
            <el-option label="未完成" value="0" />
            <el-option label="已完成" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker v-model="searchForm.createTime" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" value-format="YYYY-MM-DD" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="doSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="danger" size="small" :disabled="!multipleSelection.length" @click="deleteSelectedBugs">
            批量删除
          </el-button>
        </el-form-item>
      </el-form>

      <el-button type="info" size="small" @click="showCompleted = !showCompleted" style="margin-bottom: 12px;">
        {{ showCompleted ? "折叠已完成" : "展开已完成" }}
      </el-button>

      <el-table :data="filteredBugList" style="width: 100%" v-loading="loading"
        @selection-change="handleSelectionChange" ref="bugTableRef">
        <el-table-column type="selection" width="50" />
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="title" label="标题" />
        <el-table-column prop="description" label="描述">
          <template #default="{ row }">
            <span>
              {{ showDesc(row.description) }}
              <el-link v-if="row.description && row.description.length > 12" type="primary"
                @click="showDescDialog(row.description)" style="margin-left: 6px;">详情</el-link>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="severity" label="程度" width="100">
          <template #default="{ row }">
            <el-tag :type="severityTagType(row.severity)">
              {{ row.severity }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="statusTagType(row.status)">
              {{ statusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="openEditDialog(row)" style="margin-right: 8px">编辑</el-button>
            <el-button v-if="row.status !== 1" type="success" size="small"
              @click="updateStatus(row.id, 1)">标记完成</el-button>
            <el-button v-if="row.status !== 0" type="warning" size="small"
              @click="updateStatus(row.id, 0)">未完成</el-button>
              <el-button @click="deleteBug(row.id)" type="danger" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增 Bug 弹窗 -->
    <el-dialog v-model="dialogVisible" title="新增 Bug" width="400px">
      <el-form :model="form" label-width="80px">
        <el-form-item label="标题">
          <el-input v-model="form.title" maxlength="100" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="form.description" type="textarea" maxlength="500" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option label="新增" value="2" />
            <el-option label="未完成" value="0" />
            <el-option label="已完成" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="程度">
          <el-select v-model="form.severity" placeholder="请选择程度">
            <el-option label="一般" value="一般" />
            <el-option label="严重" value="严重" />
            <el-option label="致命" value="致命" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addBug">提交</el-button>
      </template>
    </el-dialog>

    <!-- 编辑 Bug 弹窗 -->
    <el-dialog v-model="editDialogVisible" title="编辑 Bug" width="400px">
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="标题">
          <el-input v-model="editForm.title" maxlength="100" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="editForm.description" type="textarea" maxlength="500" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="editForm.status" placeholder="请选择状态">
            <el-option label="新增" value="2" />
            <el-option label="未完成" value="0" />
            <el-option label="已完成" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="程度">
          <el-select v-model="editForm.severity" placeholder="请选择程度">
            <el-option label="一般" value="一般" />
            <el-option label="严重" value="严重" />
            <el-option label="致命" value="致命" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEdit">保存</el-button>
      </template>
    </el-dialog>

    <!-- 描述详情弹窗 -->
    <el-dialog v-model="descDialogVisible" title="描述详情" width="400px" :show-close="true">
      <div style="white-space: pre-wrap;">{{ descDialogContent }}</div>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import dayjs from "dayjs";
import {
  GetBugListApi,
  AddBugApi,
  UpdateBugStatusApi,
  DeleteBugApi,
  DeleteBugsApi,
  UpdateBugApi
} from "../utils/api";
import { ElMessage, ElMessageBox } from "element-plus";

const bugList = ref<any[]>([]);
const loading = ref(false);
const dialogVisible = ref(false);
const descDialogVisible = ref(false);
const editDialogVisible = ref(false);
const form = ref({
  title: "",
  description: "",
  status: "2", // 默认为新增
  severity: "一般", // 默认为一般
});
const editForm = ref({
  id: null,
  title: "",
  description: "",
  status: "2",
  severity: "一般",
});
const descDialogContent = ref("");
const bugTableRef = ref();
const multipleSelection = ref<any[]>([]);
const showCompleted = ref(false);

// 搜索表单
const searchForm = ref({
  title: "",
  description: "",
  severity: "",
  status: "",
  createTime: [] as string[],
});

// 状态文本和样式
const statusText = (status: number) => {
  if (status === 1) return "已完成";
  if (status === 0) return "未完成";
  return "新增";
};

const statusTagType = (status: number) => {
  if (status === 1) return "success";
  if (status === 2) return "info";
  if (status === 0) return "warning";
  return "info";
};

// 程度样式
const severityTagType = (severity: string) => {
  if (severity === "一般") return "info";
  if (severity === "致命") return "danger";
  if (severity === "严重") return "warning";
  return "info";
};

// 获取 bug 列表
const fetchBugList = async () => {
  loading.value = true;
  try {
    const res = await GetBugListApi();
    bugList.value = res.data || [];
  } catch (e) {
    ElMessage.error("获取 bug 列表失败");
  } finally {
    loading.value = false;
  }
};

// 新增 bug
const addBug = async () => {
  if (!form.value.title || !form.value.description) {
    ElMessage.warning("请填写标题和描述");
    return;
  }
  try {
    await AddBugApi({
      title: form.value.title,
      description: form.value.description,
      status: form.value.status,
      severity: form.value.severity,
    });
    ElMessage.success("新增成功");
    dialogVisible.value = false;
    form.value.title = "";
    form.value.description = "";
    form.value.status = "2"; // 重置为新增
    form.value.severity = "一般"; // 重置为一般
    fetchBugList();
  } catch (e) {
    ElMessage.error("新增失败");
  }
};

// 修改 bug 状态
const updateStatus = async (id: number, status: number) => {
  // 找到当前 bug 的 severity
  const bug = bugList.value.find((item) => item.id === id);
  const severity = bug ? bug.severity : "一般";
  try {
    await UpdateBugStatusApi(id, status, severity);
    ElMessage.success("状态已更新");
    fetchBugList();
  } catch (e) {
    ElMessage.error("状态更新失败");
  }
};

// 删除 bug
const deleteBug = async (id: number) => {
  ElMessageBox.confirm("确定要删除该 bug 吗？", "提示", {
    type: "warning",
  })
    .then(async () => {
      await DeleteBugApi(id);
      ElMessage.success("删除成功");
      fetchBugList();
    })
    .catch(() => { });
};

// 批量删除选中 bug
const deleteSelectedBugs = () => {
  if (!multipleSelection.value.length) return;
  ElMessageBox.confirm(
    `确定要删除选中的 ${multipleSelection.value.length} 条 bug 吗？`,
    "提示",
    { type: "warning" }
  )
    .then(async () => {
      const ids = multipleSelection.value.map(item => item.id);
      await DeleteBugsApi(ids);
      ElMessage.success("批量删除成功");
      fetchBugList();
      // 清空选择
      bugTableRef.value && bugTableRef.value.clearSelection();
    })
    .catch(() => { });
};

// 显示描述
const showDesc = (desc: string) => {
  if (!desc) return "";
  return desc.length > 10 ? desc.slice(0, 10) + "..." : desc;
};
// 显示描述详情弹窗
const showDescDialog = (desc: string) => {
  descDialogContent.value = desc;
  descDialogVisible.value = true;
};

// 时间格式化方法
const formatTime = (time: string) => {
  return time ? dayjs(time).format("YYYY-MM-DD HH:mm:ss") : "";
};

// 过滤后的 bug 列表
const filteredBugList = computed(() => {
  return bugList.value.filter((bug) => {
    // 标题
    if (searchForm.value.title && !bug.title.includes(searchForm.value.title)) return false;
    // 描述
    if (searchForm.value.description && !bug.description.includes(searchForm.value.description)) return false;
    // 程度
    if (searchForm.value.severity && bug.severity !== searchForm.value.severity) return false;
    // 状态
    if (searchForm.value.status && String(bug.status) !== String(searchForm.value.status)) return false;
    // 折叠已完成
    if (!showCompleted.value && bug.status === 1) return false;
    // 创建时间
    if (
      searchForm.value.createTime &&
      searchForm.value.createTime.length === 2
    ) {
      const start = dayjs(searchForm.value.createTime[0] + " 00:00:00");
      const end = dayjs(searchForm.value.createTime[1] + " 23:59:59");
      const bugTime = dayjs(bug.create_time);
      if (bugTime.isBefore(start) || bugTime.isAfter(end)) return false;
    }
    return true;
  });
});

const doSearch = () => {
  // 这里只需触发 filteredBugList 重新计算即可
};
const resetSearch = () => {
  searchForm.value = {
    title: "",
    description: "",
    severity: "",
    status: "",
    createTime: [],
  };
};

const handleSelectionChange = (val: any[]) => {
  multipleSelection.value = val;
};

// 打开编辑弹窗
const openEditDialog = (row: any) => {
  editForm.value = { ...row }; // 浅拷贝
  editDialogVisible.value = true;
};

// 提交编辑
const submitEdit = async () => {
  console.log(editForm.value);
  if (!editForm.value.title || !editForm.value.description) {
    ElMessage.warning("请填写标题和描述");
    return;
  }
  try {
    const res = await UpdateBugApi({
      id: editForm.value.id,
      title: editForm.value.title,
      description: editForm.value.description,
      status: editForm.value.status,
      severity: editForm.value.severity,
    });
    ElMessage.success("修改成功");
    editDialogVisible.value = false;
    fetchBugList();
  } catch (e) {
    ElMessage.error("修改失败");
  }
};

onMounted(() => {
  fetchBugList();
});
</script>

<style scoped lang="less">
.bug-container {
  max-width: 1200px;
  margin: 30px auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
}

.title {
  font-size: 20px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 18px;
}
</style>
