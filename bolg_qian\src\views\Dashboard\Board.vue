<template>
  <div class="dashboard-wrapper">
    <!-- 顶部统计 -->
    <el-row :gutter="16" class="stats-row">
      <el-col :span="4" v-for="item in stats" :key="item.label">
        <el-card class="stat-card" shadow="hover">
          <div class="label">{{ item.label }}</div>
          <div class="value">{{ item.value }}</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表类型切换 -->
    <div class="toolbar">
      <ChartTypeSwitcher v-model="chartType" />
      <el-select v-model="dateRange" size="small" style="width: 120px">
        <el-option label="近7天" value="7" />
        <el-option label="近30天" value="30" />
        <el-option label="全部" value="all" />
      </el-select>
    </div>

    <!-- 三个图表 -->
    <el-row :gutter="16" class="charts-row">
      <!-- 用户增长 -->
      <el-col :span="8">
        <el-card shadow="hover" class="chart-card">
          <div class="chart-title">用户增长趋势</div>
          <div v-if="userDates.length" ref="userChart" class="chart"></div>
          <el-empty v-else description="暂无数据~" />
        </el-card>
      </el-col>

      <!-- 文章发布 -->
      <el-col :span="8">
        <el-card shadow="hover" class="chart-card">
          <div class="chart-title">文章发布趋势</div>
          <div v-if="articleDates.length" ref="articleChart" class="chart"></div>
          <el-empty v-else description="暂无数据~" />
        </el-card>
      </el-col>

      <!-- 下载统计 -->
      <el-col :span="8">
        <el-card shadow="hover" class="chart-card">
          <div class="chart-title">资源下载统计</div>
          <div v-if="downloadData.length" ref="downloadChart" class="chart"></div>
          <el-empty v-else description="暂无数据~" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 下载明细 -->
    <el-card class="logs-card" shadow="hover">
      <div class="logs-header">
        <h3>资源下载明细</h3>
        <el-select v-model="filterType" size="small" style="width: 140px" @change="currentPage = 1">
          <el-option label="全部" value="" />
          <el-option label="图片" value="图片" />
          <el-option label="视频" value="视频" />
          <el-option label="文档" value="文档" />
          <el-option label="音频" value="音频" />
          <el-option label="其他" value="其他" />
        </el-select>
      </div>

      <el-table :data="pagedLogs" :empty-text="'暂无数据~'" style="width: 100%">
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="file_name" label="文件名" />
        <el-table-column label="类型" width="100">
          <template #default="scope">{{ mapFileType(scope.row.file_type) }}</template>
        </el-table-column>
        <el-table-column prop="count" label="下载次数" width="100" />
      </el-table>

      <el-pagination v-model:current-page="currentPage" :total="filteredLogs.length" :page-size="pageSize"
        layout="prev, pager, next" background style="margin-top: 12px; text-align: right" />
    </el-card>

    <!-- API测试工具 -->
    <el-card class="api-test-card" shadow="hover" style="margin-top: 16px;">
      <template #header>
        <div class="api-test-header">
          <h3>
            <el-icon><Tools /></el-icon>
            API测试工具
          </h3>
          <el-button size="small" @click="toggleApiTest">
            {{ showApiTest ? '收起' : '展开' }}
          </el-button>
        </div>
      </template>

      <div v-show="showApiTest" class="api-test-content">
        <el-row :gutter="16">
          <!-- 文件管理API测试 -->
          <el-col :span="8">
            <div class="test-section">
              <h4>📁 文件管理API</h4>
              <div class="test-buttons">
                <el-button size="small" @click="testFileStructure" :loading="testing.fileStructure">
                  测试文件结构
                </el-button>
                <el-button size="small" @click="testFileStats" :loading="testing.fileStats">
                  测试文件统计
                </el-button>
                <el-button size="small" @click="testCreateFolder" :loading="testing.createFolder">
                  测试创建文件夹
                </el-button>
              </div>
              <div v-if="testResults.fileManager" class="test-result">
                <el-tag :type="testResults.fileManager.success ? 'success' : 'danger'">
                  {{ testResults.fileManager.message }}
                </el-tag>
                <div class="result-details">
                  {{ testResults.fileManager.details }}
                </div>
              </div>
            </div>
          </el-col>

          <!-- 系统状态API测试 -->
          <el-col :span="8">
            <div class="test-section">
              <h4>⚙️ 系统状态API</h4>
              <div class="test-buttons">
                <el-button size="small" @click="testSystemStatus" :loading="testing.systemStatus">
                  测试系统状态
                </el-button>
                <el-button size="small" @click="testDatabaseConnection" :loading="testing.database">
                  测试数据库连接
                </el-button>
                <el-button size="small" @click="testMemoryUsage" :loading="testing.memory">
                  测试内存使用
                </el-button>
              </div>
              <div v-if="testResults.system" class="test-result">
                <el-tag :type="testResults.system.success ? 'success' : 'danger'">
                  {{ testResults.system.message }}
                </el-tag>
                <div class="result-details">
                  {{ testResults.system.details }}
                </div>
              </div>
            </div>
          </el-col>

          <!-- 用户权限API测试 -->
          <el-col :span="8">
            <div class="test-section">
              <h4>👤 用户权限API</h4>
              <div class="test-buttons">
                <el-button size="small" @click="testUserAuth" :loading="testing.userAuth">
                  测试用户认证
                </el-button>
                <el-button size="small" @click="testAdminPermission" :loading="testing.adminPermission">
                  测试管理员权限
                </el-button>
                <el-button size="small" @click="testTokenValidation" :loading="testing.tokenValidation">
                  测试Token验证
                </el-button>
              </div>
              <div v-if="testResults.auth" class="test-result">
                <el-tag :type="testResults.auth.success ? 'success' : 'danger'">
                  {{ testResults.auth.message }}
                </el-tag>
                <div class="result-details">
                  {{ testResults.auth.details }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 综合测试结果 -->
        <div class="comprehensive-test" style="margin-top: 20px;">
          <el-button type="primary" @click="runComprehensiveTest" :loading="testing.comprehensive">
            <el-icon><Refresh /></el-icon>
            运行综合测试
          </el-button>

          <div v-if="comprehensiveResults.length > 0" class="comprehensive-results">
            <h4>综合测试结果:</h4>
            <div class="results-grid">
              <div
                v-for="result in comprehensiveResults"
                :key="result.name"
                class="result-item"
                :class="{ success: result.success, error: !result.success }"
              >
                <div class="result-name">{{ result.name }}</div>
                <div class="result-status">
                  <el-icon v-if="result.success"><Check /></el-icon>
                  <el-icon v-else><Close /></el-icon>
                </div>
                <div class="result-time">{{ result.time }}ms</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import ChartTypeSwitcher from '../../components/ChartTypeSwitcher.vue'
import { dashboardApi, getCountApi } from '../../utils/api'
import { getFileStructureApi, createFolderApi } from '../../utils/fileManagerApi'
import { ElMessage } from 'element-plus'
import { Tools, Refresh, Check, Close } from '@element-plus/icons-vue'

const stats = ref([])
const chartType = ref('line') // 默认折线图
const dateRange = ref('7')

const userChart = ref()
const articleChart = ref()
const downloadChart = ref()

const userDates = ref<string[]>([])
const articleDates = ref<string[]>([])
const downloadData = ref<any[]>([])

const downloadLogs = ref([])
const filterType = ref('')
const currentPage = ref(1)
const pageSize = 10

// API测试相关数据
const showApiTest = ref(false)
const testing = ref({
  fileStructure: false,
  fileStats: false,
  createFolder: false,
  systemStatus: false,
  database: false,
  memory: false,
  userAuth: false,
  adminPermission: false,
  tokenValidation: false,
  comprehensive: false
})

const testResults = ref({
  fileManager: null as any,
  system: null as any,
  auth: null as any
})

const comprehensiveResults = ref<any[]>([])

// 保存 ECharts 实例，避免重复初始化
let userChartInstance: echarts.ECharts | null = null
let articleChartInstance: echarts.ECharts | null = null
let downloadChartInstance: echarts.ECharts | null = null

function mapFileType(ext: string) {
  ext = ext.toLowerCase()
  if (['jpg', 'png', 'webp'].includes(ext)) return '图片'
  if (['mp4', 'mov'].includes(ext)) return '视频'
  if (['doc', 'pdf', 'xls'].includes(ext)) return '文档'
  if (['mp3', 'wav'].includes(ext)) return '音频'
  return '其他'
}

let userCounts: number[] = []
let articleCounts: number[] = []

async function fetchData() {
  const [statRes, trendRes] = await Promise.all([
    dashboardApi(),
    getCountApi(dateRange.value),
  ])

  stats.value = [
    { label: '用户总数', value: statRes.data.userCount },
    { label: '文章数量', value: statRes.data.articleCount },
    { label: '分享数量', value: statRes.data.shareCount },
    { label: '资源数量', value: statRes.data.resourceCount },
    { label: '资源下载次数', value: statRes.data.downloadCount },
  ]

  userDates.value = trendRes.data.userGrowth.map(i => i.date) || []
  userCounts = trendRes.data.userGrowth.map(i => i.count) || []

  articleDates.value = trendRes.data.articlePublish.map(i => i.date) || []
  articleCounts = trendRes.data.articlePublish.map(i => i.count) || []

  downloadData.value = trendRes.data.downloadData || []
  downloadLogs.value = downloadData.value

  await nextTick()
  renderChart()
}

function renderChart() {
  // 用户增长
  if (userChart.value) {
    if (userChartInstance) userChartInstance.dispose()
    userChartInstance = echarts.init(userChart.value)
    userChartInstance.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: userDates.value },
      yAxis: { type: 'value' },
      series: [{
        name: '用户数',
        data: userCounts, // 修正
        type: chartType.value,
        smooth: chartType.value === 'line',
        areaStyle: chartType.value === 'line' ? { color: '#67C23A' } : undefined,
        itemStyle: chartType.value === 'bar' ? { color: '#67C23A' } : undefined,
      }],
    })
  }
  // 文章发布
  if (articleChart.value) {
    if (articleChartInstance) articleChartInstance.dispose()
    articleChartInstance = echarts.init(articleChart.value)
    articleChartInstance.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: articleDates.value },
      yAxis: { type: 'value' },
      series: [{
        name: '文章数',
        data: articleCounts, // 修正
        type: chartType.value,
        smooth: chartType.value === 'line',
        areaStyle: chartType.value === 'line' ? { color: '#409EFF' } : undefined,
        itemStyle: chartType.value === 'bar' ? { color: '#409EFF' } : undefined,
      }],
    })
  }
  // 下载统计
  if (downloadChart.value) {
    if (downloadChartInstance) downloadChartInstance.dispose()
    downloadChartInstance = echarts.init(downloadChart.value)
    const typeMap = { 图片: 0, 视频: 0, 文档: 0, 音频: 0, 其他: 0 }
    downloadData.value.forEach(i => {
      const type = mapFileType(i.file_type)
      typeMap[type] += i.count
    })
    downloadChartInstance.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: Object.keys(typeMap) },
      yAxis: { type: 'value' },
      series: [{
        name: '下载次数',
        data: Object.values(typeMap),
        type: chartType.value,
        smooth: chartType.value === 'line',
        areaStyle: chartType.value === 'line' ? { color: '#909399' } : undefined,
        itemStyle: chartType.value === 'bar' ? { color: '#909399' } : undefined,
      }],
    })
  }
}

const filteredLogs = computed(() =>
  filterType.value
    ? downloadLogs.value.filter(i => mapFileType(i.file_type) === filterType.value)
    : downloadLogs.value
)

const pagedLogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  return filteredLogs.value.slice(start, start + pageSize)
})

// 监听 chartType 和 dateRange，变化时重新渲染图表
watch([chartType, dateRange], async () => {
  await fetchData()
})

// API测试方法
const toggleApiTest = () => {
  showApiTest.value = !showApiTest.value
}

const testFileStructure = async () => {
  testing.value.fileStructure = true
  const startTime = Date.now()

  try {
    const response = await getFileStructureApi('images')
    const endTime = Date.now()

    testResults.value.fileManager = {
      success: true,
      message: '文件结构API测试成功',
      details: `响应时间: ${endTime - startTime}ms, 数据: ${JSON.stringify(response).length} 字符`
    }
    ElMessage.success('文件结构API测试成功')
  } catch (error: any) {
    testResults.value.fileManager = {
      success: false,
      message: '文件结构API测试失败',
      details: error.message || '未知错误'
    }
    ElMessage.error('文件结构API测试失败')
  } finally {
    testing.value.fileStructure = false
  }
}

const testFileStats = async () => {
  testing.value.fileStats = true
  const startTime = Date.now()

  try {
    const categories = ['images', 'documents', 'videos']
    let totalFiles = 0

    for (const category of categories) {
      const response = await getFileStructureApi(category)
      if (response?.data?.[category]?.children) {
        totalFiles += response.data[category].children.length
      }
    }

    const endTime = Date.now()
    testResults.value.fileManager = {
      success: true,
      message: '文件统计API测试成功',
      details: `响应时间: ${endTime - startTime}ms, 总文件数: ${totalFiles}`
    }
    ElMessage.success('文件统计API测试成功')
  } catch (error: any) {
    testResults.value.fileManager = {
      success: false,
      message: '文件统计API测试失败',
      details: error.message || '未知错误'
    }
    ElMessage.error('文件统计API测试失败')
  } finally {
    testing.value.fileStats = false
  }
}

const testCreateFolder = async () => {
  testing.value.createFolder = true
  const startTime = Date.now()

  try {
    const testFolderName = `api-test-${Date.now()}`
    await createFolderApi({
      category: 'images',
      folderName: testFolderName
    })

    const endTime = Date.now()
    testResults.value.fileManager = {
      success: true,
      message: '创建文件夹API测试成功',
      details: `响应时间: ${endTime - startTime}ms, 文件夹: ${testFolderName}`
    }
    ElMessage.success('创建文件夹API测试成功')
  } catch (error: any) {
    testResults.value.fileManager = {
      success: false,
      message: '创建文件夹API测试失败',
      details: error.message || '未知错误'
    }
    ElMessage.error('创建文件夹API测试失败')
  } finally {
    testing.value.createFolder = false
  }
}

const testSystemStatus = async () => {
  testing.value.systemStatus = true
  const startTime = Date.now()

  try {
    // 测试基本的API连通性
    const response = await dashboardApi()
    const endTime = Date.now()

    testResults.value.system = {
      success: true,
      message: '系统状态API测试成功',
      details: `响应时间: ${endTime - startTime}ms, 系统正常运行`
    }
    ElMessage.success('系统状态API测试成功')
  } catch (error: any) {
    testResults.value.system = {
      success: false,
      message: '系统状态API测试失败',
      details: error.message || '未知错误'
    }
    ElMessage.error('系统状态API测试失败')
  } finally {
    testing.value.systemStatus = false
  }
}

const testDatabaseConnection = async () => {
  testing.value.database = true
  const startTime = Date.now()

  try {
    // 通过获取统计数据来测试数据库连接
    const response = await dashboardApi()
    const endTime = Date.now()

    testResults.value.system = {
      success: true,
      message: '数据库连接测试成功',
      details: `响应时间: ${endTime - startTime}ms, 数据库连接正常`
    }
    ElMessage.success('数据库连接测试成功')
  } catch (error: any) {
    testResults.value.system = {
      success: false,
      message: '数据库连接测试失败',
      details: error.message || '未知错误'
    }
    ElMessage.error('数据库连接测试失败')
  } finally {
    testing.value.database = false
  }
}

const testMemoryUsage = async () => {
  testing.value.memory = true

  try {
    // 模拟内存使用测试
    const memoryInfo = (performance as any).memory
    const usedMemory = memoryInfo ? Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024) : 'N/A'

    testResults.value.system = {
      success: true,
      message: '内存使用测试成功',
      details: `当前内存使用: ${usedMemory}MB`
    }
    ElMessage.success('内存使用测试成功')
  } catch (error: any) {
    testResults.value.system = {
      success: false,
      message: '内存使用测试失败',
      details: error.message || '未知错误'
    }
    ElMessage.error('内存使用测试失败')
  } finally {
    testing.value.memory = false
  }
}

const testUserAuth = async () => {
  testing.value.userAuth = true
  const startTime = Date.now()

  try {
    // 测试当前用户认证状态
    const token = localStorage.getItem('token')
    const endTime = Date.now()

    if (token) {
      testResults.value.auth = {
        success: true,
        message: '用户认证测试成功',
        details: `响应时间: ${endTime - startTime}ms, Token存在且有效`
      }
      ElMessage.success('用户认证测试成功')
    } else {
      throw new Error('Token不存在')
    }
  } catch (error: any) {
    testResults.value.auth = {
      success: false,
      message: '用户认证测试失败',
      details: error.message || '未知错误'
    }
    ElMessage.error('用户认证测试失败')
  } finally {
    testing.value.userAuth = false
  }
}

const testAdminPermission = async () => {
  testing.value.adminPermission = true

  try {
    // 测试管理员权限（通过能否访问仪表盘数据来判断）
    const response = await dashboardApi()

    testResults.value.auth = {
      success: true,
      message: '管理员权限测试成功',
      details: '当前用户具有管理员权限'
    }
    ElMessage.success('管理员权限测试成功')
  } catch (error: any) {
    testResults.value.auth = {
      success: false,
      message: '管理员权限测试失败',
      details: error.message || '权限不足'
    }
    ElMessage.error('管理员权限测试失败')
  } finally {
    testing.value.adminPermission = false
  }
}

const testTokenValidation = async () => {
  testing.value.tokenValidation = true
  const startTime = Date.now()

  try {
    // 通过API调用来验证Token
    await dashboardApi()
    const endTime = Date.now()

    testResults.value.auth = {
      success: true,
      message: 'Token验证测试成功',
      details: `响应时间: ${endTime - startTime}ms, Token有效`
    }
    ElMessage.success('Token验证测试成功')
  } catch (error: any) {
    testResults.value.auth = {
      success: false,
      message: 'Token验证测试失败',
      details: error.message || 'Token无效'
    }
    ElMessage.error('Token验证测试失败')
  } finally {
    testing.value.tokenValidation = false
  }
}

const runComprehensiveTest = async () => {
  testing.value.comprehensive = true
  comprehensiveResults.value = []

  const tests = [
    { name: '文件结构API', fn: testFileStructure },
    { name: '文件统计API', fn: testFileStats },
    { name: '系统状态API', fn: testSystemStatus },
    { name: '数据库连接', fn: testDatabaseConnection },
    { name: '用户认证', fn: testUserAuth },
    { name: 'Token验证', fn: testTokenValidation }
  ]

  for (const test of tests) {
    const startTime = Date.now()
    try {
      await test.fn()
      const endTime = Date.now()
      comprehensiveResults.value.push({
        name: test.name,
        success: true,
        time: endTime - startTime
      })
    } catch (error) {
      const endTime = Date.now()
      comprehensiveResults.value.push({
        name: test.name,
        success: false,
        time: endTime - startTime
      })
    }
  }

  testing.value.comprehensive = false
  ElMessage.success('综合测试完成')
}

// 页面初次加载
onMounted(async () => {
  await fetchData()
})
</script>

<style scoped lang="less">
.dashboard-wrapper {
  padding: 20px;
  .stats-row {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;

      .label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }

      .value {
        font-size: 22px;
        color: #409eff;
        font-weight: bold;
      }
    }
  }

  .toolbar {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 20px;
    gap: 12px;
  }

  .charts-row {
    margin-bottom: 20px;

    .chart-card {
      height: 360px;

      .chart-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
      }

      .chart {
        width: 100%;
        height: 280px;
      }
    }
  }

  .logs-card {
    .logs-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
  }

  .api-test-card {
    .api-test-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;
        color: #303133;
      }
    }

    .api-test-content {
      .test-section {
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        padding: 16px;
        background: #fafafa;

        h4 {
          margin: 0 0 12px 0;
          color: #303133;
          font-size: 14px;
          font-weight: 600;
        }

        .test-buttons {
          display: flex;
          flex-direction: column;
          gap: 8px;
          margin-bottom: 12px;

          .el-button {
            justify-content: flex-start;
          }
        }

        .test-result {
          .el-tag {
            margin-bottom: 8px;
          }

          .result-details {
            font-size: 12px;
            color: #666;
            background: white;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #e4e7ed;
          }
        }
      }

      .comprehensive-test {
        text-align: center;
        padding: 20px;
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        background: #fafafa;

        .comprehensive-results {
          margin-top: 16px;

          h4 {
            margin-bottom: 12px;
            color: #303133;
          }

          .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;

            .result-item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 12px;
              border-radius: 6px;
              border: 1px solid #e4e7ed;
              background: white;

              &.success {
                border-color: #67c23a;
                background: #f0f9ff;
              }

              &.error {
                border-color: #f56c6c;
                background: #fef0f0;
              }

              .result-name {
                font-weight: 600;
                color: #303133;
              }

              .result-status {
                .el-icon {
                  font-size: 16px;
                }
              }

              .result-time {
                font-size: 12px;
                color: #909399;
              }

              &.success .result-status .el-icon {
                color: #67c23a;
              }

              &.error .result-status .el-icon {
                color: #f56c6c;
              }
            }
          }
        }
      }
    }
  }
}
</style>
