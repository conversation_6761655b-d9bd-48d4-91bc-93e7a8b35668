const app = require("./app");
const logger = require("./plugin/logger");
const http = require('http');
const setupWebSocket = require('./communication/chat');
const setupWebRTCSignalServer = require('./communication/webrtc');

// 只创建一个 server
const server = http.createServer(app.callback());

// 聊天 WebSocket
setupWebSocket(server);
// WebRTC 信令
setupWebRTCSignalServer(server);

server.listen(3000, '0.0.0.0', () => {
  logger.info('Server running at http://192.168.31.222:3000');
});

