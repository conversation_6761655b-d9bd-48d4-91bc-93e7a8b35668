const Router = require("koa-router");
const create = new Router();
const db = require("../../utils/db");
const logger = require("../../plugin/logger");
const bodyParser = require("koa-bodyparser");
create.use(bodyParser());

// 获取创意列表
create.post("/list", async (ctx) => {
  try {
    const rows = await db.query("SELECT * FROM creative ORDER BY create_time DESC");
    ctx.body = { code: 0, data: rows };
  } catch (err) {
    logger.error(err);
    ctx.body = { code: 1, message: "获取创意列表失败" };
  }
});

// 新增创意
create.post("/add", async (ctx) => {
  const { title, content, tags } = ctx.request.body;
  console.log(title, content, tags);
  if (!title || !content) {
    ctx.body = { code: 1, message: "标题和内容不能为空" };
    return;
  }
  try {
    const tagStr = Array.isArray(tags) ? JSON.stringify(tags) : "[]";
    const result = await db.query(
      "INSERT INTO creative (title, content, tags, create_time) VALUES (?, ?, ?, NOW())",
      [title, content, tagStr]
    );
    ctx.body = { code: 0, message: "添加成功", data: { id: result.insertId, title, content, tags, create_time: new Date() } };
  } catch (err) {
    logger.error(err);
    ctx.body = { code: 1, message: "添加失败" };
  }
});

// 删除创意
create.post("/delete", async (ctx) => {
  const { id } = ctx.request.body;
  if (!id) {
    ctx.body = { code: 1, message: "缺少id" };
    return;
  }
  try {
    await db.query("DELETE FROM creative WHERE id = ?", [id]);
    ctx.body = { code: 0, message: "删除成功" };
  } catch (err) {
    logger.error(err);
    ctx.body = { code: 1, message: "删除失败" };
  }
});

module.exports = create;