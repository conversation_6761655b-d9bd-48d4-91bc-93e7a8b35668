import { ref, reactive, watch, onMounted, onUnmounted } from 'vue'

// 特效类型定义
export type EffectType = 'none' | 'meteor' | 'nightsky' | 'particle'

// 特效配置接口
export interface EffectConfig {
  enabled: boolean
  opacity: number
  speed: number
  specific: {
    // 流星雨配置
    count?: number
    size?: number
    theme?: string
    // 夜空星辰配置
    starCount?: number
    twinkle?: boolean
    constellation?: boolean
    // 粒子连线配置
    particleCount?: number
    linkDistance?: number
    mouseInteraction?: boolean
    particleColor?: string
  }
}

// 全局特效状态
const currentEffect = ref<EffectType>('none')
const effectConfig = reactive<EffectConfig>({
  enabled: true,
  opacity: 80,
  speed: 5,
  specific: {
    // 默认配置
    count: 5,
    size: 2,
    theme: 'white',
    starCount: 200,
    twinkle: true,
    constellation: false,
    particleCount: 80,
    linkDistance: 100,
    mouseInteraction: true,
    particleColor: '#409eff'
  }
})

// 特效实例管理
const effectInstances = new Map<string, any>()

export function useEffectManager() {
  // 加载保存的配置
  const loadSavedConfig = () => {
    try {
      const saved = localStorage.getItem('effectConfig')
      if (saved) {
        const config = JSON.parse(saved)
        currentEffect.value = config.effect || 'none'
        Object.assign(effectConfig, config.config || {})
      }
    } catch (error) {
      console.warn('加载特效配置失败:', error)
    }
  }

  // 保存配置
  const saveConfig = () => {
    try {
      localStorage.setItem('effectConfig', JSON.stringify({
        effect: currentEffect.value,
        config: effectConfig
      }))
    } catch (error) {
      console.warn('保存特效配置失败:', error)
    }
  }

  // 应用特效
  const applyEffect = (effect: EffectType, config?: Partial<EffectConfig>) => {
    // 清除当前特效
    clearCurrentEffect()
    
    // 更新配置
    currentEffect.value = effect
    if (config) {
      Object.assign(effectConfig, config)
    }
    
    // 应用新特效
    if (effect !== 'none' && effectConfig.enabled) {
      createEffectInstance(effect)
    }
    
    // 保存配置
    saveConfig()
  }

  // 创建特效实例
  const createEffectInstance = (effect: EffectType) => {
    if (effect === 'none') return

    // 创建特效容器
    const container = document.createElement('div')
    container.id = `effect-${effect}`
    container.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1;
      opacity: ${effectConfig.opacity / 100};
    `
    
    document.body.appendChild(container)
    
    // 根据特效类型创建实例
    switch (effect) {
      case 'meteor':
        createMeteorEffect(container)
        break
      case 'nightsky':
        createNightSkyEffect(container)
        break
      case 'particle':
        createParticleEffect(container)
        break
    }
    
    effectInstances.set(effect, container)
  }

  // 创建流星雨特效
  const createMeteorEffect = (container: HTMLElement) => {
    const canvas = document.createElement('canvas')
    canvas.width = window.innerWidth
    canvas.height = window.innerHeight
    container.appendChild(canvas)
    
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    const meteors: any[] = []
    const config = effectConfig.specific
    
    // 创建流星
    for (let i = 0; i < (config.count || 5); i++) {
      meteors.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        length: Math.random() * 80 + 20,
        speed: Math.random() * 3 + 1,
        opacity: Math.random() * 0.5 + 0.5
      })
    }
    
    // 动画循环
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      meteors.forEach(meteor => {
        // 绘制流星
        const gradient = ctx.createLinearGradient(
          meteor.x, meteor.y,
          meteor.x - meteor.length, meteor.y - meteor.length
        )
        
        const color = getThemeColor(config.theme || 'white')
        gradient.addColorStop(0, `rgba(${color}, ${meteor.opacity})`)
        gradient.addColorStop(1, `rgba(${color}, 0)`)
        
        ctx.strokeStyle = gradient
        ctx.lineWidth = config.size || 2
        ctx.beginPath()
        ctx.moveTo(meteor.x, meteor.y)
        ctx.lineTo(meteor.x - meteor.length, meteor.y - meteor.length)
        ctx.stroke()
        
        // 更新位置
        meteor.x += meteor.speed * effectConfig.speed
        meteor.y += meteor.speed * effectConfig.speed
        
        // 重置位置
        if (meteor.x > canvas.width + meteor.length || meteor.y > canvas.height + meteor.length) {
          meteor.x = -meteor.length
          meteor.y = Math.random() * canvas.height
        }
      })
      
      if (currentEffect.value === 'meteor') {
        requestAnimationFrame(animate)
      }
    }
    
    animate()
  }

  // 创建夜空星辰特效
  const createNightSkyEffect = (container: HTMLElement) => {
    const canvas = document.createElement('canvas')
    canvas.width = window.innerWidth
    canvas.height = window.innerHeight
    container.appendChild(canvas)
    
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    const stars: any[] = []
    const config = effectConfig.specific
    
    // 创建星星
    for (let i = 0; i < (config.starCount || 200); i++) {
      stars.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        radius: Math.random() * 2 + 1,
        opacity: Math.random() * 0.8 + 0.2,
        twinkleSpeed: Math.random() * 0.02 + 0.01
      })
    }
    
    let time = 0
    
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      time += 0.01
      
      stars.forEach(star => {
        // 闪烁效果
        let opacity = star.opacity
        if (config.twinkle) {
          opacity = star.opacity + Math.sin(time * star.twinkleSpeed) * 0.3
        }
        
        ctx.fillStyle = `rgba(255, 255, 255, ${opacity})`
        ctx.beginPath()
        ctx.arc(star.x, star.y, star.radius, 0, Math.PI * 2)
        ctx.fill()
      })
      
      if (currentEffect.value === 'nightsky') {
        requestAnimationFrame(animate)
      }
    }
    
    animate()
  }

  // 创建粒子连线特效
  const createParticleEffect = (container: HTMLElement) => {
    const canvas = document.createElement('canvas')
    canvas.width = window.innerWidth
    canvas.height = window.innerHeight
    container.appendChild(canvas)
    
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    const particles: any[] = []
    const config = effectConfig.specific
    const mouse = { x: 0, y: 0 }
    
    // 创建粒子
    for (let i = 0; i < (config.particleCount || 80); i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 2,
        vy: (Math.random() - 0.5) * 2,
        radius: Math.random() * 3 + 1
      })
    }
    
    // 鼠标事件
    if (config.mouseInteraction) {
      const handleMouseMove = (e: MouseEvent) => {
        mouse.x = e.clientX
        mouse.y = e.clientY
      }
      window.addEventListener('mousemove', handleMouseMove)
    }
    
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      // 更新粒子
      particles.forEach(particle => {
        particle.x += particle.vx * effectConfig.speed * 0.5
        particle.y += particle.vy * effectConfig.speed * 0.5
        
        // 边界检测
        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1
        
        // 绘制粒子
        ctx.fillStyle = config.particleColor || '#409eff'
        ctx.beginPath()
        ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2)
        ctx.fill()
      })
      
      // 绘制连线
      const linkDistance = config.linkDistance || 100
      for (let i = 0; i < particles.length; i++) {
        for (let j = i + 1; j < particles.length; j++) {
          const dx = particles[i].x - particles[j].x
          const dy = particles[i].y - particles[j].y
          const distance = Math.sqrt(dx * dx + dy * dy)
          
          if (distance < linkDistance) {
            ctx.strokeStyle = `rgba(64, 158, 255, ${1 - distance / linkDistance})`
            ctx.lineWidth = 1
            ctx.beginPath()
            ctx.moveTo(particles[i].x, particles[i].y)
            ctx.lineTo(particles[j].x, particles[j].y)
            ctx.stroke()
          }
        }
      }
      
      if (currentEffect.value === 'particle') {
        requestAnimationFrame(animate)
      }
    }
    
    animate()
  }

  // 获取主题颜色
  const getThemeColor = (theme: string): string => {
    const colors = {
      white: '255, 255, 255',
      rainbow: `${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}`,
      blue: '64, 158, 255',
      gold: '255, 215, 0'
    }
    return colors[theme as keyof typeof colors] || colors.white
  }

  // 清除当前特效
  const clearCurrentEffect = () => {
    effectInstances.forEach((instance, key) => {
      if (instance && instance.parentNode) {
        instance.parentNode.removeChild(instance)
      }
    })
    effectInstances.clear()
  }

  // 切换特效开关
  const toggleEffect = (enabled: boolean) => {
    effectConfig.enabled = enabled
    if (enabled && currentEffect.value !== 'none') {
      createEffectInstance(currentEffect.value)
    } else {
      clearCurrentEffect()
    }
    saveConfig()
  }

  // 更新特效配置
  const updateConfig = (newConfig: Partial<EffectConfig>) => {
    Object.assign(effectConfig, newConfig)
    
    // 重新应用特效
    if (currentEffect.value !== 'none' && effectConfig.enabled) {
      clearCurrentEffect()
      createEffectInstance(currentEffect.value)
    }
    
    saveConfig()
  }

  // 监听窗口大小变化
  const handleResize = () => {
    if (currentEffect.value !== 'none' && effectConfig.enabled) {
      clearCurrentEffect()
      createEffectInstance(currentEffect.value)
    }
  }

  // 生命周期
  onMounted(() => {
    loadSavedConfig()
    window.addEventListener('resize', handleResize)
    
    // 监听全局特效事件
    window.addEventListener('effectChange', (e: any) => {
      applyEffect(e.detail.effect, e.detail.config)
    })
    
    window.addEventListener('effectConfigChange', (e: any) => {
      updateConfig(e.detail.config)
    })
    
    // 应用保存的特效
    if (currentEffect.value !== 'none' && effectConfig.enabled) {
      createEffectInstance(currentEffect.value)
    }
  })

  onUnmounted(() => {
    clearCurrentEffect()
    window.removeEventListener('resize', handleResize)
  })

  return {
    currentEffect,
    effectConfig,
    applyEffect,
    toggleEffect,
    updateConfig,
    clearCurrentEffect
  }
}
