const db = require("../db");

async function uploadFile(user_id, file_name, file_path, file_size, file_type) {
  const sql = `INSERT INTO files(user_id, file_name, file_path, file_size, file_type, is_share, is_deleted, created_at) VALUES(?, ?, ?, ?, ?, ?, ?, NOW())`;
  const result = await db.query(sql, [
    user_id,
    file_name,
    file_path,
    file_size,
    file_type,
    0, // 🔧 默认不分享，由用户决定
    0  // 🔧 修复：设置为未删除
  ]);
  if (result.insertId) {
    return { success: true, message: "文件上传成功" };
  }
  return { error: false, message: "文件上传失败" };
}


// 根据用户 ID 获取文件列表 判断is_deleted是否为0
async function getFileList(user_id) {
  const sql = `SELECT * FROM files WHERE user_id = ?`;
  const result = await db.query(sql, [user_id]);
  if (result.length > 0) {
    return { success: true, data: result };
  }
  return { error: false, message: "文件列表获取失败" };
}

// 下载文件 filename, user_id 必须 只查询是否存在，不返回文件内容
async function downloadFile(filename, user_id) {
  const sql = `SELECT * FROM files WHERE file_name = ? AND user_id = ?`;
  const result = await db.query(sql, [filename, user_id]);
  if (result.length > 0) {
    return { success: true, message: "文件下载成功" };
  }
  return { error: false, message: "文件下载失败" };
}

// 真删除文件 filename, user_id 必须
async function deleteFileTrue(filename, user_id) {
  const sql = `DELETE FROM files WHERE file_name = ? AND user_id = ?`;
  const result = await db.query(sql, [filename, user_id]);
  if (result.affectedRows > 0) {
    return { success: true, message: "文件删除成功" };
  }
  return { error: false, message: "文件删除失败" };
}

// 假删除文件 is_deleted 置为 1
async function deleteFileFalse(filename, user_id) {
  const sql = `UPDATE files SET is_deleted = 1 WHERE file_name = ? AND user_id = ?`;
  const result = await db.query(sql, [filename, user_id]);
  if (result.affectedRows > 0) {
    return { success: true, message: "文件删除成功" };
  }
  return { error: false, message: "文件删除失败" };
}

module.exports = {
  uploadFile,
  getFileList,
  downloadFile,
  deleteFileTrue,
  deleteFileFalse,
};
