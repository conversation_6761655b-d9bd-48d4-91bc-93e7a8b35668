import request from './index';

// 文件管理API接口

// 获取文件夹结构
export const getFileStructureApi = (category: string = 'all') =>
  request.get('/file-manager/structure', { params: { category } });

// 创建文件夹
export const createFolderApi = (data: {
  category: string;
  folderPath?: string;
  folderName: string;
}) => request.post('/file-manager/create-folder', data);

// 移动文件
export const moveFileApi = (data: {
  fileName: string;
  fromCategory: string;
  fromPath?: string;
  toCategory: string;
  toPath?: string;
}) => request.post('/file-manager/move-file', data);

// 重命名文件/文件夹
export const renameItemApi = (data: {
  category: string;
  oldPath: string;
  newName: string;
  type: 'file' | 'folder';
}) => request.post('/file-manager/rename', data);

// 删除文件/文件夹
export const deleteItemApi = (data: {
  category: string;
  targetPath: string;
  type: 'file' | 'folder';
}) => request.delete('/file-manager/delete', { data });

// 文件管理相关的类型定义
export interface FileItem {
  name: string;
  type: 'file' | 'folder';
  path: string;
  size?: number;
  mtime?: string | Date;
  extension?: string;
  children?: FileItem[];
}

export interface FileStructure {
  [category: string]: FileItem;
}

export interface CreateFolderRequest {
  category: string;
  folderPath?: string;
  folderName: string;
}

export interface MoveFileRequest {
  fileName: string;
  fromCategory: string;
  fromPath?: string;
  toCategory: string;
  toPath?: string;
}

export interface RenameItemRequest {
  category: string;
  oldPath: string;
  newName: string;
  type: 'file' | 'folder';
}

export interface DeleteItemRequest {
  category: string;
  targetPath: string;
  type: 'file' | 'folder';
}
