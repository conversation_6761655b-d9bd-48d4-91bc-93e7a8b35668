#!/usr/bin/env node

/**
 * 内存监控脚本
 * 定期检查服务器内存使用情况
 */

const http = require('http');

async function getHealthData() {
  return new Promise((resolve, reject) => {
    const req = http.get('http://localhost:3000/health', (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const healthData = JSON.parse(data);
          resolve(healthData);
        } catch (error) {
          reject(error);
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });
  });
}

function formatBytes(bytes) {
  return Math.round(bytes / 1024 / 1024) + 'MB';
}

function formatUptime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  return `${hours}h ${minutes}m ${secs}s`;
}

async function monitorMemory() {
  try {
    const health = await getHealthData();
    const memory = health.system?.memory;
    const uptime = health.system?.uptime;
    
    if (!memory) {
      console.log('❌ 无法获取内存信息');
      return;
    }
    
    const timestamp = new Date().toLocaleTimeString();
    const usageRatio = Math.round((memory.heapUsed / memory.heapTotal) * 100);
    
    // 根据内存使用率显示不同的状态
    let status = '✅';
    if (usageRatio > 90) status = '🔴';
    else if (usageRatio > 80) status = '🟡';
    else if (usageRatio > 70) status = '🟠';
    
    console.log(`${status} [${timestamp}] 内存: ${formatBytes(memory.heapUsed)}/${formatBytes(memory.heapTotal)} (${usageRatio}%) | 运行时间: ${formatUptime(uptime)} | RSS: ${formatBytes(memory.rss)}`);
    
    // 显示详细信息（如果内存使用率高）
    if (usageRatio > 80) {
      console.log(`   📊 详细信息: External: ${formatBytes(memory.external)} | ArrayBuffers: ${formatBytes(memory.arrayBuffers || 0)}`);
      
      if (health.cache) {
        console.log(`   💾 缓存: ${health.cache.totalEntries} 条目 | ${Math.round(health.cache.memoryUsage / 1024)}KB`);
      }
      
      if (health.database) {
        console.log(`   🗄️  数据库: ${health.database.totalConnections} 连接 | ${health.database.freeConnections} 空闲`);
      }
    }
    
  } catch (error) {
    console.log(`❌ [${new Date().toLocaleTimeString()}] 监控失败: ${error.message}`);
  }
}

// 显示启动信息
console.log('🚀 内存监控器启动');
console.log('📊 每10秒检查一次内存使用情况');
console.log('🔴 >90% | 🟡 >80% | 🟠 >70% | ✅ <70%');
console.log('按 Ctrl+C 停止监控\n');

// 立即执行一次
monitorMemory();

// 每10秒监控一次
const interval = setInterval(monitorMemory, 10000);

// 优雅退出
process.on('SIGINT', () => {
  console.log('\n🛑 停止内存监控');
  clearInterval(interval);
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 停止内存监控');
  clearInterval(interval);
  process.exit(0);
});
