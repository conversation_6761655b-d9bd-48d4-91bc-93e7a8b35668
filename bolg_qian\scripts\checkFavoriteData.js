import axios from 'axios';

const BASE_URL = 'http://localhost:3000';

// 获取新的token（需要先登录）
async function login() {
  try {
    const loginResponse = await axios.post(`${BASE_URL}/users/login`, {
      username: 'lion',
      password: '123456'
    });
    
    if (loginResponse.data.code === 200) {
      return loginResponse.data.data.token;
    } else {
      throw new Error('登录失败: ' + loginResponse.data.message);
    }
  } catch (error) {
    console.error('登录失败:', error.response?.data || error.message);
    throw error;
  }
}

async function checkFavoriteData() {
  try {
    console.log('🚀 检查收藏数据...\n');

    // 1. 先登录获取token
    console.log('1. 登录获取token...');
    const token = await login();
    console.log('✅ 登录成功，token:', token.substring(0, 50) + '...');

    const headers = { Authorization: `Bearer ${token}` };

    // 2. 检查收藏历史API
    console.log('\n2. 检查收藏历史API...');
    try {
      const historyResponse = await axios.get(`${BASE_URL}/article-favorites/history`, {
        headers,
        params: {
          page: 1,
          limit: 10
        }
      });
      console.log('收藏历史API响应:', JSON.stringify(historyResponse.data, null, 2));
    } catch (error) {
      console.error('❌ 收藏历史API失败:', error.response?.data || error.message);
    }

    // 3. 测试收藏一篇文章
    console.log('\n3. 测试收藏文章...');
    const testArticleId = 39;
    try {
      const toggleResponse = await axios.post(`${BASE_URL}/article-favorites/toggle`, {
        article_id: testArticleId
      }, { headers });
      console.log('收藏操作响应:', JSON.stringify(toggleResponse.data, null, 2));
    } catch (error) {
      console.error('❌ 收藏操作失败:', error.response?.data || error.message);
    }

    // 4. 再次检查收藏历史
    console.log('\n4. 再次检查收藏历史...');
    try {
      const historyResponse2 = await axios.get(`${BASE_URL}/article-favorites/history`, {
        headers,
        params: {
          page: 1,
          limit: 10
        }
      });
      console.log('更新后的收藏历史:', JSON.stringify(historyResponse2.data, null, 2));
    } catch (error) {
      console.error('❌ 收藏历史API失败:', error.response?.data || error.message);
    }

    console.log('\n✅ 检查完成!');

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  }
}

checkFavoriteData();
