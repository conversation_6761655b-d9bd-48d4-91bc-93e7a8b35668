// composables/useImagePreview.ts
import { ref } from 'vue';

export function useImagePreview() {
  const previewVisible = ref(false);
  const previewSrc = ref('');

  const showPreview = (src: string) => {
    previewSrc.value = src;
    previewVisible.value = true;
  };

  const closePreview = () => {
    previewVisible.value = false;
    previewSrc.value = '';
  };

  return {
    previewVisible,
    previewSrc,
    showPreview,
    closePreview,
  };
}
