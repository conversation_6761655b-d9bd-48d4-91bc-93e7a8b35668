const db = require("../db");

// 检查文章是否存在
async function checkArticleExists(article_id) {
  const sql = 'SELECT id FROM articles WHERE id = ? AND is_deleted = 0';
  const result = await db.query(sql, [article_id]);
  return result.length > 0;
}

// 检查用户是否已收藏文章（登录用户）
async function checkUserFavorite(user_id, article_id) {
  const sql = 'SELECT * FROM article_favorites WHERE user_id = ? AND article_id = ?';
  const result = await db.query(sql, [user_id, article_id]);
  return result.length > 0 ? result[0] : null;
}

// 检查匿名用户是否已收藏文章
async function checkAnonymousFavorite(ip_address, session_id, article_id) {
  const sql = 'SELECT * FROM article_favorites WHERE ip_address = ? AND session_id = ? AND article_id = ?';
  const result = await db.query(sql, [ip_address, session_id, article_id]);
  return result.length > 0 ? result[0] : null;
}

// 删除收藏记录
async function removeFavorite(favoriteId) {
  const sql = 'DELETE FROM article_favorites WHERE id = ?';
  const result = await db.query(sql, [favoriteId]);
  return result;
}

// 添加收藏记录
async function addFavorite(user_id, article_id, ip_address, user_agent, session_id) {
  const sql = `
    INSERT INTO article_favorites
    (user_id, article_id, ip_address, user_agent, session_id)
    VALUES (?, ?, ?, ?, ?)
  `;
  const result = await db.query(sql, [user_id, article_id, ip_address, user_agent, session_id]);
  return result;
}

// 获取用户收藏状态（登录用户）
async function getUserFavoriteStatus(user_id, articleId) {
  const sql = 'SELECT id FROM article_favorites WHERE user_id = ? AND article_id = ?';
  const result = await db.query(sql, [user_id, articleId]);
  return result.length > 0;
}

// 获取匿名用户收藏状态
async function getAnonymousFavoriteStatus(ip_address, session_id, articleId) {
  const sql = 'SELECT id FROM article_favorites WHERE ip_address = ? AND session_id = ? AND article_id = ?';
  const result = await db.query(sql, [ip_address, session_id, articleId]);
  return result.length > 0;
}

// 获取文章的收藏用户列表
async function getArticleFavoriteUsers(articleId, limit, offset) {
  const sql = `
    SELECT af.created_at, u.id, u.username, u.avatar
    FROM article_favorites af
    LEFT JOIN users u ON af.user_id = u.id
    WHERE af.article_id = ? AND af.user_id IS NOT NULL
    ORDER BY af.created_at DESC
    LIMIT ? OFFSET ?
  `;
  const result = await db.query(sql, [articleId, parseInt(limit), offset]);
  return result;
}

// 获取文章收藏用户总数
async function getArticleFavoriteUsersCount(articleId) {
  const sql = 'SELECT COUNT(*) as total FROM article_favorites WHERE article_id = ? AND user_id IS NOT NULL';
  const result = await db.query(sql, [articleId]);
  return result[0]?.total || 0;
}

// 获取用户收藏历史
async function getUserFavoriteHistory(user_id, limit, offset) {
  const sql = `
    SELECT af.*, a.title, a.summary, a.cover_image, a.user_id as author_id,
           u.username as author_name, u.avatar as author_avatar
    FROM article_favorites af
    LEFT JOIN articles a ON af.article_id = a.id
    LEFT JOIN users u ON a.user_id = u.id
    WHERE af.user_id = ? AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
    ORDER BY af.created_at DESC
    LIMIT ? OFFSET ?
  `;
  const result = await db.query(sql, [user_id, parseInt(limit), offset]);
  return result;
}

// 获取用户收藏历史总数
async function getUserFavoriteHistoryCount(user_id) {
  const sql = `
    SELECT COUNT(*) as total FROM article_favorites af
    LEFT JOIN articles a ON af.article_id = a.id
    WHERE af.user_id = ? AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
  `;
  const result = await db.query(sql, [user_id]);
  return result[0]?.total || 0;
}

// 获取热门收藏文章
async function getPopularFavoriteArticles(limit) {
  const sql = `
    SELECT a.id, a.title, a.summary, a.cover_image, a.favorites_count,
           u.username as author_name, u.avatar as author_avatar
    FROM articles a
    LEFT JOIN users u ON a.user_id = u.id
    WHERE a.is_deleted = 0 AND a.favorites_count > 0
    ORDER BY a.favorites_count DESC, a.created_at DESC
    LIMIT ?
  `;
  const result = await db.query(sql, [parseInt(limit)]);
  return result;
}

// 获取文章收藏统计
async function getArticleFavoriteStats(articleId) {
  const sql = `
    SELECT COUNT(*) as favorites_count
    FROM article_favorites
    WHERE article_id = ?
  `;
  const result = await db.query(sql, [articleId]);
  return result[0]?.favorites_count || 0;
}

// 更新文章收藏数量
async function updateArticleFavoriteCount(articleId) {
  const sql = `
    UPDATE articles
    SET favorites_count = (
      SELECT COUNT(*) FROM article_favorites WHERE article_id = ?
    )
    WHERE id = ?
  `;
  const result = await db.query(sql, [articleId, articleId]);
  return result;
}

module.exports = {
  checkArticleExists,
  checkUserFavorite,
  checkAnonymousFavorite,
  removeFavorite,
  addFavorite,
  getUserFavoriteStatus,
  getAnonymousFavoriteStatus,
  getArticleFavoriteUsers,
  getArticleFavoriteUsersCount,
  getUserFavoriteHistory,
  getUserFavoriteHistoryCount,
  getPopularFavoriteArticles,
  getArticleFavoriteStats,
  updateArticleFavoriteCount,
};
