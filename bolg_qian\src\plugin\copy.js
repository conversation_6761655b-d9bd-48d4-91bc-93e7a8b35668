import { ElMessage } from "element-plus";

// 通用复制函数，兼容移动端和桌面端
export function copyText(text, successMsg = "复制成功！", failMsg = "复制失败，请手动长按复制~") {
  if (navigator.clipboard && window.isSecureContext) {
    navigator.clipboard.writeText(text)
      .then(() => {
        ElMessage.success(successMsg);
      })
      .catch(() => {
        fallbackCopyText(text, successMsg, failMsg);
      });
  } else {
    fallbackCopyText(text, successMsg, failMsg);
  }
}

function fallbackCopyText(text, successMsg, failMsg) {
  const textarea = document.createElement("textarea");
  textarea.value = text;
  textarea.setAttribute("readonly", "");
  textarea.style.position = "absolute";
  textarea.style.left = "0";
  textarea.style.top = "0";
  textarea.style.opacity = "0.01";
  textarea.style.zIndex = "-1";
  document.body.appendChild(textarea);

  textarea.focus();
  textarea.select();
  textarea.setSelectionRange(0, text.length);

  try {
    const successful = document.execCommand("copy");
    if (successful) {
      ElMessage.success(successMsg);
    } else {
      ElMessage.warning(failMsg);
    }
  } catch (err) {
    ElMessage.error(failMsg);
  }
  document.body.removeChild(textarea);
}