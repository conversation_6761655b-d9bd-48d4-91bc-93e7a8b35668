const Router = require("koa-router");
const redisCache = require("../../utils/redisCache");
const logger = require("../../plugin/logger");

const router = new Router();

// 获取缓存统计信息
router.get("/stats", async (ctx) => {
  try {
    const stats = await redisCache.getStats();
    
    ctx.body = {
      success: true,
      data: {
        redis: stats,
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString()
      }
    };
  } catch (error) {
    logger.error("获取缓存统计失败:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: "获取缓存统计失败"
    };
  }
});

// 清空所有缓存
router.delete("/clear", async (ctx) => {
  try {
    const result = await redisCache.flushAll();
    
    if (result) {
      logger.info("缓存已清空", { userId: ctx.state.user?.id });
      ctx.body = {
        success: true,
        message: "缓存已清空"
      };
    } else {
      ctx.body = {
        success: false,
        message: "清空缓存失败"
      };
    }
  } catch (error) {
    logger.error("清空缓存失败:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: "清空缓存失败"
    };
  }
});

// 删除指定缓存
router.delete("/key/:key", async (ctx) => {
  try {
    const { key } = ctx.params;
    const result = await redisCache.del(key);
    
    if (result) {
      logger.info(`缓存键已删除: ${key}`, { userId: ctx.state.user?.id });
      ctx.body = {
        success: true,
        message: `缓存键 ${key} 已删除`
      };
    } else {
      ctx.body = {
        success: false,
        message: "删除缓存键失败"
      };
    }
  } catch (error) {
    logger.error("删除缓存键失败:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: "删除缓存键失败"
    };
  }
});

// 检查缓存键是否存在
router.get("/exists/:key", async (ctx) => {
  try {
    const { key } = ctx.params;
    const exists = await redisCache.exists(key);
    
    ctx.body = {
      success: true,
      data: {
        key,
        exists
      }
    };
  } catch (error) {
    logger.error("检查缓存键失败:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: "检查缓存键失败"
    };
  }
});

// 获取缓存值
router.get("/get/:key", async (ctx) => {
  try {
    const { key } = ctx.params;
    const value = await redisCache.get(key);
    
    ctx.body = {
      success: true,
      data: {
        key,
        value,
        found: value !== null
      }
    };
  } catch (error) {
    logger.error("获取缓存值失败:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: "获取缓存值失败"
    };
  }
});

// 设置缓存值
router.post("/set", async (ctx) => {
  try {
    const { key, value, ttl = 300 } = ctx.request.body;
    
    if (!key || value === undefined) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        message: "缺少必要参数: key 和 value"
      };
      return;
    }
    
    const result = await redisCache.set(key, value, ttl);
    
    if (result) {
      logger.info(`缓存已设置: ${key}`, { userId: ctx.state.user?.id, ttl });
      ctx.body = {
        success: true,
        message: `缓存 ${key} 已设置，TTL: ${ttl}秒`
      };
    } else {
      ctx.body = {
        success: false,
        message: "设置缓存失败"
      };
    }
  } catch (error) {
    logger.error("设置缓存失败:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: "设置缓存失败"
    };
  }
});

// 测试Redis连接
router.get("/test", async (ctx) => {
  try {
    const testKey = "test_connection";
    const testValue = { timestamp: Date.now(), test: true };

    // 检查Redis是否可用
    if (!redisCache.isAvailable()) {
      // 尝试手动初始化
      const initSuccess = await redisCache.manualInit();
      if (!initSuccess) {
        throw new Error("Redis未连接且初始化失败");
      }
    }

    // 设置测试值
    const setResult = await redisCache.set(testKey, testValue, 10);
    if (!setResult) {
      throw new Error("设置测试值失败");
    }

    // 获取测试值
    const getValue = await redisCache.get(testKey);
    if (!getValue || getValue.test !== true) {
      throw new Error("获取测试值失败");
    }

    // 删除测试值
    await redisCache.del(testKey);

    // 获取Redis统计信息
    const stats = await redisCache.getStats();

    ctx.body = {
      success: true,
      message: "Redis连接测试成功",
      data: {
        connected: redisCache.isAvailable(),
        testValue: getValue,
        stats: stats
      }
    };
  } catch (error) {
    logger.error("Redis连接测试失败:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: "Redis连接测试失败: " + error.message,
      data: {
        connected: redisCache.isAvailable()
      }
    };
  }
});

// Redis状态检查
router.get("/status", async (ctx) => {
  try {
    const stats = await redisCache.getStats();

    ctx.body = {
      success: true,
      data: {
        available: redisCache.isAvailable(),
        config: {
          enabled: redisCache.config.enabled,
          host: redisCache.config.host,
          port: redisCache.config.port,
          db: redisCache.config.db,
          keyPrefix: redisCache.config.keyPrefix
        },
        stats: stats
      }
    };
  } catch (error) {
    logger.error("获取Redis状态失败:", error);
    ctx.body = {
      success: false,
      message: "获取Redis状态失败: " + error.message,
      data: {
        available: redisCache.isAvailable()
      }
    };
  }
});

module.exports = router;
