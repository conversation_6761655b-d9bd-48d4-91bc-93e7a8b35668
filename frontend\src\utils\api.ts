// 导入
import request from "./index";

// ================= 用户相关 =================
export const loginApi = (params: { username: string; password: string }) =>
  request.post("/user/login", params);

export const registerApi = (params: {
  username: string;
  email: string;
  password: string;
}) => request.post("/user/register", params);

export const logoutApi = () => request.post("/user/logout");

export const GetProfileApi = (params: any) =>
  request.post("/user/profile", params);

export const UpdateProfileApi = (formData: FormData) =>
  request.post("/user/updateUser", formData);

// ================= 文章相关 =================
export const ShowArticlesApi = (params: any) =>
  request.post("/articles/pages", params);

export const ShowArticlesByPageApi = (params: any) =>
  request.post("/articles/pagesNumber", params);

export const AddArticleApi = (params: any) =>
  request.post("/articles/add", params);

export const UpdateArticleApi = (params: any) =>
  request.post("/articles/update", params);

export const DeleteArticleApi = (params: any) =>
  request.post("/articles/delete", params);

export const FakeDeleteArticleApi = (params: any) =>
  request.post("/articles/fakeDelete", params);

export const PublishArticleApi = (formData: FormData) =>
  request.post("/articles/add", formData);

export const GetArticleByIdApi = (params: any) =>
  request.post("/articles/detail", params);

export const CommentArticleApi = (params: any) =>
  request.post("/articles/comments", params);

export const SubmitCommentApi = (params: any) =>
  request.post("/articles/addComment", params);

export const ShareArticleApi = (id: number, is_share: number) =>
  request.post("/articles/share", { id, is_share });

// 获取所有共享文章
export const GetSharedArticlesApi = (params: any) =>
  request.get("/articles/shared", params);

// 恢复已删除文章
export const RestoreArticleApi = (params: any) =>
  request.post("/articles/restore", params);
// 获取已删除文章
export const DeleteRestoredArticleApi = (params: any) =>
  request.post("/articles/deletedByUser", params);

// ================= 日志相关 =================
export const ShowLogsApi = (params: { page: number; limit: number }) =>
  request.get("/logs/showlogs", { params });

// ================= 上传相关 =================
export const UploadImageApi = (formData: FormData) =>
  request.post("/upload/updateAvatar", formData);

export const UploadApi = (formData: FormData, config = {}) =>
  request.post("/upload/uploads", formData, config);

export const UploadChunkApi = (formData: FormData, config = {}) =>
  request.post("/upload/uploads/chunk", formData, config);

export const MergeChunksApi = (data: any) =>
  request.post("/upload/uploads/merge", data);

// ================= 媒体相关 =================
export const GetMediaApi = (params: { filename: string }) =>
  request.get(`/media/${params.filename}`);

export const GetMediaListApi = () => request.get("/media/list");

// 获取视频封面及文件列表
export const GetMediaCoverListApi = () => request.get("/media/listpic");

// ================= 墙相关 =================
export const ShowWallApi = (params: any) =>
  request.post("/showwall/images", params);

// ================= 标签相关 =================
export const GetTagsApi = () => request.get("/user/tech-tags");

// ================= 导出SQL相关 =================
export const ExportSqlApi = () =>
  request.get("/daosql/export/download", { responseType: "blob" });

// ================= 加密相关 =================
export const EncryptImagesApi = (params: any) =>
  request.post("/jiami/images", params);

export const EncryptVideosApi = (params: any) =>
  request.post("/jiamivideos/videos", params);

// 新增：单文件加密解密接口
export const EncryptSingleVideoApi = (params: any) =>
  request.post("/jiamivideos/video1", params);

// ================= 大模型相关 =================
export const ChatApi = (params: any) =>
  request.post("/bigmodel/chatai", params);

// ================= 好友相关 =================
export const SearchFriendApi = (q: string) =>
  request.get("/chat/search", { params: { q } });



export const GetFriendsApi = (params: any) =>
  request.post("/chat/friends", params);

// ================= 资源相关 ================= GetResourceListApi, DownloadResourceApi
export const GetResourceListApi = () => request.get("/resource/list");

export const DownloadResourceApi = (filename: string) =>
  request.get(`/resource/${filename}`, { responseType: "blob" });

// 预览
export const PreviewResourceApi = (filename: string) =>
  request.get(`/resource/prew/${filename}`);

// 搜索用户
export const SearchUserApi = (username: string) =>
  request.post("/friend/search", { username });

// 添加好友
export const AddFriendApi = (user_id: number, friend_id: number) =>
  request.post("/friend/add", { user_id, friend_id });

// 获取好友列表
export const GetFriendListApi = (user_id: number) =>
  request.post("/friend/list", { user_id });

// 发送聊天记录
export const SaveChatRecordApi = (user_id: number, friend_id: number, message: string) =>
  request.post("/friend/chat", { user_id, friend_id, message });

// 获取聊天记录
export const GetChatRecordApi = (user_id: number, friend_id: number) =>
  request.post("/friend/chatList", { user_id, friend_id });

// 发送好友请求（需要对方同意）
export const SendFriendRequestApi = (from_user_id: number, to_user_id: number) =>
  request.post("/friend/add", { user_id: from_user_id, friend_id: to_user_id });

// 查询待处理的好友请求
export const GetPendingFriendRequestsApi = (user_id: number) =>
  request.post("/friend/pendingRequest", { user_id });

// 处理好友请求（同意/拒绝）
export const HandleFriendRequestApi = (request_id: number, agree: boolean) =>
  request.post("/friend/handleRequest", { request_id, agree });

// bug
export const GetBugListApi = () => request.post("/bugsend/list");

export const AddBugApi = (params: any) =>
  request.post("/bugsend/add", params);

export const UpdateBugStatusApi = (id: number, status: number, severity: string) =>
  request.post("/bugsend/updateStatus", { id, status, severity });

// 删除 bug
export const DeleteBugApi = (id: number) =>
  request.post("/bugsend/delete", { id });
// 批量删除
export const DeleteBugsApi = (ids: number[]) =>
  request.post("/bugsend/deleteBatch", { ids });
// 
export const UpdateBugApi = (params: any) =>
  request.post("/bugsend/update", params);

// ================= 创意记录相关 =================

// 获取创意列表
export const GetCreativeListApi = () =>
  request.post("/creative/list");

// 新增创意
export const AddCreativeApi = (params: any) =>
  request.post("/creative/add", params);

// 删除创意
export const DeleteCreativeApi = (id: number) =>
  request.post("/creative/delete", { id });
