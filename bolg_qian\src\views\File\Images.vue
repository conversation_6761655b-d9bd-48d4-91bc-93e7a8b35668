<template>
  <div class="photo-wall" @scroll.passive="onScroll" ref="container">
    <div class="photo-list">
      <div v-for="(img, index) in images" :key="index" class="photo-item">
        <!-- 使用stream加载的图片 -->
        <img
          v-if="!img.endsWith('.enc') && imageBlobUrls.has(img)"
          :src="imageBlobUrls.get(img)"
          alt="photo"
          class="lazy-img"
          @load="onImageLoad(img)"
          @error="onImageError(img)"
        />
        <!-- 原始懒加载图片 -->
        <img
          v-else-if="!img.endsWith('.enc')"
          :data-src="img"
          alt="photo"
          class="lazy-img"
        />
        <div v-else class="encrypted-placeholder">
          <span class="encrypted-text">已加密</span>
        </div>
        <div
          class="zoom-icon"
          @click="showPreview(img)"
          title="点击放大"
        >
          🔍
        </div>
      </div>
    </div>
    <div v-if="loading" class="loading">加载中...</div>
    <div v-if="!loadingMore && !loading" class="no-more">没有更多图片了</div>
    <div v-if="!images.length && !loading" class="no-images">暂无图片</div>

    <!-- 放大预览遮罩 -->
    <div v-if="previewVisible" class="preview-mask" @click="closePreview">
      <img :src="previewSrc" class="preview-image" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from "vue";
import { ShowWallApi } from "../../utils/api";
import { useImagePreview } from "../../composables/useImagePreview";
import { throttle } from "lodash";
import { 
  getImageBlobUrl, 
  preloadImages, 
  revokeBlobUrl, 
  revokeBlobUrls,
  revokeImageMap
} from "../../utils/imageStream";

const images = ref([]);
const page = ref(1);
const limit = 20;
const loading = ref(false);
const loadingMore = ref(true);
const container = ref(null);
const imageBlobUrls = ref(new Map()); // 存储图片URL到blob URL的映射
const loadedBlobUrls = ref([]); // 存储已加载的blob URL用于清理

const { previewVisible, previewSrc, showPreview, closePreview } =
  useImagePreview();

// 懒加载 IntersectionObserver
let observer = null;
const initLazyObserver = () => {
  observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target;
        if (img.dataset.src) {
          // 使用stream加载图片
          loadImageWithStream(img.dataset.src).then(blobUrl => {
            if (blobUrl) {
              img.src = blobUrl;
              imageBlobUrls.value.set(img.dataset.src, blobUrl);
              loadedBlobUrls.value.push(blobUrl);
            }
          }).catch(() => {
            // 如果stream加载失败，回退到原始方式
            img.src = img.dataset.src;
          });
        }
        observer.unobserve(img);
      }
    });
  });
  document
    .querySelectorAll(".lazy-img")
    .forEach((img) => observer.observe(img));
};

// 使用stream加载单张图片
const loadImageWithStream = async (imageUrl) => {
  try {
    const blobUrl = await getImageBlobUrl(imageUrl);
    return blobUrl;
  } catch (error) {
    console.error('Stream加载图片失败:', error);
    return null;
  }
};

// 预加载图片流
const preloadImageStreams = async (imageUrls) => {
  try {
    const newImageMap = await preloadImages(imageUrls);
    // 合并新的图片映射
    newImageMap.forEach((blobUrl, originalUrl) => {
      imageBlobUrls.value.set(originalUrl, blobUrl);
      loadedBlobUrls.value.push(blobUrl);
    });
  } catch (error) {
    console.error('预加载图片流失败:', error);
  }
};

// 图片加载成功回调
const onImageLoad = (imageUrl) => {
  console.log('图片加载成功:', imageUrl);
};

// 图片加载失败回调
const onImageError = (imageUrl) => {
  console.error('图片加载失败:', imageUrl);
  // 移除失败的blob URL
  const blobUrl = imageBlobUrls.value.get(imageUrl);
  if (blobUrl) {
    revokeBlobUrl(blobUrl);
    imageBlobUrls.value.delete(imageUrl);
  }
};

const fetchMoreImages = async () => {
  if (!loadingMore.value || loading.value) return;
  loading.value = true;
  try {
    const res = await ShowWallApi({ page: page.value, limit });
    console.log("获取图片列表", res);
    if (res.length) {
      images.value.push(...res);
      page.value++;
      
      // 预加载新获取的图片
      const newImageUrls = res.filter(img => !img.endsWith('.enc'));
      if (newImageUrls.length > 0) {
        preloadImageStreams(newImageUrls);
      }
      
      await nextTick();
      initLazyObserver(); // 初始化懒加载
    } else {
      loadingMore.value = false;
    }
  } catch (error) {
    console.error("获取图片列表失败", error);
  } finally {
    loading.value = false;
  }
};

// 滚动节流处理
const onScroll = throttle(() => {
  if (!container.value) return;
  const scrollBottom =
    container.value.scrollHeight -
    container.value.scrollTop -
    container.value.clientHeight;
  if (scrollBottom < 150) fetchMoreImages();
}, 200);

onMounted(() => {
  fetchMoreImages();
});

// 组件卸载时清理blob URL
onUnmounted(() => {
  if (observer) {
    observer.disconnect();
  }
  // 清理所有已加载的blob URL
  revokeImageMap(imageBlobUrls.value);
  revokeBlobUrls(loadedBlobUrls.value);
});
</script>

<style scoped>
.photo-wall {
  height: 100vh;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #ddd;
  position: relative;
  background: #f7f7f7;
}

.photo-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.photo-item {
  margin-bottom: 12px;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgb(0 0 0 / 0.1);
  background: #fff;
  position: relative;
  aspect-ratio: 1/1; /* 确保每个图片项保持 1:1 的宽高比 */
}

.photo-item img {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover; /* 确保图片覆盖整个容器 */
  border-radius: 6px;
}

/* 放大图标 */
.zoom-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.4);
  color: white;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  font-size: 18px;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.3s;
}
.zoom-icon:hover {
  background: rgba(0, 0, 0, 0.7);
}

/* 加密占位符 */
.encrypted-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #eee;
  border-radius: 6px;
  font-size: 16px;
  color: #999;
  position: relative;
}

.encrypted-text {
  font-weight: bold;
}

/* 加载中、没有更多、暂无图片 */
.loading,
.no-more,
.no-images {
  text-align: center;
  padding: 10px 0;
  color: #666;
}

/* 放大预览遮罩 */
.preview-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  cursor: zoom-out;
}
.preview-image {
  max-width: 90vw;
  max-height: 90vh;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}
</style>
