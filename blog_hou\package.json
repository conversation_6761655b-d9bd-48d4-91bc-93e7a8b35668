{"name": "koa2-backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "nodemon index.js", "dev": "nodemon index.js", "clear-db": "node scripts/clearDatabase.js", "clear-db-safe": "node scripts/clearDatabaseSafe.js"}, "dependencies": {"@koa/cors": "^3.0.0", "@koa/multer": "^3.0.2", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "fs-path": "^0.0.25", "jsonwebtoken": "^9.0.2", "koa": "^2.13.0", "koa-body": "^4.2.0", "koa-bodyparser": "^4.4.1", "koa-compress": "^5.1.1", "koa-helmet": "^8.0.1", "koa-jwt": "^4.0.4", "koa-mount": "^4.2.0", "koa-router": "^10.0.0", "koa-send": "^5.0.1", "koa-static": "^5.0.0", "koa2-swagger-ui": "^5.11.0", "lodash": "^4.17.21", "log4js": "^6.9.1", "mime": "^4.0.6", "mime-types": "^3.0.1", "mysql": "^2.18.1", "nodemon": "^3.1.7", "socket.io": "^4.8.1", "stream-throttle": "^0.1.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-koa": "^0.0.1", "ua-parser-js": "^2.0.4", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.18.0"}, "devDependencies": {"dotenv": "^16.6.1", "nodemon": "^2.0.0"}}