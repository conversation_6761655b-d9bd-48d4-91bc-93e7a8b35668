<template>
  <div class="p-4">
    <el-card shadow="always">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-bold">🎞️ 媒体播放列表</span>
          <el-button @click="$router.back()">返回</el-button>
        </div>
      </template>

      <!-- 搜索框 -->
      <el-input
        v-model="search"
        placeholder="搜索媒体文件..."
        class="mb-4"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>

      <!-- 内容加载骨架屏 -->
      <el-skeleton :loading="loading" animated>
        <el-row :gutter="20">
          <el-col
            v-for="file in filteredList"
            :key="file.file_name"
            :xs="24"
            :sm="12"
            :md="8"
          >
            <el-card class="mb-4" shadow="hover">
              <p class="truncate text-sm mb-2" :title="file.file_name">
                {{ file.file_name }}
              </p>

              <!-- 封面图 or 播放器 -->
              <div v-if="!playing[file.file_name]" class="media-cover" @click="play(file.file_name)">
                <img
                  :src="file.cover"
                  class="w-full rounded object-cover"
                  style="max-height: 200px; cursor: pointer"
                  :alt="file.file_name"
                />
                <div class="play-icon">
                  <el-icon :size="48" color="#fff"><VideoPlay /></el-icon>
                </div>
              </div>

              <video
                v-else-if="isVideo(file.file_name)"
                :src="getMediaUrl(file.file_name)"
                controls
                autoplay
                preload="auto"
                controlsList="nodownload"
                class="w-full rounded"
                @error="onError(file.file_name)"
                @play="handlePlay"
              />
              <audio
                v-else
                :src="getMediaUrl(file.file_name)"
                controls
                preload="metadata"
                class="w-full"
                @error="onError(file.file_name)"
                @play="handlePlay"
              />
            </el-card>
          </el-col>
        </el-row>
      </el-skeleton>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { GetMediaCoverListApi } from '../utils/api'
import { ElMessage } from 'element-plus'
import { Search, VideoPlay } from '@element-plus/icons-vue'

const mediaList = ref<any[]>([])
const loading = ref(true)
const search = ref('')
const currentMedia = ref<HTMLMediaElement | null>(null)
const playing = ref<Record<string, boolean>>({})

// 拉取媒体列表
const fetchMediaList = async () => {
  try {
    const res = await GetMediaCoverListApi()
    mediaList.value = Array.isArray(res.data?.media_list) ? res.data.media_list : []
  } catch (err) {
    console.error(err)
    ElMessage.error('媒体文件获取失败')
  } finally {
    loading.value = false
  }
}

const getMediaUrl = (filename: string) => {
  return `/media/videos/${encodeURIComponent(filename.trim())}`
}

const filteredList = computed(() =>
  mediaList.value.filter((file) =>
    file.file_name.toLowerCase().includes(search.value.toLowerCase())
  )
)

const isVideo = (filename: string) => /\.(mp4|webm|mov)$/i.test(filename)

const handlePlay = (e: Event) => {
  const target = e.target as HTMLMediaElement
  if (currentMedia.value && currentMedia.value !== target) {
    currentMedia.value.pause()
  }
  currentMedia.value = target
}

const play = (filename: string) => {
  playing.value = { [filename]: true }
}

const onError = (filename: string) => {
  console.error(`媒体加载失败：${filename}`)
}

onMounted(fetchMediaList)
</script>

<style scoped>
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.media-cover {
  position: relative;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}
</style>
