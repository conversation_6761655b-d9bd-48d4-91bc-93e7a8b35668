<template>
  <div class="download-test-container">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon class="title-icon"><Download /></el-icon>
        下载测试
      </h1>
      <p class="page-subtitle">测试文件下载功能和网络连接速度</p>
    </div>

    <div class="test-content">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="test-card">
            <template #header>
              <div class="card-header">
                <el-icon><Timer /></el-icon>
                <span>速度测试</span>
              </div>
            </template>
            <div class="speed-test">
              <div class="test-item">
                <span class="label">下载速度:</span>
                <span class="value">{{ downloadSpeed }}</span>
              </div>
              <div class="test-item">
                <span class="label">上传速度:</span>
                <span class="value">{{ uploadSpeed }}</span>
              </div>
              <div class="test-item">
                <span class="label">延迟:</span>
                <span class="value">{{ latency }}</span>
              </div>
              <el-button type="primary" @click="runSpeedTest" :loading="speedTesting" style="margin-top: 15px;">
                <el-icon><Refresh /></el-icon>
                开始测速
              </el-button>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="test-card">
            <template #header>
              <div class="card-header">
                <el-icon><Document /></el-icon>
                <span>文件下载测试</span>
              </div>
            </template>
            <div class="download-test">
              <el-select v-model="selectedTestFile" placeholder="选择测试文件" style="width: 100%; margin-bottom: 15px;">
                <el-option
                  v-for="file in testFiles"
                  :key="file.value"
                  :label="file.label"
                  :value="file.value"
                />
              </el-select>
              <el-button type="success" @click="downloadTestFile" :loading="downloading" style="width: 100%;">
                <el-icon><Download /></el-icon>
                下载测试文件
              </el-button>
              <div v-if="downloadProgress > 0" class="progress-container">
                <el-progress :percentage="downloadProgress" :status="downloadStatus" />
                <div class="progress-info">
                  <span>{{ downloadedSize }} / {{ totalSize }}</span>
                  <span>{{ currentDownloadSpeed }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card class="test-card">
            <template #header>
              <div class="card-header">
                <el-icon><List /></el-icon>
                <span>测试历史</span>
                <el-button size="small" @click="clearHistory" style="margin-left: auto;">
                  清空历史
                </el-button>
              </div>
            </template>
            <el-table :data="testHistory" style="width: 100%">
              <el-table-column prop="time" label="时间" width="180" />
              <el-table-column prop="type" label="测试类型" width="120" />
              <el-table-column prop="file" label="文件" />
              <el-table-column prop="size" label="大小" width="100" />
              <el-table-column prop="speed" label="速度" width="120" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status === '成功' ? 'success' : 'danger'">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Download,
  Timer,
  Document,
  List,
  Refresh
} from '@element-plus/icons-vue';

// 响应式数据
const downloadSpeed = ref('-- MB/s');
const uploadSpeed = ref('-- MB/s');
const latency = ref('-- ms');
const speedTesting = ref(false);
const downloading = ref(false);
const downloadProgress = ref(0);
const downloadStatus = ref<'success' | 'exception' | undefined>(undefined);
const downloadedSize = ref('0 MB');
const totalSize = ref('0 MB');
const currentDownloadSpeed = ref('0 MB/s');
const selectedTestFile = ref('small');

// 测试文件选项
const testFiles = ref([
  { label: '小文件 (1MB)', value: 'small' },
  { label: '中等文件 (10MB)', value: 'medium' },
  { label: '大文件 (100MB)', value: 'large' },
  { label: '超大文件 (1GB)', value: 'xlarge' }
]);

// 测试历史
const testHistory = ref<Array<{
  time: string;
  type: string;
  file: string;
  size: string;
  speed: string;
  status: string;
}>>([]);

// 速度测试
const runSpeedTest = async () => {
  speedTesting.value = true;
  try {
    // 模拟延迟测试
    const startTime = Date.now();
    await fetch('/api/ping', { method: 'HEAD' });
    const endTime = Date.now();
    latency.value = `${endTime - startTime} ms`;

    // 模拟下载速度测试
    const downloadStart = Date.now();
    const response = await fetch('/api/speed-test/download');
    const downloadEnd = Date.now();
    const downloadTime = (downloadEnd - downloadStart) / 1000;
    const downloadSpeedValue = (1 / downloadTime).toFixed(2); // 假设1MB测试文件
    downloadSpeed.value = `${downloadSpeedValue} MB/s`;

    // 模拟上传速度测试
    const uploadData = new Blob(['x'.repeat(1024 * 1024)]); // 1MB测试数据
    const uploadStart = Date.now();
    await fetch('/api/speed-test/upload', {
      method: 'POST',
      body: uploadData
    });
    const uploadEnd = Date.now();
    const uploadTime = (uploadEnd - uploadStart) / 1000;
    const uploadSpeedValue = (1 / uploadTime).toFixed(2);
    uploadSpeed.value = `${uploadSpeedValue} MB/s`;

    addTestRecord('速度测试', '网络测试', '1MB', downloadSpeed.value, '成功');
    ElMessage.success('速度测试完成');
  } catch (error) {
    ElMessage.error('速度测试失败');
    addTestRecord('速度测试', '网络测试', '1MB', '0 MB/s', '失败');
  } finally {
    speedTesting.value = false;
  }
};

// 文件下载测试
const downloadTestFile = async () => {
  if (!selectedTestFile.value) {
    ElMessage.warning('请选择测试文件');
    return;
  }

  downloading.value = true;
  downloadProgress.value = 0;
  downloadStatus.value = undefined;

  try {
    const fileInfo = testFiles.value.find(f => f.value === selectedTestFile.value);
    const fileName = fileInfo?.label || '测试文件';
    
    // 模拟文件大小
    const fileSizes = {
      small: 1,
      medium: 10,
      large: 100,
      xlarge: 1024
    };
    const fileSize = fileSizes[selectedTestFile.value as keyof typeof fileSizes];
    totalSize.value = `${fileSize} MB`;

    // 模拟下载进度
    const startTime = Date.now();
    for (let i = 0; i <= 100; i += 10) {
      await new Promise(resolve => setTimeout(resolve, 200));
      downloadProgress.value = i;
      const currentTime = Date.now();
      const elapsed = (currentTime - startTime) / 1000;
      const downloaded = (fileSize * i) / 100;
      downloadedSize.value = `${downloaded.toFixed(1)} MB`;
      currentDownloadSpeed.value = `${(downloaded / elapsed).toFixed(2)} MB/s`;
    }

    downloadStatus.value = 'success';
    addTestRecord('文件下载', fileName, `${fileSize} MB`, currentDownloadSpeed.value, '成功');
    ElMessage.success('文件下载测试完成');
  } catch (error) {
    downloadStatus.value = 'exception';
    ElMessage.error('文件下载测试失败');
    addTestRecord('文件下载', '测试文件', totalSize.value, '0 MB/s', '失败');
  } finally {
    downloading.value = false;
  }
};

// 添加测试记录
const addTestRecord = (type: string, file: string, size: string, speed: string, status: string) => {
  testHistory.value.unshift({
    time: new Date().toLocaleString(),
    type,
    file,
    size,
    speed,
    status
  });
  // 限制历史记录数量
  if (testHistory.value.length > 50) {
    testHistory.value = testHistory.value.slice(0, 50);
  }
};

// 清空历史
const clearHistory = () => {
  testHistory.value = [];
  ElMessage.success('测试历史已清空');
};

onMounted(() => {
  // 初始化时添加一些示例数据
  addTestRecord('速度测试', '网络测试', '1MB', '5.2 MB/s', '成功');
  addTestRecord('文件下载', '小文件 (1MB)', '1MB', '3.8 MB/s', '成功');
});
</script>

<style scoped>
.download-test-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0 0 8px 0;
}

.title-icon {
  margin-right: 8px;
  color: #409eff;
}

.page-subtitle {
  color: #666;
  margin: 0;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
}

.speed-test, .download-test {
  padding: 10px 0;
}

.test-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.label {
  font-weight: bold;
  color: #666;
}

.value {
  color: #333;
  font-weight: bold;
}

.progress-container {
  margin-top: 15px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}
</style>
