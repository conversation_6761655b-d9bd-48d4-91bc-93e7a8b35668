<template>
  <div class="container">
    <!-- 左侧目录树 -->
    <div class="left-pane">
      <el-button type="primary" @click="selectDirectory">选择目录</el-button>
      <el-button @click="$router.back()">返回</el-button>

      <el-tree
        class="file-tree"
        :data="treeData"
        :props="defaultProps"
        node-key="path"
        highlight-current
        :expand-on-click-node="false"
        @node-click="handleNodeClick"
      >
        <template #default="{ data }">
          <span>
            <el-icon style="margin-right: 5px">
              <component :is="data.type === 'directory' ? Folder : Document" />
            </el-icon>
            {{ data.name }}
          </span>
        </template>
      </el-tree>
    </div>

    <!-- 右侧多标签文件内容展示 -->
    <div class="right-pane">
      <el-tabs v-model="activeTab" type="card" closable @tab-remove="closeTab">
        <el-tab-pane
          v-for="tab in openTabs"
          :key="tab.path"
          :label="tab.name"
          :name="tab.path"
        >
          <template v-if="tab.type === 'text'">
            <pre class="code-block">
              <el-button
                size="mini"
                type="primary"
                class="copy-btn"
                @click="copyToClipboard(tab.highlightedContent)"
              >复制</el-button>
              <code v-html="tab.highlightedContent"></code>
            </pre>
          </template>

          <template v-else-if="tab.type === 'image'">
            <img :src="tab.dataUrl" class="preview-image" />
          </template>

          <template v-else>
            <p>无法预览该文件类型。</p>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { Folder, Document } from "@element-plus/icons-vue";
import hljs from "highlight.js/lib/core";
// 需要的语言模块按需引入
import javascript from "highlight.js/lib/languages/javascript";
import json from "highlight.js/lib/languages/json";
import css from "highlight.js/lib/languages/css";
import xml from "highlight.js/lib/languages/xml";
import markdown from "highlight.js/lib/languages/markdown";
import { ElLoading } from 'element-plus';


import "highlight.js/styles/github.css"; // 高亮样式，你可以换其他样式

hljs.registerLanguage("javascript", javascript);
hljs.registerLanguage("json", json);
hljs.registerLanguage("css", css);
hljs.registerLanguage("html", xml);
hljs.registerLanguage("xml", xml);
hljs.registerLanguage("markdown", markdown);

const treeData = ref([]);
const fileHandleMap = ref(new Map());
const defaultProps = { children: "children", label: "name" };

// 标签页管理
const openTabs = ref([]);
const activeTab = ref("");

// 选择目录
const selectDirectory = async () => {
  try {
    const dirHandle = await window.showDirectoryPicker(); // 先让用户选择目录

    // 开始加载数据，显示 loading
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在加载目录内容，请稍候...',
      background: 'rgba(0, 0, 0, 0.3)',
    });

    try {
      const root = {
        name: dirHandle.name,
        path: "",
        type: "directory",
        children: [],
      };
      fileHandleMap.value.clear();
      await readDirectory(dirHandle, "", root.children);
      treeData.value = [root];
      openTabs.value = [];
      activeTab.value = "";
    } finally {
      // 关闭 loading（无论成功或失败）
      loadingInstance.close();
    }
  } catch (err) {
    // 只有用户取消选择或系统报错才会进入这里
    if (err.name !== 'AbortError') {
      ElMessage.error("目录读取失败");
      console.error(err);
    }
  }
};



const readDirectory = async (dirHandle, path, children) => {
  for await (const entry of dirHandle.values()) {
    const entryPath = `${path}${entry.name}`;
    if (entry.kind === "file") {
      fileHandleMap.value.set(entryPath, entry);
      const file = await entry.getFile();
      children.push({
        name: entry.name,
        path: entryPath,
        type: "file",
        size: file.size,
      });
    } else if (entry.kind === "directory") {
      const folderNode = {
        name: entry.name,
        path: `${entryPath}/`,
        type: "directory",
        children: [],
      };
      children.push(folderNode);
      await readDirectory(entry, `${entryPath}/`, folderNode.children);
    }
  }
};

// 文件扩展名判断
const getFileExtension = (filename) => {
  const parts = filename.toLowerCase().split(".");
  return parts.length > 1 ? parts.pop() : "";
};

// 判断是否为文本文件扩展名（常见）
const isTextFile = (ext) => {
  const textExts = [
    "txt",
    "js",
    "json",
    "html",
    "css",
    "md",
    "xml",
    "vue",
    "ts",
    "jsx",
    "tsx",
  ];
  return textExts.includes(ext);
};

// 判断是否为图片扩展名
const isImageFile = (ext) => {
  const imageExts = ["png", "jpg", "jpeg", "gif", "bmp", "webp", "svg"];
  return imageExts.includes(ext);
};

// 处理点击文件，打开标签页
const handleNodeClick = async (nodeData) => {
  if (nodeData.type !== "file") return;

  // 如果标签页已打开，切换激活即可
  const existTab = openTabs.value.find((t) => t.path === nodeData.path);
  if (existTab) {
    activeTab.value = existTab.path;
    return;
  }

  const fileHandle = fileHandleMap.value.get(nodeData.path);
  if (!fileHandle) {
    ElMessage.error("无法读取该文件");
    return;
  }
  const file = await fileHandle.getFile();
  const ext = getFileExtension(nodeData.name);

  if (isTextFile(ext)) {
    const text = await file.text();
    // 代码高亮
    const lang = ext === "html" ? "xml" : ext; // highlight.js 用 xml 处理 html
    const highlighted = hljs.highlight(text, { language: lang }).value;
    openTabs.value.push({
      path: nodeData.path,
      name: nodeData.name,
      type: "text",
      highlightedContent: highlighted,
    });
  } else if (isImageFile(ext)) {
    // 读取成DataURL展示图片
    const dataUrl = await new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.readAsDataURL(file);
    });
    openTabs.value.push({
      path: nodeData.path,
      name: nodeData.name,
      type: "image",
      dataUrl,
    });
  } else {
    openTabs.value.push({
      path: nodeData.path,
      name: nodeData.name,
      type: "unsupported",
    });
  }
  activeTab.value = nodeData.path;
};

// 关闭标签
const closeTab = (targetName) => {
  const tabs = openTabs.value;
  let newActive = activeTab.value;
  if (newActive === targetName) {
    // 关闭的是当前激活tab，切换激活到左边一个或者空
    const idx = tabs.findIndex((t) => t.path === targetName);
    if (idx > 0) {
      newActive = tabs[idx - 1].path;
    } else if (tabs.length > 1) {
      newActive = tabs[1].path;
    } else {
      newActive = "";
    }
  }
  openTabs.value = tabs.filter((t) => t.path !== targetName);
  activeTab.value = newActive;
};

// 一键复制函数
const copyToClipboard = (htmlContent) => {
  const el = document.createElement("div");
  el.innerHTML = htmlContent;
  const text = el.textContent || el.innerText || "";

  if (navigator.clipboard && window.isSecureContext) {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        ElMessage.success("复制成功");
      })
      .catch(() => {
        ElMessage.error("复制失败，请手动复制");
      });
  } else {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.position = "fixed";
    textArea.style.top = "-9999px";
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand("copy");
      ElMessage.success("复制成功");
    } catch {
      ElMessage.error("复制失败，请手动复制");
    }
    document.body.removeChild(textArea);
  }
};
</script>

<style scoped>
.container {
  display: flex;
  height: 100vh;
  overflow: hidden;
  background: #f0f2f5;
}

.left-pane {
  width: 320px;
  padding: 16px;
  border-right: 1px solid #ebeef5;
  box-sizing: border-box;
  overflow-y: auto;
  background-color: #fafafa;
}

.right-pane {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background-color: #fff;
}

.file-tree {
  margin-top: 20px;
}

pre.code-block {
  position: relative;
  white-space: pre-wrap;
  font-family: 'Source Code Pro', monospace, monospace;
  font-size: 14px;
  padding: 12px;
  background: #f6f8fa;
  border-radius: 6px;
  border: 1px solid #ddd;
  overflow-x: auto;
}

.copy-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 2px 8px;
  font-size: 12px;
  z-index: 10;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.copy-btn:hover {
  opacity: 1;
}

.preview-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
}
</style>

