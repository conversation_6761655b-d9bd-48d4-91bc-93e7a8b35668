/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ChatBox: typeof import('./src/components/liaotian/ChatBox.vue')['default']
    ChatLogin: typeof import('./src/components/chat-login.vue')['default']
    CommentItem: typeof import('./src/components/CommentItem.vue')['default']
    Company: typeof import('./src/components/company.vue')['default']
    Daochusql: typeof import('./src/components/daochusql.vue')['default']
    Ddd: typeof import('./src/components/ddd.vue')['default']
    Demo: typeof import('./src/components/demo.vue')['default']
    FriendList: typeof import('./src/components/liaotian/FriendList.vue')['default']
    FriendManager: typeof import('./src/components/liaotian/FriendManager.vue')['default']
    Jiami: typeof import('./src/components/jiami.vue')['default']
    Jianting: typeof import('./src/components/jianting.vue')['default']
    List: typeof import('./src/components/list.vue')['default']
    MarkDown: typeof import('./src/components/markDown.vue')['default']
    Pagman: typeof import('./src/components/Pagman.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Rtc: typeof import('./src/components/rtc.vue')['default']
    Shipinjiajian: typeof import('./src/components/shipinjiajian.vue')['default']
    Shipinjiami: typeof import('./src/components/shipinjiami.vue')['default']
    Uploads: typeof import('./src/components/uploads.vue')['default']
    UserSearch: typeof import('./src/components/liaotian/UserSearch.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanField: typeof import('vant/es')['Field']
    VanFiled: typeof import('vant/es')['Filed']
    VanForm: typeof import('vant/es')['Form']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanTabbar: typeof import('vant/es')['Tabbar']
    VanTabbarItem: typeof import('vant/es')['TabbarItem']
    VanUploader: typeof import('vant/es')['Uploader']
  }
}
