<template>
  <div id="app">
    <!-- 特效层将由特效管理器动态创建 -->
    <div class="content-layer">
      <router-view />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useEffectManager } from "@/composables/useEffectManager";

// 初始化特效管理器
const effectManager = useEffectManager();
</script>

<style>
/* 应用级别样式 - 与全局样式配合 */
#app {
  position: relative;
  min-height: 100vh;
  font-family: var(--font-family-sans);
  color: var(--text-primary);
  /* background: var(--bg-secondary); */
}

/* 确保全局样式优先级 */
html,
body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* 覆盖默认字体，使用全局规范 */
body {
  font-family: var(--font-family-sans) !important;
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  /* background: var(--bg-secondary); */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 确保所有元素使用统一的盒模型 */
*,
*::before,
*::after {
  box-sizing: border-box;
}
</style>

<style scoped>
.particle-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 0;
  pointer-events: none;
}

.content-layer {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  width: 100%;
}
</style>
