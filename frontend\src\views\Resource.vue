<template>
  <div class="source-list">
    <div class="table-wrapper">
      <el-table :data="resources" style="width: 100%" class="responsive-table">
        <el-table-column prop="filename" label="资源名称" />
        <el-table-column label="类型" width="100">
          <template #default="scope">
            {{ getFileType(scope.row.filename) }}
          </template>
        </el-table-column>
        <el-table-column prop="size" label="大小" width="120">
          <template #default="scope">
            {{ formatSize(scope.row.size) }}
          </template>
        </el-table-column>
        <el-table-column prop="mtime" label="上传时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.mtime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <el-button type="primary" @click="download(scope.row)" :loading="downloadingSet.has(scope.row.filename)"
              :disabled="downloadingSet.has(scope.row.filename)" size="small">下载</el-button>
            <el-button v-if="canPreview(scope.row.filename)" type="success" @click="preview(scope.row)" size="small"
              style="margin-left: 6px">预览</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog v-model="previewVisible" width="60%" :title="previewType === 'image' ? '图片预览' : '文本预览'">
      <div v-if="previewType === 'image'" style="text-align:center;">
        <img :src="previewContent" style="max-width:100%;max-height:60vh;" />
      </div>
      <div v-else-if="previewType === 'text'" style="white-space:pre-wrap;max-height:60vh;overflow:auto;">
        {{ previewContent }}
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { GetResourceListApi, DownloadResourceApi, PreviewResourceApi } from "../utils/api";

const resources = ref<any[]>([]);
const downloadingSet = ref<Set<string>>(new Set());

onMounted(async () => {
  const res = await GetResourceListApi();
  if (res?.data) {
    resources.value = res.data;
  }
});

function getFileType(filename: string) {
  const ext = filename.split(".").pop()?.toLowerCase() || "";
  return ext;
}

function formatSize(size: number) {
  if (size < 1024) return size + " B";
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + " KB";
  if (size < 1024 * 1024 * 1024) return (size / 1024 / 1024).toFixed(1) + " MB";
  return (size / 1024 / 1024 / 1024).toFixed(1) + " GB";
}

function formatTime(time: string | Date) {
  const d = new Date(time);
  return d.toLocaleString();
}

async function download(row: any) {
  if (downloadingSet.value.has(row.filename)) return;
  downloadingSet.value.add(row.filename);
  try {
    const res = await DownloadResourceApi(row.filename);
    const blob = new Blob([res]);
    const link = document.createElement("a");
    link.href = window.URL.createObjectURL(blob);
    link.download = row.filename;
    link.click();
    window.URL.revokeObjectURL(link.href);
  } catch (e) {
    ElMessage.error("下载失败");
  } finally {
    downloadingSet.value.delete(row.filename);
  }
}

const baseUrl = "http://192.168.31.222:3000";
const previewVisible = ref(false);
const previewType = ref("");
const previewContent = ref("");

function canPreview(filename: string) {
  const ext = getFileType(filename);
  return ["jpg", "jpeg", "png", "gif", "bmp", "webp", "txt"].includes(ext);
}

async function preview(row: any) {
  const ext = getFileType(row.filename);
  if (["jpg", "jpeg", "png", "gif", "bmp", "webp"].includes(ext)) {
    previewType.value = "image";
    previewContent.value = baseUrl + "/resource/" + row.filename;
    previewVisible.value = true;
  } else if (ext === "txt") {
    try {
      const res = await PreviewResourceApi(row.filename);
      previewType.value = "text";
      previewContent.value = typeof res === "string" ? res : await res.text?.();
      previewVisible.value = true;
    } catch {
      ElMessage.error("预览失败");
    }
  }
}
</script>

<style scoped lang="less">
.source-list {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.table-wrapper {
  overflow-x: auto;
}

.responsive-table {
  min-width: 600px;
}

@media (max-width: 600px) {
  .source-list {
    padding: 8px;
  }

  .responsive-table {
    font-size: 12px;
  }

  .el-table th,
  .el-table td {
    padding: 4px !important;
    word-break: break-word;
  }

  .el-button {
    font-size: 12px;
    padding: 4px 6px;
  }

  .el-dialog {
    width: 95vw !important;
  }

  .el-dialog__body {
    padding: 10px !important;
  }

  img {
    max-width: 100%;
    max-height: 40vh;
  }
}
</style>
