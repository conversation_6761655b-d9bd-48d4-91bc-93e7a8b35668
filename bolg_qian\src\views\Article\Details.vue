<template>
  <div class="article-detail-page">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-section">
      <div class="breadcrumb-container">
        <el-breadcrumb separator="/" class="breadcrumb">
          <el-breadcrumb-item :to="{ path: '/index/home' }">
            <el-icon><i-ep-House /></el-icon>
            首页
          </el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/index/allarticles' }">
            <el-icon><i-ep-Document /></el-icon>
            文章列表
          </el-breadcrumb-item>
          <el-breadcrumb-item>文章详情</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>

    <!-- 文章主体 -->
    <div class="article-container">
      <article class="article-main">
        <!-- 文章头部 -->
        <header class="article-header">
          <h1 class="article-title">{{ article.title || '加载中...' }}</h1>

          <div class="article-meta">
            <div class="author-section">
              <el-avatar :size="48" :src="imgUrl" class="author-avatar">
                <el-icon><i-ep-User /></el-icon>
              </el-avatar>
              <div class="author-info">
                <div class="author-name">{{ article.authorName || '未知作者' }}</div>
                <div class="publish-info">
                  <span class="publish-time">
                    {{ formatDate(article.updated_at) }}
                  </span>
                  <span class="view-count">
                    <el-icon><i-ep-View /></el-icon>
                    {{ article.viewCount || 0 }} 次浏览
                  </span>
                </div>
              </div>
            </div>

            <!-- 文章互动区域 -->
            <div class="article-interactions">
              <!-- 收藏和分享按钮 -->
              <div class="interaction-buttons">
                <ArticleFavorite
                  :article-id="articleId"
                  :show-users-list="false"
                  :show-text="true"
                  :show-count="false"
                  size="small"
                  @favorite-changed="handleFavoriteChanged"
                />
                <el-button
                  @click="showShareDialog"
                  class="share-btn"
                >
                  <el-icon><Share /></el-icon>
                  分享
                </el-button>
              </div>

              <!-- 作者操作按钮 -->
              <div class="article-actions" v-if="isAuthor">
                <el-button
                  type="primary"
                  :plain="!article.is_share"
                  @click="toggleShare"
                  class="action-btn"
                >
                  <el-icon><Promotion /></el-icon>
                  {{ article.is_share ? "已共享" : "共享" }}
                </el-button>
                <el-button
                  type="danger"
                  @click="delArticle"
                  class="action-btn"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </header>

        <!-- 文章封面 -->
        <div v-if="article.cover_image" class="cover-section">
          <el-image
            :src="coverImg"
            fit="cover"
            alt="文章封面"
            class="cover-image"
            :preview-src-list="[coverImg]"
          />
        </div>

        <!-- 文章内容 -->
        <div class="article-content">
          <div class="markdown-body" v-html="articleHtml"></div>
        </div>

        <!-- 文章底部 -->
        <footer class="article-footer">
          <div class="tags-section" v-if="article.tags">
            <div class="tags-label">
              <el-icon><i-ep-PriceTag /></el-icon>
              标签：
            </div>
            <div class="tags-list">
              <el-tag
                v-for="tag in article.tags.split(',')"
                :key="tag"
                effect="light"
                size="small"
                class="tag-item"
              >
                {{ tag.trim() }}
              </el-tag>
            </div>
          </div>

          <div class="article-tools">
            <el-button
              type="primary"
              size="small"
              @click="copyText(article.summary || '')"
              class="tool-btn"
            >
              <el-icon><i-ep-DocumentCopy /></el-icon>
              复制摘要
            </el-button>
            <el-button
              size="small"
              @click="goBack"
              class="tool-btn"
            >
              <el-icon><i-ep-Back /></el-icon>
              返回列表
            </el-button>
          </div>
        </footer>
      </article>
    </div>

    <!-- 分享对话框 -->
    <el-dialog
      v-model="shareDialogVisible"
      title="分享文章"
      width="500px"
      :before-close="closeShareDialog"
    >
      <div class="share-dialog-content">
        <div class="share-info">
          <h4>{{ article.title }}</h4>
          <p class="share-description">{{ article.summary || '分享这篇精彩的文章给朋友们' }}</p>
        </div>

        <div class="share-methods">
          <div class="share-method-item">
            <div class="method-header">
              <el-icon><i-ep-Link /></el-icon>
              <span>复制链接</span>
            </div>
            <div class="link-container">
              <el-input
                v-model="shareUrl"
                readonly
                class="share-url-input"
              >
                <template #append>
                  <el-button @click="copyShareUrl" type="primary">
                    复制
                  </el-button>
                </template>
              </el-input>
            </div>
          </div>

          <div class="share-method-item">
            <div class="method-header">
              <el-icon><i-ep-Share /></el-icon>
              <span>社交分享</span>
            </div>
            <div class="social-share-buttons">
              <el-button
                @click="shareToWeChat"
                class="share-btn-social wechat"
                circle
                size="large"
              >
                微信
              </el-button>
              <el-button
                @click="shareToWeibo"
                class="share-btn-social weibo"
                circle
                size="large"
              >
                微博
              </el-button>
              <el-button
                @click="shareToQQ"
                class="share-btn-social qq"
                circle
                size="large"
              >
                QQ
              </el-button>
            </div>
          </div>

          <div class="share-method-item">
            <div class="method-header">
              <el-icon><i-ep-Picture /></el-icon>
              <span>二维码分享</span>
            </div>
            <div class="qr-code-container">
              <div class="qr-code-placeholder">
                <el-icon size="48"><i-ep-Picture /></el-icon>
                <p>二维码</p>
                <small>扫码分享文章</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 评论区域 -->
    <div class="comments-container">
      <div class="comments-card">
        <header class="comments-header">
          <div class="header-left">
            <h2 class="comments-title">
              <el-icon><i-ep-ChatDotRound /></el-icon>
              评论区
            </h2>
            <el-tag type="info" size="small" effect="light">
              {{ comments.length }} 条评论
            </el-tag>
          </div>
          <div class="header-right">
            <el-button
              size="small"
              @click="fetchComments"
              :loading="loadingComments"
              type="primary"
              link
            >
              <el-icon><i-ep-Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </header>

        <!-- 评论表单 -->
        <div class="comment-form">
          <div class="form-header">
            <el-avatar :size="36" :src="currentUserAvatar" class="user-avatar">
              <el-icon><i-ep-User /></el-icon>
            </el-avatar>
            <div class="form-title">
              <span>发表评论</span>
              <span class="form-subtitle">分享你的想法和见解</span>
            </div>
          </div>

          <div class="form-content">
            <el-input
              v-model="commentContent"
              type="textarea"
              :rows="4"
              placeholder="写下你的评论...支持 Markdown 语法"
              maxlength="1000"
              show-word-limit
              @keydown.ctrl.enter="submitComment"
              class="comment-textarea"
            />

            <div class="form-actions">
              <div class="form-tips">
                <el-icon><i-ep-InfoFilled /></el-icon>
                <span>按 Ctrl+Enter 快速发布</span>
              </div>
              <div class="form-buttons">
                <el-button
                  @click="commentContent = ''"
                  :disabled="!commentContent"
                  size="small"
                >
                  <el-icon><i-ep-RefreshLeft /></el-icon>
                  清空
                </el-button>
                <el-button
                  type="primary"
                  @click="submitComment"
                  :loading="submittingComment"
                  :disabled="!commentContent.trim()"
                  size="small"
                >
                  <el-icon><i-ep-Promotion /></el-icon>
                  发表评论
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 评论列表 -->
        <div class="comments-list">
          <div v-if="commentTree.length === 0 && !loadingComments" class="empty-comments">
            <el-empty description="暂无评论" :image-size="80">
              <span>成为第一个评论的人吧！</span>
            </el-empty>
          </div>

          <div v-else class="comment-items">
            <CommentItem
              v-for="comment in commentTree"
              :key="comment.id"
              :comment="comment"
              :get-avatar-url="getAvatarUrl"
              :format-date="formatDate"
              :reply-to-id="replyToId"
              :reply-to-author="replyToAuthor"
              :reply-content="replyContent"
              @update:replyContent="val => replyContent = val"
              :show-reply-input="showReplyInput"
              :submit-reply="submitReply"
              :cancel-reply="cancelReply"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import hljs from "highlight.js";
import "highlight.js/styles/github.css";
import MarkdownIt from "markdown-it";
import { useViewHistory } from "@/composables/useViewHistory";
import { copyText } from "@/plugin/copy.js";
import {
  GetPublicArticleDetailApi,
  GetPublicArticleCommentsApi,
  SubmitPublicCommentApi,
  FakeDeleteArticleApi,
  ShareArticleApi,
} from "../../utils/api";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  ChatDotRound, Refresh, InfoFilled, Promotion, Clock, View,
  House, Document, User, Share, Delete, PriceTag, DocumentCopy,
  Back, RefreshLeft, Link, Picture
} from '@element-plus/icons-vue';
import CommentItem from '../../components/CommentItem.vue';
import ArticleFavorite from '../../components/ArticleFavorite.vue';

const route = useRoute();
const router = useRouter();
const articleId = Number(route.params.id);

// 浏览记录功能
const {
  viewDuration,
  scrollDepth,
  isRecording,
  startListening,
  stopListening,
  triggerRecord,
  getCurrentStats
} = useViewHistory(articleId);

interface Article {
  id: number;
  title: string;
  content: string;
  summary?: string;
  authorName: string;
  authorAvatar: string;
  cover_image: string;
  published_at: string;
  updated_at: string;
  viewCount: number;
  tags: string;
  is_share?: number;
}

interface Comment {
  id: number;
  author: string;
  avatar: string;
  content: string;
  created_at: string;
  parent_id?: number;
  children?: Comment[];
}

const article = ref<Article>({} as Article);
const comments = ref<Comment[]>([]);
const commentTree = ref<Comment[]>([]);
const commentContent = ref("");
const imgUrl = ref("");
const coverImg = ref("");
const articleHtml = ref("");
const replyToId = ref<number | null>(null);
const replyToAuthor = ref("");
const replyContent = ref("");
const loadingComments = ref(false);
const submittingComment = ref(false);
const currentUserAvatar = ref("/api/avatars/moren.png");

const currentUser = localStorage.getItem("username") || ""; // 确保是字符串
console.log("当前用户：", currentUser);
const isAuthor = computed(() => {
  // 兼容 authorName 可能为 undefined/null
  return currentUser && article.value.authorName && currentUser === article.value.authorName;
});

// 分享相关数据
const shareDialogVisible = ref(false);
const shareUrl = ref('');

// 生成分享链接
const generateShareUrl = () => {
  const baseUrl = window.location.origin;
  const articlePath = `/index/details/${articleId}`;
  return `${baseUrl}${articlePath}`;
};

const md = new MarkdownIt({
  highlight(str: string, lang: string) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return `<pre class="hljs"><code>${hljs.highlight(str, { language: lang }).value}</code></pre>`;
      } catch {
        // ignore
      }
    }
    return `<pre class="hljs"><code>${md.utils.escapeHtml(str)}</code></pre>`;
  }
});

const formatDate = (dateStr: string): string => {
  if (!dateStr) return "";
  const d = new Date(dateStr);
  const pad = (n: number) => n.toString().padStart(2, "0");
  return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}`;
};

const fetchArticleDetail = async () => {
  try {
    const result = await GetPublicArticleDetailApi({ id: articleId });
    const data = result?.data;
    if (data) {
      article.value = data;
      imgUrl.value = data.authorAvatar
        ? `/api/avatars/${data.authorAvatar}`
        : "";
      coverImg.value = data.cover_image
        ? `/api/articles/${data.cover_image}`
        : "";
      articleHtml.value = md.render(data.content || "");
      await nextTick();
    } else {
      ElMessage.error("文章不存在或已被删除");
      router.push("/index/allarticles");
    }
  } catch (error) {
    console.error("获取文章详情失败:", error);
    ElMessage.error("获取文章详情失败");
  }
};

// 将评论列表转为树结构（去重+递归）
function buildCommentTree(list: any[]): any[] {
  if (!list || list.length === 0) return [];

  const map: Record<number, any> = {};
  const tree: any[] = [];

  // 先去重，只保留id唯一的节点
  list.forEach(item => {
    if (!map[item.id]) {
      map[item.id] = {
        ...item,
        children: [],
        // 统一处理parent_id，将0和null都视为顶级评论
        parent_id: (item.parent_id === 0 || item.parent_id === null) ? null : item.parent_id
      };
    }
  });

  // 再组装树结构
  Object.values(map).forEach(item => {
    if (item.parent_id && map[item.parent_id]) {
      // 有父评论且父评论存在
      map[item.parent_id].children.push(item);
    } else {
      // 顶级评论
      tree.push(item);
    }
  });

  // 按时间排序：顶级评论按时间正序，子评论按时间正序
  tree.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
  tree.forEach(item => {
    if (item.children.length > 0) {
      item.children.sort((a: any, b: any) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
    }
  });

  return tree;
}

const fetchComments = async () => {
  loadingComments.value = true;
  try {
    const result = await GetPublicArticleCommentsApi({ article_id: articleId });
    if (result?.data) {
      comments.value = result.data;
      console.log("评论数据：", comments.value);
      commentTree.value = buildCommentTree(result.data);
      ElMessage.success(`已加载 ${comments.value.length} 条评论`);
    }
  } catch (error) {
    console.error("获取评论失败:", error);
    ElMessage.error("获取评论失败");
  } finally {
    loadingComments.value = false;
  }
};

const submitComment = async () => {
  const content = commentContent.value.trim();
  if (!content) {
    ElMessage.warning("评论内容不能为空");
    return;
  }

  submittingComment.value = true;

  // 对于公开访问，允许匿名评论或使用输入的用户名
  let username = localStorage.getItem("username");
  if (!username) {
    // 如果没有登录，提示用户输入用户名
    try {
      const { value } = await ElMessageBox.prompt('请输入您的用户名（用于显示评论）', '发表评论', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]{2,20}$/,
        inputErrorMessage: '用户名长度2-20位，支持中文、英文、数字、下划线'
      });
      if (!value) {
        submittingComment.value = false;
        return;
      }
      username = value;
    } catch {
      submittingComment.value = false;
      return;
    }
  }

  try {
    const result = await SubmitPublicCommentApi({
      username,
      article_id: articleId,
      content
    });
    console.log("评论结果：", result);
    if (result?.code === 200) {
      ElMessage.success("评论发布成功！");
      await fetchComments();
      commentContent.value = "";
      // 触发浏览记录更新（用户互动）
      triggerRecord();
    } else {
      ElMessage.error("评论发布失败");
    }
  } catch (error) {
    console.error("提交评论失败:", error);
    ElMessage.error("评论发布失败，请稍后重试");
  } finally {
    submittingComment.value = false;
  }
};

const delArticle = async () => {


  ElMessageBox.confirm("确定要删除该文章吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      const result = await FakeDeleteArticleApi({ id: articleId });
      if (result) {
        ElMessage.success("删除成功");
        router.push("/index/home");
      } else {
        ElMessage.error("删除失败，请稍后再试");
      }
    })
    .catch(() => {
      // 用户取消操作
      console.log("用户取消删除操作");
    });
};

const toggleShare = async () => {
  try {
    // 检查用户登录状态
    const token = localStorage.getItem("token");
    console.log('用户token:', token ? '存在' : '不存在');

    if (!token) {
      ElMessage.error("请先登录");
      return;
    }

    const newShare = article.value.is_share ? 0 : 1;
    console.log('切换共享状态:', {
      articleId: article.value.id,
      currentShare: article.value.is_share,
      newShare,
      isAuthor: isAuthor.value
    });

    const res = await ShareArticleApi(article.value.id, newShare);
    console.log('共享API响应:', res);

    if (res?.code === 200) {
      article.value.is_share = newShare;
      ElMessage.success(newShare ? "已共享" : "已取消共享");
    } else {
      console.error('API返回错误:', res);
      ElMessage.error(res?.message || "操作失败");
    }
  } catch (error) {
    console.error('共享操作失败:', error);
    if (error.response) {
      console.error('错误响应:', error.response.data);
      console.error('错误状态:', error.response.status);
    }
    ElMessage.error("操作失败，请稍后重试");
  }
};



function getAvatarUrl(avatar: string) {
  if (!avatar) return "";
  if (avatar === "moren.png") {
    return "/api/avatars/moren.png";
  }
  return `/api/avatars/${avatar}`;
}

function showReplyInput(commentId: number, author: string) {
  replyToId.value = commentId;
  replyToAuthor.value = author;
  replyContent.value = "";
}

function cancelReply() {
  replyToId.value = null;
  replyToAuthor.value = "";
  replyContent.value = "";
}

const submitReply = async (parentId: number) => {
  const content = replyContent.value.trim();
  if (!content) {
    ElMessage.warning("回复内容不能为空");
    return;
  }

  let username = localStorage.getItem("username");
  if (!username) {
    const { value } = await ElMessageBox.prompt('请输入您的用户名（用于显示回复）', '回复', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /\S+/,
      inputErrorMessage: '用户名不能为空'
    });
    if (!value) return;
    username = value;
  }

  try {
    const result = await SubmitPublicCommentApi({
      username,
      article_id: articleId,
      content,
      parent_id: parentId,
    });
    if (result?.data?.message) {
      ElMessage.success("回复成功");
      await fetchComments();
      cancelReply();
    } else {
      ElMessage.error("回复失败");
    }
  } catch (error) {
    console.error("提交回复失败:", error);
    ElMessage.error("回复失败");
  }
};

const goBack = () => {
  router.go(-1);
};

// 处理收藏状态变化
const handleFavoriteChanged = (data) => {
  console.log('收藏状态变化:', data);
  ElMessage.success('收藏状态已更新');
  // 可以在这里添加额外的处理逻辑
};

// 分享相关函数
const showShareDialog = () => {
  shareUrl.value = generateShareUrl();
  shareDialogVisible.value = true;
};

const closeShareDialog = () => {
  shareDialogVisible.value = false;
};

const copyShareUrl = async () => {
  try {
    await navigator.clipboard.writeText(shareUrl.value);
    ElMessage.success('链接已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    // 降级方案：使用传统方法复制
    const textArea = document.createElement('textarea');
    textArea.value = shareUrl.value;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand('copy');
      ElMessage.success('链接已复制到剪贴板');
    } catch (err) {
      ElMessage.error('复制失败，请手动复制链接');
    }
    document.body.removeChild(textArea);
  }
};

const shareToWeChat = () => {
  ElMessage.info('请复制链接手动分享到微信');
  copyShareUrl();
};

const shareToWeibo = () => {
  const title = encodeURIComponent(article.value.title || '');
  const url = encodeURIComponent(shareUrl.value);
  const summary = encodeURIComponent(article.value.summary || '');
  const weiboUrl = `https://service.weibo.com/share/share.php?url=${url}&title=${title}&summary=${summary}`;
  window.open(weiboUrl, '_blank');
};

const shareToQQ = () => {
  const title = encodeURIComponent(article.value.title || '');
  const url = encodeURIComponent(shareUrl.value);
  const summary = encodeURIComponent(article.value.summary || '');
  const qqUrl = `https://connect.qq.com/widget/shareqq/index.html?url=${url}&title=${title}&summary=${summary}`;
  window.open(qqUrl, '_blank');
};

onMounted(() => {
  fetchArticleDetail();
  fetchComments();
  // 启动浏览记录
  startListening();
});

onUnmounted(() => {
  // 停止浏览记录
  stopListening();
});
</script>

<style scoped>
@import "highlight.js/styles/github.css";

/* 页面容器 */
.article-detail-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  font-family: var(--font-family-sans);
}

/* 面包屑区域 */
.breadcrumb-section {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-md) 0;
}

.breadcrumb-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.breadcrumb {
  font-size: var(--text-sm);
}

.breadcrumb :deep(.el-breadcrumb__item) {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 文章容器 */
.article-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

.article-main {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
}

/* 文章头部 */
.article-header {
  padding: var(--spacing-2xl);
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
}

.article-title {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xl) 0;
  line-height: var(--leading-tight);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-lg);
}

.author-section {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  flex: 1;
}

.author-avatar {
  border: 2px solid var(--border-light);
  box-shadow: var(--shadow-sm);
}

.author-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  text-align: left;
  align-items: flex-start;
}

.author-name {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  text-align: left;
}

.publish-info {
  display: flex;
  gap: var(--spacing-lg);
  font-size: var(--text-sm);
  color: var(--text-secondary);
  text-align: left;
}

.publish-time,
.view-count {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  text-align: left;
}

/* 文章互动区域 */
.article-interactions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
  position: relative;
  z-index: 10;
  background: #ffffff;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

.interaction-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
  position: relative;
  z-index: 10;
  pointer-events: auto;
}

/* 统一互动按钮样式 */
.interaction-buttons .el-button {
  min-width: 90px;
  min-height: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #e4e7ed;
  background: #ffffff;
  color: #606266;
  padding: 0 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.interaction-buttons .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
  color: #409eff;
}

.interaction-buttons .el-button .el-icon {
  font-size: 16px;
}

/* 分享按钮特殊样式 */
.share-btn:hover {
  border-color: #67c23a;
  color: #67c23a;
}

.article-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.action-btn {
  min-width: 90px;
  min-height: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  padding: 0 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn .el-icon {
  font-size: 16px;
}

/* 封面图片 */
.cover-section {
  padding: 0 var(--spacing-2xl);
  margin-bottom: var(--spacing-lg);
}

.cover-image {
  width: 100%;
  max-height: 400px;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  object-fit: cover;
}

/* 文章内容 */
.article-content {
  padding: var(--spacing-2xl);
}

.markdown-body {
  line-height: var(--leading-relaxed);
  font-size: var(--text-base);
  color: var(--text-primary);
}

.markdown-body :deep(h1),
.markdown-body :deep(h2),
.markdown-body :deep(h3),
.markdown-body :deep(h4),
.markdown-body :deep(h5),
.markdown-body :deep(h6) {
  color: var(--text-primary);
  font-weight: var(--font-semibold);
  margin: var(--spacing-xl) 0 var(--spacing-md) 0;
}

.markdown-body :deep(p) {
  margin-bottom: var(--spacing-md);
  text-align: justify;
}

.markdown-body :deep(pre) {
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  overflow-x: auto;
  font-size: var(--text-sm);
  border: 1px solid var(--border-light);
}

.markdown-body :deep(code) {
  font-family: var(--font-family-mono);
  background: var(--bg-tertiary);
  padding: 2px 4px;
  border-radius: var(--radius-sm);
  font-size: 0.9em;
}

.markdown-body :deep(blockquote) {
  border-left: 4px solid var(--primary-color);
  padding-left: var(--spacing-md);
  margin: var(--spacing-md) 0;
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
}

/* 文章底部 */
.article-footer {
  padding: var(--spacing-xl) var(--spacing-2xl);
  border-top: 1px solid var(--border-light);
  background: var(--bg-tertiary);
}

.tags-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.tags-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.tag-item {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.article-tools {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.tool-btn {
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
}

/* 评论区域 */
.comments-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.comments-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.comments-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 评论表单 */
.comment-form {
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
}

.form-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.user-avatar {
  border: 2px solid var(--border-light);
  box-shadow: var(--shadow-sm);
}

.form-title {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.form-title > span:first-child {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  font-size: var(--text-base);
}

.form-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.form-content {
  margin-left: calc(36px + var(--spacing-md));
}

.comment-textarea {
  margin-bottom: var(--spacing-md);
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-tips {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-tertiary);
  font-size: var(--text-xs);
}

.form-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

/* 评论列表 */
.comments-list {
  padding: var(--spacing-lg);
}

.empty-comments {
  text-align: center;
  padding: var(--spacing-2xl);
}

.comment-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .breadcrumb-container,
  .article-container,
  .comments-container {
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }

  .article-header {
    padding: var(--spacing-lg);
  }

  .article-title {
    font-size: var(--text-2xl);
  }

  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
    text-align: left;
  }

  .article-content {
    padding: var(--spacing-lg);
  }

  .article-footer {
    padding: var(--spacing-lg);
  }

  .tags-section {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .article-tools {
    flex-direction: column;
  }

  .comments-header {
    padding: var(--spacing-lg);
  }

  .comment-form {
    padding: var(--spacing-lg);
  }

  .form-content {
    margin-left: 0;
    margin-top: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .breadcrumb-container,
  .article-container,
  .comments-container {
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
  }

  .article-header {
    padding: var(--spacing-md);
  }

  .article-title {
    font-size: var(--text-xl);
  }

  .publish-info {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .interaction-buttons {
    flex-direction: row;
    justify-content: center;
    gap: var(--spacing-sm);
  }

  .interaction-buttons .el-button {
    min-width: 80px;
    min-height: 32px;
    height: 32px;
    font-size: 12px;
    padding: 0 12px;
  }

  .interaction-buttons .el-button .el-icon {
    font-size: 14px;
  }

  .title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .title-actions {
    align-self: flex-end;
  }

  .article-actions {
    flex-direction: row;
    justify-content: center;
    gap: var(--spacing-sm);
    width: 100%;
  }

  .action-btn {
    min-width: 80px;
    min-height: 32px;
    height: 32px;
    font-size: 12px;
    padding: 0 12px;
    flex: 1;
    max-width: 120px;
  }

  .action-btn .el-icon {
    font-size: 14px;
  }
}

/* 分享对话框样式 */
.share-dialog-content {
  padding: var(--spacing-md);
}

.share-info {
  margin-bottom: var(--spacing-xl);
  text-align: center;
}

.share-info h4 {
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
}

.share-description {
  color: var(--text-secondary);
  margin: 0;
  font-size: var(--text-sm);
}

.share-methods {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.share-method-item {
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
}

.method-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.link-container {
  width: 100%;
}

.share-url-input {
  width: 100%;
}

.social-share-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

.share-btn-social {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  transition: all 0.3s ease;
}

.share-btn-social:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.share-btn-social.wechat {
  background: #07c160;
  border-color: #07c160;
  color: white;
}

.share-btn-social.weibo {
  background: #e6162d;
  border-color: #e6162d;
  color: white;
}

.share-btn-social.qq {
  background: #12b7f5;
  border-color: #12b7f5;
  color: white;
}

.qr-code-container {
  display: flex;
  justify-content: center;
}

.qr-code-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  border: 2px dashed var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  background: var(--bg-tertiary);
}

.qr-code-placeholder p {
  margin: var(--spacing-xs) 0 0 0;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.qr-code-placeholder small {
  margin: var(--spacing-xs) 0 0 0;
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}
</style>