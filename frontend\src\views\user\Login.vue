<template>
  <div class="login-container">
    <van-nav-bar title="用户登录" />

    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <van-field
          v-model="username"
          name="用户名"
          label="用户名"
          placeholder="请输入用户名"
          :rules="[{ required: true, message: '请填写用户名' }]"
        />
        <van-field
          v-model="password"
          type="password"
          name="密码"
          label="密码"
          placeholder="请输入密码"
          :rules="[{ required: true, message: '请填写密码' }]"
        />
      </van-cell-group>
      <div class="btn-group">
        <van-button round block type="primary" native-type="submit" :loading="loading">
          登录
        </van-button>
        <van-button round block type="default" @click="$router.push('/register')">
          注册
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import { loginApi } from "../../utils/api.ts";
import type { LoginResponse } from "./props.ts";

const router = useRouter();
const username = ref("");
const password = ref("");
const loading = ref(false);

const onSubmit = async () => {
  loading.value = true;
  try {
    const res = (await loginApi({
      username: username.value,
      password: password.value,
    })) as unknown as LoginResponse;
    // 存储 Token 和用户信息
    localStorage.setItem("token", res.token);
    localStorage.setItem("username", res.user.username);
    localStorage.setItem("id", `${res.user.id}`);
    router.push({ name: 'Home' });
  } catch (err: any) {
    alert("登录失败：" + (err.message || "网络错误"));
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.login-container {
  max-width: 380px;
  margin: 40px auto 0 auto;
  padding: 32px 24px 24px 24px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
}

.van-nav-bar {
  margin-bottom: 24px;
}

.btn-group {
  margin: 24px 0 0 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.van-button {
  font-size: 16px;
  letter-spacing: 1px;
}
</style>