<template>
  <div>
    <router-view />
    <van-tabbar v-model="active" @change="onChange">
      <van-tabbar-item icon="home-o">首页</van-tabbar-item>
      <van-tabbar-item v-if="!isMobile" icon="label-o">文件</van-tabbar-item>
      <van-tabbar-item icon="friends-o" @click="handleCurrentChange">媒体</van-tabbar-item>
      <van-tabbar-item icon="setting-o">设置</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";

const router = useRouter();
const active = ref<number>(0);
const isMobile = ref(false);

onMounted(() => {
  isMobile.value = /Android|webOS|iPhone|iPod|BlackBerry|iPad|Windows Phone/i.test(navigator.userAgent);
});

const onChange = (index: number) => {
  // 计算实际跳转的tab索引
  let realIndex = index;
  if (isMobile.value && index > 0) realIndex++; // 跳过文件tab

  if (realIndex === 2) {
    active.value = active.value;
    return;
  }

  switch (realIndex) {
    case 0:
      router.push("/index/home");
      break;
    case 1:
      router.push("/index/files");
      break;
    case 3:
      router.push("/index/settings");
      break;
  }
};

// 媒体页面切换,输入密码123456
const handleCurrentChange = async () => {
  // 检查本地是否已验证
  const verified = localStorage.getItem('media_verified')
  if (verified === '1') {
    router.push("/index/media")
    return
  }
  try {
    const { value } = await ElMessageBox.prompt(
      "请输入访问照片墙的密码",
      "身份验证",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputType: "password",
      }
    )
    if (value === "123456") {
      localStorage.setItem('media_verified', '1')
      router.push("/index/media")
    } else {
      ElMessage.error("密码错误，无法访问照片墙")
    }
  } catch (error) {
    // 用户取消输入，不做处理
  }
}
</script>