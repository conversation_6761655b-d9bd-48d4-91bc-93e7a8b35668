import { createRouter, createWebHistory } from "vue-router";

const routes = [
  // 登录页面
  {
    path: "/login",
    name: "Login",
    component: () => import("../views/user/Login.vue"),
  },

  // 注册页面
  {
    path: "/register",
    name: "Register",
    component: () => import("../views/user/Register.vue"),
  },
  // 个人信息页面
  {
    path: "/user/intro",
    name: "UserIntro",
    component: () => import("../views/user/intro.vue"),
  },
  // 首页及其子页面
  {
    path: "/index", // 父路径
    name: "Index",
    // 重定向到子页面
    redirect: "/index/home",
    component: () => import("../views/Index.vue"),
    children: [
      {
        path: "home",
        name: "Home",
        component: () => import("../views/Home.vue"),
      },
      {
        path: "dashboard", // 子路径，不带斜杠
        name: "Dashboard",
        component: () => import("../views/Dashboard.vue"),
      },
      {
        path: "files", // 子路径，不带斜杠
        name: "Files",
        component: () => import("../views/Files.vue"),
      },
      {
        path: "media", // 子路径，不带斜杠
        name: "Media",
        component: () => import("../views/Media.vue"),
      },
      {
        path: "settings", // 子路径，不带斜杠
        name: "Settings",
        component: () => import("../views/Settings.vue"),
      },
      {
        path: "logs", // 子路径，不带斜杠
        name: "Logs",
        component: () => import("../views/logs/Logs.vue"),
      },
      {
        path: "details/:id",
        name: "Details",
        component: () => import("../views/Details.vue"),
      },
      {
        path: "edit",
        name: "Edit",
        component: () => import("../views/Edit.vue"),
      },
      {
        path: "model",
        name: "Model",
        component: () => import("../views/Model.vue"),
      },
      {
        // 聊天室 chat
        path: "chat",
        name: "Chat",
        component: () => import("../views/Chat.vue"),
      },
      {
        // image
        path: "images",
        name: "Images",
        component: () => import("../views/Images.vue"),
      },
      {
        // pagman
        path: "pagman",
        name: "Pagman",
        component: () => import("../views/Pagman.vue"),
      },
      {
        path: "allarticles",
        name: "AllArticles",
        component: () => import("../views/AllArticles.vue"),
      },
      {
        path: "resource",
        name: "Resource",
        component: () => import("../views/Resource.vue"),
      },
      // Uploads
      {
        path: "uploads",
        name: "Uploads",
        component: () => import("../views/Uploads.vue"),
      },
      // rtc
      {
        path: "rtc",
        name: "Rtc",
        component: () => import("../views/Rtc.vue"),
      },

      // bug
      {
        path: "bug",
        name: "Bug",
        component: () => import("../views/Bug.vue"),
      },
      // create
      {
        path: "create",
        name: "Create",
        component: () => import("../views/Create.vue"),
      }
    ],
  },
  // 根路径重定向到首页
  {
    path: "/",
    redirect: "/index/home",
  },
  // 404 页面
  {
    path: "/:catchAll(.*)",
    name: "NotFound",
    component: () => import("../views/NotFound.vue"),
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

// 添加路由守卫

router.beforeEach((to, from, next) => {
  // 不需要登录的页面 登录 注册
  const whiteList = ["/login", "/register"];
  const token = localStorage.getItem('token');

  if (whiteList.includes(to.path)) {
    next();
  } else {
    if (token) {
      next();
    } else {
      next('/login');
    }
  }
});

export default router;
