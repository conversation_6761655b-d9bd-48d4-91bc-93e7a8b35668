import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { resolve } from "path";
import path from "path";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { VantResolver } from "@vant/auto-import-resolver";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import Icons from "unplugin-icons/vite";
import IconsResolver from "unplugin-icons/resolver";
import tailwindcss from "@tailwindcss/vite";

// https://vite.dev/config/
export default defineConfig({
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"), // 将 '@' 映射到 src 目录
    },
  },
  plugins: [
    vue(),
    tailwindcss(),
    AutoImport({
      resolvers: [
        VantResolver(),
        ElementPlusResolver(),
        IconsResolver({
          prefix: "Icon",
        }),
      ],
    }),
    Components({
      resolvers: [
        VantResolver(),
        ElementPlusResolver(),
        IconsResolver({
          enabledCollections: ["ep"], // element-plus图标集
          prefix: false, // 不使用前缀
        }),
      ],
    }),
    Icons({
      autoInstall: true,
    }),
  ],
  server: {
    host: "0.0.0.0",
    port: 5173,
    proxy: {
      "/api": {
        target: "http://**************:3000",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log(`[VITE PROXY] ${req.method} ${req.url} -> ${options.target}${req.url}`);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log(`[VITE PROXY RES] ${req.method} ${req.url} <- ${proxyRes.statusCode}`);
          });
        }
      },
      "/images": {
        target: "http://**************:3000",
        changeOrigin: true,
      },
      "/articles": {
        target: "http://**************:3000",
        changeOrigin: true,
      },
      "/media": {
        target: "http://**************:3000",
        changeOrigin: true,
      },
    },
  },
});
