import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
// import { resolve } from "path";
import path from "path";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { VantResolver } from "@vant/auto-import-resolver";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import Icons from "unplugin-icons/vite";
import IconsResolver from "unplugin-icons/resolver";
import tailwindcss from "@tailwindcss/vite";

// https://vite.dev/config/
export default defineConfig({
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"), // 将 '@' 映射到 src 目录
    },
  },
  plugins: [
    vue(),
    tailwindcss(),
    AutoImport({
      resolvers: [
        VantResolver(),
        ElementPlusResolver(),
        IconsResolver({
          prefix: "Icon",
        }),
      ],
    }),
    Components({
      resolvers: [
        VantResolver(),
        ElementPlusResolver(),
        IconsResolver({
          enabledCollections: ["ep"], // element-plus图标集
          prefix: false, // 不使用前缀
        }),
      ],
    }),
    Icons({
      autoInstall: true,
    }),
  ],

  // 构建优化
  build: {
    // 代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          // 将Vue相关库分离
          vue: ['vue', 'vue-router', 'pinia'],
          // 将UI库分离
          ui: ['element-plus', 'vant'],
          // 将工具库分离
          utils: ['axios', 'lodash'],
          // 将图标库分离
          icons: ['@iconify/vue'],
        },
      },
    },
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // 生产环境移除console
        drop_debugger: true,
      },
    },
    // 启用gzip压缩
    reportCompressedSize: false,
    chunkSizeWarningLimit: 1000,
  },
  server: {
    host: "0.0.0.0",
    port: 5173,
    proxy: {
      "/api": {
        target: "http://**************:3000",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
        configure: (proxy, options) => {
          proxy.on('proxyReq', (_proxyReq, req, _res) => {
            console.log(`[VITE PROXY] ${req.method} ${req.url} -> ${options.target}${req.url}`);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log(`[VITE PROXY RES] ${req.method} ${req.url} <- ${proxyRes.statusCode}`);
          });
        }
      },
      "/images": {
        target: "http://**************:3000",
        changeOrigin: true,
      },
      "/articles": {
        target: "http://**************:3000",
        changeOrigin: true,
      },
      "/media": {
        target: "http://**************:3000",
        changeOrigin: true,
      },
    },
  },
});
