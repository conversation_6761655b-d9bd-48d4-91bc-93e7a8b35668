<template>
  <div class="effect-management">
    <div class="page-header">
      <h2>✨ 特效管理</h2>
      <p class="page-description">管理和配置页面特效，提升用户体验</p>
    </div>

    <!-- 当前特效状态 -->
    <div class="current-status">
      <el-card class="status-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>当前特效状态</span>
            <el-tag :type="getEffectStatusType()" size="large">
              {{ getEffectStatusText() }}
            </el-tag>
          </div>
        </template>
        
        <div class="status-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="当前特效">
              {{ getEffectName(currentEffect) }}
            </el-descriptions-item>
            <el-descriptions-item label="运行状态">
              <el-tag :type="isEffectRunning ? 'success' : 'info'" size="small">
                {{ isEffectRunning ? '运行中' : '已停止' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="性能影响">
              <el-rate
                :model-value="getPerformanceImpact()"
                :max="5"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value} 级"
              />
            </el-descriptions-item>
            <el-descriptions-item label="适用场景">
              {{ getEffectScene(currentEffect) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>

    <!-- 特效选择 -->
    <div class="effect-selection">
      <el-card class="selection-card">
        <template #header>
          <span>特效选择</span>
        </template>
        
        <div class="effect-grid">
          <div 
            v-for="effect in effectOptions" 
            :key="effect.value"
            class="effect-item"
            :class="{ active: currentEffect === effect.value }"
            @click="selectEffect(effect.value)"
          >
            <div class="effect-preview">
              <EffectPreview
                :effect-type="previewEffect === effect.value ? effect.value : 'none'"
                :config="getPreviewConfig(effect.value)"
                :width="280"
                :height="120"
              />
            </div>
            
            <div class="effect-info">
              <h4>{{ effect.label }}</h4>
              <p>{{ effect.description }}</p>
              
              <div class="effect-meta">
                <el-tag size="small" :type="getPerformanceTagType(effect.performance)">
                  {{ effect.performance }}
                </el-tag>
                <span class="effect-size">{{ effect.size }}</span>
              </div>
            </div>
            
            <div class="effect-actions">
              <el-button 
                size="small" 
                @click.stop="previewEffect = previewEffect === effect.value ? null : effect.value"
              >
                {{ previewEffect === effect.value ? '停止预览' : '预览' }}
              </el-button>
              <el-button 
                type="primary" 
                size="small"
                :disabled="currentEffect === effect.value"
                @click.stop="applyEffect(effect.value)"
              >
                {{ currentEffect === effect.value ? '已应用' : '应用' }}
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 特效配置 -->
    <div class="effect-config" v-if="currentEffect !== 'none'">
      <el-card class="config-card">
        <template #header>
          <span>特效配置</span>
        </template>
        
        <div class="config-content">
          <!-- 通用配置 -->
          <div class="config-section">
            <h4>通用设置</h4>
            <el-form :model="effectConfig" label-width="120px" size="small">
              <el-form-item label="启用特效">
                <el-switch 
                  v-model="effectConfig.enabled"
                  @change="toggleEffect"
                />
              </el-form-item>
              
              <el-form-item label="透明度">
                <el-slider 
                  v-model="effectConfig.opacity"
                  :min="0"
                  :max="100"
                  @change="updateEffectConfig"
                />
              </el-form-item>
              
              <el-form-item label="动画速度">
                <el-slider 
                  v-model="effectConfig.speed"
                  :min="1"
                  :max="10"
                  @change="updateEffectConfig"
                />
              </el-form-item>
            </el-form>
          </div>

          <!-- 特效专用配置 -->
          <div class="config-section" v-if="currentEffect !== 'none'">
            <h4>{{ getEffectName(currentEffect) }} 专用设置</h4>
            <div class="specific-config">
              <!-- 流星雨配置 -->
              <div v-if="currentEffect === 'meteor'">
                <el-form label-width="120px" size="small">
                  <el-form-item label="流星数量">
                    <el-slider
                      v-model="effectConfig.specific.count"
                      :min="1"
                      :max="20"
                      @change="updateEffectConfig"
                    />
                  </el-form-item>
                  <el-form-item label="流星大小">
                    <el-slider
                      v-model="effectConfig.specific.size"
                      :min="1"
                      :max="5"
                      @change="updateEffectConfig"
                    />
                  </el-form-item>
                  <el-form-item label="颜色主题">
                    <el-select v-model="effectConfig.specific.theme" @change="updateEffectConfig">
                      <el-option label="经典白色" value="white" />
                      <el-option label="彩虹色" value="rainbow" />
                      <el-option label="蓝色" value="blue" />
                      <el-option label="金色" value="gold" />
                    </el-select>
                  </el-form-item>
                </el-form>
              </div>

              <!-- 夜空星辰配置 -->
              <div v-if="currentEffect === 'nightsky'">
                <el-form label-width="120px" size="small">
                  <el-form-item label="星星数量">
                    <el-slider
                      v-model="effectConfig.specific.starCount"
                      :min="50"
                      :max="500"
                      @change="updateEffectConfig"
                    />
                  </el-form-item>
                  <el-form-item label="闪烁效果">
                    <el-switch
                      v-model="effectConfig.specific.twinkle"
                      @change="updateEffectConfig"
                    />
                  </el-form-item>
                  <el-form-item label="星座连线">
                    <el-switch
                      v-model="effectConfig.specific.constellation"
                      @change="updateEffectConfig"
                    />
                  </el-form-item>
                </el-form>
              </div>

              <!-- 粒子连线配置 -->
              <div v-if="currentEffect === 'particle'">
                <el-form label-width="120px" size="small">
                  <el-form-item label="粒子数量">
                    <el-slider
                      v-model="effectConfig.specific.particleCount"
                      :min="20"
                      :max="200"
                      @change="updateEffectConfig"
                    />
                  </el-form-item>
                  <el-form-item label="连线距离">
                    <el-slider
                      v-model="effectConfig.specific.linkDistance"
                      :min="50"
                      :max="200"
                      @change="updateEffectConfig"
                    />
                  </el-form-item>
                  <el-form-item label="鼠标交互">
                    <el-switch
                      v-model="effectConfig.specific.mouseInteraction"
                      @change="updateEffectConfig"
                    />
                  </el-form-item>
                  <el-form-item label="粒子颜色">
                    <el-color-picker
                      v-model="effectConfig.specific.particleColor"
                      @change="updateEffectConfig"
                    />
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 性能监控 -->
    <div class="performance-monitor">
      <el-card class="monitor-card">
        <template #header>
          <span>性能监控</span>
        </template>
        
        <div class="monitor-content">
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="monitor-item">
                <div class="monitor-label">CPU 使用率</div>
                <div class="monitor-value">
                  <el-progress 
                    :percentage="performanceData.cpu" 
                    :color="getProgressColor(performanceData.cpu)"
                  />
                </div>
              </div>
            </el-col>
            
            <el-col :span="8">
              <div class="monitor-item">
                <div class="monitor-label">内存使用</div>
                <div class="monitor-value">
                  <el-progress 
                    :percentage="performanceData.memory" 
                    :color="getProgressColor(performanceData.memory)"
                  />
                </div>
              </div>
            </el-col>
            
            <el-col :span="8">
              <div class="monitor-item">
                <div class="monitor-label">帧率 (FPS)</div>
                <div class="monitor-value">
                  <span class="fps-value">{{ performanceData.fps }}</span>
                </div>
              </div>
            </el-col>
          </el-row>
          
          <div class="performance-tips" v-if="showPerformanceTips">
            <el-alert
              title="性能优化建议"
              :description="getPerformanceTips()"
              type="info"
              :closable="false"
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button @click="resetToDefault">重置为默认</el-button>
      <el-button @click="exportConfig">导出配置</el-button>
      <el-button @click="importConfig">导入配置</el-button>
      <el-button type="primary" @click="saveConfig">保存配置</el-button>
    </div>

    <!-- 导入配置对话框 -->
    <el-dialog v-model="showImportDialog" title="导入配置" width="500px">
      <el-input
        v-model="importConfigText"
        type="textarea"
        :rows="10"
        placeholder="请粘贴配置JSON..."
      />
      <template #footer>
        <el-button @click="showImportDialog = false">取消</el-button>
        <el-button type="primary" @click="doImportConfig">确认导入</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Close, ArrowUp, ArrowDown } from '@element-plus/icons-vue'

// 导入特效组件
import Meteor from '../../components/texiao/Meteor.vue'
import NightSky from '../../components/texiao/NightSky.vue'
import Particle from '../../components/texiao/Particle.vue'
import EffectPreview from '../../components/EffectPreview.vue'

export default {
  name: 'EffectManagement',
  components: {
    Close,
    ArrowUp,
    ArrowDown,
    Meteor,
    NightSky,
    Particle,
    EffectPreview
  },
  setup() {
    const currentEffect = ref('none')
    const previewEffect = ref(null)
    const isEffectRunning = ref(false)
    const showImportDialog = ref(false)
    const importConfigText = ref('')

    // 特效配置
    const effectConfig = reactive({
      enabled: true,
      opacity: 80,
      speed: 5,
      specific: {
        // 流星雨配置
        count: 5,
        size: 2,
        theme: 'white',
        // 夜空星辰配置
        starCount: 200,
        twinkle: true,
        constellation: false,
        // 粒子连线配置
        particleCount: 80,
        linkDistance: 100,
        mouseInteraction: true,
        particleColor: '#409eff'
      }
    })

    // 性能数据
    const performanceData = reactive({
      cpu: 15,
      memory: 25,
      fps: 60
    })

    // 特效选项
    const effectOptions = [
      {
        value: 'none',
        label: '无特效',
        description: '关闭所有页面特效，获得最佳性能',
        performance: '最佳',
        size: '0KB'
      },
      {
        value: 'meteor',
        label: '流星雨',
        description: '浪漫的流星雨效果，适合夜间浏览',
        performance: '中等',
        size: '15KB'
      },
      {
        value: 'nightsky',
        label: '夜空星辰',
        description: '静谧的星空背景，营造宁静氛围',
        performance: '良好',
        size: '12KB'
      },
      {
        value: 'particle',
        label: '粒子连线',
        description: '科技感粒子连线效果，适合技术类内容',
        performance: '较高',
        size: '20KB'
      }
    ]

    // 获取特效名称
    const getEffectName = (value) => {
      const effect = effectOptions.find(e => e.value === value)
      return effect ? effect.label : '未知特效'
    }

    // 获取特效状态类型
    const getEffectStatusType = () => {
      if (currentEffect.value === 'none') return 'info'
      return isEffectRunning.value ? 'success' : 'warning'
    }

    // 获取特效状态文本
    const getEffectStatusText = () => {
      if (currentEffect.value === 'none') return '无特效'
      return isEffectRunning.value ? '运行中' : '已停止'
    }

    // 获取性能影响等级
    const getPerformanceImpact = () => {
      const impacts = {
        'none': 0,
        'nightsky': 2,
        'meteor': 3,
        'particle': 4
      }
      return impacts[currentEffect.value] || 0
    }

    // 获取适用场景
    const getEffectScene = (value) => {
      const scenes = {
        'none': '所有场景',
        'meteor': '夜间浏览、浪漫主题',
        'nightsky': '阅读、静谧环境',
        'particle': '技术博客、科技主题'
      }
      return scenes[value] || '通用'
    }

    // 获取特效组件
    const getEffectComponent = (value) => {
      const components = {
        'meteor': 'Meteor',
        'nightsky': 'NightSky',
        'particle': 'Particle'
      }
      return components[value]
    }

    // 获取预览配置
    const getPreviewConfig = (value) => {
      const configs = {
        'meteor': { count: 3, speed: 2 },
        'nightsky': { starCount: 50 },
        'particle': { particleCount: 30 }
      }
      return configs[value] || {}
    }

    // 获取性能标签类型
    const getPerformanceTagType = (performance) => {
      const types = {
        '最佳': 'success',
        '良好': 'success',
        '中等': 'warning',
        '较高': 'danger'
      }
      return types[performance] || 'info'
    }

    // 选择特效
    const selectEffect = (value) => {
      currentEffect.value = value
      isEffectRunning.value = value !== 'none' && effectConfig.enabled
    }

    // 应用特效
    const applyEffect = (value) => {
      selectEffect(value)
      ElMessage.success(`已应用 ${getEffectName(value)} 特效`)
      
      // 这里可以添加实际应用特效的逻辑
      // 例如：通知父组件或全局状态管理器
      window.dispatchEvent(new CustomEvent('effectChange', {
        detail: { effect: value, config: effectConfig }
      }))
    }

    // 切换特效开关
    const toggleEffect = (enabled) => {
      isEffectRunning.value = enabled && currentEffect.value !== 'none'
      ElMessage.success(enabled ? '特效已启用' : '特效已禁用')
    }

    // 更新特效配置
    const updateEffectConfig = () => {
      // 实时更新特效配置
      window.dispatchEvent(new CustomEvent('effectConfigChange', {
        detail: { config: effectConfig }
      }))
    }

    // 获取进度条颜色
    const getProgressColor = (percentage) => {
      if (percentage < 50) return '#67c23a'
      if (percentage < 80) return '#e6a23c'
      return '#f56c6c'
    }

    // 性能提示
    const showPerformanceTips = computed(() => {
      return performanceData.cpu > 70 || performanceData.memory > 80 || performanceData.fps < 30
    })

    const getPerformanceTips = () => {
      const tips = []
      if (performanceData.cpu > 70) tips.push('CPU使用率较高，建议选择轻量特效')
      if (performanceData.memory > 80) tips.push('内存使用较多，建议关闭部分特效')
      if (performanceData.fps < 30) tips.push('帧率较低，建议降低特效复杂度')
      return tips.join('；')
    }

    // 重置为默认
    const resetToDefault = () => {
      ElMessageBox.confirm('确定要重置为默认配置吗？', '确认重置', {
        type: 'warning'
      }).then(() => {
        currentEffect.value = 'none'
        effectConfig.enabled = true
        effectConfig.opacity = 80
        effectConfig.speed = 5
        effectConfig.specific = {}
        ElMessage.success('已重置为默认配置')
      })
    }

    // 导出配置
    const exportConfig = () => {
      const config = {
        effect: currentEffect.value,
        config: effectConfig
      }
      const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'effect-config.json'
      a.click()
      URL.revokeObjectURL(url)
      ElMessage.success('配置已导出')
    }

    // 导入配置
    const importConfig = () => {
      showImportDialog.value = true
    }

    const doImportConfig = () => {
      try {
        const config = JSON.parse(importConfigText.value)
        currentEffect.value = config.effect || 'none'
        Object.assign(effectConfig, config.config || {})
        showImportDialog.value = false
        importConfigText.value = ''
        ElMessage.success('配置导入成功')
      } catch (error) {
        ElMessage.error('配置格式错误，请检查JSON格式')
      }
    }

    // 保存配置
    const saveConfig = () => {
      // 保存到本地存储或发送到服务器
      localStorage.setItem('effectConfig', JSON.stringify({
        effect: currentEffect.value,
        config: effectConfig
      }))
      ElMessage.success('配置已保存')
    }

    // 模拟性能监控
    let performanceTimer = null
    const startPerformanceMonitoring = () => {
      performanceTimer = setInterval(() => {
        // 模拟性能数据变化
        performanceData.cpu = Math.max(10, Math.min(90, performanceData.cpu + (Math.random() - 0.5) * 10))
        performanceData.memory = Math.max(15, Math.min(85, performanceData.memory + (Math.random() - 0.5) * 8))
        performanceData.fps = Math.max(20, Math.min(60, performanceData.fps + (Math.random() - 0.5) * 5))
      }, 2000)
    }

    onMounted(() => {
      // 加载保存的配置
      const savedConfig = localStorage.getItem('effectConfig')
      if (savedConfig) {
        try {
          const config = JSON.parse(savedConfig)
          currentEffect.value = config.effect || 'none'
          Object.assign(effectConfig, config.config || {})
        } catch (error) {
          console.warn('加载配置失败:', error)
        }
      }
      
      startPerformanceMonitoring()
    })

    onUnmounted(() => {
      if (performanceTimer) {
        clearInterval(performanceTimer)
      }
    })

    return {
      currentEffect,
      previewEffect,
      isEffectRunning,
      effectConfig,
      performanceData,
      effectOptions,
      showImportDialog,
      importConfigText,
      showPerformanceTips,
      getEffectName,
      getEffectStatusType,
      getEffectStatusText,
      getPerformanceImpact,
      getEffectScene,
      getEffectComponent,
      getPreviewConfig,
      getPerformanceTagType,
      selectEffect,
      applyEffect,
      toggleEffect,
      updateEffectConfig,
      getProgressColor,
      getPerformanceTips,
      resetToDefault,
      exportConfig,
      importConfig,
      doImportConfig,
      saveConfig
    }
  }
}
</script>

<style scoped>
.effect-management {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.current-status,
.effect-selection,
.effect-config,
.performance-monitor {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.effect-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.effect-item {
  border: 2px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.effect-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
}

.effect-item.active {
  border-color: #409eff;
  background: #f0f9ff;
}

.effect-preview {
  height: 120px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 12px;
  position: relative;
  overflow: hidden;
}

.no-effect-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #c0c4cc;
}

.mini-effect {
  width: 100%;
  height: 100%;
  transform: scale(0.5);
  transform-origin: center;
}

.effect-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.effect-info p {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.effect-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.effect-size {
  font-size: 12px;
  color: #909399;
}

.effect-actions {
  display: flex;
  gap: 8px;
}

.config-section {
  margin-bottom: 24px;
}

.config-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.monitor-item {
  text-align: center;
}

.monitor-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.monitor-value {
  margin-bottom: 8px;
}

.fps-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.performance-tips {
  margin-top: 16px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .effect-management {
    padding: 16px;
  }
  
  .effect-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
</style>
