const db = require('../utils/db');

async function addRoleColumn() {
  try {
    console.log('开始为 users 表添加 role 字段...');
    
    // 检查 role 字段是否已存在
    const checkColumnSql = `
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'users' 
      AND COLUMN_NAME = 'role'
    `;
    
    const existingColumn = await db.query(checkColumnSql);
    
    if (existingColumn.length > 0) {
      console.log('role 字段已存在，跳过添加');
    } else {
      // 添加 role 字段
      const addColumnSql = `
        ALTER TABLE users 
        ADD COLUMN role ENUM('user', 'admin') DEFAULT 'user' 
        AFTER status
      `;
      
      await db.query(addColumnSql);
      console.log('成功添加 role 字段');
    }
    
    // 将 admin123 用户设置为管理员
    const updateAdminSql = `
      UPDATE users 
      SET role = 'admin' 
      WHERE username = 'admin123'
    `;
    
    const result = await db.query(updateAdminSql);
    console.log(`成功设置 admin123 为管理员，影响行数: ${result.affectedRows}`);
    
    // 验证结果
    const verifySql = `
      SELECT id, username, role 
      FROM users 
      WHERE username = 'admin123'
    `;
    
    const adminUser = await db.query(verifySql);
    console.log('管理员用户信息:', adminUser[0]);
    
    console.log('数据库迁移完成！');
    
  } catch (error) {
    console.error('数据库迁移失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addRoleColumn()
    .then(() => {
      console.log('脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { addRoleColumn };
