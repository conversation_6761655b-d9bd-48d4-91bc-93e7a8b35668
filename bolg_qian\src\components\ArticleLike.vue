<template>
  <div class="article-like-container">
    <!-- 点赞按钮 -->
    <div class="like-buttons">
      <el-button
        :type="userLikeStatus === 'like' ? 'primary' : 'default'"
        :icon="userLikeStatus === 'like' ? 'StarFilled' : 'Star'"
        @click="toggleLike('like')"
        :loading="loading"
        class="like-btn"
        size="default"
      >
        <el-icon><StarFilled v-if="userLikeStatus === 'like'" /><Star v-else /></el-icon>
        <span class="like-text">{{ likesCount }}</span>
      </el-button>

      <el-button
        :type="userLikeStatus === 'dislike' ? 'danger' : 'default'"
        @click="toggleLike('dislike')"
        :loading="loading"
        class="dislike-btn"
        size="default"
      >
        <el-icon><ArrowDown /></el-icon>
        <span class="like-text">{{ dislikesCount }}</span>
      </el-button>
    </div>

    <!-- 点赞用户列表弹窗 -->
    <el-dialog
      v-model="showLikeUsers"
      :title="`${currentLikeType === 'like' ? '点赞' : '踩'}用户列表`"
      width="500px"
      :before-close="closeLikeUsersDialog"
    >
      <div class="like-users-content">
        <el-tabs v-model="currentLikeType" @tab-change="handleTabChange">
          <el-tab-pane label="点赞" name="like">
            <div class="users-list">
              <div 
                v-for="user in likeUsers" 
                :key="user.id"
                class="user-item"
              >
                <el-avatar 
                  :src="user.avatar" 
                  :size="32"
                  class="user-avatar"
                >
                  {{ user.username }}
                </el-avatar>
                <div class="user-info">
                  <div class="user-name">{{ user.username }}</div>
                  <div class="like-time">{{ formatTime(user.created_at) }}</div>
                </div>
              </div>
              
              <div v-if="likeUsers.length === 0" class="empty-state">
                <el-empty description="暂无点赞用户" />
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="踩" name="dislike">
            <div class="users-list">
              <div 
                v-for="user in dislikeUsers" 
                :key="user.id"
                class="user-item"
              >
                <el-avatar 
                  :src="user.avatar" 
                  :size="32"
                  class="user-avatar"
                >
                  {{ user.username }}
                </el-avatar>
                <div class="user-info">
                  <div class="user-name">{{ user.username }}</div>
                  <div class="like-time">{{ formatTime(user.created_at) }}</div>
                </div>
              </div>
              
              <div v-if="dislikeUsers.length === 0" class="empty-state">
                <el-empty description="暂无踩用户" />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>

        <!-- 分页 -->
        <el-pagination
          v-if="pagination.total > 0"
          :current-page="pagination.page"
          :page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          @current-change="loadLikeUsers"
          @size-change="loadLikeUsers"
          class="pagination"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Star, StarFilled, ArrowDown } from '@element-plus/icons-vue'
import axios from 'axios'

export default {
  name: 'ArticleLike',
  components: {
    Star,
    StarFilled,
    ArrowDown
  },
  props: {
    articleId: {
      type: [Number, String],
      required: true
    },
    showUsersList: {
      type: Boolean,
      default: true
    }
  },
  emits: ['like-changed'],
  setup(props, { emit }) {
    const loading = ref(false)
    const likesCount = ref(0)
    const dislikesCount = ref(0)
    const userLikeStatus = ref(null) // 'like', 'dislike', null
    const showLikeUsers = ref(false)
    const currentLikeType = ref('like')
    const likeUsers = ref([])
    const dislikeUsers = ref([])
    
    const pagination = reactive({
      page: 1,
      limit: 20,
      total: 0
    })

    // 获取点赞统计
    const loadLikeStats = async () => {
      try {
        const response = await axios.get(`/article-likes/stats/${props.articleId}`)
        if (response.data.code === 200) {
          const data = response.data.data
          likesCount.value = data.likes_count || 0
          dislikesCount.value = data.dislikes_count || 0
          userLikeStatus.value = data.userLikeStatus
        }
      } catch (error) {
        console.error('获取点赞统计失败:', error)
      }
    }

    // 切换点赞状态
    const toggleLike = async (type) => {
      console.log('toggleLike 被调用，type:', type, 'articleId:', props.articleId)
      if (loading.value) return

      loading.value = true
      try {
        console.log('发送点赞请求...')
        const response = await axios.post('/article-likes/toggle', {
          article_id: props.articleId,
          like_type: type
        })
        console.log('点赞请求响应:', response.data)
        
        if (response.data.code === 200) {
          const { action, stats } = response.data.data
          
          // 更新统计数据
          likesCount.value = stats.likes_count || 0
          dislikesCount.value = stats.dislikes_count || 0
          
          // 更新用户点赞状态
          if (action === 'removed') {
            userLikeStatus.value = null
          } else {
            userLikeStatus.value = type
          }
          
          ElMessage.success(response.data.message)
          
          // 触发事件
          emit('like-changed', {
            action,
            type,
            stats: {
              likes_count: likesCount.value,
              dislikes_count: dislikesCount.value,
              userLikeStatus: userLikeStatus.value
            }
          })
        } else {
          ElMessage.error(response.data.message || '操作失败')
        }
      } catch (error) {
        console.error('点赞操作失败:', error)
        ElMessage.error('操作失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 加载点赞用户列表
    const loadLikeUsers = async () => {
      if (!props.showUsersList) return
      
      try {
        const response = await axios.get(`/article-likes/users/${props.articleId}`, {
          params: {
            page: pagination.page,
            limit: pagination.limit,
            like_type: currentLikeType.value
          }
        })
        
        if (response.data.code === 200) {
          const data = response.data.data
          
          if (currentLikeType.value === 'like') {
            likeUsers.value = data.users || []
          } else {
            dislikeUsers.value = data.users || []
          }
          
          pagination.total = data.pagination?.total || 0
        }
      } catch (error) {
        console.error('获取点赞用户列表失败:', error)
        if (error.response?.status === 401) {
          ElMessage.warning('请先登录')
        }
      }
    }

    // 显示点赞用户列表
    const showLikeUsersList = () => {
      if (!props.showUsersList) return
      showLikeUsers.value = true
      loadLikeUsers()
    }

    // 关闭点赞用户列表弹窗
    const closeLikeUsersDialog = () => {
      showLikeUsers.value = false
      likeUsers.value = []
      dislikeUsers.value = []
      pagination.page = 1
      pagination.total = 0
    }

    // 切换标签页
    const handleTabChange = (tab) => {
      currentLikeType.value = tab
      pagination.page = 1
      loadLikeUsers()
    }

    // 格式化时间
    const formatTime = (time) => {
      if (!time) return ''
      const date = new Date(time)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) { // 1天内
        return `${Math.floor(diff / 3600000)}小时前`
      } else {
        return date.toLocaleDateString()
      }
    }

    // 监听文章ID变化
    watch(() => props.articleId, () => {
      if (props.articleId) {
        loadLikeStats()
      }
    }, { immediate: true })

    onMounted(() => {
      if (props.articleId) {
        loadLikeStats()
      }
    })

    return {
      loading,
      likesCount,
      dislikesCount,
      userLikeStatus,
      showLikeUsers,
      currentLikeType,
      likeUsers,
      dislikeUsers,
      pagination,
      toggleLike,
      showLikeUsersList,
      closeLikeUsersDialog,
      handleTabChange,
      loadLikeUsers,
      formatTime
    }
  }
}
</script>

<style scoped>
.article-like-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.like-buttons {
  display: flex;
  gap: 8px;
}

.like-btn, .dislike-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  border-radius: 20px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.like-btn:hover, .dislike-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.like-text {
  font-size: 14px;
  font-weight: 500;
}

.like-users-content {
  max-height: 400px;
}

.users-list {
  max-height: 300px;
  overflow-y: auto;
}

.user-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.user-item:last-child {
  border-bottom: none;
}

.user-avatar {
  flex-shrink: 0;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.like-time {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.empty-state {
  text-align: center;
  padding: 20px;
}

.pagination {
  margin-top: 16px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .like-btn, .dislike-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .like-text {
    font-size: 12px;
  }
}
</style>
