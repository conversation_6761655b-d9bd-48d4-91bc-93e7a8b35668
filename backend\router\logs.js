const fs = require("fs");
const path = require("path");
const Router = require("koa-router");

const logs = new Router();
const { handleResponse } = require("./responseHandler");

logs.get("/showlogs", async (ctx) => {
  try {
    // 合并 error.log 和 info.log
    const errorLogPath = path.join(__dirname, "../logs/error.log");
    const infoLogPath = path.join(__dirname, "../logs/info.log");

    let logsData = [];
    if (fs.existsSync(errorLogPath)) {
      logsData = logsData.concat(
        fs.readFileSync(errorLogPath, "utf-8").split("\n").filter(Boolean)
      );
    }
    if (fs.existsSync(infoLogPath)) {
      logsData = logsData.concat(
        fs.readFileSync(infoLogPath, "utf-8").split("\n").filter(Boolean)
      );
    }

    // 合并后倒序排列，最新日志在前
    logsData = logsData.reverse();

    // 处理筛选参数
    const { page = 1, limit = 50, keyword = "", level = "" } = ctx.query;
    const pageNum = parseInt(page) || 1;
    const limitNum = parseInt(limit) || 50;

    // 过滤关键字和日志级别
    const filteredLogs = logsData.filter(line => {
      // 假设日志格式: [时间][级别] 内容
      let match = line.match(/\[(.*?)\]\s*\[(.*?)\]\s*(.*)/);
      let logLevel = "";
      let content = line;
      if (match) {
        logLevel = match[2].toLowerCase();
        content = match[3];
      }
      // 关键字和级别过滤
      const keywordOk = !keyword || content.includes(keyword);
      const levelOk = !level || logLevel === level.toLowerCase();
      return keywordOk && levelOk;
    });

    const start = (pageNum - 1) * limitNum;
    const paginatedLogs = filteredLogs.slice(start, start + limitNum);

    return handleResponse(ctx, 200, {
      page: pageNum,
      limit: limitNum,
      total: filteredLogs.length,
      logs: paginatedLogs,
    });
  } catch (error) {
    console.error("日志读取失败:", error);
    return handleResponse(ctx, 500, { error: "无法读取日志文件" });
  }
});


module.exports = logs;