<template>
  <div class="article-like-history">
    <div class="page-header">
      <h2>我的点赞记录</h2>
      <p class="page-description">查看您对文章的点赞和踩记录</p>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <el-radio-group v-model="currentFilter" @change="handleFilterChange">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button label="like">点赞</el-radio-button>
        <el-radio-button label="dislike">踩</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 点赞历史列表 -->
    <div class="history-list">
      <el-card v-for="record in historyList" :key="record.id" class="history-item" shadow="hover">
        <div class="record-content">
          <div class="article-info">
            <div class="article-cover">
              <img v-if="record.cover_image" :src="getImageUrl(record.cover_image)" :alt="record.title"
                @error="handleImageError" />
              <div v-else class="default-cover">
                <el-icon>
                  <Document />
                </el-icon>
              </div>
            </div>

            <div class="article-details">
              <h3 class="article-title" @click="goToArticle(record.article_id)">
                {{ record.title || '文章标题' }}
              </h3>
              <p class="article-summary">
                {{ record.summary || '暂无摘要' }}
              </p>
              <div class="article-meta">
                <span class="author">
                  <el-icon>
                    <User />
                  </el-icon>
                  {{ record.author_name || '未知作者' }}
                </span>
                <span class="like-time">
                  <el-icon>
                    <Clock />
                  </el-icon>
                  {{ formatTime(record.created_at) }}
                </span>
              </div>
            </div>
          </div>

          <div class="like-action">
            <el-tag :type="record.like_type === 'like' ? 'success' : 'danger'"
              :icon="record.like_type === 'like' ? 'StarFilled' : 'ArrowDown'" size="large">
              {{ record.like_type === 'like' ? '已点赞' : '已踩' }}
            </el-tag>
          </div>
        </div>
      </el-card>

      <!-- 空状态 -->
      <div v-if="historyList.length === 0 && !loading" class="empty-state">
        <el-empty :description="getEmptyDescription()" :image-size="120">
          <el-button type="primary" @click="goToArticles">
            去浏览文章
          </el-button>
        </el-empty>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <el-skeleton :rows="3" animated />
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="pagination.total > 0" class="pagination-wrapper">
      <el-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.limit"
        :total="pagination.total" :page-sizes="[10, 20, 50]" layout="total, sizes, prev, pager, next, jumper"
        @current-change="loadHistory" @size-change="loadHistory" />
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, User, Clock, StarFilled, ArrowDown } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
// import axios from 'axios'
import { getArticleLikesHistoryApi } from '@/utils/articles'

export default {
  name: 'ArticleLikeHistory',
  components: {
    Document,
    User,
    Clock,
    StarFilled,
    ArrowDown
  },
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const historyList = ref([])
    const currentFilter = ref('')

    const pagination = reactive({
      page: 1,
      limit: 20,
      total: 0
    })

    // 加载点赞历史
    const loadHistory = async () => {
      loading.value = true
      try {
        const params = {
          page: pagination.page,
          limit: pagination.limit
        }

        if (currentFilter.value) {
          params.like_type = currentFilter.value
        }

        const response = await getArticleLikesHistoryApi(params)
        console.log('点赞历史API响应:', response)

        if (response.code === 200) {
          historyList.value = response.data.history || []
          pagination.total = response.data.pagination?.total || 0
          console.log('点赞历史数据:', historyList.value.length, '条')
        } else {
          ElMessage.error(response.message || '获取点赞历史失败')
        }
      } catch (error) {
        console.error('获取点赞历史失败:', error)
        if (error.response?.status === 401) {
          ElMessage.warning('请先登录')
          router.push('/login')
        } else {
          ElMessage.error('获取点赞历史失败，请稍后重试')
        }
      } finally {
        loading.value = false
      }
    }

    // 筛选器变化
    const handleFilterChange = () => {
      pagination.page = 1
      loadHistory()
    }

    // 格式化时间
    const formatTime = (time) => {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 获取图片URL
    const getImageUrl = (imagePath) => {
      if (!imagePath) return ''
      if (imagePath.startsWith('http')) return imagePath
      const imageUrl = `/articles/${imagePath}`
      console.log('图片URL:', imageUrl, '原始路径:', imagePath)
      return imageUrl
    }

    // 图片加载错误处理
    const handleImageError = (event) => {
      console.error('图片加载失败:', event.target.src)
      event.target.style.display = 'none'
      const defaultCover = document.createElement('div')
      defaultCover.className = 'default-cover'
      defaultCover.innerHTML = '<i class="el-icon-document"></i>'
      event.target.parentNode.appendChild(defaultCover)
    }

    // 跳转到文章详情
    const goToArticle = (articleId) => {
      if (articleId) {
        router.push(`/index/articles/detail/${articleId}`)
      }
    }

    // 跳转到文章列表
    const goToArticles = () => {
      router.push('/index/allarticles')
    }

    // 获取空状态描述
    const getEmptyDescription = () => {
      if (currentFilter.value === 'like') {
        return '您还没有点赞过任何文章'
      } else if (currentFilter.value === 'dislike') {
        return '您还没有踩过任何文章'
      } else {
        return '您还没有任何点赞记录'
      }
    }

    onMounted(() => {
      loadHistory()
    })

    return {
      loading,
      historyList,
      currentFilter,
      pagination,
      loadHistory,
      handleFilterChange,
      formatTime,
      getImageUrl,
      handleImageError,
      goToArticle,
      goToArticles,
      getEmptyDescription
    }
  }
}
</script>

<style scoped>
.article-like-history {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filters {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.history-list {
  margin-bottom: 24px;
}

.history-item {
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.history-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.record-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.article-info {
  display: flex;
  flex: 1;
  gap: 16px;
}

.article-cover {
  width: 80px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.article-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-cover {
  color: #c0c4cc;
  font-size: 24px;
}

.article-details {
  flex: 1;
  min-width: 0;
}

.article-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  cursor: pointer;
  transition: color 0.3s ease;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.article-title:hover {
  color: #409eff;
}

.article-summary {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.article-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.article-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.like-action {
  flex-shrink: 0;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.loading-state {
  padding: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .article-like-history {
    padding: 16px;
  }

  .record-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .article-info {
    width: 100%;
  }

  .article-meta {
    flex-direction: column;
    gap: 8px;
  }

  .like-action {
    align-self: flex-end;
  }
}
</style>
