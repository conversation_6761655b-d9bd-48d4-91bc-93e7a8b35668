// 使用 koa-jwt 验证 JWT 令牌
const koaJwt = require("koa-jwt");
const { JWT_SECRET } = require("../utils/config");

// 配置路由白名单
module.exports = koaJwt({
  secret: JWT_SECRET,
  key: "user",
}).unless({
  path: [
    // 用户登录、注册、找回密码等
    /^\/user\/login/,
    /^\/user\/reg/,
    /^\/user\/changePassword/,

    // // 公开获取文章列表、详情、评论等（只读接口）
    // /^\/articles\/pages/,
    // /^\/articles\/pagesNumber/,
    // /^\/articles\/detail/,
    // /^\/articles\/comments/,
    // /^\/articles\/search/,

    // 公开媒体资源
    /^\/media\/listpic/,
    /^\/media\/videos\/.*$/,
    /^\/media\/covers\/.*$/,

    // 静态资源
    /^\/public\/.*\.(mp4|webm|mov|jpg|jpeg|png|gif|svg)$/,
    /^\/uploads\/.*\.(jpg|jpeg|png|gif|svg)$/,

    // // 其它公开下载、上传头像等
    // /^\/upload\/avatars/,
    // /^\/upload\/uploads/,
    // /^\/upload\/download/,
    // /^\/upload\/files/,
    // /^\/articles\/uploadCoverImage/,
  ],
});
