<template>
  <div class="dashboard-file-manager">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2 class="page-title">
            <el-icon><FolderOpened /></el-icon>
            文件管理器
          </h2>
          <p class="page-description">
            统一管理所有文件和文件夹，支持创建、移动、重命名、删除等操作
          </p>
        </div>
        
        <div class="header-actions">
          <el-button type="primary" @click="refreshAll">
            <el-icon><Refresh /></el-icon>
            刷新全部
          </el-button>
          <el-button @click="showHelp">
            <el-icon><QuestionFilled /></el-icon>
            使用帮助
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon images">
                <el-icon><Picture /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.images }}</div>
                <div class="stat-label">图片文件</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon documents">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.documents }}</div>
                <div class="stat-label">文档文件</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon videos">
                <el-icon><VideoPlay /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.videos }}</div>
                <div class="stat-label">视频文件</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Files /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.total }}</div>
                <div class="stat-label">总文件数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 文件管理器主体 -->
    <div class="manager-container">
      <FileManager @stats-updated="updateStats" />
    </div>

    <!-- 帮助对话框 -->
    <el-dialog 
      v-model="helpVisible" 
      title="文件管理器使用帮助" 
      width="600px"
      :show-close="true"
    >
      <div class="help-content">
        <div class="help-section">
          <h4>🗂️ 基本操作</h4>
          <ul>
            <li><strong>创建文件夹</strong>：点击"新建文件夹"按钮，输入文件夹名称</li>
            <li><strong>文件导航</strong>：点击文件夹树或双击文件夹进入</li>
            <li><strong>切换分类</strong>：使用顶部下拉菜单切换不同文件分类</li>
            <li><strong>视图切换</strong>：支持列表视图和网格视图两种模式</li>
          </ul>
        </div>

        <div class="help-section">
          <h4>📁 文件操作</h4>
          <ul>
            <li><strong>移动文件</strong>：点击"移动"按钮，选择目标分类和路径</li>
            <li><strong>重命名</strong>：点击"重命名"按钮，输入新名称</li>
            <li><strong>删除</strong>：点击"删除"按钮，确认删除操作</li>
            <li><strong>跨分类移动</strong>：可以将文件在不同分类间移动</li>
          </ul>
        </div>

        <div class="help-section">
          <h4>🔧 高级功能</h4>
          <ul>
            <li><strong>批量操作</strong>：支持批量选择和操作文件</li>
            <li><strong>搜索功能</strong>：快速查找特定文件</li>
            <li><strong>权限管理</strong>：管理文件访问权限</li>
            <li><strong>自动同步</strong>：文件操作自动同步到数据库</li>
          </ul>
        </div>

        <div class="help-section">
          <h4>⚠️ 注意事项</h4>
          <ul>
            <li>删除操作不可恢复，请谨慎操作</li>
            <li>移动大文件时请耐心等待</li>
            <li>建议定期备份重要文件</li>
            <li>文件名不能包含特殊字符</li>
          </ul>
        </div>
      </div>
      
      <template #footer>
        <el-button type="primary" @click="helpVisible = false">
          我知道了
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  FolderOpened,
  Refresh,
  QuestionFilled,
  Picture,
  Document,
  VideoPlay,
  Files
} from '@element-plus/icons-vue';
import FileManager from '../../components/FileManager.vue';
import { getFileStructureApi } from '../../utils/fileManagerApi';

// 响应式数据
const helpVisible = ref(false);
const stats = ref({
  images: 0,
  documents: 0,
  videos: 0,
  total: 0
});

// 方法
const showHelp = () => {
  helpVisible.value = true;
};

const refreshAll = async () => {
  try {
    await updateStats();
    ElMessage.success('刷新成功');
  } catch (error) {
    ElMessage.error('刷新失败');
  }
};

const updateStats = async () => {
  try {
    // 获取所有分类的文件统计
    const categories = ['images', 'documents', 'videos', 'archives', 'media'];
    let totalFiles = 0;
    const categoryStats = {
      images: 0,
      documents: 0,
      videos: 0,
      total: 0
    };

    for (const category of categories) {
      try {
        const response = await getFileStructureApi(category);
        console.log(`${category} API响应:`, response); // 调试日志

        // 后端响应格式: { code: 200, message: "成功", data: {...} }
        if (response && response.data) {
          const structure = response.data[category];

          if (structure && structure.children) {
            const fileCount = countFiles(structure.children);
            totalFiles += fileCount;

            if (category === 'images') {
              categoryStats.images = fileCount;
            } else if (category === 'documents') {
              categoryStats.documents = fileCount;
            } else if (category === 'videos' || category === 'media') {
              categoryStats.videos += fileCount;
            }
          }
        }
      } catch (error) {
        console.warn(`获取 ${category} 分类统计失败:`, error);
      }
    }

    categoryStats.total = totalFiles;
    stats.value = categoryStats;
  } catch (error) {
    console.error('更新统计信息失败:', error);
  }
};

const countFiles = (items: any[]): number => {
  let count = 0;
  for (const item of items) {
    if (item.type === 'file') {
      count++;
    } else if (item.type === 'folder' && item.children) {
      count += countFiles(item.children);
    }
  }
  return count;
};

// 生命周期
onMounted(() => {
  updateStats();
});
</script>

<style scoped>
.dashboard-file-manager {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.title-section {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.images {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.documents {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.videos {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.total {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.manager-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.help-content {
  max-height: 400px;
  overflow-y: auto;
}

.help-section {
  margin-bottom: 24px;
}

.help-section h4 {
  color: #303133;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
}

.help-section ul {
  margin: 0;
  padding-left: 20px;
}

.help-section li {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.help-section strong {
  color: #303133;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .dashboard-file-manager {
    padding: 16px;
  }
  
  .stats-cards :deep(.el-col) {
    margin-bottom: 16px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .stat-value {
    font-size: 24px;
  }
}
</style>
