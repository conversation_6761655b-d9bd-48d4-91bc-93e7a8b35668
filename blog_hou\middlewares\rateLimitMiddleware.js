// API限流中间件
const logger = require('../plugin/logger');

// 简单内存存储 (生产环境建议使用Redis)
const requestCounts = new Map();
const blockedIPs = new Map();

// 限流配置
const RATE_LIMIT_CONFIG = {
  // 通用限流：每分钟100次请求
  general: {
    windowMs: 60 * 1000,    // 1分钟
    maxRequests: 100,       // 最大请求数
    message: '请求过于频繁，请稍后再试'
  },
  
  // 登录限流：每分钟5次请求
  auth: {
    windowMs: 60 * 1000,
    maxRequests: 5,
    message: '登录尝试过于频繁，请稍后再试'
  },
  
  // 上传限流：每分钟10次请求
  upload: {
    windowMs: 60 * 1000,
    maxRequests: 10,
    message: '上传过于频繁，请稍后再试'
  },
  
  // 聊天限流：每分钟50次请求
  chat: {
    windowMs: 60 * 1000,
    maxRequests: 50,
    message: '聊天消息发送过于频繁，请稍后再试'
  }
};

// 获取客户端IP
function getClientIP(ctx) {
  return ctx.request.ip || 
         ctx.request.header['x-forwarded-for'] || 
         ctx.request.header['x-real-ip'] || 
         ctx.request.socket.remoteAddress || 
         'unknown';
}

// 获取限流键
function getRateLimitKey(ip, path, userId = null) {
  // 对于已登录用户，使用用户ID；否则使用IP
  const identifier = userId || ip;
  return `${identifier}:${path}`;
}

// 检查是否被阻止
function isBlocked(ip) {
  const blockInfo = blockedIPs.get(ip);
  if (!blockInfo) return false;
  
  if (Date.now() > blockInfo.expiry) {
    blockedIPs.delete(ip);
    return false;
  }
  
  return true;
}

// 阻止IP
function blockIP(ip, duration = 5 * 60 * 1000) { // 默认阻止5分钟
  blockedIPs.set(ip, {
    expiry: Date.now() + duration,
    blockedAt: new Date().toISOString()
  });
  
  logger.warn('IP被阻止', { ip, duration, reason: '超过限流阈值' });
}

// 清理过期记录
function cleanupExpiredRecords() {
  const now = Date.now();
  
  // 清理请求计数
  for (const [key, data] of requestCounts.entries()) {
    if (now > data.resetTime) {
      requestCounts.delete(key);
    }
  }
  
  // 清理阻止的IP
  for (const [ip, blockInfo] of blockedIPs.entries()) {
    if (now > blockInfo.expiry) {
      blockedIPs.delete(ip);
    }
  }
}

// 每分钟清理一次过期记录
setInterval(cleanupExpiredRecords, 60 * 1000);

// 创建限流中间件
function createRateLimit(configKey = 'general') {
  const config = RATE_LIMIT_CONFIG[configKey];
  
  if (!config) {
    throw new Error(`未知的限流配置: ${configKey}`);
  }
  
  return async (ctx, next) => {
    const ip = getClientIP(ctx);
    const path = ctx.request.path;
    const userId = ctx.state.user?.id;
    
    // 检查IP是否被阻止
    if (isBlocked(ip)) {
      ctx.status = 429;
      ctx.body = {
        code: 429,
        message: 'IP已被临时阻止，请稍后再试',
        retryAfter: Math.ceil((blockedIPs.get(ip).expiry - Date.now()) / 1000)
      };
      
      logger.warn('阻止的IP尝试访问', { ip, path, userId });
      return;
    }
    
    const key = getRateLimitKey(ip, path, userId);
    const now = Date.now();
    
    // 获取或创建请求记录
    let requestData = requestCounts.get(key);
    if (!requestData || now > requestData.resetTime) {
      requestData = {
        count: 0,
        resetTime: now + config.windowMs,
        firstRequest: now
      };
      requestCounts.set(key, requestData);
    }
    
    // 增加请求计数
    requestData.count++;
    
    // 检查是否超过限制
    if (requestData.count > config.maxRequests) {
      // 记录超限行为
      logger.warn('API限流触发', {
        ip,
        path,
        userId,
        count: requestData.count,
        limit: config.maxRequests,
        configKey
      });
      
      // 如果严重超限，阻止IP
      if (requestData.count > config.maxRequests * 2) {
        blockIP(ip);
      }
      
      ctx.status = 429;
      ctx.body = {
        code: 429,
        message: config.message,
        retryAfter: Math.ceil((requestData.resetTime - now) / 1000),
        limit: config.maxRequests,
        remaining: 0,
        reset: new Date(requestData.resetTime).toISOString()
      };
      return;
    }
    
    // 设置响应头
    ctx.set({
      'X-RateLimit-Limit': config.maxRequests.toString(),
      'X-RateLimit-Remaining': (config.maxRequests - requestData.count).toString(),
      'X-RateLimit-Reset': new Date(requestData.resetTime).toISOString()
    });
    
    // 记录请求信息（仅在接近限制时）
    if (requestData.count > config.maxRequests * 0.8) {
      logger.info('API请求接近限制', {
        ip,
        path,
        userId,
        count: requestData.count,
        limit: config.maxRequests,
        remaining: config.maxRequests - requestData.count
      });
    }
    
    await next();
  };
}

// 获取限流统计信息
function getRateLimitStats() {
  const stats = {
    activeKeys: requestCounts.size,
    blockedIPs: blockedIPs.size,
    topRequests: [],
    blockedIPsList: []
  };
  
  // 获取请求最多的键
  const sortedRequests = Array.from(requestCounts.entries())
    .sort((a, b) => b[1].count - a[1].count)
    .slice(0, 10);
  
  stats.topRequests = sortedRequests.map(([key, data]) => ({
    key,
    count: data.count,
    resetTime: new Date(data.resetTime).toISOString()
  }));
  
  // 获取被阻止的IP列表
  stats.blockedIPsList = Array.from(blockedIPs.entries()).map(([ip, info]) => ({
    ip,
    expiry: new Date(info.expiry).toISOString(),
    blockedAt: info.blockedAt
  }));
  
  return stats;
}

// 手动解除IP阻止
function unblockIP(ip) {
  const wasBlocked = blockedIPs.has(ip);
  blockedIPs.delete(ip);
  
  if (wasBlocked) {
    logger.info('手动解除IP阻止', { ip });
  }
  
  return wasBlocked;
}

// 重置特定键的限流计数
function resetRateLimit(key) {
  const wasReset = requestCounts.has(key);
  requestCounts.delete(key);
  
  if (wasReset) {
    logger.info('重置限流计数', { key });
  }
  
  return wasReset;
}

module.exports = {
  // 预定义的限流中间件
  generalRateLimit: createRateLimit('general'),
  authRateLimit: createRateLimit('auth'),
  uploadRateLimit: createRateLimit('upload'),
  chatRateLimit: createRateLimit('chat'),
  
  // 自定义限流中间件创建器
  createRateLimit,
  
  // 管理功能
  getRateLimitStats,
  unblockIP,
  resetRateLimit,
  isBlocked,
  
  // 配置
  RATE_LIMIT_CONFIG
};
