const Koa = require("koa");
const Router = require("koa-router");
const path = require("path");
const fs = require("fs");
const { handleResponse } = require("../../middlewares/responseHandler");
const app = new Koa();
const show = new Router();

const imageDir = path.join(__dirname, "../../public/images");

// 图片列表接口，支持分页，每页默认20张
show.post("/list", async (ctx) => {
  const { page = 1, limit = 20 } = ctx.request.body;
  const pageNum = parseInt(page, 10);
  const limitNum = parseInt(limit, 10);

  try {
    // 检查目录是否存在
    if (!fs.existsSync(imageDir)) {
      console.error('图片目录不存在:', imageDir);
      return handleResponse(ctx, 404, { error: '图片目录不存在' });
    }

    // 读取目录中的所有文件
    const allFiles = fs.readdirSync(imageDir);
    
    // 过滤出图片文件
    const imageFiles = allFiles.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'].includes(ext);
    });

    // 计算分页起止索引
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;

    // 取分页数据
    const pagedFiles = imageFiles.slice(startIndex, endIndex);

    // 返回相对路径的数组（前端会通过代理访问）
    const imageUrls = pagedFiles.map((file) => `/images/${file}`);
    
    console.log(`返回第${pageNum}页图片，共${pagedFiles.length}张，总计${imageFiles.length}张`);
    ctx.body = imageUrls;
    
  } catch (error) {
    console.error('处理图片列表失败:', error);
    return handleResponse(ctx, 500, { error: '处理图片列表失败' });
  }
});

// 使用 stream 提供图片访问接口
show.get("/image/:filename", async (ctx) => {
  const filePath = path.join(imageDir, ctx.params.filename);
  
  try {
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      ctx.status = 404;
      ctx.body = "图片文件不存在";
      return;
    }

    // 获取文件信息
    const stats = fs.statSync(filePath);
    if (!stats.isFile()) {
      ctx.status = 400;
      ctx.body = "请求的不是文件";
      return;
    }

    // 设置响应头
    // 不再手动设置CORS头，让全局CORS中间件处理
    ctx.set("Content-Type", getMimeType(path.extname(filePath)));
    ctx.set("Content-Length", stats.size);
    ctx.set("Cache-Control", "public, max-age=31536000"); // 缓存1年

    // 使用 stream 读取并返回文件
    const readStream = fs.createReadStream(filePath);
    
    // 错误处理
    readStream.on('error', (err) => {
      console.error('读取图片文件失败:', err);
      if (!ctx.res.headersSent) {
        ctx.status = 500;
        ctx.body = "读取图片文件失败";
      }
    });

    // 设置响应体为 stream
    ctx.body = readStream;
    console.log("访问图片：" + filePath);
    
  } catch (error) {
    console.error('处理图片请求失败:', error);
    ctx.status = 500;
    ctx.body = "服务器内部错误";
  }
});

// 辅助函数：根据文件扩展名获取 MIME 类型
function getMimeType(ext) {
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.bmp': 'image/bmp',
    '.svg': 'image/svg+xml'
  };
  return mimeTypes[ext.toLowerCase()] || 'application/octet-stream';
}

module.exports = show;
