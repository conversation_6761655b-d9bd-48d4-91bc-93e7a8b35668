<template>
  <div class="english-translator">
    <el-card class="translator-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>英文填空练习（Ollama 本地大模型）</span>
          <div class="status-info">
            <el-tag
              :type="aiStatus === 'online' ? 'success' : aiStatus === 'checking' ? 'warning' : 'danger'"
              size="small"
              effect="dark"
            >
              <span v-if="aiStatus === 'online'">🟢 在线</span>
              <span v-else-if="aiStatus === 'checking'">🟡 检测中</span>
              <span v-else>🔴 离线</span>
            </el-tag>
            <el-tooltip v-if="modelInfo" :content="modelInfo" placement="bottom">
              <el-button
                type="primary"
                size="small"
                circle
                @click="checkModelConnection"
                :loading="aiStatus === 'checking'"
              >
                <template v-if="aiStatus !== 'checking'">🔄</template>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </template>

      <div class="reverse-mode">
        <div class="reverse-prompt">
          <div class="section-title dark-title">
            <span>🇨🇳 请将下面的中文翻译成英文</span>
            <el-button
              type="success"
              size="small"
              @click="generateChinesePrompt"
              :loading="generating"
              round
            >
              <template v-if="!generating">🎲 换一个</template>
              <template v-else>生成中...</template>
            </el-button>
          </div>
          <div class="chinese-prompt" :class="{ 'generating-animation': generating }">
            {{ chinesePrompt || '🚀 点击"换一个"按钮开始练习' }}
          </div>
        </div>
        
        <div class="sentence-practice" v-if="chinesePrompt">
          <div class="section-title dark-title">
            <span>🇬🇧 在空白处填写英文单词</span>
            <el-button
              type="warning"
              size="small"
              @click="checkSentenceAnswer"
              :disabled="sentenceAnswerChecked"
              round
            >
              <template v-if="!sentenceAnswerChecked">✅ 检查答案</template>
              <template v-else>已检查</template>
            </el-button>
          </div>
          
          <div class="sentence-container">
            <div class="sentence-words">
              <template v-for="(word, index) in sentenceWords" :key="index">
                <div class="word-container">
                  <input 
                    v-if="word.hidden"
                    type="text"
                    v-model="sentenceAnswers[index]"
                    class="sentence-input"
                    :class="{
                      'correct-word': isWordCorrect(index) || isInputCorrect(index), 
                      'incorrect-word': isWordIncorrect(index)
                    }"
                    :disabled="sentenceAnswerChecked"
                    @input="checkInputCorrectness(index)"
                  />
                  <span v-else class="visible-sentence-word">{{ word.text }}</span>
                  <div class="sentence-underline"></div>
                  <div v-if="sentenceAnswerChecked && word.hidden && !isWordCorrect(index)" class="word-correction">
                    {{ word.text }}
                  </div>
                  <div v-if="word.hidden && !sentenceAnswerChecked" class="word-hint">
                    <el-tooltip :content="getFullHint(index)" placement="bottom">
                      <el-button
                        type="text"
                        size="small"
                        class="hint-button"
                        @click="getHint(index)"
                      >
                        💡 提示
                      </el-button>
                    </el-tooltip>
                  </div>
                </div>
                <span class="word-space" v-if="index < sentenceWords.length - 1"> </span>
              </template>
            </div>
          </div>
          
          <div class="sentence-controls" v-if="sentenceAnswerChecked">
            <el-button type="primary" @click="nextSentence" size="large" round>
              🎯 下一题
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

// Ollama 配置
const OLLAMA_CONFIG = {
  name: 'Ollama',
  apiBase: 'http://localhost:11434/api/chat',
  modelName: 'qwen2.5:1.5b',
  timeout: 30000,
  maxRetries: 3
};

// 提示词模板
const PROMPT_TEMPLATES = {
  chineseGeneration: [
    "请生成一句适合英语初学者翻译的中文日常对话，长度12-18字，内容关于日常生活、工作或学习，不要包含英文字母或特殊符号。",
    "请创建一个简单的中文句子，适合英语学习者练习翻译，内容涉及家庭、朋友或兴趣爱好，长度10-16字，纯中文表达。",
    "请写一句中文日常用语，难度适中，适合翻译成英文练习，内容可以是描述天气、食物或活动，长度12-20字。"
  ],
  translationInstruction: function(chinese) {
    return `请将下面的中文句子翻译成自然、地道的英文，要求：
1. 使用简单易懂的词汇，适合英语学习者
2. 语法正确，表达自然
3. 只返回英文翻译结果，不要其他解释

中文句子：${chinese}`;
  }
};

const aiStatus = ref('checking');
const modelInfo = ref('');
const connectionError = ref('');

// 英文练习模式核心变量
const chinesePrompt = ref('');
const englishReference = ref('');
const generating = ref(false);
const sentenceWords = ref([]);
const sentenceAnswers = reactive({});
const sentenceAnswerChecked = ref(false);
const wordHints = reactive({});
const inputCorrectWords = reactive({});

const MAX_CHINESE_RETRY = 5;
const CHINESE_ONLY_REGEX = /^[\u4e00-\u9fa5，。！？、“”‘’（）《》：；—…·\s]{8,}$/; // 只允许常用中文和标点，长度8以上

// 调用 Ollama API
async function callOllamaAPI(messages, retryCount = 0) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), OLLAMA_CONFIG.timeout);

    const response = await fetch(OLLAMA_CONFIG.apiBase, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: OLLAMA_CONFIG.modelName,
        messages: messages,
        stream: false,
        temperature: 0.7,
        max_tokens: 200
      }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const content = data.message?.content || '';

    if (!content) {
      throw new Error('响应中未找到有效内容');
    }

    return content.trim();

  } catch (error) {
    if (retryCount < OLLAMA_CONFIG.maxRetries) {
      console.warn(`API调用失败，正在重试 (${retryCount + 1}/${OLLAMA_CONFIG.maxRetries}):`,
        error instanceof Error ? error.message : error);

      await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
      return callOllamaAPI(messages, retryCount + 1);
    }

    throw error;
  }
}

// 检查模型连接状态
async function checkModelConnection() {
  aiStatus.value = 'checking';
  connectionError.value = '';

  try {
    const content = await callOllamaAPI([{ role: 'user', content: '你好' }]);

    aiStatus.value = 'online';
    modelInfo.value = `${OLLAMA_CONFIG.name} - ${OLLAMA_CONFIG.modelName}`;
    ElMessage.success('Ollama 连接成功');
  } catch (error) {
    aiStatus.value = 'offline';
    connectionError.value = error instanceof Error ? error.message : '未知错误';
    ElMessage.error(`Ollama 连接失败: ${connectionError.value}`);
  }
}



// 优化的中文句子生成函数
async function generateChinesePrompt(retry = 0) {
  generating.value = true;
  sentenceAnswerChecked.value = false;

  // 检查模型连接状态
  if (aiStatus.value === 'offline') {
    ElMessage.error('模型离线，请检查连接');
    generating.value = false;
    return;
  }

  try {
    // 使用提示词模板
    const randomPrompt = PROMPT_TEMPLATES.chineseGeneration[
      Math.floor(Math.random() * PROMPT_TEMPLATES.chineseGeneration.length)
    ];

    const zh = await callOllamaAPI([{ role: "user", content: randomPrompt }]);
    const cleanZh = zh.replace(/["“”]/g, '').trim();

    // 判断是否为纯中文（无英文字母且长度合适）
    if (!cleanZh || /[a-zA-Z]/.test(cleanZh) || !CHINESE_ONLY_REGEX.test(cleanZh)) {
      if (retry < MAX_CHINESE_RETRY) {
        await generateChinesePrompt(retry + 1);
        return;
      } else {
        ElMessage.error('多次生成均未获得纯中文句子，请稍后重试');
        generating.value = false;
        return;
      }
    }
    chinesePrompt.value = cleanZh;

    // 使用翻译提示词模板
    const translatePrompt = PROMPT_TEMPLATES.translationInstruction(cleanZh);

    const englishTranslation = await callOllamaAPI([{ role: "user", content: translatePrompt }]);

    englishReference.value = englishTranslation;
    processSentenceForPractice();
    ElMessage.success('已生成新的练习题');
  } catch (error) {
    console.error('生成练习题出错:', error);
    ElMessage.error(`生成练习题失败: ${error instanceof Error ? error.message : '未知错误'}`);

    // 如果是连接错误，更新状态
    const errorMsg = error instanceof Error ? error.message : '';
    if (errorMsg.includes('fetch') || errorMsg.includes('timeout') || errorMsg.includes('HTTP')) {
      aiStatus.value = 'offline';
      connectionError.value = errorMsg;
    }
  } finally {
    generating.value = false;
  }
}

// 处理句子，为练习做准备
function processSentenceForPractice() {
  if (!englishReference.value) return;
  const words = englishReference.value.split(/\s+/);
  const processed = [];
  Object.keys(sentenceAnswers).forEach(key => delete sentenceAnswers[key]);
  Object.keys(wordHints).forEach(key => delete wordHints[key]);
  Object.keys(inputCorrectWords).forEach(key => delete inputCorrectWords[key]);
  words.forEach((word, index) => {
    const cleanWord = word.replace(/[.,!?;:'"()\[\]{}]/g, '');
    const shouldHide = cleanWord.length > 3 && Math.random() < 0.5;
    processed.push({
      text: word,
      hidden: shouldHide,
      index
    });
  });
  sentenceWords.value = processed;
}

// 实时检查输入单词是否正确
function checkInputCorrectness(index) {
  if (!sentenceWords.value[index] || !sentenceWords.value[index].hidden) return;
  const userAnswer = (sentenceAnswers[index] || '').trim().replace(/[.,!?;:'"()\[\]{}]/g, '');
  const correctAnswer = sentenceWords.value[index].text.replace(/[.,!?;:'"()\[\]{}]/g, '');
  if (userAnswer.toLowerCase() === correctAnswer.toLowerCase()) {
    inputCorrectWords[index] = true;
  } else {
    inputCorrectWords[index] = false;
  }
}
function isInputCorrect(index) {
  return inputCorrectWords[index] === true;
}

// 检查句子答案
function checkSentenceAnswer() {
  let correctCount = 0;
  let totalHidden = 0;
  sentenceWords.value.forEach((word, index) => {
    if (word.hidden) {
      totalHidden++;
      const userAnswer = (sentenceAnswers[index] || '').trim().replace(/[.,!?;:'"()\[\]{}]/g, '');
      const correctAnswer = word.text.replace(/[.,!?;:'"()\[\]{}]/g, '');
      if (userAnswer.toLowerCase() === correctAnswer.toLowerCase()) {
        correctCount++;
      }
    }
  });
  sentenceAnswerChecked.value = true;
  if (totalHidden === 0) {
    ElMessage.info('没有需要填写的单词');
    return;
  }
  const score = Math.round((correctCount / totalHidden) * 100);
  if (score === 100) {
    ElMessage.success(`太棒了！所有答案都正确！`);
  } else if (score >= 80) {
    ElMessage.success(`做得很好！正确率: ${score}%`);
  } else if (score >= 60) {
    ElMessage.warning(`还不错！正确率: ${score}%`);
  } else {
    ElMessage.error(`需要更多练习！正确率: ${score}%`);
  }
}
function isWordCorrect(index) {
  if (!sentenceAnswerChecked.value || !sentenceWords.value[index]) return false;
  const userAnswer = (sentenceAnswers[index] || '').trim().replace(/[.,!?;:'"()\[\]{}]/g, '');
  const correctAnswer = sentenceWords.value[index].text.replace(/[.,!?;:'"()\[\]{}]/g, '');
  return userAnswer.toLowerCase() === correctAnswer.toLowerCase();
}
function isWordIncorrect(index) {
  if (!sentenceAnswerChecked.value || !sentenceWords.value[index]) return false;
  const userAnswer = (sentenceAnswers[index] || '').trim();
  if (!userAnswer) return false;
  return !isWordCorrect(index);
}
function nextSentence() {
  sentenceAnswerChecked.value = false;
  generateChinesePrompt();
}

// 获取提示
function getHint(index) {
  if (!sentenceWords.value[index] || !sentenceWords.value[index].hidden) return;
  const word = sentenceWords.value[index].text;
  if (!wordHints[index]) {
    wordHints[index] = 1;
  } else if (wordHints[index] < 3) {
    wordHints[index]++;
  }
  const hintLevel = wordHints[index];
  let hint = '';
  if (hintLevel === 1) {
    hint = word.charAt(0) + '...';
  } else if (hintLevel === 2) {
    const halfLength = Math.ceil(word.length / 2);
    hint = word.substring(0, halfLength) + '...';
  } else {
    hint = word.substring(0, word.length - 1) + '?';
  }
  return hint;
}
function getFullHint(index) {
  if (!sentenceWords.value[index] || !sentenceWords.value[index].hidden) return '';
  const word = sentenceWords.value[index].text;
  const hintLevel = wordHints[index] || 0;
  if (hintLevel === 0) {
    return '点击获取提示';
  } else if (hintLevel === 1) {
    return `提示: 单词以 "${word.charAt(0)}" 开头`;
  } else if (hintLevel === 2) {
    const halfLength = Math.ceil(word.length / 2);
    return `提示: ${word.substring(0, halfLength)}...`;
  } else {
    return `提示: ${word.substring(0, word.length - 1)}?`;
  }
}

// 初始化时检查连接并生成第一句
async function initialize() {
  await checkModelConnection();
  if (aiStatus.value === 'online') {
    generateChinesePrompt();
  }
}

initialize();
</script>

<style scoped>
.english-translator {
  margin: 0 auto;
  max-width: 1000px;
  padding: 20px;
}

.translator-card {
  margin-bottom: 20px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.translator-card :deep(.el-card__header) {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  padding: 20px 24px;
}

.translator-card :deep(.el-card__body) {
  padding: 0;
  background: transparent;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header > span {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-header > span::before {
  content: "🎯";
  font-size: 20px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.reverse-mode {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.reverse-prompt {
  padding: 24px;
  border-radius: 16px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  box-shadow: 0 8px 24px rgba(30, 60, 114, 0.3);
  position: relative;
  overflow: hidden;
}

.reverse-prompt::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.dark-title {
  color: white;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.chinese-prompt {
  font-size: 24px;
  font-weight: 500;
  margin-top: 16px;
  padding: 24px;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  line-height: 1.6;
  position: relative;
  z-index: 1;
}
.sentence-practice {
  padding: 24px;
  border-radius: 16px;
  background: linear-gradient(135deg, #0f0c29 0%, #302b63 50%, #24243e 100%);
  color: white;
  box-shadow: 0 8px 24px rgba(15, 12, 41, 0.4);
  position: relative;
  overflow: hidden;
}

.sentence-practice::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%);
  pointer-events: none;
}

.sentence-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.sentence-words {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12px;
  margin-bottom: 32px;
  line-height: 2.2;
  font-size: 26px;
  max-width: 800px;
}

.word-container {
  position: relative;
  display: inline-flex;
  align-items: center;
  margin: 4px 6px;
  transition: all 0.3s ease;
}

.word-container:hover {
  transform: translateY(-2px);
}

.sentence-input {
  min-width: 80px;
  max-width: 150px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  outline: none;
  text-align: center;
  padding: 8px 12px;
  font-size: 24px;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.sentence-input:focus {
  border-color: #64b5f6;
  background: rgba(100, 181, 246, 0.2);
  box-shadow: 0 0 20px rgba(100, 181, 246, 0.3);
  transform: scale(1.05);
}

.visible-sentence-word {
  font-size: 26px;
  font-weight: 500;
  color: #e8eaf6;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  padding: 4px 8px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
}

.sentence-underline {
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #64b5f6, #42a5f5);
  border-radius: 2px;
  opacity: 0.7;
}
.sentence-input.correct-word {
  border-color: #4caf50;
  background: rgba(76, 175, 80, 0.2);
  color: #81c784;
  box-shadow: 0 0 20px rgba(76, 175, 80, 0.4);
}

.sentence-input.incorrect-word {
  border-color: #f44336;
  background: rgba(244, 67, 54, 0.2);
  color: #e57373;
  box-shadow: 0 0 20px rgba(244, 67, 54, 0.4);
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

.word-correction {
  position: absolute;
  top: calc(100% + 8px);
  left: 50%;
  transform: translateX(-50%);
  font-size: 14px;
  color: #4caf50;
  background: rgba(76, 175, 80, 0.9);
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.word-hint {
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.hint-button {
  padding: 4px 8px;
  font-size: 12px;
  color: #90caf9;
  background: rgba(144, 202, 249, 0.2);
  border: 1px solid rgba(144, 202, 249, 0.3);
  border-radius: 12px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.hint-button:hover {
  color: #42a5f5;
  background: rgba(66, 165, 245, 0.3);
  border-color: #42a5f5;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(66, 165, 245, 0.3);
}

.sentence-controls {
  margin-top: 32px;
  display: flex;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.sentence-controls .el-button {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.sentence-controls .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
}

.word-space {
  width: 12px;
}

/* 加载动画 */
@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

.generating-animation {
  animation: pulse 1.5s ease-in-out infinite;
}

/* 卡片进入动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.translator-card {
  animation: slideInUp 0.6s ease-out;
}

.reverse-mode > * {
  animation: slideInUp 0.8s ease-out;
}

.reverse-mode > *:nth-child(2) {
  animation-delay: 0.2s;
}

/* 输入框聚焦效果增强 */
.sentence-input:focus {
  animation: focusGlow 0.3s ease-out;
}

@keyframes focusGlow {
  0% {
    box-shadow: 0 0 5px rgba(100, 181, 246, 0.3);
  }
  100% {
    box-shadow: 0 0 20px rgba(100, 181, 246, 0.3);
  }
}

/* 按钮悬停效果增强 */
.el-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.el-button:hover {
  transform: translateY(-1px);
}

.el-button:active {
  transform: translateY(0);
}

/* 状态标签样式增强 */
.status-info .el-tag {
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .english-translator {
    padding: 12px;
  }

  .translator-card {
    border-radius: 12px;
  }

  .reverse-mode {
    padding: 16px;
    gap: 16px;
  }

  .reverse-prompt,
  .sentence-practice {
    padding: 16px;
    border-radius: 12px;
  }

  .chinese-prompt {
    font-size: 20px;
    padding: 16px;
  }

  .sentence-words {
    font-size: 22px;
    gap: 8px;
  }

  .sentence-input,
  .visible-sentence-word {
    font-size: 20px;
  }

  .sentence-input {
    min-width: 60px;
    max-width: 120px;
    padding: 6px 8px;
  }
}

@media (max-width: 480px) {
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .section-title {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .chinese-prompt {
    font-size: 18px;
  }

  .sentence-words {
    font-size: 18px;
  }

  .sentence-input,
  .visible-sentence-word {
    font-size: 16px;
  }
}
</style>