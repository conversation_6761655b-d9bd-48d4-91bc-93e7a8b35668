const db = require("../utils/db");

async function checkArticles() {
  try {
    console.log("🔍 查询数据库中的文章...\n");

    // 先查看表结构
    console.log("📋 查看articles表结构:");
    const columns = await db.query("DESCRIBE articles");
    columns.forEach(col => {
      console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(可空)' : '(非空)'}`);
    });
    console.log("");

    const articles = await db.query(`
      SELECT id, title, views, created_at
      FROM articles
      WHERE is_deleted = 0
      ORDER BY id ASC
      LIMIT 10
    `);

    if (articles.length === 0) {
      console.log("❌ 数据库中没有文章");
      
      // 创建一个测试文章
      console.log("📝 创建测试文章...");
      const result = await db.query(`
        INSERT INTO articles (title, content, summary, views, is_deleted, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        '测试文章 - 浏览记录功能',
        '这是一篇用于测试浏览记录功能的文章。\n\n## 功能特点\n\n1. 记录用户浏览时长\n2. 记录滚动深度\n3. 统计设备类型\n4. 支持匿名用户',
        '这是一篇用于测试浏览记录功能的测试文章',
        0,
        0
      ]);
      
      console.log("✅ 测试文章创建成功，ID:", result.insertId);
      return result.insertId;
    } else {
      console.log("✅ 找到以下文章:");
      articles.forEach(article => {
        console.log(`ID: ${article.id}, 标题: ${article.title}, 浏览量: ${article.views || 0}`);
      });
      return articles[0].id;
    }
  } catch (error) {
    console.error("❌ 查询文章失败:", error.message);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkArticles()
    .then((articleId) => {
      console.log(`\n✅ 可用的文章ID: ${articleId}`);
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ 脚本执行失败:", error);
      process.exit(1);
    });
}

module.exports = { checkArticles };
