// articleService.js
const db = require("../db");
const path = require("path");
const fs = require("fs");

// 根据 user_id 获取文章
async function getArticleByUserId(user_id) {
  const sql = `SELECT * FROM articles WHERE user_id = ? AND is_deleted != 1`;
  const result = await db.query(sql, [user_id]);
  return result;
}

// 分页获取文章
async function getArticleByPage({ user_id, page, limit }) {
  const start = (page - 1) * limit;
  if (isNaN(start) || isNaN(limit) || limit <= 0) {
    throw new Error("Invalid pagination parameters");
  }
  const sql = `SELECT * FROM articles WHERE user_id = ? AND is_deleted != 1 LIMIT ?, ?`;
  const result = await db.query(sql, [user_id, start, limit]);
  return result;
}

// 增加文章
async function addArticle(
  user_id,
  title,
  summary,
  tags,
  category,
  content,
  cover_image
) {
  const sql = `INSERT INTO articles(user_id, title, summary, tags, content, category, cover_image) VALUES(?, ?, ?, ?, ?, ?, ?)`;
  const result = await db.query(sql, [
    user_id,
    title,
    summary,
    tags,
    content,
    category,
    cover_image,
  ]);
  return result.insertId;
}

// 更新文章
async function updateArticle(
  id,
  user_id,
  title,
  summary,
  content,
  cover_image
) {
  const sql = `UPDATE articles SET user_id = ?, title = ?, summary = ?, content = ?, cover_image = ? WHERE id = ?`;
  const result = await db.query(sql, [
    user_id,
    title,
    summary,
    content,
    cover_image,
    id,
  ]);
  return result.affectedRows > 0;
}


// 真删除文章
async function deleteArticle(id) {
  // 先查出封面图片名
  const articleSql = `SELECT cover_image FROM articles WHERE id = ?`;
  const articleResult = await db.query(articleSql, [id]);
  let cover_image = articleResult[0]?.cover_image;

  // 先删除数据库记录
  const sql = `DELETE FROM articles WHERE id = ?`;
  const result = await db.query(sql, [id]);

  // 删除图片文件
  if (cover_image) {
    const uploadDir = path.join(__dirname, "../../uploads/articles");
    const imgPath = path.join(uploadDir, cover_image);
    if (fs.existsSync(imgPath)) {
      try {
        fs.unlinkSync(imgPath);
      } catch (e) {
        // 可以记录日志
        console.warn("删除封面图片失败:", imgPath, e);
      }
    }
  }

  return result.affectedRows > 0;
}

// 假删除文章 将文章的is_delete字段设置为1
async function fakeDeleteArticle(id) {
  const sql = `UPDATE articles SET is_deleted = 1 WHERE id = ?`;
  const result = await db.query(sql, [id]);
  return result.affectedRows > 0;
}

// 搜索文章
async function searchArticle(keyword) {
  const sql = `SELECT * FROM articles WHERE title LIKE ? AND is_deleted != 1`;
  const result = await db.query(sql, [`%${keyword}%`]);
  return result;
}

// 获取文章的评论（返回扁平数组，不带children字段）
async function commentArticle(article_id) {
  const sql = `SELECT * FROM comments WHERE article_id = ? ORDER BY created_at ASC`;
  const result = await db.query(sql, [article_id]);
  // 根据comments的 username 查询users表获取用户的avatar
  const commentsWithAvatars = await Promise.all(
    result.map(async (comment) => {
      const userSql = `SELECT avatar FROM users WHERE username = ?`;
      const userResult = await db.query(userSql, [comment.username]);
      // 如果用户存在，返回评论和用户头像，否则返回评论和默认头像
      return {
        ...comment,
        avatar: userResult.length > 0 ? userResult[0].avatar : "moren.png",
      };
    })
  );
  // 只返回扁平数组，不要组装children字段
  return commentsWithAvatars;
}

// 提交评论
async function addComment(username, article_id, content, parent_id) {
  const sql = `INSERT INTO comments(username, article_id, content, parent_id) VALUES (?, ?, ?, ?)`;
  const result = await db.query(sql, [
    username,
    article_id,
    content,
    parent_id,
  ]);
  return result.insertId;
}

// 详情
async function getArticleById(id) {
  const sql = `SELECT * FROM articles WHERE id = ? AND is_deleted != 1`;
  const result = await db.query(sql, [id]);
  if (!result[0]) return null;
  const user_id = result[0].user_id;
  const userSql = `SELECT username, avatar FROM users WHERE id = ?`;
  const userResult = await db.query(userSql, [user_id]);
  result[0].authorName = userResult[0]?.username || "";
  result[0].authorAvatar = userResult[0]?.avatar || "";
  return result;
}

// 设置文章分享状态
async function setArticleShare(id, is_share) {
  const sql = `UPDATE articles SET is_share = ? WHERE id = ?`;
  const result = await db.query(sql, [is_share, id]);
  return result.affectedRows > 0;
}

// 获取is_share为1的所有文章
async function getSharedArticles() {
  const sql = `SELECT * FROM articles WHERE is_share = 1 AND is_deleted != 1`;
  const result = await db.query(sql);
  return result;
}

// 恢复文章
async function restoreArticle(id) {
  const sql = `UPDATE articles SET is_deleted = 0 WHERE id = ?`;
  const result = await db.query(sql, [id]);
  return result.affectedRows > 0;
}
// 通过用户user_id获取用户删除的文章
async function getDeletedArticlesByUserId(user_id) {
  const sql = `SELECT * FROM articles WHERE user_id = ? AND is_deleted = 1`;
  const result = await db.query(sql, [user_id]);
  return result;
}

// 获取所有公开的文章（不需要用户认证）
async function getAllPublicArticles({ page, limit }) {
  const start = (page - 1) * limit;
  if (isNaN(start) || isNaN(limit) || limit <= 0) {
    throw new Error("Invalid pagination parameters");
  }
  const sql = `SELECT a.*, u.username as authorName, u.avatar as authorAvatar
               FROM articles a
               LEFT JOIN users u ON a.user_id = u.id
               WHERE a.is_deleted != 1
               ORDER BY a.created_at DESC
               LIMIT ?, ?`;
  const result = await db.query(sql, [start, limit]);

  // 将 views 字段映射为 viewCount，保持与前端一致
  result.forEach(article => {
    article.viewCount = article.views || 0;
  });

  return result;
}

// 获取公开文章的总数
async function getPublicArticlesCount() {
  const sql = `SELECT COUNT(*) as total FROM articles WHERE is_deleted != 1`;
  const result = await db.query(sql);
  return result[0].total;
}

// 获取公开的文章详情（不需要用户认证）
async function getPublicArticleById(id) {
  const sql = `SELECT a.*, u.username as authorName, u.avatar as authorAvatar
               FROM articles a
               LEFT JOIN users u ON a.user_id = u.id
               WHERE a.id = ? AND a.is_deleted != 1`;
  const result = await db.query(sql, [id]);
  if (result[0]) {
    // 将 views 字段映射为 viewCount，保持与前端一致
    result[0].viewCount = result[0].views || 0;
  }
  return result[0] || null;
}

// 获取公开文章的评论（不需要用户认证）
async function getPublicArticleComments(article_id) {
  const sql = `SELECT c.*, u.avatar
               FROM comments c
               LEFT JOIN users u ON c.username = u.username
               WHERE c.article_id = ?
               ORDER BY c.created_at ASC`;
  const result = await db.query(sql, [article_id]);
  return result.map(comment => ({
    ...comment,
    avatar: comment.avatar || "moren.png",
    author: comment.username, // 添加author字段以兼容前端
    parent_id: comment.parent_id === 0 ? null : comment.parent_id // 统一parent_id格式
  }));
}

module.exports = {
  getArticleByUserId,
  getArticleByPage,
  addArticle,
  updateArticle,
  deleteArticle,
  fakeDeleteArticle,
  searchArticle,
  commentArticle,
  addComment,
  getArticleById,
  setArticleShare,
  getSharedArticles,
  restoreArticle,
  getDeletedArticlesByUserId,
  getAllPublicArticles,
  getPublicArticlesCount,
  getPublicArticleById,
  getPublicArticleComments,
};
