// WebRTC 客户端管理器
import { io } from 'socket.io-client'
import { ElMessage, ElNotification } from 'element-plus'

// 通话状态枚举
export const CALL_STATUS = {
  CALLING: 'calling',
  RINGING: 'ringing',
  CONNECTED: 'connected',
  ENDED: 'ended',
  REJECTED: 'rejected',
  BUSY: 'busy',
  NO_ANSWER: 'no_answer'
}

// 通话类型枚举
export const CALL_TYPE = {
  AUDIO: 'audio',
  VIDEO: 'video'
}

class WebRTCClient {
  constructor() {
    this.socket = null
    this.peerConnection = null
    this.localStream = null
    this.remoteStream = null
    this.currentCall = null
    this.isConnected = false
    
    // 事件回调
    this.onCallIncoming = null
    this.onCallAccepted = null
    this.onCallEnded = null
    this.onCallError = null
    this.onRemoteStream = null
    this.onLocalStream = null
    
    // WebRTC配置
    this.rtcConfig = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      ]
    }
  }

  // 连接到WebRTC信令服务器
  async connect(userInfo) {
    try {
      if (this.socket && this.isConnected) {
        console.log('WebRTC客户端已连接')
        return true
      }

      console.log('🔌 尝试连接WebRTC服务器...')

      this.socket = io('http://192.168.31.222:3000', {
        path: '/webrtc/socket.io/',
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true,
        reconnection: true,
        reconnectionDelay: 1000,
        reconnectionAttempts: 5
      })

      return new Promise((resolve, reject) => {
        this.socket.on('connect', () => {
          console.log('✅ WebRTC客户端连接成功')
          this.isConnected = true

          // 加入WebRTC服务
          this.socket.emit('webrtc:join', userInfo)

          this.setupEventHandlers()
          resolve(true)
        })

        this.socket.on('disconnect', (reason) => {
          console.log('🔌 WebRTC连接断开:', reason)
          this.isConnected = false
        })

        this.socket.on('connect_error', (error) => {
          console.error('❌ WebRTC连接失败:', error)
          this.isConnected = false

          // 提供更详细的错误信息
          let errorMessage = 'WebRTC服务器连接失败'
          if (error.message.includes('timeout')) {
            errorMessage = 'WebRTC服务器连接超时，请检查服务器是否启动'
          } else if (error.message.includes('404')) {
            errorMessage = 'WebRTC服务器未启动，请先启动视频通话服务'
          }

          reject(new Error(errorMessage))
        })

        this.socket.on('webrtc:joined', (data) => {
          console.log('✅ 已加入WebRTC服务:', data)
        })

        // 设置超时
        setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error('连接超时'))
          }
        }, 20000)
      })

    } catch (error) {
      console.error('WebRTC连接异常:', error)
      throw error
    }
  }

  // 设置事件处理器
  setupEventHandlers() {
    if (!this.socket) return

    // 收到来电
    this.socket.on('call:incoming', (data) => {
      console.log('📞 收到来电:', data)
      this.currentCall = data.callInfo
      if (this.onCallIncoming) {
        this.onCallIncoming(data.callInfo)
      }
    })

    // 通话被接受
    this.socket.on('call:accepted', (data) => {
      console.log('✅ 通话被接受:', data)
      this.currentCall = data.callInfo
      if (this.onCallAccepted) {
        this.onCallAccepted(data.callInfo)
      }
    })

    // 通话结束
    this.socket.on('call:ended', (data) => {
      console.log('📴 通话结束:', data)
      this.handleCallEnd(data.status)
      if (this.onCallEnded) {
        this.onCallEnded(data.status, data.callInfo)
      }
    })

    // 通话错误
    this.socket.on('call:error', (data) => {
      console.error('❌ 通话错误:', data)
      ElMessage.error(data.message)
      if (this.onCallError) {
        this.onCallError(data.message)
      }
    })

    // WebRTC信令处理
    this.socket.on('webrtc:offer', async (data) => {
      console.log('📨 收到offer:', data)
      await this.handleOffer(data.offer)
    })

    this.socket.on('webrtc:answer', async (data) => {
      console.log('📨 收到answer:', data)
      await this.handleAnswer(data.answer)
    })

    this.socket.on('webrtc:ice-candidate', async (data) => {
      console.log('📨 收到ICE candidate:', data)
      await this.handleIceCandidate(data.candidate)
    })
  }

  // 发起通话
  async initiateCall(targetUserId, type = CALL_TYPE.VIDEO) {
    try {
      if (!this.socket || !this.isConnected) {
        throw new Error('WebRTC客户端未连接')
      }

      console.log('📞 发起通话:', { targetUserId, type })
      
      // 获取本地媒体流
      await this.getLocalStream(type === CALL_TYPE.VIDEO)
      
      // 创建PeerConnection
      this.createPeerConnection()
      
      // 发送通话请求
      this.socket.emit('call:initiate', { targetUserId, type })
      
      return true
    } catch (error) {
      console.error('发起通话失败:', error)
      ElMessage.error('发起通话失败: ' + error.message)
      throw error
    }
  }

  // 接受通话
  async acceptCall() {
    try {
      if (!this.currentCall) {
        throw new Error('没有待接听的通话')
      }

      console.log('✅ 接受通话:', this.currentCall.callId)
      
      // 获取本地媒体流
      await this.getLocalStream(this.currentCall.type === CALL_TYPE.VIDEO)
      
      // 创建PeerConnection
      this.createPeerConnection()
      
      // 发送接受通话
      this.socket.emit('call:accept', { callId: this.currentCall.callId })
      
      return true
    } catch (error) {
      console.error('接受通话失败:', error)
      ElMessage.error('接受通话失败: ' + error.message)
      throw error
    }
  }

  // 拒绝通话
  rejectCall() {
    if (!this.currentCall) return
    
    console.log('❌ 拒绝通话:', this.currentCall.callId)
    this.socket.emit('call:reject', { callId: this.currentCall.callId })
    this.currentCall = null
  }

  // 结束通话
  endCall() {
    if (!this.currentCall) return
    
    console.log('📴 结束通话:', this.currentCall.callId)
    this.socket.emit('call:end', { callId: this.currentCall.callId })
    this.handleCallEnd(CALL_STATUS.ENDED)
  }

  // 获取本地媒体流
  async getLocalStream(video = true) {
    try {
      const constraints = {
        audio: true,
        video: video ? { width: 640, height: 480 } : false
      }
      
      this.localStream = await navigator.mediaDevices.getUserMedia(constraints)
      console.log('✅ 获取本地媒体流成功')
      
      if (this.onLocalStream) {
        this.onLocalStream(this.localStream)
      }
      
      return this.localStream
    } catch (error) {
      console.error('获取本地媒体流失败:', error)
      throw new Error('无法访问摄像头或麦克风')
    }
  }

  // 创建PeerConnection
  createPeerConnection() {
    this.peerConnection = new RTCPeerConnection(this.rtcConfig)
    
    // 添加本地流
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => {
        this.peerConnection.addTrack(track, this.localStream)
      })
    }
    
    // 处理远程流
    this.peerConnection.ontrack = (event) => {
      console.log('📺 收到远程流')
      this.remoteStream = event.streams[0]
      if (this.onRemoteStream) {
        this.onRemoteStream(this.remoteStream)
      }
    }
    
    // 处理ICE候选
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate && this.currentCall) {
        this.socket.emit('webrtc:ice-candidate', {
          callId: this.currentCall.callId,
          candidate: event.candidate
        })
      }
    }
    
    // 连接状态变化
    this.peerConnection.onconnectionstatechange = () => {
      console.log('连接状态:', this.peerConnection.connectionState)
    }
  }

  // 处理offer
  async handleOffer(offer) {
    if (!this.peerConnection) return
    
    await this.peerConnection.setRemoteDescription(offer)
    const answer = await this.peerConnection.createAnswer()
    await this.peerConnection.setLocalDescription(answer)
    
    this.socket.emit('webrtc:answer', {
      callId: this.currentCall.callId,
      answer: answer
    })
  }

  // 处理answer
  async handleAnswer(answer) {
    if (!this.peerConnection) return
    
    await this.peerConnection.setRemoteDescription(answer)
  }

  // 处理ICE候选
  async handleIceCandidate(candidate) {
    if (!this.peerConnection) return
    
    await this.peerConnection.addIceCandidate(candidate)
  }

  // 处理通话结束
  handleCallEnd(status) {
    console.log('🔚 清理通话资源, 状态:', status)
    
    // 停止本地流
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop())
      this.localStream = null
    }
    
    // 关闭PeerConnection
    if (this.peerConnection) {
      this.peerConnection.close()
      this.peerConnection = null
    }
    
    this.remoteStream = null
    this.currentCall = null
  }

  // 断开连接
  disconnect() {
    if (this.currentCall) {
      this.endCall()
    }
    
    this.handleCallEnd(CALL_STATUS.ENDED)
    
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
    
    this.isConnected = false
    console.log('🔌 WebRTC客户端已断开连接')
  }
}

export default WebRTCClient
