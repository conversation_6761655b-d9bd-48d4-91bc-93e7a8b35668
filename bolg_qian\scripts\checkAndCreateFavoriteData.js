import { createRequire } from 'module';
const require = createRequire(import.meta.url);

const db = require('../../blog_hou/utils/db');

async function checkAndCreateFavoriteData() {
  try {
    console.log('🔍 检查收藏数据状态...\n');

    // 1. 检查当前所有用户
    console.log('1. 检查用户列表:');
    const users = await db.query('SELECT id, username FROM users ORDER BY id');
    users.forEach(user => {
      console.log(`  - 用户ID: ${user.id}, 用户名: ${user.username}`);
    });

    // 2. 检查所有文章
    console.log('\n2. 检查文章列表:');
    const articles = await db.query('SELECT id, title FROM articles WHERE is_deleted = 0 ORDER BY id LIMIT 10');
    articles.forEach(article => {
      console.log(`  - 文章ID: ${article.id}, 标题: ${article.title}`);
    });

    // 3. 检查现有收藏记录
    console.log('\n3. 检查现有收藏记录:');
    const existingFavorites = await db.query(`
      SELECT af.*, u.username, a.title 
      FROM article_favorites af
      LEFT JOIN users u ON af.user_id = u.id
      LEFT JOIN articles a ON af.article_id = a.id
      ORDER BY af.created_at DESC
    `);
    
    if (existingFavorites.length > 0) {
      console.log(`  找到 ${existingFavorites.length} 条收藏记录:`);
      existingFavorites.forEach(fav => {
        console.log(`  - ID: ${fav.id}, 用户: ${fav.username || '匿名'} (${fav.user_id}), 文章: ${fav.title} (${fav.article_id}), 时间: ${fav.created_at}`);
      });
    } else {
      console.log('  ❌ 没有找到任何收藏记录');
    }

    // 4. 为用户 lion (通常是ID 21) 创建测试收藏数据
    console.log('\n4. 为用户创建测试收藏数据...');
    
    // 查找用户 lion
    const lionUser = await db.query('SELECT id FROM users WHERE username = ?', ['lion']);
    if (lionUser.length === 0) {
      console.log('  ❌ 未找到用户 lion');
      return;
    }
    
    const userId = lionUser[0].id;
    console.log(`  找到用户 lion，ID: ${userId}`);

    // 检查该用户是否已有收藏记录
    const userFavorites = await db.query('SELECT * FROM article_favorites WHERE user_id = ?', [userId]);
    console.log(`  用户 ${userId} 现有收藏记录: ${userFavorites.length} 条`);

    if (userFavorites.length === 0) {
      console.log('  创建测试收藏数据...');
      
      // 获取前3篇文章
      const testArticles = await db.query('SELECT id FROM articles WHERE is_deleted = 0 ORDER BY id LIMIT 3');
      
      if (testArticles.length > 0) {
        for (const article of testArticles) {
          await db.query(`
            INSERT INTO article_favorites (user_id, article_id, ip_address, user_agent, session_id, created_at)
            VALUES (?, ?, '127.0.0.1', 'Test Browser', 'test_session', NOW())
          `, [userId, article.id]);
          
          console.log(`  ✅ 为用户 ${userId} 创建文章 ${article.id} 的收藏记录`);
        }
      } else {
        console.log('  ❌ 没有找到可用的文章');
      }
    } else {
      console.log('  ✅ 用户已有收藏记录，无需创建');
    }

    // 5. 验证创建结果
    console.log('\n5. 验证收藏数据:');
    const finalFavorites = await db.query(`
      SELECT af.*, a.title, a.summary, a.cover_image, a.user_id as author_id,
             u.username as author_name, u.avatar as author_avatar
      FROM article_favorites af
      LEFT JOIN articles a ON af.article_id = a.id
      LEFT JOIN users u ON a.user_id = u.id
      WHERE af.user_id = ? AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
      ORDER BY af.created_at DESC
    `, [userId]);

    console.log(`  用户 ${userId} 的收藏记录数量: ${finalFavorites.length}`);
    finalFavorites.forEach(fav => {
      console.log(`  - 收藏ID: ${fav.id}, 文章: ${fav.title}, 作者: ${fav.author_name}, 时间: ${fav.created_at}`);
    });

    // 6. 检查收藏统计
    console.log('\n6. 检查收藏统计:');
    const totalCount = await db.query(`
      SELECT COUNT(*) as total 
      FROM article_favorites af
      LEFT JOIN articles a ON af.article_id = a.id
      WHERE af.user_id = ? AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
    `, [userId]);
    console.log(`  总收藏数: ${totalCount[0].total}`);

    console.log('\n✅ 收藏数据检查和创建完成!');

  } catch (error) {
    console.error('❌ 操作失败:', error.message);
    console.error(error);
  }
}

checkAndCreateFavoriteData();
