const Koa = require("koa");
const cors = require("@koa/cors");
const koaBody = require("koa-body");
const path = require("path");
const router = require("./router/index");
// 使用统一的中间件管理器
const { getMiddlewareStack } = require("./middlewares");
const serve = require("koa-static");
const mount = require("koa-mount");
const compress = require("koa-compress");
const helmet = require("koa-helmet");

// 导入配置管理
const { initializeConfig, getAppConfig, getConfigSummary } = require("./config");
const redisCache = require("./utils/redisCache");
const logger = require("./plugin/logger");

// 初始化配置系统
try {
  initializeConfig();
  console.log("✅ 配置系统初始化成功");
  console.log("📊 配置摘要:", JSON.stringify(getConfigSummary(), null, 2));
} catch (error) {
  console.error("❌ 配置系统初始化失败:", error.message);
  process.exit(1);
}

// Redis缓存已在上面导入

// 获取应用配置
const appConfig = getAppConfig();

// 获取配置化的中间件栈
const middlewareStack = getMiddlewareStack({
  logger: {
    logLevel: appConfig.server.env === 'development' ? 'debug' : 'info'
  },
  errorHandler: {
    exposeStack: appConfig.server.env === 'development'
  }
});

// 创建 Koa 实例
const app = new Koa();

// 安全中间件（最前面）
app.use(helmet({
  contentSecurityPolicy: false, // 根据需要配置CSP
  crossOriginEmbedderPolicy: false
}));

// 应用统一的中间件栈（按正确顺序）
middlewareStack.forEach(middleware => {
  app.use(middleware);
});

// 压缩中间件
app.use(compress({
  threshold: appConfig.server.compression.threshold,
  level: appConfig.server.compression.level,
  gzip: {
    flush: require('zlib').constants.Z_SYNC_FLUSH
  },
  deflate: {
    flush: require('zlib').constants.Z_SYNC_FLUSH
  },
  br: false // 禁用Brotli压缩以提高兼容性
}));

// 中间件已通过middlewareStack统一应用

// CORS 中间件（必须在静态资源之前）
app.use(cors({
  origin: function (ctx) {
    const allowedOrigins = appConfig.server.cors.allowedOrigins;
    const origin = ctx.header.origin;

    if (allowedOrigins.includes(origin)) {
      return origin;
    }

    // 开发环境也返回具体的origin，而不是通配符
    if (appConfig.server.env !== 'production') {
      // 如果有origin头，返回它；否则返回第一个允许的origin
      return origin || allowedOrigins[0];
    }

    return false;
  },
  credentials: appConfig.server.cors.credentials,
  allowMethods: appConfig.server.cors.allowMethods,
  allowHeaders: appConfig.server.cors.allowHeaders,
  exposeHeaders: appConfig.server.cors.exposeHeaders
}));

// 静态资源中间件（在CORS之后）
const staticDirs = [
  // 媒体文件访问
  { url: "/media", dir: path.join(__dirname, "public", "media") },
  { url: "/media/covers", dir: path.join(__dirname, "public", "media", "covers") },

  // 统一的文件类型访问
  { url: "/images", dir: path.join(__dirname, "public", "images") },         // 展示墙图片访问
  { url: "/uploaded-images", dir: path.join(__dirname, "uploads", "images") }, // 上传图片访问
  { url: "/documents", dir: path.join(__dirname, "uploads", "documents") },  // 统一文档访问
  { url: "/videos", dir: path.join(__dirname, "uploads", "videos") },        // 统一视频访问
  { url: "/archives", dir: path.join(__dirname, "uploads", "archives") },    // 统一压缩包访问

  // 功能性文件访问
  { url: "/avatars", dir: path.join(__dirname, "uploads", "avatars") },
  { url: "/articles", dir: path.join(__dirname, "uploads", "articles") },
  { url: "/chat_images", dir: path.join(__dirname, "uploads", "chat_images") },

  // 兼容旧版本
  { url: "/resource", dir: path.join(__dirname, "uploads", "resource") },
  { url: "/public-images", dir: path.join(__dirname, "public", "images") },

];
staticDirs.forEach(({ url, dir }) => {
  app.use(mount(url, serve(dir)));
});


app.use(
  koaBody({
    multipart: true,
    formidable: {
      maxFileSize: appConfig.server.bodyParser.maxFileSize,
      maxFieldSize: appConfig.server.bodyParser.maxFieldSize,
      maxFields: appConfig.server.bodyParser.maxFields,
      keepExtensions: true,
    },
  })
);

// JWT认证和缓存中间件已通过middlewareStack统一应用

// 路由
app.use(router.routes()).use(router.allowedMethods());

// 应用启动后初始化Redis
app.on('ready', async () => {
  try {
    const success = await redisCache.manualInit();
    if (success) {
      logger.info('✅ Redis缓存初始化成功');
    } else {
      logger.warn('⚠️ Redis缓存初始化失败，将使用内存缓存');
    }
  } catch (error) {
    logger.error('Redis初始化异常:', error);
  }
});

module.exports = app;