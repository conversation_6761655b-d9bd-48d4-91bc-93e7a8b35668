/**
 * 配置验证脚本
 * 用于验证配置系统是否正常工作
 */

const { initializeConfig, getAllConfig, getConfigSummary } = require("../config");
const logger = require("../plugin/logger");

async function validateConfiguration() {
  console.log("🔍 开始配置验证...\n");

  try {
    // 1. 初始化配置系统
    console.log("1️⃣ 初始化配置系统...");
    initializeConfig();
    console.log("✅ 配置系统初始化成功\n");

    // 2. 获取所有配置
    console.log("2️⃣ 获取配置信息...");
    const allConfig = getAllConfig();
    console.log("✅ 配置信息获取成功\n");

    // 3. 验证数据库配置
    console.log("3️⃣ 验证数据库配置...");
    const dbConfig = allConfig.database;
    console.log(`   数据库主机: ${dbConfig.host}`);
    console.log(`   数据库名称: ${dbConfig.database}`);
    console.log(`   连接池大小: ${dbConfig.connectionLimit}`);
    console.log(`   SSL启用: ${dbConfig.ssl ? '是' : '否'}`);
    console.log(`   慢查询阈值: ${dbConfig.slowQueryThreshold}ms`);
    console.log("✅ 数据库配置验证通过\n");

    // 4. 验证服务器配置
    console.log("4️⃣ 验证服务器配置...");
    const serverConfig = allConfig.app.server;
    console.log(`   服务器端口: ${serverConfig.port}`);
    console.log(`   服务器主机: ${serverConfig.host}`);
    console.log(`   运行环境: ${serverConfig.env}`);
    console.log(`   允许的CORS源: ${serverConfig.cors.allowedOrigins.join(', ')}`);
    console.log("✅ 服务器配置验证通过\n");

    // 5. 验证JWT配置
    console.log("5️⃣ 验证JWT配置...");
    const jwtConfig = allConfig.app.jwt;
    console.log(`   JWT密钥长度: ${jwtConfig.secret.length}字符`);
    console.log(`   JWT过期时间: ${jwtConfig.expiresIn}`);
    console.log(`   JWT算法: ${jwtConfig.algorithm}`);
    console.log(`   JWT发行者: ${jwtConfig.issuer}`);
    
    if (jwtConfig.secret === 'your-super-secret-jwt-key-change-in-production') {
      console.log("⚠️  警告: 使用默认JWT密钥，生产环境请更换");
    } else {
      console.log("✅ JWT密钥已自定义");
    }
    console.log("✅ JWT配置验证通过\n");

    // 6. 验证文件上传配置
    console.log("6️⃣ 验证文件上传配置...");
    const uploadConfig = allConfig.app.upload;
    console.log(`   最大文件大小: ${(uploadConfig.maxSizes.video / 1024 / 1024).toFixed(0)}MB`);
    console.log(`   分块上传: ${uploadConfig.chunked.enabled ? '启用' : '禁用'}`);
    console.log(`   分块大小: ${(uploadConfig.chunked.chunkSize / 1024).toFixed(0)}KB`);
    console.log("✅ 文件上传配置验证通过\n");

    // 7. 验证AI配置
    console.log("7️⃣ 验证AI配置...");
    const aiConfig = allConfig.app.ai;
    console.log(`   AI功能: ${aiConfig.enabled ? '启用' : '禁用'}`);
    console.log(`   Ollama地址: ${aiConfig.ollama.baseUrl}`);
    console.log(`   默认模型: ${aiConfig.ollama.defaultModel}`);
    console.log(`   最大令牌数: ${aiConfig.chat.maxTokens}`);
    console.log("✅ AI配置验证通过\n");

    // 8. 验证缓存配置
    console.log("8️⃣ 验证缓存配置...");
    const cacheConfig = allConfig.cache;
    console.log(`   缓存功能: ${cacheConfig.enabled ? '启用' : '禁用'}`);
    console.log(`   默认TTL: ${(cacheConfig.defaultTTL / 1000).toFixed(0)}秒`);
    console.log(`   最大缓存数: ${cacheConfig.maxSize}`);
    console.log("✅ 缓存配置验证通过\n");

    // 9. 验证安全配置
    console.log("9️⃣ 验证安全配置...");
    const securityConfig = allConfig.app.security;
    console.log(`   限流窗口: ${(securityConfig.rateLimit.windowMs / 1000 / 60).toFixed(0)}分钟`);
    console.log(`   最大请求数: ${securityConfig.rateLimit.max}`);
    console.log(`   最小密码长度: ${securityConfig.password.minLength}`);
    console.log("✅ 安全配置验证通过\n");

    // 10. 显示配置摘要
    console.log("🔟 配置摘要:");
    const summary = getConfigSummary();
    console.log(JSON.stringify(summary, null, 2));
    console.log("✅ 配置摘要生成成功\n");

    // 11. 环境特定验证
    console.log("1️⃣1️⃣ 环境特定验证...");
    if (serverConfig.env === 'production') {
      console.log("🔒 生产环境检查:");
      
      const issues = [];
      if (jwtConfig.secret === 'your-super-secret-jwt-key-change-in-production') {
        issues.push("JWT密钥使用默认值");
      }
      if (dbConfig.password === '0519' || dbConfig.password.length < 8) {
        issues.push("数据库密码过于简单");
      }
      if (!dbConfig.ssl) {
        issues.push("数据库未启用SSL");
      }

      if (issues.length > 0) {
        console.log("❌ 生产环境配置问题:");
        issues.forEach(issue => console.log(`   - ${issue}`));
      } else {
        console.log("✅ 生产环境配置检查通过");
      }
    } else {
      console.log("🔧 开发环境配置检查通过");
    }
    console.log();

    // 12. 配置完整性检查
    console.log("1️⃣2️⃣ 配置完整性检查...");
    const requiredConfigs = [
      'database.host',
      'database.user', 
      'database.password',
      'database.database',
      'app.server.port',
      'app.jwt.secret'
    ];

    const missingConfigs = [];
    requiredConfigs.forEach(configPath => {
      const keys = configPath.split('.');
      let value = allConfig;
      
      for (const key of keys) {
        value = value?.[key];
      }
      
      if (!value) {
        missingConfigs.push(configPath);
      }
    });

    if (missingConfigs.length > 0) {
      console.log("❌ 缺少必需配置:");
      missingConfigs.forEach(config => console.log(`   - ${config}`));
    } else {
      console.log("✅ 所有必需配置都已设置");
    }
    console.log();

    console.log("🎉 配置验证完成！所有配置项都正常工作。");
    return true;

  } catch (error) {
    console.error("❌ 配置验证失败:", error.message);
    console.error("详细错误:", error.stack);
    return false;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  validateConfiguration()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error("脚本执行失败:", error);
      process.exit(1);
    });
}

module.exports = { validateConfiguration };
