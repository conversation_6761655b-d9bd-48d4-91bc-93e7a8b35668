// WebRTC 视频通话信令服务器
const { Server } = require("socket.io");
const logger = require("../plugin/logger");

// 通话状态管理
const activeCalls = new Map(); // callId -> callInfo
const userCalls = new Map(); // userId -> callId

// 通话状态枚举
const CALL_STATUS = {
  CALLING: 'calling',
  RINGING: 'ringing', 
  CONNECTED: 'connected',
  ENDED: 'ended',
  REJECTED: 'rejected',
  BUSY: 'busy',
  NO_ANSWER: 'no_answer'
};

// 通话类型枚举
const CALL_TYPE = {
  AUDIO: 'audio',
  VIDEO: 'video'
};

// 生成通话ID
function generateCallId() {
  return `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 获取通话统计信息
function getCallStats() {
  return {
    activeCalls: activeCalls.size,
    activeCallsList: Array.from(activeCalls.values()).map(call => ({
      callId: call.callId,
      caller: call.caller.nickname,
      callee: call.callee.nickname,
      type: call.type,
      status: call.status,
      startTime: call.startTime,
      duration: call.status === CALL_STATUS.CONNECTED ? Date.now() - call.connectTime : 0
    }))
  };
}

// 设置WebRTC信令服务器
function setupWebRTCServer(server) {
  const io = new Server(server, {
    cors: {
      origin: "*",
      methods: ["GET", "POST"],
      credentials: true
    },
    path: "/webrtc/socket.io/",
    pingTimeout: 60000,
    pingInterval: 25000
  });

  io.on("connection", (socket) => {
    logger.info('新WebRTC连接', { socketId: socket.id });

    // 用户加入WebRTC服务
    socket.on("webrtc:join", (userInfo) => {
      socket.userInfo = userInfo;
      socket.userId = userInfo.id;
      
      logger.info('用户加入WebRTC服务', {
        socketId: socket.id,
        userId: userInfo.id,
        nickname: userInfo.nickname
      });

      socket.emit("webrtc:joined", { success: true });
    });

    // 发起通话
    socket.on("call:initiate", async (data) => {
      try {
        const { targetUserId, type = CALL_TYPE.VIDEO } = data;
        const caller = socket.userInfo;
        
        if (!caller) {
          socket.emit("call:error", { message: "用户未登录" });
          return;
        }

        // 检查目标用户是否在线
        const targetSocket = Array.from(io.sockets.sockets.values())
          .find(s => s.userInfo?.id === targetUserId);
          
        if (!targetSocket) {
          socket.emit("call:error", { message: "对方不在线" });
          return;
        }

        // 检查对方是否正在通话中
        if (userCalls.has(targetUserId)) {
          socket.emit("call:error", { message: "对方正在通话中" });
          return;
        }

        // 检查自己是否正在通话中
        if (userCalls.has(caller.id)) {
          socket.emit("call:error", { message: "您正在通话中" });
          return;
        }

        const callId = generateCallId();
        const callInfo = {
          callId,
          caller: {
            id: caller.id,
            nickname: caller.nickname,
            avatar: caller.avatar,
            socketId: socket.id
          },
          callee: {
            id: targetUserId,
            nickname: targetSocket.userInfo.nickname,
            avatar: targetSocket.userInfo.avatar,
            socketId: targetSocket.id
          },
          type,
          status: CALL_STATUS.CALLING,
          startTime: Date.now(),
          connectTime: null
        };

        // 保存通话信息
        activeCalls.set(callId, callInfo);
        userCalls.set(caller.id, callId);
        userCalls.set(targetUserId, callId);

        logger.info('发起通话', {
          callId,
          caller: caller.nickname,
          callee: targetSocket.userInfo.nickname,
          type
        });

        // 通知发起者通话已发起
        socket.emit("call:initiated", { callId, callInfo });

        // 通知被叫方有来电
        targetSocket.emit("call:incoming", { callId, callInfo });

        // 设置超时处理（30秒无应答自动结束）
        setTimeout(() => {
          const currentCall = activeCalls.get(callId);
          if (currentCall && currentCall.status === CALL_STATUS.CALLING) {
            endCall(callId, CALL_STATUS.NO_ANSWER);
          }
        }, 30000);

      } catch (error) {
        logger.error('发起通话失败', { error: error.message });
        socket.emit("call:error", { message: "发起通话失败" });
      }
    });

    // 接受通话
    socket.on("call:accept", async (data) => {
      try {
        const { callId } = data;
        const callInfo = activeCalls.get(callId);
        
        if (!callInfo) {
          socket.emit("call:error", { message: "通话不存在" });
          return;
        }

        if (callInfo.callee.socketId !== socket.id) {
          socket.emit("call:error", { message: "无权限操作此通话" });
          return;
        }

        // 更新通话状态
        callInfo.status = CALL_STATUS.CONNECTED;
        callInfo.connectTime = Date.now();

        logger.info('接受通话', {
          callId,
          caller: callInfo.caller.nickname,
          callee: callInfo.callee.nickname
        });

        // 通知双方通话已连接
        io.to(callInfo.caller.socketId).emit("call:accepted", { callId, callInfo });
        io.to(callInfo.callee.socketId).emit("call:accepted", { callId, callInfo });

      } catch (error) {
        logger.error('接受通话失败', { error: error.message });
        socket.emit("call:error", { message: "接受通话失败" });
      }
    });

    // 拒绝通话
    socket.on("call:reject", async (data) => {
      try {
        const { callId } = data;
        endCall(callId, CALL_STATUS.REJECTED);
      } catch (error) {
        logger.error('拒绝通话失败', { error: error.message });
        socket.emit("call:error", { message: "拒绝通话失败" });
      }
    });

    // 结束通话
    socket.on("call:end", async (data) => {
      try {
        const { callId } = data;
        endCall(callId, CALL_STATUS.ENDED);
      } catch (error) {
        logger.error('结束通话失败', { error: error.message });
        socket.emit("call:error", { message: "结束通话失败" });
      }
    });

    // WebRTC信令交换
    socket.on("webrtc:offer", (data) => {
      const { callId, offer } = data;
      const callInfo = activeCalls.get(callId);
      
      if (callInfo && callInfo.caller.socketId === socket.id) {
        io.to(callInfo.callee.socketId).emit("webrtc:offer", { callId, offer });
        logger.info('转发offer', { callId });
      }
    });

    socket.on("webrtc:answer", (data) => {
      const { callId, answer } = data;
      const callInfo = activeCalls.get(callId);
      
      if (callInfo && callInfo.callee.socketId === socket.id) {
        io.to(callInfo.caller.socketId).emit("webrtc:answer", { callId, answer });
        logger.info('转发answer', { callId });
      }
    });

    socket.on("webrtc:ice-candidate", (data) => {
      const { callId, candidate } = data;
      const callInfo = activeCalls.get(callId);
      
      if (callInfo) {
        const targetSocketId = callInfo.caller.socketId === socket.id 
          ? callInfo.callee.socketId 
          : callInfo.caller.socketId;
        io.to(targetSocketId).emit("webrtc:ice-candidate", { callId, candidate });
      }
    });

    // 处理断开连接
    socket.on("disconnect", () => {
      // 查找用户的活跃通话
      const userId = socket.userInfo?.id;
      if (userId && userCalls.has(userId)) {
        const callId = userCalls.get(userId);
        endCall(callId, CALL_STATUS.ENDED);
      }

      logger.info('WebRTC连接断开', { 
        socketId: socket.id, 
        userId: socket.userInfo?.id 
      });
    });

    // 结束通话的通用函数
    function endCall(callId, status) {
      const callInfo = activeCalls.get(callId);
      if (!callInfo) return;

      callInfo.status = status;
      callInfo.endTime = Date.now();

      logger.info('通话结束', {
        callId,
        status,
        duration: callInfo.connectTime ? callInfo.endTime - callInfo.connectTime : 0
      });

      // 通知双方通话结束
      io.to(callInfo.caller.socketId).emit("call:ended", { callId, status, callInfo });
      io.to(callInfo.callee.socketId).emit("call:ended", { callId, status, callInfo });

      // 清理通话记录
      activeCalls.delete(callId);
      userCalls.delete(callInfo.caller.id);
      userCalls.delete(callInfo.callee.id);
    }
  });

  // 添加管理方法
  io.getCallStats = () => getCallStats();
  io.getActiveCalls = () => Array.from(activeCalls.values());
  io.endCall = (callId) => {
    const callInfo = activeCalls.get(callId);
    if (callInfo) {
      endCall(callId, CALL_STATUS.ENDED);
    }
  };

  logger.info('WebRTC信令服务器已启动', {
    path: "/webrtc/socket.io/",
    pingTimeout: 60000,
    pingInterval: 25000
  });

  return io;
}

module.exports = {
  setupWebRTCServer,
  CALL_STATUS,
  CALL_TYPE
};
