import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const id = ref(localStorage.getItem('id') || '')
  const username = ref(localStorage.getItem('username') || '')
  const nickname = ref(localStorage.getItem('nickname') || '')
  const avatar = ref(localStorage.getItem('avatar') || '')
  const token = ref(localStorage.getItem('token') || '')
  const isLoggedIn = ref(!!localStorage.getItem('token'))

  // 计算属性
  const userInfo = computed(() => ({
    id: id.value,
    username: username.value,
    nickname: nickname.value,
    avatar: avatar.value
  }))

  // 动作
  function setUser(userInfo: any) {
    id.value = userInfo.id
    username.value = userInfo.username
    nickname.value = userInfo.nickname || userInfo.username
    avatar.value = userInfo.avatar || ''
    
    // 持久化到localStorage
    localStorage.setItem('id', id.value)
    localStorage.setItem('username', username.value)
    localStorage.setItem('nickname', nickname.value)
    localStorage.setItem('avatar', avatar.value)
  }

  function setToken(newToken: string) {
    token.value = newToken
    isLoggedIn.value = true
    localStorage.setItem('token', newToken)
  }

  function logout() {
    id.value = ''
    username.value = ''
    nickname.value = ''
    avatar.value = ''
    token.value = ''
    isLoggedIn.value = false
    
    // 清除localStorage
    localStorage.removeItem('id')
    localStorage.removeItem('username')
    localStorage.removeItem('nickname')
    localStorage.removeItem('avatar')
    localStorage.removeItem('token')
  }

  return {
    // 状态
    id,
    username,
    nickname,
    avatar,
    token,
    isLoggedIn,
    
    // 计算属性
    userInfo,
    
    // 动作
    setUser,
    setToken,
    logout
  }
}) 