#!/usr/bin/env node

/**
 * API安全测试工具
 * 专门测试REST API的安全漏洞
 */

const http = require('http');
const crypto = require('crypto');

class APISecurityTester {
  constructor(baseUrl = 'http://localhost:3000') {
    this.baseUrl = baseUrl;
    this.vulnerabilities = [];
    this.testResults = [];
  }

  async request(endpoint, options = {}) {
    return new Promise((resolve, reject) => {
      const url = new URL(endpoint, this.baseUrl);
      
      const requestOptions = {
        hostname: url.hostname,
        port: url.port || 80,
        path: url.pathname + url.search,
        method: options.method || 'GET',
        headers: {
          'User-Agent': 'API-Security-Tester/1.0',
          'Accept': 'application/json',
          ...options.headers
        },
        timeout: 10000
      };

      const req = http.request(requestOptions, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: data,
            responseTime: Date.now() - startTime
          });
        });
      });

      const startTime = Date.now();
      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      if (options.body) {
        req.write(options.body);
      }
      req.end();
    });
  }

  // 测试API端点枚举
  async testEndpointEnumeration() {
    console.log('🔍 测试API端点枚举...');
    
    const commonEndpoints = [
      '/api', '/api/v1', '/api/v2',
      '/admin', '/admin/api',
      '/user', '/users', '/user/profile',
      '/auth', '/login', '/logout',
      '/articles', '/posts', '/blog',
      '/upload', '/files', '/media',
      '/dashboard', '/stats',
      '/health', '/status', '/info',
      '/config', '/settings',
      '/backup', '/export', '/import',
      '/.env', '/config.json', '/package.json'
    ];

    for (const endpoint of commonEndpoints) {
      try {
        const response = await this.request(endpoint);
        
        if (response.statusCode === 200) {
          this.testResults.push({
            type: 'Endpoint Discovery',
            endpoint,
            status: response.statusCode,
            info: '发现可访问的端点'
          });
        }
      } catch (error) {
        // 忽略连接错误
      }
    }
  }

  // 测试HTTP方法
  async testHTTPMethods() {
    console.log('🔍 测试HTTP方法安全...');
    
    const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS', 'TRACE'];
    const endpoints = ['/api', '/user', '/articles'];

    for (const endpoint of endpoints) {
      for (const method of methods) {
        try {
          const response = await this.request(endpoint, { method });
          
          if (response.statusCode < 400 && method !== 'GET') {
            this.vulnerabilities.push({
              type: 'Unsafe HTTP Method',
              endpoint,
              method,
              severity: method === 'TRACE' ? 'HIGH' : 'MEDIUM',
              description: `端点允许 ${method} 方法`
            });
          }
        } catch (error) {
          // 忽略错误
        }
      }
    }
  }

  // 测试输入验证
  async testInputValidation() {
    console.log('🔍 测试输入验证...');
    
    const maliciousInputs = [
      // 超长字符串
      'A'.repeat(10000),
      // 特殊字符
      '../../etc/passwd',
      '../../../windows/system32/drivers/etc/hosts',
      // JSON注入
      '{"$ne": null}',
      '{"$gt": ""}',
      // NoSQL注入
      '{$where: "this.username == this.password"}',
      // 命令注入
      '; cat /etc/passwd',
      '| whoami',
      '`id`',
      // 脚本注入
      '<script>alert(1)</script>',
      'javascript:alert(1)'
    ];

    const testEndpoints = [
      { endpoint: '/user/login', params: ['username', 'password'] },
      { endpoint: '/articles/search', params: ['query', 'category'] },
      { endpoint: '/user/profile', params: ['name', 'email'] }
    ];

    for (const test of testEndpoints) {
      for (const input of maliciousInputs) {
        for (const param of test.params) {
          try {
            const body = JSON.stringify({ [param]: input });
            const response = await this.request(test.endpoint, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body
            });

            // 检查是否有错误信息泄露
            if (response.body.includes('Error:') || 
                response.body.includes('Exception:') ||
                response.body.includes('Stack trace:') ||
                response.statusCode === 500) {
              this.vulnerabilities.push({
                type: 'Input Validation Bypass',
                endpoint: test.endpoint,
                parameter: param,
                payload: input.substring(0, 50) + '...',
                severity: 'MEDIUM',
                description: '输入验证不充分，可能导致信息泄露'
              });
            }
          } catch (error) {
            // 忽略网络错误
          }
        }
      }
    }
  }

  // 测试认证和授权
  async testAuthentication() {
    console.log('🔍 测试认证和授权...');
    
    const protectedEndpoints = [
      '/admin',
      '/dashboard',
      '/user/profile',
      '/articles/create',
      '/upload'
    ];

    // 测试无认证访问
    for (const endpoint of protectedEndpoints) {
      try {
        const response = await this.request(endpoint);
        
        if (response.statusCode === 200) {
          this.vulnerabilities.push({
            type: 'Missing Authentication',
            endpoint,
            severity: 'HIGH',
            description: '受保护的端点无需认证即可访问'
          });
        }
      } catch (error) {
        // 忽略错误
      }
    }

    // 测试弱Token
    const weakTokens = [
      'admin',
      '123456',
      'token',
      'Bearer admin',
      'Bearer 123',
      'Basic YWRtaW46YWRtaW4=', // admin:admin
    ];

    for (const endpoint of protectedEndpoints) {
      for (const token of weakTokens) {
        try {
          const response = await this.request(endpoint, {
            headers: { 'Authorization': token }
          });
          
          if (response.statusCode === 200) {
            this.vulnerabilities.push({
              type: 'Weak Authentication',
              endpoint,
              token: token.substring(0, 20) + '...',
              severity: 'CRITICAL',
              description: '接受弱认证令牌'
            });
          }
        } catch (error) {
          // 忽略错误
        }
      }
    }
  }

  // 测试信息泄露
  async testInformationDisclosure() {
    console.log('🔍 测试信息泄露...');
    
    const sensitiveEndpoints = [
      '/api/config',
      '/api/env',
      '/api/debug',
      '/api/logs',
      '/api/status',
      '/api/health',
      '/api/version',
      '/.git/config',
      '/package.json',
      '/composer.json'
    ];

    for (const endpoint of sensitiveEndpoints) {
      try {
        const response = await this.request(endpoint);
        
        if (response.statusCode === 200) {
          // 检查敏感信息
          const sensitivePatterns = [
            /password/i,
            /secret/i,
            /key/i,
            /token/i,
            /database/i,
            /mysql/i,
            /mongodb/i,
            /redis/i,
            /api[_-]?key/i
          ];

          const hasSensitiveInfo = sensitivePatterns.some(pattern => 
            pattern.test(response.body)
          );

          if (hasSensitiveInfo) {
            this.vulnerabilities.push({
              type: 'Information Disclosure',
              endpoint,
              severity: 'HIGH',
              description: '端点泄露敏感信息'
            });
          }
        }
      } catch (error) {
        // 忽略错误
      }
    }
  }

  // 测试CORS配置
  async testCORS() {
    console.log('🔍 测试CORS配置...');
    
    const testOrigins = [
      'http://evil.com',
      'https://attacker.com',
      'null',
      '*'
    ];

    for (const origin of testOrigins) {
      try {
        const response = await this.request('/', {
          headers: { 'Origin': origin }
        });

        const corsHeader = response.headers['access-control-allow-origin'];
        if (corsHeader === '*' || corsHeader === origin) {
          this.vulnerabilities.push({
            type: 'CORS Misconfiguration',
            origin,
            corsHeader,
            severity: corsHeader === '*' ? 'HIGH' : 'MEDIUM',
            description: 'CORS配置过于宽松'
          });
        }
      } catch (error) {
        // 忽略错误
      }
    }
  }

  // 测试速率限制
  async testRateLimit() {
    console.log('🔍 测试速率限制...');
    
    const endpoint = '/user/login';
    const requests = [];
    
    // 快速发送多个请求
    for (let i = 0; i < 20; i++) {
      requests.push(
        this.request(endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ username: 'test', password: 'test' })
        }).catch(() => ({ statusCode: 0 }))
      );
    }

    const responses = await Promise.all(requests);
    const successCount = responses.filter(r => r.statusCode !== 429).length;

    if (successCount > 15) {
      this.vulnerabilities.push({
        type: 'Missing Rate Limiting',
        endpoint,
        successfulRequests: successCount,
        severity: 'MEDIUM',
        description: '端点缺少速率限制保护'
      });
    }
  }

  // 生成报告
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('🛡️  API安全测试报告');
    console.log('='.repeat(60));
    console.log(`测试时间: ${new Date().toLocaleString()}`);
    console.log(`发现的端点: ${this.testResults.length}`);
    console.log(`安全漏洞: ${this.vulnerabilities.length}`);
    
    // 按严重程度分类
    const critical = this.vulnerabilities.filter(v => v.severity === 'CRITICAL');
    const high = this.vulnerabilities.filter(v => v.severity === 'HIGH');
    const medium = this.vulnerabilities.filter(v => v.severity === 'MEDIUM');
    const low = this.vulnerabilities.filter(v => v.severity === 'LOW');

    console.log(`\n严重程度分布:`);
    console.log(`  🔴 严重: ${critical.length}`);
    console.log(`  🟠 高危: ${high.length}`);
    console.log(`  🟡 中危: ${medium.length}`);
    console.log(`  🟢 低危: ${low.length}`);

    // 详细漏洞列表
    if (this.vulnerabilities.length > 0) {
      console.log('\n发现的安全漏洞:');
      this.vulnerabilities.forEach((vuln, index) => {
        console.log(`\n${index + 1}. ${vuln.type} [${vuln.severity}]`);
        console.log(`   端点: ${vuln.endpoint || 'N/A'}`);
        console.log(`   描述: ${vuln.description}`);
        if (vuln.payload) console.log(`   载荷: ${vuln.payload}`);
        if (vuln.method) console.log(`   方法: ${vuln.method}`);
      });
    } else {
      console.log('\n✅ 未发现明显的API安全漏洞');
    }

    console.log('\n' + '='.repeat(60));
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始API安全测试...\n');
    
    try {
      await this.testEndpointEnumeration();
      await this.testHTTPMethods();
      await this.testInputValidation();
      await this.testAuthentication();
      await this.testInformationDisclosure();
      await this.testCORS();
      await this.testRateLimit();
    } catch (error) {
      console.error('测试过程中发生错误:', error.message);
    }
    
    this.generateReport();
  }
}

// 命令行使用
if (require.main === module) {
  const baseUrl = process.argv[2] || 'http://localhost:3000';
  const tester = new APISecurityTester(baseUrl);
  
  console.log('⚠️  警告: 此工具仅用于测试自己的API系统');
  console.log('🎯 测试目标:', baseUrl);
  console.log('');
  
  tester.runAllTests().catch(console.error);
}

module.exports = APISecurityTester;
