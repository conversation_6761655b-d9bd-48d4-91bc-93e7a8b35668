// 使用 koa-jwt 验证 JWT 令牌
const koaJwt = require("koa-jwt");
const { getAppConfig } = require("../config");

// 获取应用配置
const appConfig = getAppConfig();

// 配置路由白名单
module.exports = koaJwt({
  secret: appConfig.jwt.secret,
  key: "user",
  algorithm: appConfig.jwt.algorithm,
  issuer: appConfig.jwt.issuer,
  audience: appConfig.jwt.audience
}).unless({
  method: 'OPTIONS', // 排除所有OPTIONS请求
  path: [
    // 用户登录、注册、找回密码等
    /^\/user\/login/,
    /^\/user\/reg/,
    /^\/user\/changePassword/,
    // 退出登录
    /^\/user\/logout/,

    // /daosql/swagger.json
    /^\/swagger\.json/,

    // 公开文章接口
    /^\/articles\/pages$/,           // 获取文章列表
    /^\/articles\/pagesNumber$/,     // 获取文章分页信息
    /^\/articles\/detail$/,          // 获取文章详情
    /^\/articles\/shared$/,          // 获取公开文章
    /^\/articles\/comments$/,        // 获取文章评论
    /^\/articles\/public\/pages$/,   // 公开文章列表
    /^\/articles\/public\/detail$/,  // 公开文章详情
    /^\/articles\/public\/comments$/, // 公开文章评论
    /^\/articles\/public\/addComment$/, // 公开提交评论

    // 浏览记录接口（部分允许匿名访问）
    /^\/view-history\/record$/,      // 记录浏览历史
    /^\/view-history\/article\/.*\/stats$/, // 获取文章浏览统计
    // 注意：/view-history/user/* 需要认证，不在白名单中

    // 文章点赞接口（允许匿名访问）
    /^\/article-likes\/toggle$/,     // 点赞/取消点赞
    /^\/article-likes\/stats\/.*$/,  // 获取点赞统计
    // 注意：/article-likes/users/* 和 /article-likes/history 需要认证

    // 文章收藏接口（允许匿名访问）
    /^\/article-favorites\/toggle$/,     // 收藏/取消收藏
    /^\/article-favorites\/stats\/.*$/,  // 获取收藏统计
    /^\/article-favorites\/popular$/,    // 获取热门收藏文章
    // 注意：/article-favorites/users/* 和 /article-favorites/history 需要认证

    // 公开媒体资源
    /^\/media\/listpic/,
    /^\/media\/videos\/.*$/,
    /^\/media\/covers\/.*$/,
    /^\/media\/.*\.(mp4|avi|mov|wmv|flv|webm|mkv|mp3|wav|flac|aac|ogg)$/,

    // 媒体功能接口（需要用户认证，但允许访问）
    /^\/media-features\//,

    // 视频管理接口（需要管理员权限）
    /^\/video-management\//,

    // 静态资源
    /^\/public\/.*\.(mp4|webm|mov|jpg|jpeg|png|gif|svg)$/,
    /^\/uploads\/.*\.(jpg|jpeg|png|gif|svg)$/,

    // 头像资源（允许公开访问）
    /^\/avatars\/.*\.(jpg|jpeg|png|gif|svg|webp)$/,

    // 文章相关图片资源（允许公开访问）
    /^\/articles\/.*\.(jpg|jpeg|png|gif|svg|webp)$/,

    // 音乐相关接口（公开访问）
    /^\/music\/list$/,               // 获取音乐列表
    /^\/music\/stream\/.*$/,         // 音乐流播放
    /^\/music\/lyrics\/.*$/,         // 获取歌词
    /^\/music\/cover\/.*$/,          // 获取音乐封面

    // 密码管理公开接口
    /^\/password\/verify$/,          // 验证密码
    /^\/password\/security-question\/.*$/,  // 获取密保问题

    // /chats/history
    /^\/chats\/history/,
    

    // WebRTC健康检查和Socket.IO
    /^\/webrtc\/health/,
    /^\/webrtc\/socket\.io\//,

    // // 其它公开下载、上传头像等
    // /^\/upload\/avatars/,
    // /^\/upload\/uploads/,
    // /^\/upload\/download/,
    // /^\/upload\/files/,
    // /^\/articles\/uploadCoverImage/,
  ],
});
