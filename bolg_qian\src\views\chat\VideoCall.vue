<template>
  <div class="video-call-container">
    <!-- 主界面 -->
    <div class="main-interface">
      <el-card class="call-manager">
        <template #header>
          <div class="card-header">
            <span>视频通话管理</span>
            <el-tag :type="webrtcClient ? 'success' : 'info'">
              {{ webrtcClient ? '已连接' : '未连接' }}
            </el-tag>
          </div>
        </template>

        <!-- 连接状态 -->
        <div class="status-section">
          <h3>连接状态</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="WebRTC状态">
              <el-tag :type="webrtcClient ? 'success' : 'danger'">
                {{ webrtcClient ? '已连接' : '未连接' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="当前通话">
              <el-tag :type="currentCall ? 'warning' : 'info'">
                {{ currentCall ? '通话中' : '空闲' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 操作按钮 -->
        <div class="action-section">
          <h3>操作</h3>
          <div class="action-buttons">
            <el-button
              type="primary"
              @click="initWebRTCClient"
              :loading="connecting"
              :disabled="!!webrtcClient"
            >
              <el-icon><VideoCamera /></el-icon>
              连接WebRTC服务器
            </el-button>

            <el-button
              type="success"
              @click="testVideoCall"
              :disabled="!webrtcClient"
            >
              <el-icon><VideoCamera /></el-icon>
              测试视频通话
            </el-button>

            <el-button
              type="info"
              @click="testAudioCall"
              :disabled="!webrtcClient"
            >
              <el-icon><Microphone /></el-icon>
              测试语音通话
            </el-button>

            <el-button
              type="danger"
              @click="disconnect"
              :disabled="!webrtcClient"
            >
              <el-icon><Close /></el-icon>
              断开连接
            </el-button>
          </div>
        </div>

        <!-- 测试说明 -->
        <div class="info-section">
          <h3>使用说明</h3>
          <el-alert
            title="视频通话测试"
            type="info"
            :closable="false"
          >
            <p>1. 首先点击"连接WebRTC服务器"建立连接</p>
            <p>2. 连接成功后可以测试视频通话和语音通话功能</p>
            <p>3. 在实际使用中，通话功能会集成到聊天界面中</p>
          </el-alert>
        </div>
      </el-card>
    </div>
    <!-- 来电通知 -->
    <el-dialog
      v-model="showIncomingCall"
      title="来电"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="incoming-call">
        <div class="caller-info">
          <el-avatar :size="80" :src="incomingCall?.caller?.avatar" />
          <h3>{{ incomingCall?.caller?.nickname }}</h3>
          <p>{{ incomingCall?.type === 'video' ? '视频通话' : '语音通话' }}</p>
        </div>
        <div class="call-actions">
          <el-button type="success" size="large" @click="acceptCall" :loading="accepting">
            <el-icon><Phone /></el-icon>
            接听
          </el-button>
          <el-button type="danger" size="large" @click="rejectCall">
            <el-icon><Close /></el-icon>
            拒绝
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 通话界面 -->
    <el-dialog
      v-model="showCallDialog"
      :title="callTitle"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      class="call-dialog"
    >
      <div class="call-interface">
        <!-- 视频区域 -->
        <div class="video-area" v-if="currentCall?.type === 'video'">
          <!-- 远程视频 -->
          <div class="remote-video">
            <video
              ref="remoteVideo"
              autoplay
              playsinline
              class="video-element"
            ></video>
            <div v-if="!remoteStream" class="video-placeholder">
              <el-avatar :size="120" :src="remoteUserAvatar" />
              <p>{{ remoteUserName }}</p>
            </div>
          </div>
          
          <!-- 本地视频 -->
          <div class="local-video">
            <video
              ref="localVideo"
              autoplay
              playsinline
              muted
              class="video-element"
            ></video>
          </div>
        </div>

        <!-- 音频通话界面 -->
        <div class="audio-interface" v-else>
          <div class="audio-avatar">
            <el-avatar :size="150" :src="remoteUserAvatar" />
            <h3>{{ remoteUserName }}</h3>
            <p>{{ callStatusText }}</p>
          </div>
        </div>

        <!-- 通话控制 -->
        <div class="call-controls">
          <el-button
            v-if="currentCall?.type === 'video'"
            :type="videoEnabled ? 'primary' : 'info'"
            size="large"
            circle
            @click="toggleVideo"
          >
            <el-icon><VideoCamera v-if="videoEnabled" /><Switch v-else /></el-icon>
          </el-button>
          
          <el-button
            :type="audioEnabled ? 'primary' : 'info'"
            size="large"
            circle
            @click="toggleAudio"
          >
            <el-icon><Microphone v-if="audioEnabled" /><Mute v-else /></el-icon>
          </el-button>
          
          <el-button
            type="danger"
            size="large"
            circle
            @click="endCall"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>

        <!-- 通话状态 -->
        <div class="call-status">
          <p>{{ callStatusText }}</p>
          <p v-if="callDuration">通话时长: {{ formatDuration(callDuration) }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 隐藏的音频元素 -->
    <audio ref="remoteAudio" autoplay v-if="currentCall?.type === 'audio'"></audio>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Phone, VideoCamera, Microphone, Mute, Close, Switch } from '@element-plus/icons-vue'
import WebRTCClient, { CALL_STATUS, CALL_TYPE } from '@/utils/webrtc-client'

// 响应式数据
const webrtcClient = ref(null)
const showIncomingCall = ref(false)
const showCallDialog = ref(false)
const incomingCall = ref(null)
const currentCall = ref(null)
const accepting = ref(false)
const localStream = ref(null)
const remoteStream = ref(null)
const videoEnabled = ref(true)
const audioEnabled = ref(true)
const callStartTime = ref(null)
const callDuration = ref(0)
const connecting = ref(false)

// DOM引用
const localVideo = ref(null)
const remoteVideo = ref(null)
const remoteAudio = ref(null)

// 计算属性
const callTitle = computed(() => {
  if (!currentCall.value) return '通话'
  const type = currentCall.value.type === 'video' ? '视频通话' : '语音通话'
  return `${type} - ${remoteUserName.value}`
})

const remoteUserName = computed(() => {
  if (currentCall.value) {
    const user = currentCall.value.caller?.id === getUserId() 
      ? currentCall.value.callee 
      : currentCall.value.caller
    return user?.nickname || '未知用户'
  }
  return ''
})

const remoteUserAvatar = computed(() => {
  if (currentCall.value) {
    const user = currentCall.value.caller?.id === getUserId() 
      ? currentCall.value.callee 
      : currentCall.value.caller
    return user?.avatar || ''
  }
  return ''
})

const callStatusText = computed(() => {
  if (!currentCall.value) return ''
  
  switch (currentCall.value.status) {
    case CALL_STATUS.CALLING:
      return '正在呼叫...'
    case CALL_STATUS.RINGING:
      return '对方振铃中...'
    case CALL_STATUS.CONNECTED:
      return '通话中'
    default:
      return ''
  }
})

// 获取用户ID的辅助函数
function getUserId() {
  return parseInt(localStorage.getItem('id') || '0')
}

// 获取用户信息的辅助函数
function getUserInfo() {
  return {
    id: getUserId(),
    nickname: localStorage.getItem('username') || '用户',
    avatar: localStorage.getItem('avatar') || ''
  }
}

// 自动启动WebRTC服务器
async function autoStartWebRTCServer() {
  try {
    console.log('🚀 尝试自动启动WebRTC服务器...')
    const response = await fetch('/api/webrtc/start', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()
    if (result.code === 200) {
      console.log('✅ WebRTC服务器自动启动成功')
      return true
    } else {
      console.warn('WebRTC服务器启动失败:', result.message)
      return false
    }
  } catch (error) {
    console.error('自动启动WebRTC服务器失败:', error)
    return false
  }
}

// 初始化WebRTC客户端
async function initWebRTCClient() {
  if (webrtcClient.value) {
    ElMessage.info('WebRTC客户端已连接')
    return
  }

  connecting.value = true
  try {
    webrtcClient.value = new WebRTCClient()

    // 设置事件回调
    webrtcClient.value.onCallIncoming = handleIncomingCall
    webrtcClient.value.onCallAccepted = handleCallAccepted
    webrtcClient.value.onCallEnded = handleCallEnded
    webrtcClient.value.onCallError = handleCallError
    webrtcClient.value.onLocalStream = handleLocalStream
    webrtcClient.value.onRemoteStream = handleRemoteStream

    // 连接到WebRTC服务器
    await webrtcClient.value.connect(getUserInfo())

    console.log('✅ WebRTC客户端初始化成功')
    ElMessage.success('WebRTC连接成功')

  } catch (error) {
    console.error('❌ WebRTC客户端初始化失败:', error)
    webrtcClient.value = null

    // 如果连接失败，尝试自动启动WebRTC服务器
    if (error.message.includes('websocket error') || error.message.includes('连接失败')) {
      ElMessage({
        message: 'WebRTC服务器未启动，正在尝试自动启动...',
        type: 'warning',
        duration: 3000
      })

      const started = await autoStartWebRTCServer()
      if (started) {
        ElMessage({
          message: 'WebRTC服务器启动成功，请重新点击连接',
          type: 'success',
          duration: 3000
        })
      } else {
        ElMessage({
          message: 'WebRTC服务器启动失败，请手动启动或联系管理员',
          type: 'error',
          duration: 5000
        })
      }
    } else {
      ElMessage.error('WebRTC连接失败: ' + error.message)
    }
  } finally {
    connecting.value = false
  }
}

// 处理来电
function handleIncomingCall(callInfo) {
  console.log('📞 处理来电:', callInfo)
  incomingCall.value = callInfo
  currentCall.value = callInfo
  showIncomingCall.value = true
}

// 处理通话被接受
function handleCallAccepted(callInfo) {
  console.log('✅ 通话被接受:', callInfo)
  currentCall.value = callInfo
  showIncomingCall.value = false
  showCallDialog.value = true
  callStartTime.value = Date.now()
  startCallTimer()
}

// 处理通话结束
function handleCallEnded(status, callInfo) {
  console.log('📴 通话结束:', status)
  showIncomingCall.value = false
  showCallDialog.value = false
  currentCall.value = null
  incomingCall.value = null
  callStartTime.value = null
  callDuration.value = 0
  stopCallTimer()
  
  // 清理媒体流
  if (localVideo.value) localVideo.value.srcObject = null
  if (remoteVideo.value) remoteVideo.value.srcObject = null
  if (remoteAudio.value) remoteAudio.value.srcObject = null
}

// 处理通话错误
function handleCallError(message) {
  console.error('❌ 通话错误:', message)
  ElMessage.error(message)
}

// 处理本地媒体流
function handleLocalStream(stream) {
  console.log('📹 设置本地媒体流')
  localStream.value = stream
  
  nextTick(() => {
    if (localVideo.value) {
      localVideo.value.srcObject = stream
    }
  })
}

// 处理远程媒体流
function handleRemoteStream(stream) {
  console.log('📺 设置远程媒体流')
  remoteStream.value = stream
  
  nextTick(() => {
    if (currentCall.value?.type === 'video' && remoteVideo.value) {
      remoteVideo.value.srcObject = stream
    } else if (currentCall.value?.type === 'audio' && remoteAudio.value) {
      remoteAudio.value.srcObject = stream
    }
  })
}

// 发起通话
async function initiateCall(targetUserId, type = CALL_TYPE.VIDEO) {
  try {
    if (!webrtcClient.value) {
      await initWebRTCClient()
    }
    
    await webrtcClient.value.initiateCall(targetUserId, type)
    
    // 显示通话界面
    showCallDialog.value = true
    
  } catch (error) {
    console.error('发起通话失败:', error)
    ElMessage.error('发起通话失败')
  }
}

// 接受通话
async function acceptCall() {
  try {
    accepting.value = true
    await webrtcClient.value.acceptCall()
    
  } catch (error) {
    console.error('接受通话失败:', error)
    ElMessage.error('接受通话失败')
  } finally {
    accepting.value = false
  }
}

// 拒绝通话
function rejectCall() {
  webrtcClient.value?.rejectCall()
  showIncomingCall.value = false
  incomingCall.value = null
  currentCall.value = null
}

// 结束通话
function endCall() {
  webrtcClient.value?.endCall()
}

// 切换视频
function toggleVideo() {
  if (localStream.value) {
    const videoTrack = localStream.value.getVideoTracks()[0]
    if (videoTrack) {
      videoTrack.enabled = !videoTrack.enabled
      videoEnabled.value = videoTrack.enabled
    }
  }
}

// 切换音频
function toggleAudio() {
  if (localStream.value) {
    const audioTrack = localStream.value.getAudioTracks()[0]
    if (audioTrack) {
      audioTrack.enabled = !audioTrack.enabled
      audioEnabled.value = audioTrack.enabled
    }
  }
}

// 通话计时器
let callTimer = null

function startCallTimer() {
  callTimer = setInterval(() => {
    if (callStartTime.value) {
      callDuration.value = Math.floor((Date.now() - callStartTime.value) / 1000)
    }
  }, 1000)
}

function stopCallTimer() {
  if (callTimer) {
    clearInterval(callTimer)
    callTimer = null
  }
}

// 格式化通话时长
function formatDuration(seconds) {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 测试视频通话
function testVideoCall() {
  if (!webrtcClient.value) {
    ElMessage.error('请先连接WebRTC服务器')
    return
  }

  ElMessage.info('视频通话测试功能需要另一个用户才能完成')
  console.log('测试视频通话功能')
}

// 测试语音通话
function testAudioCall() {
  if (!webrtcClient.value) {
    ElMessage.error('请先连接WebRTC服务器')
    return
  }

  ElMessage.info('语音通话测试功能需要另一个用户才能完成')
  console.log('测试语音通话功能')
}

// 断开连接
function disconnect() {
  if (webrtcClient.value) {
    webrtcClient.value.disconnect()
    webrtcClient.value = null
    ElMessage.success('已断开WebRTC连接')
  }
}

// 暴露方法给父组件
defineExpose({
  initiateCall,
  initWebRTCClient
})

// 生命周期
onMounted(() => {
  console.log('VideoCall页面已挂载，可以手动连接WebRTC服务器')
})

onUnmounted(() => {
  stopCallTimer()
  webrtcClient.value?.disconnect()
})
</script>

<style scoped>
.video-call-container {
  position: relative;
  padding: 20px;
}

.main-interface {
  max-width: 800px;
  margin: 0 auto;
}

.call-manager {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-section,
.action-section,
.info-section {
  margin-bottom: 20px;
}

.status-section h3,
.action-section h3,
.info-section h3 {
  margin-bottom: 15px;
  color: #303133;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  min-width: 160px;
}

.incoming-call {
  text-align: center;
  padding: 20px;
}

.caller-info {
  margin-bottom: 30px;
}

.caller-info h3 {
  margin: 15px 0 5px 0;
  font-size: 24px;
}

.caller-info p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.call-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.call-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.call-interface {
  position: relative;
  height: 500px;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-area {
  position: relative;
  width: 100%;
  height: 100%;
}

.remote-video {
  width: 100%;
  height: 100%;
  position: relative;
  background: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
}

.local-video {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 200px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #fff;
  background: #333;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-placeholder {
  text-align: center;
  color: #fff;
}

.video-placeholder p {
  margin-top: 15px;
  font-size: 18px;
}

.audio-interface {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.audio-avatar {
  text-align: center;
  color: #fff;
}

.audio-avatar h3 {
  margin: 20px 0 10px 0;
  font-size: 28px;
}

.audio-avatar p {
  margin: 0;
  font-size: 16px;
  opacity: 0.8;
}

.call-controls {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
  z-index: 10;
}

.call-controls .el-button {
  width: 60px;
  height: 60px;
  font-size: 24px;
}

.call-status {
  position: absolute;
  top: 20px;
  left: 20px;
  color: #fff;
  z-index: 10;
}

.call-status p {
  margin: 5px 0;
  font-size: 14px;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}
</style>
