<template>
  <div class="effect-test">
    <div class="test-container">
      <h1>特效测试页面</h1>
      
      <div class="test-controls">
        <h2>当前特效: {{ currentEffectName }}</h2>
        
        <div class="effect-buttons">
          <el-button @click="testEffect('none')" :type="currentEffect === 'none' ? 'primary' : ''">
            无特效
          </el-button>
          <el-button @click="testEffect('meteor')" :type="currentEffect === 'meteor' ? 'primary' : ''">
            流星雨
          </el-button>
          <el-button @click="testEffect('nightsky')" :type="currentEffect === 'nightsky' ? 'primary' : ''">
            夜空星辰
          </el-button>
          <el-button @click="testEffect('particle')" :type="currentEffect === 'particle' ? 'primary' : ''">
            粒子连线
          </el-button>
        </div>
        
        <div class="test-info">
          <p>点击上方按钮测试不同特效</p>
          <p>粒子特效支持鼠标交互，移动鼠标试试！</p>
          <p>夜空特效支持点击交互，点击屏幕试试！</p>
        </div>
      </div>
    </div>
    
    <!-- 特效渲染区域 -->
    <div class="effect-area">
      <Meteor 
        v-if="currentEffect === 'meteor'"
        :count="25"
        :speed="4"
      />
      <NightSky v-if="currentEffect === 'nightsky'" />
      <Particle v-if="currentEffect === 'particle'" />
    </div>
    
    <!-- 返回按钮 -->
    <div class="back-button">
      <el-button @click="goBack" type="primary">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import Meteor from '@/components/texiao/Meteor.vue'
import NightSky from '@/components/texiao/NightSky.vue'
import Particle from '@/components/texiao/Particle.vue'

const router = useRouter()
const currentEffect = ref('none')

const currentEffectName = computed(() => {
  const names = {
    none: '无特效',
    meteor: '流星雨',
    nightsky: '夜空星辰',
    particle: '粒子连线'
  }
  return names[currentEffect.value] || '未知'
})

const testEffect = (effect) => {
  currentEffect.value = effect
  ElMessage.success(`切换到${currentEffectName.value}`)
}

const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.effect-test {
  min-height: 100vh;
  background: var(--bg-secondary);
  position: relative;
}

.test-container {
  position: relative;
  z-index: 10;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  margin-top: var(--spacing-2xl);
  box-shadow: var(--shadow-lg);
}

.test-container h1 {
  text-align: center;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xl);
  font-size: var(--text-3xl);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.test-controls h2 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.effect-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  flex-wrap: wrap;
}

.test-info {
  background: var(--bg-tertiary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--primary-color);
}

.test-info p {
  margin: var(--spacing-xs) 0;
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

.effect-area {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.effect-area canvas {
  pointer-events: auto;
}

.back-button {
  position: fixed;
  bottom: var(--spacing-lg);
  left: var(--spacing-lg);
  z-index: 20;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .test-container {
    margin: var(--spacing-lg);
    padding: var(--spacing-lg);
  }
  
  .effect-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .effect-buttons .el-button {
    width: 200px;
  }
}
</style>
