const mysql = require("mysql");
const { getExportConfig } = require("../../config/database");

// 获取导出服务专用数据库配置
const dbConfig = getExportConfig();

// 创建数据库连接并执行查询
function createConnection() {
  return mysql.createConnection(dbConfig);
}

// 执行SQL查询
function executeQuery(connection, sql) {
  return new Promise((resolve, reject) => {
    connection.query(sql, (err, results) => {
      if (err) reject(err);
      else resolve(results);
    });
  });
}

// 获取所有表名
async function getAllTables(connection) {
  const tables = await executeQuery(connection, "SHOW TABLES");
  return tables.map((row) => Object.values(row)[0]);
}

// 获取表结构
async function getTableStructure(connection, tableName) {
  const result = await executeQuery(connection, `SHOW CREATE TABLE \`${tableName}\``);
  return result[0]["Create Table"];
}

// 获取表数据（限制前2条）
async function getTableData(connection, tableName) {
  const result = await executeQuery(connection, `SELECT * FROM \`${tableName}\` LIMIT 2`);
  return result;
}

// 转义SQL值
function escapeValue(val) {
  if (val === null) return "NULL";
  if (typeof val === "number") return val;
  return `'${val.toString().replace(/\\/g, "\\\\").replace(/'/g, "\\'")}'`;
}

module.exports = {
  createConnection,
  executeQuery,
  getAllTables,
  getTableStructure,
  getTableData,
  escapeValue,
  dbConfig,
};
