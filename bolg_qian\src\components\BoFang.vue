<template>
  <div
    class="drop-area"
    @dragover.prevent
    @drop.prevent="handleDrop"
    style="width: 100%; height: 320px; border: 2px dashed #aaa; display: flex; align-items: center; justify-content: center; flex-direction: column;"
  >
    <div v-if="!videoUrl" style="color: #888;">
      拖拽本地视频文件到此区域播放（支持mp4/webm/ogg/mov/avi等）<br />
      <el-button type="primary" @click="selectFile" style="margin-top: 16px; transition: background-color 0.3s, transform 0.2s;">点击选择视频文件</el-button>
      <input ref="fileInput" type="file" accept="video/*" style="display: none" @change="handleFileChange" />
    </div>
    <div v-else class="video-container" style="position: relative; width: 100%; max-height: 320px; display: flex; align-items: center; justify-content: center;">
      <video
        :src="videoUrl"
        controls
        autoplay
        style="width: 100%; max-height: 300px; border-radius: 8px; border: 2px solid #ccc; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);"
      ></video>
      <div v-if="loading" class="loading-overlay" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255, 255, 255, 0.8); display: flex; align-items: center; justify-content: center;">
        <el-icon :size="40" color="#666"><loading /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { ElButton, ElIcon } from "element-plus";
import { Loading } from '@element-plus/icons-vue';

const videoUrl = ref("");
const fileInput = ref(null);
const loading = ref(false);

function handleDrop(e) {
  const file = e.dataTransfer.files[0];
  playFile(file);
}

function selectFile() {
  fileInput.value && fileInput.value.click();
}

function handleFileChange(e) {
  const file = e.target.files[0];
  playFile(file);
}

async function playFile(file) {
  if (file && file.type.startsWith("video/")) {
    loading.value = true;
    videoUrl.value = URL.createObjectURL(file);
    setTimeout(() => {
      loading.value = false;
    }, 1000); // 模拟加载时间
  } else {
    alert("请拖入或选择视频文件！");
  }
}
</script>

<style scoped>
.drop-area {
  width: 100%;
  height: 320px;
  border: 2px dashed #aaa;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.el-button {
  margin-top: 16px;
  transition: background-color 0.3s, transform 0.2s;
}

.el-button:hover {
  background-color: #66b1ff;
  transform: scale(1.05);
}

.video-container {
  position: relative;
  width: 100%;
  max-height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
}

video {
  width: 100%;
  max-height: 300px;
  border-radius: 8px;
  border: 2px solid #ccc;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
