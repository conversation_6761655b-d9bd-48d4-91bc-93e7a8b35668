<template>
  <div class="article-detail-container">
    <el-breadcrumb separator="/" class="breadcrumb">
      <el-breadcrumb-item :to="{ path: '/index/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>文章详情</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card class="article-card">
      <h1 class="article-title">{{ article.title }}</h1>
      <div class="author-info">
        <el-avatar :size="40" :src="imgUrl" />
        <div class="author-meta">
          <span class="author-name">{{ article.authorName }}</span>
          <div class="publish-info">
            <span><el-icon>
                <Clock />
              </el-icon>{{ formatDate(article.updated_at) }}</span>
            <span><el-icon>
                <View />
              </el-icon>{{ article.viewCount }} 浏览</span>
          </div>
        </div>
        <div class="actions">
          <el-button v-if="isAuthor" type="danger" size="small" style="margin-left: 10px"
            @click="delArticle">删除</el-button>
          <el-button v-if="isAuthor" type="success" size="small" :plain="!article.is_share" @click="toggleShare"
            style="margin-left: 10px">
            {{ article.is_share ? "已共享" : "共享" }}
          </el-button>
        </div>
      </div>

      <el-image v-if="article.cover_image" class="cover-image" :src="coverImg" fit="cover" alt="文章封面" />

      <!-- 渲染 Markdown 转成的 HTML -->
      <div class="article-content markdown-body" v-html="articleHtml"></div>

      <div v-if="article.tags" class="tags" style="display: flex; align-items: center; justify-content: space-between;">
        <div>
          <el-tag v-for="tag in article.tags.split(',')" :key="tag" type="info" class="tag-item">
            {{ tag }}
          </el-tag>
        </div>
        <el-button class="copy-btn" type="primary" size="small" @click="copyText(article.summary)">
          📋 复制摘要
        </el-button>
      </div>
    </el-card>

    <el-card class="comment-card">
      <template #header>
        <div class="comment-header">评论 ({{ comments.length }})</div>
      </template>

      <div class="comment-form">
        <el-input v-model="commentContent" type="textarea" :rows="3" placeholder="写下你的评论..." />
        <div class="form-actions">
          <el-button type="primary" @click="submitComment">发表评论</el-button>
        </div>
      </div>

      <!-- 替换原有的评论递归渲染部分 -->
      <div class="comment-list">
        <CommentItem v-for="comment in [...commentTree].reverse()" :key="comment.id" :comment="comment"
          :get-avatar-url="getAvatarUrl" :format-date="formatDate" :reply-to-id="replyToId"
          :reply-to-author="replyToAuthor" :reply-content="replyContent"
          @update:replyContent="val => replyContent = val" :show-reply-input="showReplyInput"
          :submit-reply="submitReply" :cancel-reply="cancelReply" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import hljs from "highlight.js";
import "highlight.js/styles/github.css";
import MarkdownIt from "markdown-it";
import { copyText } from "@/plug/copy";
import {
  CommentArticleApi,
  GetArticleByIdApi,
  FakeDeleteArticleApi,
  SubmitCommentApi,
  ShareArticleApi,
} from "../utils/api";
import { ElMessage, ElMessageBox } from "element-plus";
import CommentItem from '@/components/CommentItem.vue';

const route = useRoute();
const router = useRouter();
const articleId = Number(route.params.id);

interface Article {
  id: number;
  title: string;
  content: string;
  authorName: string;
  authorAvatar: string;
  cover_image: string;
  published_at: string;
  updated_at: string;
  viewCount: number;
  tags: string;
  is_share?: number;
}

interface Comment {
  id: number;
  author: string;
  avatar: string;
  content: string;
  created_at: string;
  parent_id?: number;
  children?: Comment[];
}

const article = ref<Article>({} as Article);
const comments = ref<Comment[]>([]);
const commentTree = ref<Comment[]>([]);
const commentContent = ref("");
const imgUrl = ref("");
const coverImg = ref("");
const articleHtml = ref("");
const replyToId = ref<number | null>(null);
const replyToAuthor = ref("");
const replyContent = ref("");

const currentUser = localStorage.getItem("username") || ""; // 确保是字符串
console.log("当前用户：", currentUser);
const isAuthor = computed(() => {
  // 兼容 authorName 可能为 undefined/null
  return currentUser && article.value.authorName && currentUser === article.value.authorName;
});

const md = new MarkdownIt({
  highlight(str: string, lang: string) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return `<pre class="hljs"><code>${hljs.highlight(str, { language: lang }).value}</code></pre>`;
      } catch {
        // ignore
      }
    }
    return `<pre class="hljs"><code>${md.utils.escapeHtml(str)}</code></pre>`;
  }
});

const formatDate = (dateStr: string): string => {
  if (!dateStr) return "";
  const d = new Date(dateStr);
  const pad = (n: number) => n.toString().padStart(2, "0");
  return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}`;
};

const fetchArticleDetail = async () => {
  const result = await GetArticleByIdApi({ id: articleId });
  const data = result?.data?.[0];
  if (data) {
    article.value = data;
    imgUrl.value = data.authorAvatar
      ? `http://192.168.31.222:3000/avatars/${data.authorAvatar}`
      : "";
    coverImg.value = data.cover_image
      ? `http://192.168.31.222:3000/articles/${data.cover_image}`
      : "";
    articleHtml.value = md.render(data.content || "");
    await nextTick();
  }
};

// 将评论列表转为树结构（去重+递归）
function buildCommentTree(list: any[]): any[] {
  const map: Record<number, any> = {};
  const tree: any[] = [];
  // 先去重，只保留id唯一的节点
  list.forEach(item => {
    map[item.id] = { ...item, children: [] };
  });
  // 再组装树结构
  Object.values(map).forEach(item => {
    if (item.parent_id && map[item.parent_id]) {
      map[item.parent_id].children.push(item);
    } else if (!item.parent_id || item.parent_id === 0) {
      tree.push(item);
    }
  });
  return tree;
}

const fetchComments = async () => {
  const result = await CommentArticleApi({ article_id: articleId, t: Date.now() });
  if (result?.data) {
    comments.value = result.data;
    console.log("评论数据：", comments.value);
    commentTree.value = buildCommentTree(result.data);
  }
};

const submitComment = async () => {
  const content = commentContent.value.trim();
  if (!content) {
    ElMessage.warning("评论内容不能为空");
    return;
  }
  const username = localStorage.getItem("username") || "匿名用户";
  const result = await SubmitCommentApi({
    username,
    article_id: articleId,
    content
  });
  if (result?.message) {
    ElMessage.success("评论发布成功");
    await fetchComments();
    commentContent.value = "";
    // 再次获取评论列表，刷新评论列表
  } else {
    ElMessage.error("评论发布失败");
  }
};

const delArticle = async () => {


  ElMessageBox.confirm("确定要删除该文章吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      const result = await FakeDeleteArticleApi({ id: articleId });
      if (result) {
        ElMessage.success("删除成功");
        router.push("/index/home");
      } else {
        ElMessage.error("删除失败，请稍后再试");
      }
    })
    .catch(() => {
      // 用户取消操作
      console.log("用户取消删除操作");
    });
};

const toggleShare = async () => {
  const newShare = article.value.is_share ? 0 : 1;
  const res = await ShareArticleApi(article.value.id, newShare);
  if (res?.message) {
    article.value.is_share = newShare;
    ElMessage.success(newShare ? "已共享" : "已取消共享");
  } else {
    ElMessage.error("操作失败");
  }
};



function getAvatarUrl(avatar: string) {
  if (!avatar) return "";
  if (avatar === "moren.png") {
    return "http://192.168.31.222:3000/avatars/moren.png";
  }
  return `http://192.168.31.222:3000/avatars/${avatar}`;
}

function showReplyInput(commentId: number, author: string) {
  replyToId.value = commentId;
  replyToAuthor.value = author;
  replyContent.value = "";
}

function cancelReply() {
  replyToId.value = null;
  replyToAuthor.value = "";
  replyContent.value = "";
}

const submitReply = async (parentId: number) => {
  const content = replyContent.value.trim();
  if (!content) {
    ElMessage.warning("回复内容不能为空");
    return;
  }
  const username = localStorage.getItem("username") || "匿名用户";
  const result = await SubmitCommentApi({
    username,
    article_id: articleId,
    content,
    parent_id: parentId,
  });
  if (result?.message) {
    ElMessage.success("回复成功");
    await fetchComments();
    cancelReply();
  } else {
    ElMessage.error("回复失败");
  }
};

onMounted(() => {
  fetchArticleDetail();
  fetchComments();
});
</script>

<style scoped lang="less">
@import "highlight.js/styles/github.css";

.article-detail-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;

  .breadcrumb {
    margin-bottom: 20px;
  }

  .article-card {
    margin-bottom: 20px;

    .article-title {
      font-size: 24px;
      margin-bottom: 20px;
      color: #333;
    }

    .author-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;

      .author-meta {
        margin-left: 10px;

        .author-name {
          font-weight: bold;
          font-size: 16px;
        }

        .publish-info {
          margin-top: 5px;
          font-size: 12px;
          color: #999;

          span {
            margin-right: 15px;
            display: inline-flex;
            align-items: center;

            .el-icon {
              margin-right: 4px;
            }
          }
        }
      }

      .actions {
        margin-left: auto;
      }
    }

    .cover-image {
      width: 100%;
      height: 100%;
      margin-bottom: 20px;
      border-radius: 4px;
      object-fit: contain;
      background: #f6f8fa;
      display: block;
    }

    .article-content {
      line-height: 1.8;
      font-size: 16px;
      color: #333;
      margin-bottom: 20px;
      text-indent: 2em;

      pre {
        padding: 15px;
        background: #f6f8fa;
        border-radius: 6px;
        overflow-x: auto;
        font-size: 14px;
      }

      code {
        font-family: Menlo, Monaco, Consolas, 'Courier New', monospace;
      }
    }

    .tags {
      margin-top: 20px;

      .tag-item {
        margin-right: 10px;
      }
    }
  }

  .comment-card {
    .comment-header {
      font-weight: bold;
      font-size: 18px;
    }

    .comment-form {
      margin-bottom: 20px;

      .form-actions {
        margin-top: 10px;
        text-align: right;
      }
    }

    .comment-list {
      .comment-item {
        display: flex;
        padding: 15px 0;
        border-bottom: 1px solid #eee;

        .comment-content {
          margin-left: 15px;
          flex: 1;

          .comment-meta {
            margin-bottom: 5px;

            .comment-author {
              font-weight: bold;
              margin-right: 10px;
            }

            .comment-time {
              font-size: 12px;
              color: #999;
            }
          }

          .comment-text {
            line-height: 1.6;
          }

          .reply-form {
            margin-top: 10px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
            border: 1px solid #e0e0e0;

            .form-actions {
              margin-top: 10px;
              text-align: right;
            }
          }
        }
      }
    }
  }

  .copy-btn {
    // margin-top: 8px;
  }
}
</style>