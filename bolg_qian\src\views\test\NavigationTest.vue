<template>
  <div class="nav-test-page">
    <el-card class="test-card">
      <template #header>
        <h2>🧭 导航菜单对齐测试</h2>
      </template>
      
      <div class="test-content">
        <div class="test-section">
          <h3>📋 测试说明</h3>
          <p>此页面用于测试首页导航菜单的水平对齐效果。请检查以下项目：</p>
          
          <el-alert type="info" :closable="false" style="margin: 16px 0;">
            <template #title>
              <strong>检查要点</strong>
            </template>
            <ul style="margin: 8px 0; padding-left: 20px;">
              <li>所有菜单项（首页、内容、媒体、工具、管理）应在同一水平线上</li>
              <li>菜单项之间的间距应该一致</li>
              <li>图标和文字应该垂直居中对齐</li>
              <li>悬停效果应该正常工作</li>
              <li>下拉菜单应该正确显示</li>
            </ul>
          </el-alert>
        </div>

        <div class="test-section">
          <h3>🎯 对齐参考线</h3>
          <div class="alignment-guide">
            <div class="guide-line"></div>
            <p>上方的红色虚线可以作为参考，帮助您检查菜单项是否对齐</p>
          </div>
        </div>

        <div class="test-section">
          <h3>🔧 已应用的修复</h3>
          <el-timeline>
            <el-timeline-item timestamp="修复 1" type="primary">
              <h4>统一菜单项高度</h4>
              <p>设置所有菜单项的高度为 40px，确保一致性</p>
            </el-timeline-item>
            <el-timeline-item timestamp="修复 2" type="success">
              <h4>使用 Flexbox 布局</h4>
              <p>将导航菜单设置为 flex 布局，使用 align-items: center</p>
            </el-timeline-item>
            <el-timeline-item timestamp="修复 3" type="warning">
              <h4>重置默认样式</h4>
              <p>移除 Element Plus 默认的边框和边距样式</p>
            </el-timeline-item>
            <el-timeline-item timestamp="修复 4" type="info">
              <h4>强制对齐规则</h4>
              <p>添加 !important 规则确保样式优先级</p>
            </el-timeline-item>
          </el-timeline>
        </div>

        <div class="test-section">
          <h3>📱 响应式测试</h3>
          <p>请在不同屏幕尺寸下测试导航菜单：</p>
          <div class="responsive-test">
            <el-tag type="success">✅ 桌面端 (>1024px)</el-tag>
            <el-tag type="warning">⚠️ 平板端 (768-1024px)</el-tag>
            <el-tag type="info">📱 移动端 (<768px)</el-tag>
          </div>
        </div>

        <div class="test-section">
          <h3>🎨 样式代码预览</h3>
          <el-collapse>
            <el-collapse-item title="查看关键CSS代码" name="css">
              <pre class="code-preview"><code>.main-nav {
  background: transparent;
  border: none;
  height: 70px;
  line-height: 70px;
}

:deep(.el-menu--horizontal) {
  display: flex;
  align-items: center;
  height: 70px;
  border-bottom: none;
}

:deep(.el-menu-item),
:deep(.el-sub-menu) {
  height: 40px;
  line-height: 40px;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}</code></pre>
            </el-collapse-item>
          </el-collapse>
        </div>

        <div class="test-actions">
          <el-button type="primary" @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回首页
          </el-button>
          <el-button type="success" @click="refreshPage">
            <el-icon><Refresh /></el-icon>
            刷新页面
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { ArrowLeft, Refresh } from '@element-plus/icons-vue';

const router = useRouter();

const goBack = () => {
  router.push('/index/home');
};

const refreshPage = () => {
  window.location.reload();
};
</script>

<style scoped>
.nav-test-page {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.test-card h2 {
  margin: 0;
  color: #409eff;
  text-align: center;
}

.test-content {
  padding: 20px 0;
}

.test-section {
  margin-bottom: 32px;
}

.test-section h3 {
  color: #2c3e50;
  margin-bottom: 16px;
  font-size: 18px;
}

.alignment-guide {
  position: relative;
  padding: 20px 0;
  text-align: center;
}

.guide-line {
  height: 2px;
  background: repeating-linear-gradient(
    to right,
    #ff4757 0px,
    #ff4757 10px,
    transparent 10px,
    transparent 20px
  );
  margin-bottom: 10px;
}

.responsive-test {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 12px;
}

.code-preview {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #2c3e50;
  overflow-x: auto;
}

.test-actions {
  text-align: center;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.test-actions .el-button {
  margin: 0 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-test-page {
    padding: 10px;
  }
  
  .responsive-test {
    flex-direction: column;
  }
  
  .test-actions .el-button {
    width: 100%;
    margin: 4px 0;
  }
}
</style>
