// 性能监控路由
const Router = require("koa-router");
const { handleResponse } = require("../../middlewares/responseHandler");
const { getPerformanceStats, performanceHealthCheck, resetStats } = require("../../middlewares/performanceMiddleware");
const { getCacheStats, clearCache, healthCheck: cacheHealthCheck } = require("../../middlewares/cacheMiddleware");
const { getRateLimitStats, unblockIP, resetRateLimit } = require("../../middlewares/rateLimitMiddleware");
const db = require("../../utils/db");
const logger = require("../../plugin/logger");

const performance = new Router();

// 获取性能统计信息
performance.get("/stats", async (ctx) => {
  try {
    const performanceStats = getPerformanceStats();
    const cacheStats = getCacheStats();
    const rateLimitStats = getRateLimitStats();
    const dbStats = db.getPoolStatus();
    
    const stats = {
      performance: performanceStats,
      cache: cacheStats,
      rateLimit: rateLimitStats,
      database: dbStats,
      timestamp: new Date().toISOString()
    };
    
    return handleResponse(ctx, 200, stats);
  } catch (error) {
    logger.error("获取性能统计失败:", error);
    return handleResponse(ctx, 500, { error: "获取性能统计失败" });
  }
});

// 性能健康检查
performance.get("/health", async (ctx) => {
  try {
    const performanceHealth = performanceHealthCheck();
    const cacheHealth = cacheHealthCheck();
    
    // 数据库健康检查
    let dbHealth = { status: 'healthy', message: '数据库连接正常' };
    try {
      await db.query('SELECT 1');
    } catch (error) {
      dbHealth = { 
        status: 'critical', 
        message: '数据库连接失败',
        error: error.message 
      };
    }
    
    // 综合健康状态
    const overallStatus = [performanceHealth.status, cacheHealth.status, dbHealth.status];
    let status = 'healthy';
    
    if (overallStatus.includes('critical')) {
      status = 'critical';
    } else if (overallStatus.includes('warning')) {
      status = 'warning';
    }
    
    const health = {
      status,
      components: {
        performance: performanceHealth,
        cache: cacheHealth,
        database: dbHealth
      },
      timestamp: new Date().toISOString()
    };
    
    // 根据健康状态设置HTTP状态码
    const httpStatus = status === 'critical' ? 503 : 200;
    
    return handleResponse(ctx, httpStatus, health);
  } catch (error) {
    logger.error("健康检查失败:", error);
    return handleResponse(ctx, 500, { 
      status: 'critical',
      error: "健康检查失败",
      timestamp: new Date().toISOString()
    });
  }
});

// 获取慢查询日志
performance.get("/slow-queries", async (ctx) => {
  try {
    const { limit = 50 } = ctx.query;
    const stats = getPerformanceStats();
    
    const slowQueries = stats.slowQueries.slice(0, parseInt(limit));
    
    return handleResponse(ctx, 200, {
      slowQueries,
      total: stats.slowQueries.length,
      limit: parseInt(limit)
    });
  } catch (error) {
    logger.error("获取慢查询失败:", error);
    return handleResponse(ctx, 500, { error: "获取慢查询失败" });
  }
});

// 获取端点性能排行
performance.get("/endpoints", async (ctx) => {
  try {
    const { sortBy = 'responseTime', limit = 20 } = ctx.query;
    const stats = getPerformanceStats();
    
    let endpoints;
    if (sortBy === 'requests') {
      endpoints = stats.endpoints.mostFrequent;
    } else {
      endpoints = stats.endpoints.slowest;
    }
    
    return handleResponse(ctx, 200, {
      endpoints: endpoints.slice(0, parseInt(limit)),
      sortBy,
      total: stats.endpoints.total
    });
  } catch (error) {
    logger.error("获取端点性能失败:", error);
    return handleResponse(ctx, 500, { error: "获取端点性能失败" });
  }
});

// 清理缓存
performance.post("/cache/clear", async (ctx) => {
  try {
    const { pattern } = ctx.request.body || {};
    const cleared = clearCache(pattern);
    
    logger.info("手动清理缓存", { 
      pattern, 
      cleared, 
      operator: ctx.state.user?.username 
    });
    
    return handleResponse(ctx, 200, {
      message: "缓存清理成功",
      cleared,
      pattern
    });
  } catch (error) {
    logger.error("清理缓存失败:", error);
    return handleResponse(ctx, 500, { error: "清理缓存失败" });
  }
});

// 解除IP限流
performance.post("/ratelimit/unblock", async (ctx) => {
  try {
    const { ip } = ctx.request.body || {};
    
    if (!ip) {
      return handleResponse(ctx, 400, { error: "缺少IP参数" });
    }
    
    const wasBlocked = unblockIP(ip);
    
    logger.info("手动解除IP限流", { 
      ip, 
      wasBlocked, 
      operator: ctx.state.user?.username 
    });
    
    return handleResponse(ctx, 200, {
      message: wasBlocked ? "IP解除限流成功" : "IP未被限流",
      ip,
      wasBlocked
    });
  } catch (error) {
    logger.error("解除IP限流失败:", error);
    return handleResponse(ctx, 500, { error: "解除IP限流失败" });
  }
});

// 重置限流计数
performance.post("/ratelimit/reset", async (ctx) => {
  try {
    const { key } = ctx.request.body || {};
    
    if (!key) {
      return handleResponse(ctx, 400, { error: "缺少key参数" });
    }
    
    const wasReset = resetRateLimit(key);
    
    logger.info("重置限流计数", { 
      key, 
      wasReset, 
      operator: ctx.state.user?.username 
    });
    
    return handleResponse(ctx, 200, {
      message: wasReset ? "限流计数重置成功" : "限流计数不存在",
      key,
      wasReset
    });
  } catch (error) {
    logger.error("重置限流计数失败:", error);
    return handleResponse(ctx, 500, { error: "重置限流计数失败" });
  }
});

// 重置性能统计
performance.post("/stats/reset", async (ctx) => {
  try {
    resetStats();
    
    logger.info("重置性能统计", { 
      operator: ctx.state.user?.username 
    });
    
    return handleResponse(ctx, 200, {
      message: "性能统计重置成功",
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error("重置性能统计失败:", error);
    return handleResponse(ctx, 500, { error: "重置性能统计失败" });
  }
});

// 获取系统资源使用情况
performance.get("/system", async (ctx) => {
  try {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    const uptime = process.uptime();
    
    const systemInfo = {
      memory: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        external: Math.round(memoryUsage.external / 1024 / 1024),
        arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024)
      },
      cpu: {
        user: Math.round(cpuUsage.user / 1000), // 毫秒
        system: Math.round(cpuUsage.system / 1000)
      },
      process: {
        uptime: Math.round(uptime),
        pid: process.pid,
        version: process.version,
        platform: process.platform,
        arch: process.arch
      },
      node: {
        version: process.version,
        versions: process.versions
      },
      timestamp: new Date().toISOString()
    };
    
    return handleResponse(ctx, 200, systemInfo);
  } catch (error) {
    logger.error("获取系统信息失败:", error);
    return handleResponse(ctx, 500, { error: "获取系统信息失败" });
  }
});

// 获取数据库性能信息
performance.get("/database", async (ctx) => {
  try {
    // 获取数据库状态
    const [
      processlist,
      status,
      variables
    ] = await Promise.all([
      db.query("SHOW PROCESSLIST"),
      db.query("SHOW STATUS WHERE Variable_name IN ('Threads_connected', 'Threads_running', 'Queries', 'Slow_queries', 'Uptime')"),
      db.query("SHOW VARIABLES WHERE Variable_name IN ('max_connections', 'slow_query_log', 'long_query_time')")
    ]);
    
    // 转换状态和变量为对象
    const statusObj = {};
    status.forEach(row => {
      statusObj[row.Variable_name] = row.Value;
    });
    
    const variablesObj = {};
    variables.forEach(row => {
      variablesObj[row.Variable_name] = row.Value;
    });
    
    const dbInfo = {
      connections: {
        current: parseInt(statusObj.Threads_connected || 0),
        running: parseInt(statusObj.Threads_running || 0),
        max: parseInt(variablesObj.max_connections || 0)
      },
      queries: {
        total: parseInt(statusObj.Queries || 0),
        slow: parseInt(statusObj.Slow_queries || 0)
      },
      uptime: parseInt(statusObj.Uptime || 0),
      slowQueryLog: variablesObj.slow_query_log === 'ON',
      longQueryTime: parseFloat(variablesObj.long_query_time || 0),
      processlist: processlist.length,
      poolStatus: db.getPoolStatus(),
      timestamp: new Date().toISOString()
    };
    
    return handleResponse(ctx, 200, dbInfo);
  } catch (error) {
    logger.error("获取数据库性能信息失败:", error);
    return handleResponse(ctx, 500, { error: "获取数据库性能信息失败" });
  }
});

module.exports = performance;
