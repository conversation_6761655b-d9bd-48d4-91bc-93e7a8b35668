/**
 * 应用程序配置管理
 * 统一管理所有应用配置
 */

const path = require('path');
const logger = require("../plugin/logger");

/**
 * 服务器配置
 */
const server = {
  port: parseInt(process.env.PORT) || 3000,
  host: process.env.HOST || '0.0.0.0',
  env: process.env.NODE_ENV || 'development',
  
  // CORS配置
  cors: {
    allowedOrigins: process.env.CORS_ORIGINS ? 
      process.env.CORS_ORIGINS.split(',') : 
      [
        'http://localhost:5173',
        'http://localhost:5174', 
        'http://localhost:5175',
        'http://**************:5173',
        'http://127.0.0.1:5173'
      ],
    credentials: true,
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization', 'Accept', 'X-Requested-With'],
    exposeHeaders: ['Content-Length', 'Content-Type', 'Content-Disposition']
  },

  // 压缩配置
  compression: {
    threshold: parseInt(process.env.COMPRESSION_THRESHOLD) || 2048,
    level: parseInt(process.env.COMPRESSION_LEVEL) || 6
  },

  // 请求体配置
  bodyParser: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 500 * 1024 * 1024, // 500MB
    maxFieldSize: parseInt(process.env.MAX_FIELD_SIZE) || 20 * 1024 * 1024, // 20MB
    maxFields: parseInt(process.env.MAX_FIELDS) || 1000
  }
};

/**
 * JWT配置
 */
const jwt = {
  secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
  expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  algorithm: process.env.JWT_ALGORITHM || 'HS256',
  issuer: process.env.JWT_ISSUER || 'myblog-app',
  audience: process.env.JWT_AUDIENCE || 'myblog-users'
};

/**
 * 文件上传配置
 */
const upload = {
  // 上传目录配置
  directories: {
    base: path.join(__dirname, '..', 'uploads'),
    images: path.join(__dirname, '..', 'uploads', 'images'),
    documents: path.join(__dirname, '..', 'uploads', 'documents'),
    videos: path.join(__dirname, '..', 'uploads', 'videos'),
    archives: path.join(__dirname, '..', 'uploads', 'archives'),
    avatars: path.join(__dirname, '..', 'uploads', 'avatars'),
    articles: path.join(__dirname, '..', 'uploads', 'articles'),
    chatImages: path.join(__dirname, '..', 'uploads', 'chat_images'),
    media: path.join(__dirname, '..', 'public', 'media')
  },

  // 文件类型限制
  allowedTypes: {
    images: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'],
    documents: ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf'],
    videos: ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'],
    archives: ['.zip', '.rar', '.7z', '.tar', '.gz'],
    audio: ['.mp3', '.wav', '.flac', '.aac', '.ogg']
  },

  // 文件大小限制 (字节)
  maxSizes: {
    image: parseInt(process.env.MAX_IMAGE_SIZE) || 10 * 1024 * 1024,      // 10MB
    document: parseInt(process.env.MAX_DOCUMENT_SIZE) || 50 * 1024 * 1024, // 50MB
    video: parseInt(process.env.MAX_VIDEO_SIZE) || 500 * 1024 * 1024,     // 500MB
    archive: parseInt(process.env.MAX_ARCHIVE_SIZE) || 100 * 1024 * 1024,  // 100MB
    audio: parseInt(process.env.MAX_AUDIO_SIZE) || 50 * 1024 * 1024       // 50MB
  },

  // 分块上传配置
  chunked: {
    enabled: process.env.CHUNKED_UPLOAD !== 'false',
    chunkSize: parseInt(process.env.CHUNK_SIZE) || 1024 * 1024, // 1MB
    maxChunks: parseInt(process.env.MAX_CHUNKS) || 1000,
    tempDir: path.join(__dirname, '..', 'temp', 'chunks')
  }
};

/**
 * Redis配置
 */
const redis = {
  enabled: process.env.REDIS_ENABLED === 'true',
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT) || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB) || 0,
  keyPrefix: process.env.REDIS_KEY_PREFIX || 'myblog:',

  // 连接配置
  connectTimeout: 10000,
  lazyConnect: true,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,

  // 重试策略
  retryStrategy: (times) => {
    const delay = Math.min(times * 50, 2000);
    return delay;
  }
};

/**
 * 日志配置
 */
const logging = {
  level: process.env.LOG_LEVEL || 'info',
  format: process.env.LOG_FORMAT || 'combined',
  
  // 文件日志配置
  file: {
    enabled: process.env.FILE_LOGGING !== 'false',
    directory: path.join(__dirname, '..', 'logs'),
    maxSize: process.env.LOG_MAX_SIZE || '10m',
    maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5,
    datePattern: process.env.LOG_DATE_PATTERN || 'YYYY-MM-DD'
  },

  // 控制台日志配置
  console: {
    enabled: process.env.CONSOLE_LOGGING !== 'false',
    colorize: process.env.NODE_ENV !== 'production'
  }
};

/**
 * 安全配置
 */
const security = {
  // 限流配置
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW) || 15 * 60 * 1000, // 15分钟
    max: parseInt(process.env.RATE_LIMIT_MAX) || 100, // 每个IP最多100个请求
    
    // 特殊端点限制
    endpoints: {
      '/api/auth/login': {
        windowMs: 15 * 60 * 1000, // 15分钟
        max: 5 // 最多5次登录尝试
      },
      '/api/upload': {
        windowMs: 60 * 1000, // 1分钟
        max: 10 // 最多10次上传
      }
    }
  },

  // 密码策略
  password: {
    minLength: parseInt(process.env.PASSWORD_MIN_LENGTH) || 6,
    requireUppercase: process.env.PASSWORD_REQUIRE_UPPERCASE === 'true',
    requireLowercase: process.env.PASSWORD_REQUIRE_LOWERCASE === 'true',
    requireNumbers: process.env.PASSWORD_REQUIRE_NUMBERS === 'true',
    requireSymbols: process.env.PASSWORD_REQUIRE_SYMBOLS === 'true'
  },

  // 会话配置
  session: {
    maxAge: parseInt(process.env.SESSION_MAX_AGE) || 24 * 60 * 60 * 1000, // 24小时
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    sameSite: 'lax'
  }
};

/**
 * WebRTC配置
 */
const webrtc = {
  enabled: process.env.WEBRTC_ENABLED !== 'false',
  iceServers: process.env.ICE_SERVERS ? 
    JSON.parse(process.env.ICE_SERVERS) : 
    [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' }
    ],
  
  // 房间配置
  room: {
    maxParticipants: parseInt(process.env.MAX_ROOM_PARTICIPANTS) || 10,
    timeout: parseInt(process.env.ROOM_TIMEOUT) || 30 * 60 * 1000 // 30分钟
  }
};

/**
 * AI配置
 */
const ai = {
  enabled: process.env.AI_ENABLED !== 'false',
  
  // Ollama配置
  ollama: {
    baseUrl: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
    timeout: parseInt(process.env.OLLAMA_TIMEOUT) || 30000,
    defaultModel: process.env.OLLAMA_DEFAULT_MODEL || 'qwen2.5:7b'
  },

  // 聊天配置
  chat: {
    maxHistory: parseInt(process.env.AI_MAX_HISTORY) || 50,
    maxTokens: parseInt(process.env.AI_MAX_TOKENS) || 2000,
    temperature: parseFloat(process.env.AI_TEMPERATURE) || 0.7
  }
};

/**
 * 验证配置
 */
function validateConfig() {
  const errors = [];

  // 验证JWT密钥
  if (jwt.secret === 'your-super-secret-jwt-key-change-in-production' && server.env === 'production') {
    errors.push('生产环境必须设置JWT_SECRET环境变量');
  }

  // 验证端口
  if (server.port < 1 || server.port > 65535) {
    errors.push('端口号必须在1-65535之间');
  }

  if (errors.length > 0) {
    logger.error('配置验证失败', { errors });
    throw new Error(`配置验证失败: ${errors.join(', ')}`);
  }

  logger.info('应用配置验证通过', {
    environment: server.env,
    port: server.port,
    jwtConfigured: jwt.secret !== 'your-super-secret-jwt-key-change-in-production'
  });
}

/**
 * 获取完整配置
 */
function getConfig() {
  validateConfig();
  
  return {
    server,
    jwt,
    upload,
    redis,
    logging,
    security,
    webrtc,
    ai
  };
}

module.exports = {
  getConfig,
  server,
  jwt,
  upload,
  redis,
  logging,
  security,
  webrtc,
  ai,
  validateConfig
};
