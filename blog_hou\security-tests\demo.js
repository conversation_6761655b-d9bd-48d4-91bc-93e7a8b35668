#!/usr/bin/env node

/**
 * 安全测试演示脚本
 * 展示如何使用安全测试工具
 */

const SecurityTester = require('./security-tester');
const APISecurityTester = require('./api-security-tester');

async function runDemo() {
  console.log('🎯 安全测试工具演示');
  console.log('='.repeat(50));
  console.log('⚠️  这是一个演示脚本，展示如何测试您自己的系统');
  console.log('');

  const baseUrl = process.argv[2] || 'http://localhost:3000';
  console.log(`🔍 测试目标: ${baseUrl}`);
  console.log('');

  try {
    // 1. 快速API安全扫描
    console.log('📡 开始API安全扫描...');
    const apiTester = new APISecurityTester(baseUrl);
    
    // 只运行几个关键测试
    await apiTester.testEndpointEnumeration();
    await apiTester.testAuthentication();
    await apiTester.testRateLimit();
    
    console.log(`✅ API扫描完成，发现 ${apiTester.vulnerabilities.length} 个潜在问题`);
    
    if (apiTester.vulnerabilities.length > 0) {
      console.log('\n发现的问题:');
      apiTester.vulnerabilities.forEach((vuln, index) => {
        console.log(`${index + 1}. ${vuln.type} [${vuln.severity}]`);
        console.log(`   ${vuln.description}`);
      });
    }

    // 2. 基础安全测试
    console.log('\n🛡️  开始基础安全测试...');
    
    // 测试常见端点是否存在
    const commonTests = [
      { endpoint: '/admin', description: '管理员面板' },
      { endpoint: '/api', description: 'API接口' },
      { endpoint: '/health', description: '健康检查' },
      { endpoint: '/.env', description: '环境配置文件' }
    ];

    for (const test of commonTests) {
      try {
        const response = await apiTester.request(test.endpoint);
        if (response.statusCode === 200) {
          console.log(`⚠️  发现可访问端点: ${test.endpoint} (${test.description})`);
        } else {
          console.log(`✅ ${test.endpoint} 受保护`);
        }
      } catch (error) {
        console.log(`✅ ${test.endpoint} 不可访问`);
      }
    }

    // 3. 简单的性能测试
    console.log('\n⚡ 开始简单性能测试...');
    
    const performanceTests = [
      { endpoint: '/', name: '首页' },
      { endpoint: '/articles', name: '文章列表' }
    ];

    for (const test of performanceTests) {
      try {
        const startTime = Date.now();
        const response = await apiTester.request(test.endpoint);
        const responseTime = Date.now() - startTime;
        
        let status = '🟢';
        if (responseTime > 2000) status = '🔴';
        else if (responseTime > 1000) status = '🟡';
        
        console.log(`${status} ${test.name}: ${responseTime}ms (状态: ${response.statusCode})`);
      } catch (error) {
        console.log(`🔴 ${test.name}: 请求失败`);
      }
    }

    // 4. 安全建议
    console.log('\n💡 安全建议:');
    console.log('1. 确保所有敏感端点都需要认证');
    console.log('2. 实施速率限制防止暴力攻击');
    console.log('3. 使用HTTPS加密传输');
    console.log('4. 定期更新依赖包');
    console.log('5. 实施安全监控和日志记录');

    console.log('\n🎉 演示完成！');
    console.log('\n要运行完整测试，请使用:');
    console.log(`node run-tests.js --url ${baseUrl}`);

  } catch (error) {
    console.error('❌ 演示过程中发生错误:', error.message);
    console.log('\n可能的原因:');
    console.log('1. 目标服务器未运行');
    console.log('2. 网络连接问题');
    console.log('3. 防火墙阻止连接');
  }
}

// 运行演示
if (require.main === module) {
  runDemo().catch(console.error);
}

module.exports = { runDemo };
