// WebRTC 管理器
const { setupWebRTCServer } = require('./webrtc-server');
const logger = require("../plugin/logger");

class WebRTCManager {
  constructor() {
    this.server = null;
    this.io = null;
    this.isStarted = false;
    this.stats = {
      totalCalls: 0,
      activeCalls: 0,
      startTime: null,
      errors: 0
    };
  }

  // 启动WebRTC服务器
  start(httpServer) {
    try {
      if (this.isStarted) {
        logger.warn('WebRTC服务器已经启动');
        return {
          success: true,
          message: 'WebRTC服务器已经在运行',
          stats: this.getStatus()
        };
      }

      this.server = httpServer;
      this.io = setupWebRTCServer(httpServer);
      this.isStarted = true;
      this.stats.startTime = Date.now();

      logger.info('✅ WebRTC服务器启动成功');

      return {
        success: true,
        message: 'WebRTC服务器启动成功',
        stats: this.getStatus()
      };

    } catch (error) {
      this.stats.errors++;
      logger.error('WebRTC服务器启动失败', { error: error.message });
      
      return {
        success: false,
        message: 'WebRTC服务器启动失败',
        error: error.message
      };
    }
  }

  // 停止WebRTC服务器
  stop() {
    try {
      if (!this.isStarted) {
        logger.warn('WebRTC服务器未启动');
        return {
          success: true,
          message: 'WebRTC服务器未启动'
        };
      }

      if (this.io) {
        // 结束所有活跃通话
        const activeCalls = this.io.getActiveCalls();
        activeCalls.forEach(call => {
          this.io.endCall(call.callId);
        });

        // 关闭所有连接
        this.io.close();
        this.io = null;
      }

      this.server = null;
      this.isStarted = false;

      logger.info('✅ WebRTC服务器已停止');

      return {
        success: true,
        message: 'WebRTC服务器已停止'
      };

    } catch (error) {
      this.stats.errors++;
      logger.error('WebRTC服务器停止失败', { error: error.message });
      
      return {
        success: false,
        message: 'WebRTC服务器停止失败',
        error: error.message
      };
    }
  }

  // 获取服务器状态
  getStatus() {
    const callStats = this.io ? this.io.getCallStats() : { activeCalls: 0, activeCallsList: [] };
    
    return {
      isStarted: this.isStarted,
      stats: {
        ...this.stats,
        activeCalls: callStats.activeCalls,
        uptime: this.stats.startTime ? Date.now() - this.stats.startTime : 0
      },
      activeCalls: callStats.activeCallsList || [],
      serverInfo: {
        path: "/webrtc/socket.io/",
        cors: {
          origin: "*",
          methods: ["GET", "POST"],
          credentials: true
        }
      }
    };
  }

  // 获取活跃通话列表
  getActiveCalls() {
    if (!this.io) return [];
    return this.io.getActiveCalls();
  }

  // 强制结束通话
  endCall(callId) {
    if (!this.io) return false;
    
    try {
      this.io.endCall(callId);
      logger.info('管理员强制结束通话', { callId });
      return true;
    } catch (error) {
      logger.error('强制结束通话失败', { callId, error: error.message });
      return false;
    }
  }

  // 获取连接统计
  getConnectionStats() {
    if (!this.io) {
      return {
        totalConnections: 0,
        activeConnections: 0
      };
    }

    return {
      totalConnections: this.io.engine.clientsCount || 0,
      activeConnections: this.io.sockets.sockets.size || 0
    };
  }

  // 健康检查
  healthCheck() {
    return {
      status: this.isStarted ? 'healthy' : 'stopped',
      isStarted: this.isStarted,
      uptime: this.stats.startTime ? Date.now() - this.stats.startTime : 0,
      errors: this.stats.errors,
      timestamp: new Date().toISOString()
    };
  }
}

// 创建单例实例
const webrtcManager = new WebRTCManager();

module.exports = webrtcManager;
