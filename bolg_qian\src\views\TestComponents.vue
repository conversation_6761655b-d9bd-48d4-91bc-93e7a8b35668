<template>
  <div class="test-components">
    <h1>组件测试页面</h1>
    
    <div class="test-section">
      <h2>收藏组件测试</h2>
      <div class="component-test">
        <ArticleFavorite 
          :article-id="39"
          :show-text="true"
          :show-count="true"
          size="default"
          @favorite-changed="handleFavoriteChanged"
        />
      </div>
    </div>

    <div class="test-section">
      <h2>点赞组件测试</h2>
      <div class="component-test">
        <ArticleLike 
          :article-id="39"
          :show-users-list="false"
          @like-changed="handleLikeChanged"
        />
      </div>
    </div>

    <div class="test-section">
      <h2>测试按钮</h2>
      <div class="component-test">
        <el-button type="primary" @click="testClick">
          测试点击事件
        </el-button>
      </div>
    </div>

    <div class="test-section">
      <h2>事件日志</h2>
      <div class="log-container">
        <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import ArticleFavorite from '../components/ArticleFavorite.vue'
import ArticleLike from '../components/ArticleLike.vue'

const eventLogs = ref([])

const addLog = (message) => {
  const timestamp = new Date().toLocaleTimeString()
  eventLogs.value.unshift(`[${timestamp}] ${message}`)
  if (eventLogs.value.length > 10) {
    eventLogs.value.pop()
  }
}

const handleFavoriteChanged = (data) => {
  console.log('收藏状态变化:', data)
  addLog(`收藏状态变化: ${JSON.stringify(data)}`)
  ElMessage.success('收藏状态已更新')
}

const handleLikeChanged = (data) => {
  console.log('点赞状态变化:', data)
  addLog(`点赞状态变化: ${JSON.stringify(data)}`)
  ElMessage.success('点赞状态已更新')
}

const testClick = () => {
  addLog('测试按钮被点击')
  ElMessage.info('测试按钮点击成功')
}
</script>

<style scoped>
.test-components {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 32px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.test-section h2 {
  margin: 0 0 16px 0;
  color: #303133;
}

.component-test {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
}

.log-item {
  font-family: monospace;
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
  word-break: break-all;
}

.log-item:last-child {
  margin-bottom: 0;
}
</style>
