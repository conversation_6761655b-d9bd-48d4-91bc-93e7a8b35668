# Ollama 本地大模型配置指南

本项目使用 Ollama 作为本地大模型服务，为英文翻译练习提供 AI 支持。

## 快速开始

### 1. 安装 Ollama

**Windows/Mac:**
- 访问 [https://ollama.ai](https://ollama.ai) 下载安装包
- 运行安装程序

**Linux:**
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

### 2. 启动 Ollama 服务

```bash
ollama serve
```

服务将在 `http://localhost:11434` 启动。

### 3. 下载推荐模型

```bash
# 轻量级模型（推荐，适合 4-8GB 内存）
ollama pull qwen2.5:1.5b

# 或者其他模型
ollama pull llama3.2:3b
ollama pull gemma2:2b
```

### 4. 测试连接

```bash
curl http://localhost:11434/api/chat -d '{
  "model": "qwen2.5:1.5b",
  "messages": [{"role": "user", "content": "你好"}],
  "stream": false
}'
```

## 模型推荐

根据硬件配置选择合适的模型：

| 内存 | 推荐模型 | 说明 |
|------|----------|------|
| 4-8GB | qwen2.5:1.5b, gemma2:2b | 轻量级，响应快 |
| 8-16GB | llama3.2:3b, qwen2.5:3b | 平衡性能和质量 |
| 16GB+ | llama3.1:7b, qwen2.5:7b | 高质量输出 |

## 配置修改

如需修改模型或配置，编辑 `src/components/EnglishTranslator.vue` 文件中的 `OLLAMA_CONFIG`：

```javascript
const OLLAMA_CONFIG = {
  name: 'Ollama',
  apiBase: 'http://localhost:11434/api/chat',
  modelName: 'qwen2.5:1.5b', // 修改为你的模型名
  timeout: 30000,
  maxRetries: 3
};
```

## 常见问题

### 1. 连接超时
- 确保 Ollama 服务正在运行：`ollama serve`
- 检查端口 11434 是否被占用
- 检查防火墙设置

### 2. 模型未找到
```bash
# 查看已安装的模型
ollama list

# 下载需要的模型
ollama pull qwen2.5:1.5b
```

### 3. 响应慢
- 尝试使用更小的模型（如 1.5b 参数）
- 检查系统资源使用情况
- 考虑使用 GPU 加速（如果支持）

### 4. 翻译质量不佳
- 尝试不同的模型
- 检查模型是否支持中英文
- 可以调整提示词模板

## 性能优化

### 硬件优化
- 使用 GPU 加速（NVIDIA GPU + CUDA）
- 增加系统内存
- 使用 SSD 存储模型

### 软件优化
```bash
# 启用 GPU 加速（如果有 NVIDIA GPU）
ollama serve --gpu

# 设置并发数
ollama serve --parallel 2
```

## 模型管理

```bash
# 查看所有模型
ollama list

# 删除不需要的模型
ollama rm model-name

# 更新模型
ollama pull qwen2.5:1.5b
```

## 故障排除

### 检查服务状态
```bash
# 检查 Ollama 是否运行
curl http://localhost:11434/api/tags

# 测试模型
ollama run qwen2.5:1.5b "你好"
```

### 查看日志
- Windows: 查看 Ollama 应用程序日志
- Linux/Mac: 查看终端输出

### 重启服务
```bash
# 停止服务（Ctrl+C）
# 重新启动
ollama serve
```

## 支持的功能

本项目的英文翻译练习功能包括：

1. **自动生成中文句子** - 使用 AI 生成适合翻译练习的中文句子
2. **智能翻译** - 将中文句子翻译成英文作为参考答案
3. **填空练习** - 隐藏部分英文单词，让用户填写
4. **实时检查** - 输入时实时检查答案正确性
5. **提示功能** - 提供渐进式提示帮助学习
6. **连接状态监控** - 实时显示 Ollama 连接状态

## 许可证

本项目遵循 MIT 许可证。使用的 Ollama 和相关模型请遵循各自的许可证要求。
