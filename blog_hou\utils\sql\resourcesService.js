const db = require("../db");

// 通过user_id 获取files的字段is_share为1的资源
async function getShallFilesByUserId(user_id) {
  const sql = `SELECT * FROM files WHERE user_id = ?`;
  const result = await db.query(sql, [user_id]);
  return result;
}

// 取消分享or分享
async function cancelShareOrShare(user_id, file_id, is_share) {
  const sql = `UPDATE files SET is_share = ? WHERE user_id = ? AND id = ?`;
  const result = await db.query(sql, [is_share, user_id, file_id]);
  return result;
}
// 删除资源 is_deleted
async function deleteFile(user_id, file_id) {
  const sql = `UPDATE files SET is_deleted = 1 WHERE user_id = ? AND id = ?`;
  const result = await db.query(sql, [user_id, file_id]);
  return result;
}

// 永久删除资源
async function permanentlyDeleteFile(user_id, file_id) {
  const sql = `DELETE FROM files WHERE user_id = ? AND id = ?`;
  const result = await db.query(sql, [user_id, file_id]);
  return result;
}

module.exports = {
  getShallFilesByUserId,
  cancelShareOrShare,
  deleteFile,
  permanentlyDeleteFile,
};
