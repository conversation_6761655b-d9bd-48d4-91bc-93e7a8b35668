// 密码管理API服务
import request from './index'

/**
 * 密码管理API - 基于数据库表结构和后端接口设计
 */
export const passwordManagementApi = {
  // 获取所有密码配置列表 - GET /password/list
  // 返回所有配置（包括禁用的），用于管理界面
  getPasswordList() {
    return request.get('/password/list')
  },

  // 调试接口：获取原始数据库数据
  getPasswordListDebug() {
    return request.get('/password/debug-raw')
  },

  // 获取密码使用统计 - GET /password/stats
  // 返回统计信息和配置列表
  getPasswordStats() {
    return request.get('/password/stats')
  },

  // 创建密码配置 - POST /password/create
  // 基于数据库唯一约束，password_type 必须唯一
  createPassword(data) {
    return request.post('/password/create', {
      passwordType: data.passwordType,
      password: data.password,
      securityQuestion: data.securityQuestion,
      securityAnswer: data.securityAnswer,
      description: data.description
    })
  },

  // 更新密码配置 - PUT /password/update
  // 更新现有配置的密码、密保等信息
  updatePassword(data) {
    return request.put('/password/update', {
      passwordType: data.passwordType,
      password: data.password,
      securityQuestion: data.securityQuestion,
      securityAnswer: data.securityAnswer,
      description: data.description
    })
  },

  // 删除密码配置 - DELETE /password/delete/:passwordType
  // 硬删除，基于数据库约束
  deletePassword(passwordType) {
    return request.delete(`/password/delete/${passwordType}`)
  },

  // 切换密码配置状态 - PUT /password/toggle/:id
  // 切换 is_active 字段值
  togglePasswordStatus(id) {
    return request.put(`/password/toggle/${id}`)
  },

  // 获取密码配置详情 - GET /password/detail/:id
  // 返回配置详情（不包含敏感信息）
  getPasswordDetail(id) {
    return request.get(`/password/detail/${id}`)
  },

  // 获取密保问题 - GET /password/security-question/:passwordType
  // 公开接口，用于密码重置
  getSecurityQuestion(passwordType) {
    return request.get(`/password/security-question/${passwordType}`)
  },

  // 通过密保重置密码 - POST /password/reset-by-security
  // 验证密保答案后重置密码
  resetPasswordBySecurityAnswer(data) {
    return request.post('/password/reset-by-security', {
      passwordType: data.passwordType,
      securityAnswer: data.securityAnswer,
      newPassword: data.newPassword
    })
  },

  // 验证密码 - POST /password/verify
  // 公开接口，验证密码并更新使用统计
  verifyPassword(data) {
    return request.post('/password/verify', {
      passwordType: data.passwordType,
      password: data.password
    })
  },

  // 批量删除密码配置（前端循环实现）
  async batchDeletePasswords(passwordTypes) {
    const results = []
    for (const type of passwordTypes) {
      try {
        const result = await this.deletePassword(type)
        results.push({
          type,
          success: true,
          result,
          message: `删除 ${type} 成功`
        })
      } catch (error) {
        results.push({
          type,
          success: false,
          error,
          message: `删除 ${type} 失败: ${error.message}`
        })
      }
    }
    return results
  },

  // 批量切换状态（前端循环实现）
  async batchToggleStatus(ids) {
    const results = []
    for (const id of ids) {
      try {
        const result = await this.togglePasswordStatus(id)
        results.push({
          id,
          success: true,
          result,
          message: `切换状态成功`
        })
      } catch (error) {
        results.push({
          id,
          success: false,
          error,
          message: `切换状态失败: ${error.message}`
        })
      }
    }
    return results
  }
}

/**
 * 密码配置工具函数
 */
export const passwordUtils = {
  // 获取密码类型显示名称
  getPasswordTypeName(type) {
    const typeMap = {
      photo_wall: '照片墙密码',
      media: '媒体访问密码',
      admin: '管理员密码',
      system: '系统密码'
    }
    return typeMap[type] || type
  },

  // 获取密码类型标签样式
  getPasswordTypeTag(type) {
    const tagMap = {
      photo_wall: 'success',
      media: 'warning',
      admin: 'danger',
      system: 'info'
    }
    return tagMap[type] || 'info'
  },

  // 获取密码类型图标
  getPasswordTypeIcon(type) {
    const iconMap = {
      photo_wall: 'Picture',
      media: 'VideoPlay',
      admin: 'User',
      system: 'Setting'
    }
    return iconMap[type] || 'Lock'
  },

  // 格式化日期时间
  formatDateTime(dateStr) {
    if (!dateStr) return '-'
    return new Date(dateStr).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  },

  // 格式化相对时间
  formatRelativeTime(dateStr) {
    if (!dateStr) return '从未使用'
    
    const now = new Date()
    const date = new Date(dateStr)
    const diff = now - date
    
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (minutes < 1) return '刚刚'
    if (minutes < 60) return `${minutes}分钟前`
    if (hours < 24) return `${hours}小时前`
    if (days < 7) return `${days}天前`
    
    return this.formatDateTime(dateStr)
  },

  // 验证密码强度
  validatePasswordStrength(password) {
    if (!password) return { valid: false, message: '密码不能为空' }
    if (password.length < 6) return { valid: false, message: '密码长度至少为6位' }
    if (password.length > 50) return { valid: false, message: '密码长度不能超过50位' }
    
    // 检查是否包含数字和字母
    const hasNumber = /\d/.test(password)
    const hasLetter = /[a-zA-Z]/.test(password)
    
    if (!hasNumber || !hasLetter) {
      return { valid: false, message: '密码应包含数字和字母' }
    }
    
    return { valid: true, message: '密码强度良好' }
  },

  // 生成密码强度等级
  getPasswordStrengthLevel(password) {
    if (!password) return 0
    
    let score = 0
    
    // 长度评分
    if (password.length >= 8) score += 1
    if (password.length >= 12) score += 1
    
    // 字符类型评分
    if (/[a-z]/.test(password)) score += 1
    if (/[A-Z]/.test(password)) score += 1
    if (/\d/.test(password)) score += 1
    if (/[^a-zA-Z\d]/.test(password)) score += 1
    
    return Math.min(score, 5)
  },

  // 获取状态显示文本
  getStatusText(isActive) {
    return isActive ? '启用' : '禁用'
  },

  // 获取状态颜色
  getStatusColor(isActive) {
    return isActive ? '#67C23A' : '#F56C6C'
  },

  // 计算使用频率
  calculateUsageFrequency(useCount, createdAt) {
    if (!createdAt || useCount === 0) return '未使用'
    
    const now = new Date()
    const created = new Date(createdAt)
    const daysDiff = Math.floor((now - created) / (1000 * 60 * 60 * 24))
    
    if (daysDiff === 0) return '今日创建'
    
    const frequency = useCount / daysDiff
    
    if (frequency >= 1) return '高频使用'
    if (frequency >= 0.5) return '中频使用'
    if (frequency >= 0.1) return '低频使用'
    
    return '很少使用'
  },

  // 生成安全建议
  generateSecurityAdvice(config) {
    const advice = []
    
    if (config.use_count > 100) {
      advice.push('使用频率较高，建议定期更换密码')
    }
    
    if (config.last_used_at) {
      const lastUsed = new Date(config.last_used_at)
      const daysSinceLastUse = Math.floor((new Date() - lastUsed) / (1000 * 60 * 60 * 24))
      
      if (daysSinceLastUse > 90) {
        advice.push('长时间未使用，建议检查是否还需要此配置')
      }
    }
    
    const created = new Date(config.created_at)
    const daysSinceCreated = Math.floor((new Date() - created) / (1000 * 60 * 60 * 24))
    
    if (daysSinceCreated > 180) {
      advice.push('配置创建时间较久，建议更新密码和密保问题')
    }
    
    return advice.length > 0 ? advice : ['配置安全状态良好']
  }
}

// 默认导出
export default {
  passwordManagementApi,
  passwordUtils
}
