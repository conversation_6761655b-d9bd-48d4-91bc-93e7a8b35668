const db = require("../utils/db");
const fs = require("fs");
const path = require("path");

async function syncVideos() {
  try {
    console.log("🎬 同步视频文件到数据库...\n");

    const videoDir = path.join(__dirname, "../uploads/videos");
    const isVideoFile = (filename) => {
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'];
      return videoExtensions.some(ext => filename.toLowerCase().endsWith(ext));
    };

    // 确保目录存在
    if (!fs.existsSync(videoDir)) {
      fs.mkdirSync(videoDir, { recursive: true });
      console.log("✅ 创建 videos 目录");
    }

    // 读取视频文件
    const files = fs.readdirSync(videoDir).filter(isVideoFile);
    console.log(`📁 找到 ${files.length} 个视频文件`);

    if (files.length === 0) {
      console.log("⚠️  没有找到视频文件");
      return;
    }

    let syncCount = 0;
    let updateCount = 0;

    for (const file of files) {
      try {
        const filePath = path.join(videoDir, file);
        const stats = fs.statSync(filePath);
        
        // 检查是否已存在
        const existing = await db.query(
          "SELECT id FROM video_management WHERE file_name = ?",
          [file]
        );
        
        if (existing.length === 0) {
          // 新增记录
          await db.query(`
            INSERT INTO video_management (
              file_name, title, file_size, upload_time, status
            ) VALUES (?, ?, ?, ?, ?)
          `, [
            file,
            file.replace(/\.[^/.]+$/, ""), // 去掉扩展名作为标题
            stats.size,
            stats.mtime.getTime(),
            'online'
          ]);
          console.log(`  ✅ 新增: ${file}`);
          syncCount++;
        } else {
          // 更新文件信息
          await db.query(`
            UPDATE video_management 
            SET file_size = ?, upload_time = ?
            WHERE file_name = ?
          `, [
            stats.size,
            stats.mtime.getTime(),
            file
          ]);
          console.log(`  🔄 更新: ${file}`);
          updateCount++;
        }
      } catch (error) {
        console.log(`  ❌ 处理失败: ${file} - ${error.message}`);
      }
    }

    console.log(`\n📊 同步完成:`);
    console.log(`  新增: ${syncCount} 个文件`);
    console.log(`  更新: ${updateCount} 个文件`);
    console.log(`  总计: ${files.length} 个文件`);

    // 显示当前数据库中的视频记录
    const allVideos = await db.query("SELECT file_name, title, status FROM video_management ORDER BY upload_time DESC");
    console.log(`\n📋 数据库中的视频记录 (${allVideos.length} 个):`);
    allVideos.forEach((video, index) => {
      const statusIcon = video.status === 'online' ? '🟢' : '🔴';
      console.log(`  ${index + 1}. ${statusIcon} ${video.title} (${video.file_name})`);
    });

  } catch (error) {
    console.error("❌ 同步失败:", error);
  } finally {
    process.exit(0);
  }
}

// 运行同步
syncVideos();
