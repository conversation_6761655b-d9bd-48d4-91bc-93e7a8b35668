<template>
  <div class="resource-container">
    <el-card class="resource-card">
      <!-- 头部区域 -->
      <div class="resource-header">
        <div class="header-left">
          <h3 class="title">
            <el-icon><FolderOpened /></el-icon>
            我的资源
          </h3>
          <el-tag v-if="resourceList.length > 0" type="info" size="small">
            共 {{ resourceList.length }} 个文件
          </el-tag>
        </div>
        <div class="header-right">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索文件名..."
            size="small"
            style="width: 200px; margin-right: 12px;"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select
            v-model="filterStatus"
            placeholder="筛选状态"
            size="small"
            style="width: 120px; margin-right: 12px;"
            @change="handleFilter"
          >
            <el-option label="全部" value="all" />
            <el-option label="已分享" value="shared" />
            <el-option label="未分享" value="unshared" />
          </el-select>
          <el-button type="primary" size="small" @click="fetchList" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 批量操作区域 -->
      <div v-if="selectedRows.length > 0" class="batch-actions">
        <el-alert
          :title="`已选择 ${selectedRows.length} 个文件`"
          type="info"
          show-icon
          :closable="false"
        >
          <template #default>
            <div class="batch-buttons">
              <el-button size="small" @click="batchToggleShare(true)">
                <el-icon><Share /></el-icon>
                批量分享
              </el-button>
              <el-button size="small" @click="batchToggleShare(false)">
                <el-icon><Close /></el-icon>
                批量取消分享
              </el-button>
              <el-button size="small" type="danger" @click="batchDelete">
                <el-icon><Delete /></el-icon>
                批量删除
              </el-button>
              <el-button size="small" @click="clearSelection">清空选择</el-button>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 表格区域 -->
      <el-table
        :data="pagedList"
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        :empty-text="getEmptyText()"
        class="resource-table"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="file_name" label="文件名" min-width="200">
          <template #default="{ row }">
            <div class="file-info">
              <el-icon class="file-icon">
                <Document v-if="getFileType(row.file_name) === 'document'" />
                <Picture v-else-if="getFileType(row.file_name) === 'image'" />
                <VideoPlay v-else-if="getFileType(row.file_name) === 'video'" />
                <Headphone v-else-if="getFileType(row.file_name) === 'audio'" />
                <Files v-else />
              </el-icon>
              <div class="file-details">
                <div class="file-name" :title="row.file_name">{{ row.file_name }}</div>
                <div class="file-meta">
                  {{ formatFileSize(row.file_size) }} •
                  {{ formatTime(row.updated_at) }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="is_share" label="分享状态" width="120">
          <template #default="{ row }">
            <el-tag
              :type="row.is_share ? 'success' : 'info'"
              size="small"
              :icon="row.is_share ? 'Check' : 'Close'"
            >
              {{ row.is_share ? '已分享' : '未分享' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                size="small"
                @click="toggleShare(row)"
                :type="row.is_share ? 'warning' : 'success'"
                :loading="row.sharing"
                plain
              >
                <el-icon><Share v-if="!row.is_share" /><Close v-else /></el-icon>
                {{ row.is_share ? '取消分享' : '分享' }}
              </el-button>
              <el-dropdown @command="(command) => handleAction(command, row)">
                <el-button size="small" type="info" plain>
                  更多
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="delete" :icon="Delete">
                      删除
                    </el-dropdown-item>
                    <el-dropdown-item command="permanentDelete" :icon="Delete" divided>
                      <span style="color: #f56c6c;">永久删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <div class="pagination-info">
          显示第 {{ (currentPage - 1) * pageSize + 1 }} -
          {{ Math.min(currentPage * pageSize, filteredList.length) }} 条，
          共 {{ filteredList.length }} 条记录
        </div>
        <el-pagination
          background
          layout="prev, pager, next, jumper, sizes"
          :total="filteredList.length"
          :page-size="pageSize"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import {
  GetResourceListByUserApi,
  CancelShareOrShareApi,
  DeleteResourceApi,
  PermanentlyDeleteResourceApi,
} from "../utils/api";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  FolderOpened,
  Search,
  Refresh,
  Share,
  Close,
  Delete,
  Document,
  Picture,
  VideoPlay,
  Files,
  ArrowDown,
  Check
} from '@element-plus/icons-vue';

// 接口定义
interface ResourceItem {
  id?: number;
  file_id?: number;
  file_name: string;
  file_size: number;
  is_share: number;
  updated_at: string;
  sharing?: boolean;
}

const user_id = Number(localStorage.getItem("id"));
const resourceList = ref<ResourceItem[]>([]);
const loading = ref(false);

// 搜索和筛选
const searchKeyword = ref("");
const filterStatus = ref("all");

// 批量选择
const selectedRows = ref<ResourceItem[]>([]);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);

// 筛选后的列表
const filteredList = computed(() => {
  let list = resourceList.value;

  // 搜索筛选
  if (searchKeyword.value) {
    list = list.filter(item =>
      item.file_name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    );
  }

  // 状态筛选
  if (filterStatus.value === "shared") {
    list = list.filter(item => item.is_share === 1);
  } else if (filterStatus.value === "unshared") {
    list = list.filter(item => item.is_share === 0);
  }

  return list;
});

// 分页后的列表
const pagedList = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  return filteredList.value.slice(start, start + pageSize.value);
});

// 分页处理
const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
};

// 搜索和筛选处理
const handleSearch = () => {
  currentPage.value = 1;
};

const handleFilter = () => {
  currentPage.value = 1;
};

// 批量选择处理
const handleSelectionChange = (selection: ResourceItem[]) => {
  selectedRows.value = selection;
};

const clearSelection = () => {
  selectedRows.value = [];
};

// 工具函数
const getFileType = (filename: string): string => {
  const ext = filename.split('.').pop()?.toLowerCase() || '';
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) return 'image';
  if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(ext)) return 'video';
  if (['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(ext)) return 'audio';
  if (['doc', 'docx', 'pdf', 'txt', 'rtf'].includes(ext)) return 'document';
  return 'file';
};

const formatFileSize = (size: number): string => {
  if (!size) return '0 B';
  const units = ['B', 'KB', 'MB', 'GB'];
  let index = 0;
  let fileSize = size;
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  return `${fileSize.toFixed(1)} ${units[index]}`;
};

const formatTime = (time: string): string => {
  const date = new Date(time);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (days === 0) return '今天';
  if (days === 1) return '昨天';
  if (days < 7) return `${days}天前`;
  return date.toLocaleDateString();
};

const getEmptyText = (): string => {
  if (searchKeyword.value || filterStatus.value !== 'all') {
    return '没有找到匹配的文件';
  }
  return '暂无资源文件';
};

// 数据获取
const fetchList = async () => {
  loading.value = true;
  try {
    const res = await GetResourceListByUserApi(user_id);
    // 按 updated_at 倒序排序，最新的在前面
    resourceList.value = (res.data || []).slice().sort((a: ResourceItem, b: ResourceItem) => {
      return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
    });
    currentPage.value = 1; // 每次刷新回到第一页
  } catch (e) {
    ElMessage.error("获取资源失败");
  } finally {
    loading.value = false;
  }
};

// 批量操作
const batchToggleShare = async (isShare: boolean) => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning("请先选择要操作的文件");
    return;
  }

  try {
    const promises = selectedRows.value.map(row =>
      CancelShareOrShareApi(user_id, (row.id || row.file_id)!, isShare ? 1 : 0)
    );
    await Promise.all(promises);
    ElMessage.success(`批量${isShare ? '分享' : '取消分享'}成功`);
    clearSelection();
    fetchList();
  } catch {
    ElMessage.error("批量操作失败");
  }
};

const batchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning("请先选择要删除的文件");
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个文件吗？`,
      "批量删除确认",
      { type: "warning" }
    );

    const promises = selectedRows.value.map(row =>
      DeleteResourceApi(user_id, (row.id || row.file_id)!)
    );
    await Promise.all(promises);
    ElMessage.success("批量删除成功");
    clearSelection();
    fetchList();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error("批量删除失败");
    }
  }
};

// 单个操作
const toggleShare = async (row: ResourceItem) => {
  row.sharing = true;
  try {
    await CancelShareOrShareApi(user_id, (row.id || row.file_id)!, row.is_share ? 0 : 1);
    ElMessage.success(row.is_share ? "已取消分享" : "已分享");
    fetchList();
  } catch {
    ElMessage.error("操作失败");
  } finally {
    row.sharing = false;
  }
};

const handleAction = (command: string, row: ResourceItem) => {
  switch (command) {
    case 'delete':
      deleteResource(row);
      break;
    case 'permanentDelete':
      permanentlyDeleteResource(row);
      break;
  }
};

const deleteResource = (row: ResourceItem) => {
  ElMessageBox.confirm("确定要删除该资源吗？", "提示", {
    type: "warning",
  }).then(async () => {
    try {
      await DeleteResourceApi(user_id, (row.id || row.file_id)!);
      ElMessage.success("删除成功");
      fetchList();
    } catch {
      ElMessage.error("删除失败");
    }
  }).catch(() => {
    // 用户取消操作
  });
};

const permanentlyDeleteResource = (row: ResourceItem) => {
  ElMessageBox.confirm("确定要永久删除该资源吗？此操作不可恢复！", "警告", {
    type: "error",
  }).then(async () => {
    try {
      await PermanentlyDeleteResourceApi(user_id, (row.id || row.file_id)!, row.file_name);
      ElMessage.success("永久删除成功");
      fetchList();
    } catch {
      ElMessage.error("永久删除失败");
    }
  }).catch(() => {
    // 用户取消操作
  });
};

// 初始化
fetchList();
</script>

<style scoped>
.resource-container {
  margin: 0 auto;
  padding: 20px;
}

.resource-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
}

.batch-actions {
  margin-bottom: 16px;
}

.batch-buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.resource-table {
  margin-bottom: 20px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 20px;
  color: #409eff;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.file-meta {
  font-size: 12px;
  color: #909399;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.pagination-info {
  font-size: 14px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .resource-container {
    padding: 12px;
  }

  .resource-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-right {
    flex-direction: column;
    gap: 8px;
  }

  .header-right .el-input,
  .header-right .el-select {
    width: 100% !important;
  }

  .batch-buttons {
    flex-wrap: wrap;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .pagination-container {
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }

  .file-name {
    font-size: 14px;
  }

  .file-meta {
    font-size: 11px;
  }
}

/* 表格行悬停效果 */
.resource-table :deep(.el-table__row:hover) {
  background-color: #f8f9fa;
}

/* 标签样式优化 */
.resource-table :deep(.el-tag) {
  border-radius: 6px;
}

/* 按钮样式优化 */
.action-buttons .el-button {
  border-radius: 6px;
}

.batch-buttons .el-button {
  border-radius: 6px;
}
</style>
