// WebRTC 管理路由
const Router = require("koa-router");
const router = new Router();
const { handleResponse } = require("../../middlewares/responseHandler");
const verifyToken = require("../../middlewares/koaJwtMiddleware");
const webrtcManager = require("../../communication/webrtc-manager");
const logger = require("../../plugin/logger");

// 启动WebRTC服务器（需要登录）
router.post("/start", verifyToken, async (ctx) => {
  try {
    const userId = ctx.state.user?.id || ctx.state.user?.userId;
    const username = ctx.state.user?.username;
    
    logger.info('用户请求启动WebRTC服务器', { userId, username });
    
    const result = webrtcManager.start(ctx.app.server);
    
    if (result.success) {
      return handleResponse(ctx, 200, {
        message: "WebRTC服务器已启动",
        data: {
          ...result.stats,
          requestedBy: { userId, username }
        }
      });
    } else {
      return handleResponse(ctx, 500, { 
        message: result.message,
        error: result.error 
      });
    }
    
  } catch (error) {
    logger.error('启动WebRTC服务器失败', { error: error.message });
    return handleResponse(ctx, 500, { 
      message: "启动WebRTC服务器失败",
      error: error.message 
    });
  }
});

// 停止WebRTC服务器（需要登录）
router.post("/stop", verifyToken, async (ctx) => {
  try {
    const userId = ctx.state.user?.id || ctx.state.user?.userId;
    const username = ctx.state.user?.username;
    
    logger.info('用户请求停止WebRTC服务器', { userId, username });
    
    const result = webrtcManager.stop();
    
    if (result.success) {
      return handleResponse(ctx, 200, {
        message: "WebRTC服务器已停止",
        data: {
          stoppedBy: { userId, username }
        }
      });
    } else {
      return handleResponse(ctx, 500, { 
        message: result.message,
        error: result.error 
      });
    }
    
  } catch (error) {
    logger.error('停止WebRTC服务器失败', { error: error.message });
    return handleResponse(ctx, 500, { 
      message: "停止WebRTC服务器失败",
      error: error.message 
    });
  }
});

// 获取WebRTC服务器状态（需要登录）
router.get("/status", verifyToken, async (ctx) => {
  try {
    const status = webrtcManager.getStatus();
    
    return handleResponse(ctx, 200, {
      message: "获取WebRTC状态成功",
      data: {
        ...status,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('获取WebRTC状态失败', { error: error.message });
    return handleResponse(ctx, 500, { 
      message: "获取WebRTC状态失败",
      error: error.message 
    });
  }
});

// 获取活跃通话列表（需要登录）
router.get("/calls", verifyToken, async (ctx) => {
  try {
    const activeCalls = webrtcManager.getActiveCalls();
    
    return handleResponse(ctx, 200, {
      message: "获取活跃通话成功",
      data: {
        activeCalls,
        totalCount: activeCalls.length,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('获取活跃通话失败', { error: error.message });
    return handleResponse(ctx, 500, { 
      message: "获取活跃通话失败",
      error: error.message 
    });
  }
});

// 强制结束通话（管理员功能）
router.post("/calls/:callId/end", verifyToken, async (ctx) => {
  try {
    const { callId } = ctx.params;
    const userId = ctx.state.user?.id || ctx.state.user?.userId;
    const username = ctx.state.user?.username;
    
    // 这里可以添加管理员权限检查
    // if (!isAdmin(userId)) {
    //   return handleResponse(ctx, 403, { message: "权限不足" });
    // }
    
    const success = webrtcManager.endCall(callId);
    
    if (success) {
      logger.info('管理员强制结束通话', { callId, userId, username });
      return handleResponse(ctx, 200, {
        message: "通话已结束",
        data: {
          callId,
          endedBy: { userId, username }
        }
      });
    } else {
      return handleResponse(ctx, 404, { 
        message: "通话不存在或已结束" 
      });
    }
    
  } catch (error) {
    logger.error('强制结束通话失败', { error: error.message });
    return handleResponse(ctx, 500, { 
      message: "结束通话失败",
      error: error.message 
    });
  }
});

// 获取连接统计（需要登录）
router.get("/stats", verifyToken, async (ctx) => {
  try {
    const status = webrtcManager.getStatus();
    const connectionStats = webrtcManager.getConnectionStats();
    
    return handleResponse(ctx, 200, {
      message: "获取WebRTC统计成功",
      data: {
        serverStatus: status,
        connectionStats,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('获取WebRTC统计失败', { error: error.message });
    return handleResponse(ctx, 500, { 
      message: "获取WebRTC统计失败",
      error: error.message 
    });
  }
});

// 健康检查接口（无需认证）
router.get("/health", async (ctx) => {
  try {
    const health = webrtcManager.healthCheck();
    
    return handleResponse(ctx, 200, {
      message: "WebRTC健康检查",
      data: health
    });
    
  } catch (error) {
    return handleResponse(ctx, 500, { 
      message: "WebRTC健康检查失败",
      error: error.message 
    });
  }
});

module.exports = router;
