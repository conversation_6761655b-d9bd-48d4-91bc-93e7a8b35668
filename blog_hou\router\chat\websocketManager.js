const Router = require("koa-router");
const { handleResponse } = require("../../middlewares/responseHandler");
const verifyToken = require("../../middlewares/koaJwtMiddleware");
const logger = require("../../plugin/logger");

const router = new Router();

// 启动WebSocket服务器
router.post("/start", verifyToken, async (ctx) => {
  try {
    const userId = ctx.state.user?.id || ctx.state.user?.userId;
    const username = ctx.state.user?.username;
    
    logger.info('用户请求启动WebSocket服务器', { userId, username });
    
    // 启动WebSocket服务器
    const wsServer = ctx.webSocketManager.start();
    const status = ctx.webSocketManager.getStatus();
    
    return handleResponse(ctx, 200, {
      message: "WebSocket聊天服务器已启动",
      data: {
        ...status,
        wsUrl: `ws://192.168.31.222:3000`,
        requestedBy: { userId, username }
      }
    });
    
  } catch (error) {
    logger.error('启动WebSocket服务器失败', { error: error.message });
    return handleResponse(ctx, 500, { 
      message: "启动WebSocket服务器失败",
      error: error.message 
    });
  }
});

// 停止WebSocket服务器（仅管理员）
router.post("/stop", verifyToken, async (ctx) => {
  try {
    const userId = ctx.state.user?.id || ctx.state.user?.userId;
    const username = ctx.state.user?.username;
    
    // 检查是否为管理员（可选，根据需求决定）
    // 这里暂时允许任何登录用户停止，实际项目中可能需要管理员权限
    
    logger.info('用户请求停止WebSocket服务器', { userId, username });
    
    ctx.webSocketManager.stop();
    const status = ctx.webSocketManager.getStatus();
    
    return handleResponse(ctx, 200, {
      message: "WebSocket聊天服务器已停止",
      data: {
        ...status,
        stoppedBy: { userId, username }
      }
    });
    
  } catch (error) {
    logger.error('停止WebSocket服务器失败', { error: error.message });
    return handleResponse(ctx, 500, { 
      message: "停止WebSocket服务器失败",
      error: error.message 
    });
  }
});

// 获取WebSocket服务器状态
router.get("/status", verifyToken, async (ctx) => {
  try {
    const status = ctx.webSocketManager.getStatus();
    
    return handleResponse(ctx, 200, {
      message: "获取WebSocket状态成功",
      data: {
        ...status,
        wsUrl: status.isStarted ? `ws://192.168.31.222:3000` : null,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('获取WebSocket状态失败', { error: error.message });
    return handleResponse(ctx, 500, { 
      message: "获取WebSocket状态失败",
      error: error.message 
    });
  }
});

// 获取在线用户列表
router.get("/online-users", verifyToken, async (ctx) => {
  try {
    const status = ctx.webSocketManager.getStatus();
    
    if (!status.isStarted) {
      return handleResponse(ctx, 200, {
        message: "WebSocket服务器未启动",
        data: {
          isStarted: false,
          onlineUsers: []
        }
      });
    }
    
    return handleResponse(ctx, 200, {
      message: "获取在线用户成功",
      data: {
        isStarted: status.isStarted,
        onlineUsers: status.onlineUsers,
        totalCount: status.onlineUsers.length,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('获取在线用户失败', { error: error.message });
    return handleResponse(ctx, 500, { 
      message: "获取在线用户失败",
      error: error.message 
    });
  }
});

// 获取WebSocket统计信息
router.get("/stats", verifyToken, async (ctx) => {
  try {
    const status = ctx.webSocketManager.getStatus();
    
    if (!status.isStarted) {
      return handleResponse(ctx, 200, {
        message: "WebSocket服务器未启动",
        data: {
          isStarted: false,
          stats: null
        }
      });
    }
    
    return handleResponse(ctx, 200, {
      message: "获取WebSocket统计成功",
      data: {
        isStarted: status.isStarted,
        stats: status.stats,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('获取WebSocket统计失败', { error: error.message });
    return handleResponse(ctx, 500, { 
      message: "获取WebSocket统计失败",
      error: error.message 
    });
  }
});

// 健康检查接口（无需认证）
router.get("/health", async (ctx) => {
  try {
    const status = ctx.webSocketManager.getStatus();
    
    return handleResponse(ctx, 200, {
      message: "WebSocket健康检查",
      data: {
        isStarted: status.isStarted,
        healthy: true,
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      }
    });
    
  } catch (error) {
    return handleResponse(ctx, 500, { 
      message: "WebSocket健康检查失败",
      error: error.message 
    });
  }
});

module.exports = router;
