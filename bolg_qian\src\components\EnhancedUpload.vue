<template>
  <div class="enhanced-upload-container">
    <el-card class="upload-card">
      <!-- 头部标题 -->
      <div class="upload-header">
        <h3 class="title">
          <el-icon><Upload /></el-icon>
          文件上传中心
        </h3>
        <div class="upload-stats">
          <el-tag type="info" size="small">
            总计: {{ totalFiles }} 个文件
          </el-tag>
          <el-tag type="success" size="small" v-if="completedFiles > 0">
            已完成: {{ completedFiles }}
          </el-tag>
        </div>
      </div>

      <!-- 上传通道选择 -->
      <div class="upload-channels">
        <!-- 普通文件上传通道 -->
        <div class="upload-channel">
          <div class="channel-header">
            <div class="channel-title">
              <el-icon><Document /></el-icon>
              <span>普通文件上传</span>
            </div>
            <el-tag size="small" type="info">文档/图片/压缩包等</el-tag>
          </div>
          
          <el-upload
            ref="documentUploadRef"
            class="upload-area document-upload"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="(file) => onFileChange(file, 'document')"
            multiple
            drag
            :accept="documentAccept"
          >
            <div class="upload-content">
              <el-icon class="upload-icon"><Document /></el-icon>
              <div class="upload-text">
                <p>拖拽文件到此处或<em>点击上传</em></p>
                <p class="upload-hint">支持: DOC, PDF, TXT, ZIP, RAR, 图片等</p>
              </div>
            </div>
          </el-upload>

          <!-- 普通文件列表 -->
          <div v-if="documentFiles.length > 0" class="file-list">
            <div class="list-header">
              <span>普通文件 ({{ documentFiles.length }})</span>
              <el-button size="small" text @click="clearFiles('document')">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </div>
            <div class="file-items">
              <div
                v-for="(file, index) in documentFiles"
                :key="file.uid"
                class="file-item"
              >
                <div class="file-info">
                  <div class="file-icon-text">
                    {{ getFileIconText(file.name) }}
                  </div>
                  <div class="file-details">
                    <div class="file-name" :title="file.name">{{ file.name }}</div>
                    <div class="file-meta">
                      {{ formatFileSize(file.size) }} •
                      {{ getFileType(file.name) }}
                    </div>
                  </div>
                </div>
                <div class="file-actions">
                  <el-progress
                    v-if="file.progress !== undefined"
                    :percentage="file.progress"
                    :status="file.status"
                    :stroke-width="4"
                    class="file-progress"
                  />
                  <el-button
                    size="small"
                    text
                    type="danger"
                    @click="removeFile(index, 'document')"
                    :disabled="uploading"
                  >
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 视频媒体上传通道 -->
        <div class="upload-channel">
          <div class="channel-header">
            <div class="channel-title">
              <el-icon><VideoPlay /></el-icon>
              <span>视频媒体上传</span>
            </div>
            <el-tag size="small" type="warning">视频/音频文件</el-tag>
          </div>
          
          <el-upload
            ref="mediaUploadRef"
            class="upload-area media-upload"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="(file) => onFileChange(file, 'media')"
            multiple
            drag
            :accept="mediaAccept"
          >
            <div class="upload-content">
              <el-icon class="upload-icon"><VideoPlay /></el-icon>
              <div class="upload-text">
                <p>拖拽视频到此处或<em>点击上传</em></p>
                <p class="upload-hint">支持: MP4, AVI, MOV, MP3, WAV等</p>
              </div>
            </div>
          </el-upload>

          <!-- 视频文件列表 -->
          <div v-if="mediaFiles.length > 0" class="file-list">
            <div class="list-header">
              <span>视频媒体 ({{ mediaFiles.length }})</span>
              <el-button size="small" text @click="clearFiles('media')">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </div>
            <div class="file-items">
              <div
                v-for="(file, index) in mediaFiles"
                :key="file.uid"
                class="file-item"
              >
                <div class="file-info">
                  <div class="file-icon-text">
                    {{ getFileIconText(file.name) }}
                  </div>
                  <div class="file-details">
                    <div class="file-name" :title="file.name">{{ file.name }}</div>
                    <div class="file-meta">
                      {{ formatFileSize(file.size) }} •
                      {{ getFileType(file.name) }}
                    </div>
                  </div>
                </div>
                <div class="file-actions">
                  <el-progress
                    v-if="file.progress !== undefined"
                    :percentage="file.progress"
                    :status="file.status"
                    :stroke-width="4"
                    class="file-progress"
                  />
                  <el-button
                    size="small"
                    text
                    type="danger"
                    @click="removeFile(index, 'media')"
                    :disabled="uploading"
                  >
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 上传控制区域 -->
      <div class="upload-controls">
        <div class="upload-info">
          <el-alert
            v-if="totalFiles > 0"
            :title="`准备上传 ${totalFiles} 个文件 (普通文件: ${documentFiles.length}, 视频媒体: ${mediaFiles.length})`"
            type="info"
            show-icon
            :closable="false"
          />
        </div>
        
        <div class="upload-buttons">
          <el-button
            type="primary"
            size="large"
            @click="startUpload"
            :loading="uploading"
            :disabled="totalFiles === 0"
          >
            <el-icon><Upload /></el-icon>
            {{ uploading ? '上传中...' : '开始上传' }}
          </el-button>
          
          <el-button
            size="large"
            @click="clearAllFiles"
            :disabled="uploading || totalFiles === 0"
          >
            <el-icon><Delete /></el-icon>
            清空全部
          </el-button>
        </div>
      </div>

      <!-- 全局上传进度 -->
      <div v-if="uploading" class="global-progress">
        <div class="progress-info">
          <span>总进度: {{ Math.round(globalProgress) }}%</span>
          <span>{{ completedFiles }}/{{ totalFiles }} 已完成</span>
        </div>
        <el-progress
          :percentage="Math.round(globalProgress)"
          :stroke-width="8"
          :show-text="false"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Upload,
  Document,
  VideoPlay,
  Delete,
  Close
} from '@element-plus/icons-vue';
import { UploadApi, UploadChunkApi, MergeChunksApi } from '../utils/api';

// 接口定义
interface UploadFile {
  uid: string;
  name: string;
  size: number;
  raw: File;
  progress?: number;
  status?: 'uploading' | 'success' | 'exception';
}

// 响应式数据
const documentFiles = ref<UploadFile[]>([]);
const mediaFiles = ref<UploadFile[]>([]);
const uploading = ref(false);
const completedFiles = ref(0);

// 用户信息
const user_id = localStorage.getItem("id") || "";
const username = localStorage.getItem("username") || "";

// 文件类型限制
const documentAccept = ".doc,.docx,.pdf,.txt,.zip,.rar,.7z,.jpg,.jpeg,.png,.gif,.bmp,.webp,.xlsx,.xls,.ppt,.pptx";
const mediaAccept = ".mp4,.avi,.mov,.wmv,.flv,.mkv,.mp3,.wav,.flac,.aac,.ogg";

// 计算属性
const totalFiles = computed(() => documentFiles.value.length + mediaFiles.value.length);

const globalProgress = computed(() => {
  if (totalFiles.value === 0) return 0;
  
  const totalProgress = [...documentFiles.value, ...mediaFiles.value]
    .reduce((sum, file) => sum + (file.progress || 0), 0);
  
  return totalProgress / totalFiles.value;
});

// 工具函数
const getFileType = (filename: string): string => {
  const ext = filename.split('.').pop()?.toLowerCase() || '';
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
  const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'];
  const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg'];
  const docExts = ['doc', 'docx', 'pdf', 'txt', 'xlsx', 'xls', 'ppt', 'pptx'];
  const archiveExts = ['zip', 'rar', '7z'];
  
  if (imageExts.includes(ext)) return '图片';
  if (videoExts.includes(ext)) return '视频';
  if (audioExts.includes(ext)) return '音频';
  if (docExts.includes(ext)) return '文档';
  if (archiveExts.includes(ext)) return '压缩包';
  return '文件';
};

const getFileIconText = (filename: string): string => {
  const type = getFileType(filename);
  switch (type) {
    case '图片': return '🖼️';
    case '视频': return '🎬';
    case '音频': return '🎵';
    case '文档': return '📄';
    case '压缩包': return '📦';
    default: return '📁';
  }
};

const formatFileSize = (size: number): string => {
  if (!size) return '0 B';
  const units = ['B', 'KB', 'MB', 'GB'];
  let index = 0;
  let fileSize = size;
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  return `${fileSize.toFixed(1)} ${units[index]}`;
};

// 文件操作函数
const onFileChange = (uploadFile: any, channel: 'document' | 'media') => {
  const file: UploadFile = {
    uid: uploadFile.uid,
    name: uploadFile.name,
    size: uploadFile.size,
    raw: uploadFile.raw,
    progress: 0,
    status: undefined
  };

  // 文件大小检查 (100MB限制)
  const maxSize = 100 * 1024 * 1024;
  if (file.size > maxSize) {
    ElMessage.error(`文件 ${file.name} 超过100MB限制`);
    return;
  }

  // 根据通道添加到对应列表
  if (channel === 'document') {
    // 检查是否为媒体文件
    const mediaExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'mp3', 'wav', 'flac', 'aac', 'ogg'];
    const ext = file.name.split('.').pop()?.toLowerCase() || '';
    if (mediaExts.includes(ext)) {
      ElMessage.warning(`${file.name} 是媒体文件，请使用视频媒体上传通道`);
      return;
    }
    documentFiles.value.push(file);
  } else {
    // 检查是否为媒体文件
    const mediaExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'mp3', 'wav', 'flac', 'aac', 'ogg'];
    const ext = file.name.split('.').pop()?.toLowerCase() || '';
    if (!mediaExts.includes(ext)) {
      ElMessage.warning(`${file.name} 不是媒体文件，请使用普通文件上传通道`);
      return;
    }
    mediaFiles.value.push(file);
  }

  ElMessage.success(`已添加文件: ${file.name}`);
};

const removeFile = (index: number, channel: 'document' | 'media') => {
  if (channel === 'document') {
    documentFiles.value.splice(index, 1);
  } else {
    mediaFiles.value.splice(index, 1);
  }
};

const clearFiles = (channel: 'document' | 'media') => {
  if (channel === 'document') {
    documentFiles.value = [];
  } else {
    mediaFiles.value = [];
  }
};

const clearAllFiles = () => {
  documentFiles.value = [];
  mediaFiles.value = [];
  completedFiles.value = 0;
};

// 上传逻辑
const startUpload = async () => {
  if (totalFiles.value === 0) {
    ElMessage.warning("请先选择文件");
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要上传 ${totalFiles.value} 个文件吗？\n普通文件: ${documentFiles.value.length} 个\n视频媒体: ${mediaFiles.value.length} 个`,
      '确认上传',
      { type: 'info' }
    );
  } catch {
    return;
  }

  uploading.value = true;
  completedFiles.value = 0;

  // 重置所有文件进度
  [...documentFiles.value, ...mediaFiles.value].forEach(file => {
    file.progress = 0;
    file.status = undefined;
  });

  try {
    // 并发上传所有文件
    const uploadTasks = [
      ...documentFiles.value.map(file => uploadFile(file, 'document')),
      ...mediaFiles.value.map(file => uploadFile(file, 'media'))
    ];

    // 使用 Promise.all 替代 allSettled 以兼容旧版本
    await Promise.all(
      uploadTasks.map(task =>
        task.catch(error => {
          console.error('上传任务失败:', error);
          return null;
        })
      )
    );

    ElMessage.success(`上传完成！成功: ${completedFiles.value}/${totalFiles.value}`);

    // 清空已完成的文件
    setTimeout(() => {
      clearAllFiles();
    }, 2000);

  } catch (error) {
    console.error('上传过程中出现错误:', error);
    ElMessage.error('上传过程中出现错误');
  } finally {
    uploading.value = false;
  }
};

const uploadFile = async (file: UploadFile, channel: 'document' | 'media') => {
  try {
    file.status = 'uploading';

    // 根据文件大小选择上传方式
    if (file.size < 10 * 1024 * 1024) { // 10MB以下普通上传
      await normalUpload(file, channel);
    } else { // 10MB以上分片上传
      await chunkUpload(file, channel);
    }

    file.progress = 100;
    file.status = 'success';
    completedFiles.value++;

  } catch (error) {
    console.error(`文件 ${file.name} 上传失败:`, error);
    file.status = 'exception';
    ElMessage.error(`文件 ${file.name} 上传失败`);
  }
};

const normalUpload = async (file: UploadFile, channel: 'document' | 'media') => {
  const formData = new FormData();
  formData.append("file", file.raw);
  formData.append("user_id", user_id);
  formData.append("username", username);
  formData.append("channel", channel); // 添加通道标识

  await UploadApi(formData, {
    onUploadProgress: (progressEvent: any) => {
      if (progressEvent.total) {
        file.progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      }
    },
  });
};

const chunkUpload = async (file: UploadFile, channel: 'document' | 'media') => {
  const chunkSize = 2 * 1024 * 1024; // 2MB per chunk
  const chunks = createChunks(file.raw, chunkSize);
  const fileHash = getFileHash(file.raw);
  const totalChunks = chunks.length;

  // 上传分片
  for (let i = 0; i < totalChunks; i++) {
    const formData = new FormData();
    formData.append("chunk", chunks[i]);
    formData.append("chunkIndex", i.toString());
    formData.append("fileHash", fileHash);
    formData.append("fileName", file.name);
    formData.append("user_id", user_id);
    formData.append("totalChunks", totalChunks.toString());
    formData.append("channel", channel); // 添加通道标识

    await UploadChunkApi(formData);
    file.progress = Math.round(((i + 1) / totalChunks) * 100);
  }

  // 合并分片
  await MergeChunksApi({
    fileHash,
    fileName: file.name,
    user_id,
    totalChunks,
    fileType: file.raw.type,
    fileSize: file.size,
    channel // 添加通道标识
  });
};

// 工具函数
const createChunks = (file: File, size: number = 2 * 1024 * 1024): Blob[] => {
  const result: Blob[] = [];
  let cur = 0;
  while (cur < file.size) {
    result.push(file.slice(cur, cur + size));
    cur += size;
  }
  return result;
};

const getFileHash = (file: File): string => {
  return `${file.name}_${file.size}_${file.lastModified}`;
};
</script>

<style scoped>
.enhanced-upload-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.upload-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 头部样式 */
.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f0f0;
}

.title {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.upload-stats {
  display: flex;
  gap: 8px;
}

/* 上传通道样式 */
.upload-channels {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.upload-channel {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.channel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.channel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

/* 上传区域样式 */
.upload-area {
  margin-bottom: 16px;
}

.upload-area :deep(.el-upload-dragger) {
  width: 100%;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fff;
  transition: all 0.3s;
}

.upload-area :deep(.el-upload-dragger:hover) {
  border-color: #409eff;
  background: #f0f9ff;
}

.document-upload :deep(.el-upload-dragger:hover) {
  border-color: #67c23a;
  background: #f0f9ff;
}

.media-upload :deep(.el-upload-dragger:hover) {
  border-color: #e6a23c;
  background: #fdf6ec;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.upload-icon {
  font-size: 32px;
  color: #c0c4cc;
  margin-bottom: 8px;
}

.upload-text p {
  margin: 4px 0;
  color: #606266;
}

.upload-text em {
  color: #409eff;
  font-style: normal;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
}

/* 文件列表样式 */
.file-list {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
  color: #303133;
}

.file-items {
  max-height: 300px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.file-item:hover {
  background: #f8f9fa;
}

.file-item:last-child {
  border-bottom: none;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.file-icon {
  font-size: 20px;
  color: #409eff;
  flex-shrink: 0;
}

.file-icon-text {
  font-size: 20px;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.file-meta {
  font-size: 12px;
  color: #909399;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.file-progress {
  width: 100px;
}

/* 控制区域样式 */
.upload-controls {
  margin-top: 24px;
}

.upload-info {
  margin-bottom: 16px;
}

.upload-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.upload-buttons .el-button {
  min-width: 120px;
  border-radius: 8px;
}

/* 全局进度样式 */
.global-progress {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-upload-container {
    padding: 12px;
  }

  .upload-channels {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .upload-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .upload-buttons {
    flex-direction: column;
  }

  .upload-buttons .el-button {
    width: 100%;
  }

  .file-actions {
    flex-direction: column;
    gap: 8px;
  }

  .file-progress {
    width: 80px;
  }
}

/* 动画效果 */
.file-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 状态颜色 */
.file-item[data-status="success"] .file-icon {
  color: #67c23a;
}

.file-item[data-status="exception"] .file-icon {
  color: #f56c6c;
}

.file-item[data-status="uploading"] .file-icon {
  color: #409eff;
}
</style>
