<!--author lion-->
<template>
  <el-input
    v-model="inputValue"
    :placeholder="placeholder"
    clearable
    @keyup.enter="onSearch"
    @clear="onClear"
    :suffix-icon="loading ? 'el-icon-loading' : 'el-icon-search'"
    @input="onInput"
    :style="{ width: width }"
  />
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: String,
  placeholder: { type: String, default: '请输入关键词' },
  loading: Boolean,
  debounce: { type: Number, default: 300 },
  width: { type: String, default: '320px' } // 新增自定义宽度参数
})
const emit = defineEmits(['update:modelValue', 'search', 'clear'])

const inputValue = ref(props.modelValue || '')

watch(() => props.modelValue, val => inputValue.value = val)

let timer = null
function onInput(val) {
  clearTimeout(timer)
  timer = setTimeout(() => {
    emit('update:modelValue', val)
  }, props.debounce)
}
function onSearch() {
  emit('search', inputValue.value)
}
function onClear() {
  inputValue.value = ''
  emit('clear')
  emit('search', '')
}
</script>