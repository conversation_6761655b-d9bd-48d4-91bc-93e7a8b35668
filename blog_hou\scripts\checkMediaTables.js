const db = require("../utils/db");

async function checkMediaTables() {
  try {
    console.log("🔍 检查媒体相关数据表...\n");
    console.log("=" * 50);

    // 1. 检查表是否存在
    console.log("📋 检查表结构:");
    const mediaTables = [
      "media_play_history",
      "media_likes", 
      "media_collections",
      "media_stats"
    ];

    for (const tableName of mediaTables) {
      try {
        const tableExists = await db.query(`
          SELECT TABLE_NAME 
          FROM INFORMATION_SCHEMA.TABLES 
          WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
        `, [tableName]);

        if (tableExists.length > 0) {
          const countResult = await db.query(`SELECT COUNT(*) as count FROM ${tableName}`);
          console.log(`✅ ${tableName}: 存在，${countResult[0].count} 行数据`);
          
          // 显示表结构
          const structure = await db.query(`DESCRIBE ${tableName}`);
          console.log(`   字段: ${structure.map(col => `${col.Field}(${col.Type})`).join(', ')}`);
        } else {
          console.log(`❌ ${tableName}: 不存在`);
        }
      } catch (error) {
        console.log(`❌ ${tableName}: 检查失败 - ${error.message}`);
      }
      console.log("");
    }

    // 2. 检查现有数据
    console.log("📊 检查现有数据:");
    
    // 检查media_stats表的数据
    try {
      const statsData = await db.query("SELECT * FROM media_stats LIMIT 5");
      console.log("media_stats 示例数据:");
      statsData.forEach((row, index) => {
        console.log(`  ${index + 1}. ${row.file_name} - 播放:${row.play_count}, 喜欢:${row.like_count}, 收藏:${row.collect_count}`);
      });
    } catch (error) {
      console.log("❌ 无法查询 media_stats:", error.message);
    }

    // 检查media_likes表的数据
    try {
      const likesData = await db.query("SELECT * FROM media_likes LIMIT 5");
      console.log("\nmedia_likes 示例数据:");
      likesData.forEach((row, index) => {
        console.log(`  ${index + 1}. 用户${row.user_id} 喜欢 ${row.file_name}`);
      });
    } catch (error) {
      console.log("❌ 无法查询 media_likes:", error.message);
    }

    // 检查media_collections表的数据
    try {
      const collectionsData = await db.query("SELECT * FROM media_collections LIMIT 5");
      console.log("\nmedia_collections 示例数据:");
      collectionsData.forEach((row, index) => {
        console.log(`  ${index + 1}. 用户${row.user_id} 收藏 ${row.file_name}`);
      });
    } catch (error) {
      console.log("❌ 无法查询 media_collections:", error.message);
    }

    // 检查media_play_history表的数据
    try {
      const historyData = await db.query("SELECT * FROM media_play_history LIMIT 5");
      console.log("\nmedia_play_history 示例数据:");
      historyData.forEach((row, index) => {
        console.log(`  ${index + 1}. 用户${row.user_id} 播放 ${row.file_name} - 进度:${row.progress}%`);
      });
    } catch (error) {
      console.log("❌ 无法查询 media_play_history:", error.message);
    }

    console.log("\n🎉 数据库检查完成！");
  } catch (error) {
    console.error("❌ 检查失败:", error);
  } finally {
    process.exit(0);
  }
}

// 运行检查
checkMediaTables();
