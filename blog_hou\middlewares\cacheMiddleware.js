// 响应缓存中间件
const logger = require('../plugin/logger');
const crypto = require('crypto');

// 简单内存缓存 (生产环境建议使用Redis)
const cache = new Map();

// 缓存配置
const CACHE_CONFIG = {
  // 默认缓存时间 (5分钟)
  defaultTTL: 5 * 60 * 1000,
  
  // 最大缓存条目数
  maxEntries: 1000,
  
  // 缓存键前缀
  keyPrefix: 'api_cache:',
  
  // 不同类型的缓存时间
  ttlByType: {
    'articles': 10 * 60 * 1000,      // 文章缓存10分钟
    'users': 5 * 60 * 1000,          // 用户信息缓存5分钟
    'comments': 3 * 60 * 1000,       // 评论缓存3分钟
    'media': 30 * 60 * 1000,         // 媒体信息缓存30分钟
    'dashboard': 2 * 60 * 1000,      // 仪表盘缓存2分钟
    'logs': 1 * 60 * 1000,           // 日志缓存1分钟
    'static': 60 * 60 * 1000         // 静态数据缓存1小时
  }
};

// 生成缓存键
function generateCacheKey(method, url, query, userId = null) {
  const keyData = {
    method,
    url,
    query: JSON.stringify(query || {}),
    userId
  };
  
  const keyString = JSON.stringify(keyData);
  const hash = crypto.createHash('md5').update(keyString).digest('hex');
  
  return `${CACHE_CONFIG.keyPrefix}${hash}`;
}

// 检查缓存是否过期
function isExpired(cacheEntry) {
  return Date.now() > cacheEntry.expiry;
}

// 清理过期缓存
function cleanupExpiredCache() {
  const now = Date.now();
  let cleanedCount = 0;
  
  for (const [key, entry] of cache.entries()) {
    if (now > entry.expiry) {
      cache.delete(key);
      cleanedCount++;
    }
  }
  
  if (cleanedCount > 0) {
    logger.debug(`清理过期缓存: ${cleanedCount} 条`);
  }
  
  // 如果缓存条目过多，清理最旧的条目
  if (cache.size > CACHE_CONFIG.maxEntries) {
    const entries = Array.from(cache.entries());
    entries.sort((a, b) => a[1].createdAt - b[1].createdAt);
    
    const toDelete = entries.slice(0, cache.size - CACHE_CONFIG.maxEntries);
    toDelete.forEach(([key]) => cache.delete(key));
    
    logger.info(`清理最旧缓存: ${toDelete.length} 条`);
  }
}

// 每5分钟清理一次过期缓存
setInterval(cleanupExpiredCache, 5 * 60 * 1000);

// 获取缓存类型
function getCacheType(path) {
  if (path.includes('/articles')) return 'articles';
  if (path.includes('/user')) return 'users';
  if (path.includes('/comments')) return 'comments';
  if (path.includes('/media')) return 'media';
  if (path.includes('/dashboard')) return 'dashboard';
  if (path.includes('/logs')) return 'logs';
  return 'default';
}

// 检查是否应该缓存
function shouldCache(ctx) {
  const method = ctx.method;
  const path = ctx.path;
  const status = ctx.status;
  
  // 只缓存GET请求
  if (method !== 'GET') return false;
  
  // 只缓存成功响应
  if (status !== 200) return false;
  
  // 排除某些路径
  const excludePaths = [
    '/user/profile',     // 用户个人信息
    '/logs',            // 实时日志
    '/dashboard/realtime', // 实时数据
    '/friend/online'    // 在线状态
  ];
  
  if (excludePaths.some(excludePath => path.includes(excludePath))) {
    return false;
  }
  
  // 排除包含敏感参数的请求
  const sensitiveParams = ['password', 'token', 'secret'];
  const queryString = ctx.querystring.toLowerCase();
  if (sensitiveParams.some(param => queryString.includes(param))) {
    return false;
  }
  
  return true;
}

// 创建缓存中间件
function createCacheMiddleware(options = {}) {
  const {
    ttl = CACHE_CONFIG.defaultTTL,
    keyGenerator = generateCacheKey,
    shouldCacheFunc = shouldCache
  } = options;
  
  return async (ctx, next) => {
    const method = ctx.method;
    const path = ctx.path;
    const query = ctx.query;
    const userId = ctx.state.user?.id;
    
    // 生成缓存键
    const cacheKey = keyGenerator(method, path, query, userId);
    
    // 检查缓存
    const cachedEntry = cache.get(cacheKey);
    if (cachedEntry && !isExpired(cachedEntry)) {
      // 缓存命中
      ctx.status = cachedEntry.status;
      ctx.body = cachedEntry.body;
      ctx.set(cachedEntry.headers);
      ctx.set('X-Cache', 'HIT');
      ctx.set('X-Cache-Key', cacheKey.substring(0, 16) + '...');
      
      logger.debug('缓存命中', {
        path,
        cacheKey: cacheKey.substring(0, 16) + '...',
        age: Date.now() - cachedEntry.createdAt
      });
      
      return;
    }
    
    // 缓存未命中，执行请求
    await next();
    
    // 检查是否应该缓存响应
    if (!shouldCacheFunc(ctx)) {
      ctx.set('X-Cache', 'SKIP');
      return;
    }
    
    // 获取缓存TTL
    const cacheType = getCacheType(path);
    const cacheTTL = CACHE_CONFIG.ttlByType[cacheType] || ttl;
    
    // 存储到缓存
    const cacheEntry = {
      status: ctx.status,
      body: ctx.body,
      headers: { ...ctx.response.headers },
      createdAt: Date.now(),
      expiry: Date.now() + cacheTTL,
      type: cacheType
    };
    
    cache.set(cacheKey, cacheEntry);
    
    // 设置缓存响应头
    ctx.set('X-Cache', 'MISS');
    ctx.set('X-Cache-TTL', Math.floor(cacheTTL / 1000).toString());
    ctx.set('X-Cache-Key', cacheKey.substring(0, 16) + '...');
    
    logger.debug('响应已缓存', {
      path,
      cacheKey: cacheKey.substring(0, 16) + '...',
      ttl: cacheTTL,
      type: cacheType
    });
  };
}

// 手动清理缓存
function clearCache(pattern = null) {
  if (!pattern) {
    const size = cache.size;
    cache.clear();
    logger.info(`清空所有缓存: ${size} 条`);
    return size;
  }
  
  let cleared = 0;
  for (const [key, entry] of cache.entries()) {
    if (key.includes(pattern) || (entry.type && entry.type.includes(pattern))) {
      cache.delete(key);
      cleared++;
    }
  }
  
  logger.info(`按模式清理缓存: ${cleared} 条`, { pattern });
  return cleared;
}

// 预热缓存
async function warmupCache(routes = []) {
  logger.info('开始缓存预热', { routeCount: routes.length });
  
  for (const route of routes) {
    try {
      // 这里可以实现预热逻辑
      // 例如：发送内部请求到指定路由
      logger.debug('预热路由', { route });
    } catch (error) {
      logger.error('缓存预热失败', { route, error: error.message });
    }
  }
  
  logger.info('缓存预热完成');
}

// 获取缓存统计信息
function getCacheStats() {
  const stats = {
    totalEntries: cache.size,
    maxEntries: CACHE_CONFIG.maxEntries,
    memoryUsage: 0,
    hitRate: 0,
    typeDistribution: {},
    oldestEntry: null,
    newestEntry: null
  };
  
  let totalHits = 0;
  let totalRequests = 0;
  let oldestTime = Date.now();
  let newestTime = 0;
  
  for (const [key, entry] of cache.entries()) {
    // 估算内存使用
    stats.memoryUsage += JSON.stringify(entry).length;
    
    // 统计类型分布
    const type = entry.type || 'unknown';
    stats.typeDistribution[type] = (stats.typeDistribution[type] || 0) + 1;
    
    // 找到最旧和最新的条目
    if (entry.createdAt < oldestTime) {
      oldestTime = entry.createdAt;
      stats.oldestEntry = {
        key: key.substring(0, 16) + '...',
        createdAt: new Date(entry.createdAt).toISOString(),
        type: entry.type
      };
    }
    
    if (entry.createdAt > newestTime) {
      newestTime = entry.createdAt;
      stats.newestEntry = {
        key: key.substring(0, 16) + '...',
        createdAt: new Date(entry.createdAt).toISOString(),
        type: entry.type
      };
    }
  }
  
  // 转换内存使用为KB
  stats.memoryUsage = Math.round(stats.memoryUsage / 1024);
  
  return stats;
}

// 缓存健康检查
function healthCheck() {
  const stats = getCacheStats();
  const health = {
    status: 'healthy',
    issues: []
  };
  
  // 检查内存使用
  if (stats.memoryUsage > 50 * 1024) { // 50MB
    health.issues.push('缓存内存使用过高');
    health.status = 'warning';
  }
  
  // 检查缓存条目数
  if (stats.totalEntries > CACHE_CONFIG.maxEntries * 0.9) {
    health.issues.push('缓存条目接近上限');
    health.status = 'warning';
  }
  
  if (health.issues.length === 0) {
    health.message = '缓存运行正常';
  }
  
  return health;
}

module.exports = {
  // 中间件
  cacheMiddleware: createCacheMiddleware(),
  createCacheMiddleware,
  
  // 管理功能
  clearCache,
  warmupCache,
  getCacheStats,
  healthCheck,
  
  // 配置
  CACHE_CONFIG
};
