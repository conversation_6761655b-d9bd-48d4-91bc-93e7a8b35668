<template>
  <div 
    ref="imageContainer"
    class="lazy-image-container"
    :class="{ 'loading': isLoading, 'error': hasError }"
  >
    <!-- 占位符 -->
    <div 
      v-if="isLoading && !hasError" 
      class="lazy-image-placeholder"
      :style="placeholderStyle"
    >
      <div class="loading-spinner"></div>
      <span v-if="showLoadingText">{{ loadingText }}</span>
    </div>
    
    <!-- 错误状态 -->
    <div 
      v-else-if="hasError" 
      class="lazy-image-error"
      :style="placeholderStyle"
      @click="retry"
    >
      <div class="error-icon">⚠️</div>
      <span>{{ errorText }}</span>
      <button class="retry-btn">重试</button>
    </div>
    
    <!-- 实际图片 -->
    <img
      v-else
      :src="currentSrc"
      :alt="alt"
      :class="imageClass"
      :style="imageStyle"
      @load="onLoad"
      @error="onError"
    />
    
    <!-- 渐进式加载效果 -->
    <transition name="fade">
      <img
        v-if="showPreview && previewSrc && !isLoading"
        :src="previewSrc"
        class="lazy-image-preview"
        :style="imageStyle"
      />
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

interface Props {
  src: string
  alt?: string
  width?: number | string
  height?: number | string
  placeholder?: string
  previewSrc?: string // 低质量预览图
  loadingText?: string
  errorText?: string
  showLoadingText?: boolean
  lazy?: boolean
  threshold?: number
  imageClass?: string
  retryCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  loadingText: '加载中...',
  errorText: '加载失败',
  showLoadingText: true,
  lazy: true,
  threshold: 0.1,
  retryCount: 3
})

const emit = defineEmits<{
  load: [event: Event]
  error: [event: Event]
  intersect: [isIntersecting: boolean]
}>()

const imageContainer = ref<HTMLElement>()
const isLoading = ref(true)
const hasError = ref(false)
const isIntersecting = ref(false)
const currentRetryCount = ref(0)
const observer = ref<IntersectionObserver>()

// 当前显示的图片源
const currentSrc = computed(() => {
  if (!props.lazy || isIntersecting.value) {
    return props.src
  }
  return props.placeholder || ''
})

// 是否显示预览图
const showPreview = computed(() => {
  return props.previewSrc && !hasError.value
})

// 占位符样式
const placeholderStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
}))

// 图片样式
const imageStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
}))

// 图片加载成功
const onLoad = (event: Event) => {
  isLoading.value = false
  hasError.value = false
  currentRetryCount.value = 0
  emit('load', event)
}

// 图片加载失败
const onError = (event: Event) => {
  if (currentRetryCount.value < props.retryCount) {
    // 自动重试
    setTimeout(() => {
      retry()
    }, 1000 * Math.pow(2, currentRetryCount.value)) // 指数退避
  } else {
    isLoading.value = false
    hasError.value = true
    emit('error', event)
  }
}

// 重试加载
const retry = () => {
  if (currentRetryCount.value < props.retryCount) {
    currentRetryCount.value++
    isLoading.value = true
    hasError.value = false
    
    // 强制重新加载图片
    const img = new Image()
    img.onload = onLoad
    img.onerror = onError
    img.src = props.src + '?retry=' + currentRetryCount.value
  }
}

// 设置 Intersection Observer
const setupObserver = () => {
  if (!props.lazy || !imageContainer.value) return

  observer.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        isIntersecting.value = entry.isIntersecting
        emit('intersect', entry.isIntersecting)
        
        if (entry.isIntersecting) {
          // 进入视口后停止观察
          observer.value?.unobserve(entry.target)
        }
      })
    },
    {
      threshold: props.threshold,
      rootMargin: '50px' // 提前50px开始加载
    }
  )

  observer.value.observe(imageContainer.value)
}

// 清理 Observer
const cleanupObserver = () => {
  if (observer.value) {
    observer.value.disconnect()
  }
}

// 监听src变化，重置状态
watch(() => props.src, () => {
  isLoading.value = true
  hasError.value = false
  currentRetryCount.value = 0
  isIntersecting.value = false
  
  if (props.lazy) {
    setupObserver()
  }
})

onMounted(() => {
  if (props.lazy) {
    setupObserver()
  } else {
    isIntersecting.value = true
  }
})

onUnmounted(() => {
  cleanupObserver()
})

defineExpose({
  retry,
  reload: () => {
    currentRetryCount.value = 0
    retry()
  }
})
</script>

<style scoped>
.lazy-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.lazy-image-placeholder,
.lazy-image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #999;
  font-size: 14px;
  min-height: 100px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

.error-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.retry-btn {
  margin-top: 8px;
  padding: 4px 12px;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.retry-btn:hover {
  background-color: #66b1ff;
}

.lazy-image-preview {
  position: absolute;
  top: 0;
  left: 0;
  filter: blur(5px);
  z-index: 1;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}
</style>
