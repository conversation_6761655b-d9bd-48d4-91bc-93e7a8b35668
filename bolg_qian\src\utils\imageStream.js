import { getImageStreamApi } from './api';

/**
 * 获取图片blob URL
 * @param {string} imagePath 图片路径
 * @returns {Promise<string>} blob URL
 */
export const getImageBlobUrl = async (imagePath) => {
  try {
    // 从路径中提取文件名
    const filename = imagePath.split('/').pop();

    // 优先尝试直接访问静态资源（支持局域网访问）
    try {
      const staticUrl = `/images/${filename}`;
      const response = await fetch(staticUrl);
      if (response.ok) {
        const blob = await response.blob();
        return URL.createObjectURL(blob);
      }
    } catch (staticError) {
      console.log('静态资源访问失败，尝试API访问:', staticError);
    }

    // 回退到API接口访问图片（需要token验证）
    const apiPath = `/showwall/image/${filename}`;
    const response = await getImageStreamApi(apiPath);
    const blob = new Blob([response], { type: 'image/*' });
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error('获取图片blob URL失败:', error);
    throw error;
  }
};

/**
 * 预加载多张图片
 * @param {string[]} imagePaths 图片路径数组
 * @returns {Promise<Map<string, string>>} 图片路径到blob URL的映射
 */
export const preloadImages = async (imagePaths) => {
  const imageMap = new Map();
  const promises = imagePaths.map(async (path) => {
    try {
      const blobUrl = await getImageBlobUrl(path);
      imageMap.set(path, blobUrl);
    } catch (error) {
      console.error(`预加载图片失败: ${path}`, error);
    }
  });

  await Promise.allSettled(promises);
  return imageMap;
};

/**
 * 清理单个blob URL
 * @param {string} blobUrl blob URL
 */
export const revokeBlobUrl = (blobUrl) => {
  if (blobUrl && blobUrl.startsWith('blob:')) {
    URL.revokeObjectURL(blobUrl);
  }
};

/**
 * 批量清理blob URL
 * @param {string[]} blobUrls blob URL数组
 */
export const revokeBlobUrls = (blobUrls) => {
  blobUrls.forEach(revokeBlobUrl);
};

/**
 * 清理图片映射中的所有blob URL
 * @param {Map<string, string>} imageMap 图片映射
 */
export const revokeImageMap = (imageMap) => {
  imageMap.forEach((blobUrl) => {
    revokeBlobUrl(blobUrl);
  });
  imageMap.clear();
};

