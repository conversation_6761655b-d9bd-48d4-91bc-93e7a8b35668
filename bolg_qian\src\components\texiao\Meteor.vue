<template>
    <canvas ref="canvas" class="meteor-canvas"></canvas>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

const props = defineProps({
    count: { type: Number, default: 25 }, // 流星数量
    speed: { type: Number, default: 4 },  // 最小速度
    colorStart: { type: String, default: '#ffffff' }, // 尾巴开始颜色
    colorEnd: { type: String, default: '#00ffff' },   // 尾巴结束颜色
})

const canvas = ref(null)
let ctx
let meteors = []
let animationId
let WIDTH = window.innerWidth
let HEIGHT = window.innerHeight

class Meteor {
    constructor() {
        this.reset()
    }

    reset() {
        this.x = Math.random() * WIDTH
        this.y = Math.random() * -HEIGHT
        this.length = Math.random() * 100 + 80
        this.speed = Math.random() * 3 + props.speed
        this.angle = Math.PI / 4
        this.alpha = Math.random() * 0.5 + 0.5
        this.width = Math.random() * 1.5 + 0.5
    }

    update() {
        this.x += this.speed * Math.cos(this.angle)
        this.y += this.speed * Math.sin(this.angle)
        if (this.x > WIDTH || this.y > HEIGHT) {
            this.reset()
        }
    }

    draw(ctx) {
        const xEnd = this.x - this.length * Math.cos(this.angle)
        const yEnd = this.y - this.length * Math.sin(this.angle)

        const gradient = ctx.createLinearGradient(this.x, this.y, xEnd, yEnd)
        gradient.addColorStop(0, props.colorStart)
        gradient.addColorStop(1, props.colorEnd)

        ctx.save()
        ctx.beginPath()
        ctx.globalAlpha = this.alpha
        ctx.strokeStyle = gradient
        ctx.lineWidth = this.width
        ctx.moveTo(this.x, this.y)
        ctx.lineTo(xEnd, yEnd)
        ctx.stroke()
        ctx.restore()
    }
}

function createMeteors(count) {
    meteors = []
    for (let i = 0; i < count; i++) {
        meteors.push(new Meteor())
    }
}

function animate() {
    ctx.fillStyle = 'rgba(0, 0, 0, 0.2)' // 轻微残影
    ctx.globalCompositeOperation = 'destination-in'
    ctx.fillRect(0, 0, WIDTH, HEIGHT)
    ctx.globalCompositeOperation = 'lighter' // 颜色叠加增强亮度

    meteors.forEach(meteor => {
        meteor.update()
        meteor.draw(ctx)
    })

    animationId = requestAnimationFrame(animate)
}

function resizeCanvas() {
    WIDTH = window.innerWidth
    HEIGHT = window.innerHeight
    const canvasEl = canvas.value
    canvasEl.width = WIDTH
    canvasEl.height = HEIGHT
}

onMounted(async () => {
    await nextTick()
    const canvasEl = canvas.value
    resizeCanvas()
    ctx = canvasEl.getContext('2d')

    createMeteors(props.count)
    animate()

    window.addEventListener('resize', resizeCanvas)
})


onUnmounted(() => {
    cancelAnimationFrame(animationId)
    window.removeEventListener('resize', resizeCanvas)
})
</script>

<style scoped>
.meteor-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}
</style>
