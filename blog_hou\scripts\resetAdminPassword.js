const db = require('../utils/db');
const { hashPassword } = require('../plugin/crypto');

async function resetAdminPassword() {
  try {
    console.log('🔧 重置admin123密码...');
    
    // 新密码
    const newPassword = '123456';
    const hashedPassword = await hashPassword(newPassword);
    
    console.log('新密码:', newPassword);
    console.log('加密后:', hashedPassword);
    
    // 更新密码
    const result = await db.query(
      'UPDATE users SET password = ? WHERE username = ?',
      [hashedPassword, 'admin123']
    );
    
    if (result.affectedRows > 0) {
      console.log('✅ admin123密码重置成功');
      console.log('新密码: 123456');
      
      // 验证用户信息
      const userInfo = await db.query(
        'SELECT id, username, email, role FROM users WHERE username = ?',
        ['admin123']
      );
      
      if (userInfo.length > 0) {
        console.log('\n👤 用户信息:');
        console.log('ID:', userInfo[0].id);
        console.log('用户名:', userInfo[0].username);
        console.log('邮箱:', userInfo[0].email);
        console.log('角色:', userInfo[0].role);
      }
    } else {
      console.log('❌ 未找到admin123用户');
    }
    
  } catch (error) {
    console.error('❌ 重置密码失败:', error.message);
  } finally {
    process.exit(0);
  }
}

resetAdminPassword();
